# KG Studio Investigation Guide

This guide provides a structured approach to understanding the KG Studio codebase. Use it as a reference while exploring the project.

## 1. Project Setup and Configuration

### Key Files
- Project Configuration: `src/env.mjs`
- App Entry: `src/app/provider.tsx`
- Middleware: `src/middleware.ts`
- Root Layout: `src/app/[locale]/layout.tsx`

### Environment and Build
- Package dependencies and scripts in `package.json`
- Environment configurations (`.env.*` files)
- Build configuration (`next.config.mjs`, `tailwind.config.ts`)
- TypeScript configuration (`tsconfig.json`)

### Key Technical Components
- Modern Next.js application with TypeScript and strict type checking
- Multi-chain support (Ethereum, Polygon, BSC, Arbitrum)
- Shared component library `@kryptogo/2b`
- Error tracking via Sentry
- Internationalization with Localizely
- Modern styling with Tailwind CSS and container queries

## 2. Application Architecture

### Core Structure
- Next.js App Router with directory-based routing (`src/app/[locale]/*`)
- Internationalization via dynamic `[locale]` routing
- Protected routes under `(protected)` directory
- Centralized error handling (`src/app/error.tsx`)

### State Management & Data Layer
Files: `src/app/_common/store/*`
- `useAuthStore.ts`: Authentication state
- `useOrganizationStore.ts`: Organization context
- `useLayoutStore.ts`: UI layout state

### Common Utilities Organization
- `src/app/_common/components/`: Shared UI components
- `src/app/_common/hooks/`: Custom React hooks
- `src/app/_common/lib/`: Utility functions
- `src/app/_common/services/`: API services
- `src/app/_common/store/`: State management
- `src/app/_common/types/`: TypeScript definitions

## 3. Core Features

### Dashboard & Overview
Entry Point: `src/app/[locale]/(protected)/home/<USER>/page.tsx`
Components:
- Organization information management
- Module-based dashboard layout
- Permission-based feature access
- Statistics and compliance monitoring
- Transaction management (approve/release)

### Wallet Management System
Directory: `src/app/[locale]/(protected)/wallet/*`
Key Files:
- `projects/`: Project management
- `_services/`: API integration
- `_types/`: Type definitions

### User360 Platform
Directory: `src/app/[locale]/(protected)/user360/*`
Features:
- Audience management (`audience/*`)
- Data analytics (`data/*`)
- User engagement features
- Component-rich UI system
- Service integration layer

### Asset Management System
Directory: `src/app/[locale]/(protected)/asset/*`
Components:
- Financial operations (`finance/*`)
- Order management (`orders/*`)
- Market settings (`market-settings/*`)
- Transaction monitoring
- Operator management

### NFT Campaign System
Directory: `src/app/[locale]/(protected)/nft/*`
Features:
- Campaign management (`campaign/*`)
- NFT creation and distribution
- Campaign tracking and analytics

## 4. Data Flow and Integration

### Global State Management
Files: `src/app/_common/store/*`
```typescript
// Example from useOrgStore.ts
export const useOrganizationStore = create(
  persist(
    immer<OrganizationState>((set) => ({
      orgId: null,
      orgInfo: null,
      setOrgId: (id) => set((state) => { state.orgId = id; }),
      setOrgInfo: (info) => set((state) => { state.orgInfo = info; }),
    })),
    {
      name: 'org-storage',
    }
  )
);
```

### API Integration
Directory: `src/app/_common/services/*`
Example API Client:
```typescript
// Example from organization/index.ts
const apiClient = new Zodios(API_BASE_URL, apiDefinition, {
  axiosInstance: customAxiosInstance
});
```

### Feature Organization Pattern
Example from Wallet feature:
```
src/app/[locale]/(protected)/wallet/
├── _components/   # UI components
├── _services/     # API integration
├── _types/        # Type definitions
├── _utils/        # Helper functions
├── _lib/          # Core logic
└── _constants/    # Constants
```

## 5. Architecture Deep Dive

### 1. State Management
The application uses a layered state management approach:

1. **Global State (Zustand)**
   Files: `src/app/_common/store/*`
   - `useAuthStore.ts`: Manages authentication state and user info
   - `useOrganizationStore.ts`: Handles organization context with persistence
   - `useLayoutStore.ts`: Controls UI layout state (page headers, etc.)

   Example from `useOrganizationStore.ts`:
   ```typescript
   export const useOrganizationStore = create(
     persist(
       immer<OrganizationState>((set) => ({
         orgId: null,
         orgInfo: null,
         setOrgId: (id) => set((state) => { state.orgId = id; }),
         setOrgInfo: (info) => set((state) => { state.orgInfo = info; }),
       })),
       {
         name: 'org-storage',
       }
     )
   );
   ```

2. **Server State (TanStack Query)**
   Files: `src/app/_common/hooks/useQuery*`
   - Handles all API data fetching and caching
   - Automatic background refetching
   - Optimistic updates for better UX

   Example from `src/app/provider.tsx`:
   ```typescript
   export default function Providers({ children }: { children: React.ReactNode }) {
     const [queryClient] = useState(generateStudioQueryClient);
     return (
       <QueryClientProvider client={queryClient}>
         {/* ... */}
       </QueryClientProvider>
     );
   }
   ```

3. **Web3 State (Wagmi)**
   Files: `src/app/_common/lib/clients.ts`
   - Manages blockchain connections
   - Handles wallet states
   - Chain-specific configurations

### 2. Data Flow Pattern
The application follows a clear data flow pattern:

```
User Action (src/app/[locale]/(protected)/*/page.tsx)
  ↓
Component (src/app/_common/components/*)
  ↓
Zustand Store (src/app/_common/store/*)
  ↓
API Hooks (src/app/_common/services/*)
  ↓
Backend Services
```

Key characteristics:
- Unidirectional data flow
- Type-safe API calls using Zodios
- Centralized state management
- Clear separation of concerns

### 3. API Integration
The application uses Zodios for type-safe API integration:

1. **API Clients** (`src/app/_common/services/*`)
   - Organization management (`organization/`)
   - Asset management (`asset-pro/`)
   - User360 platform (`user360/`)
   - Compliance services (`compliance/`)
   - Proxy services (`proxy/`)

2. **API Structure**
   Example from `src/app/_common/services/organization/index.ts`:
   ```typescript
   const apiClient = new Zodios(API_BASE_URL, apiDefinition, {
     axiosInstance: customAxiosInstance
   });
   ```

3. **Type Safety** (`src/app/_common/lib/zod.ts`)
   - All API endpoints are defined with Zod schemas
   - Runtime type validation
   - Complete TypeScript integration

### 4. Security Implementation

1. **Authentication** (`src/app/[locale]/auth/*`)
   - OAuth token-based authentication
   - Automatic token refresh
   - Session management

2. **Authorization** (`src/middleware.ts`)
   - Role-based access control
   - Protected routes
   - Permission checks

3. **Data Protection**
   - Type-safe API calls
   - Secure environment variables (`src/env.mjs`)
   - Request/Response validation

### 5. Performance Optimizations

1. **Build Optimization** (`next.config.mjs`)
   - Code splitting by route
   - Dynamic imports
   - Tree shaking
   - Bundle size optimization

2. **Runtime Optimization**
   - TanStack Query caching (`src/app/provider.tsx`)
   - Static page generation
   - Image optimization
   - Font optimization

3. **State Management**
   - Efficient updates with Immer
   - Persistent storage with Zustand
   - Optimistic updates
   - Proper dependency management

### 6. Feature Organization
Each feature follows a consistent pattern:

```
src/app/[locale]/(protected)/[feature]/
├── _components/   # UI components
├── _services/     # API integration
├── _types/        # Type definitions
├── _utils/        # Helper functions
├── _lib/          # Core logic
└── _constants/    # Constants
```

Example implementations:
- Wallet Management: `src/app/[locale]/(protected)/wallet/*`
- User360: `src/app/[locale]/(protected)/user360/*`
- Asset Management: `src/app/[locale]/(protected)/asset/*`
- NFT Campaigns: `src/app/[locale]/(protected)/nft/*`

## 6. Development Guidelines

### Testing Strategy
Files:
- Unit Tests: `__tests__/`
- E2E Tests: `cypress/`
- Test Utils: `test/utils/`

### Internationalization
Files:
- Translation Files: `src/locales/`
- i18n Config: `next-i18next.config.js`

### Performance Optimization
Key Files:
- `next.config.mjs`: Build optimization
- `src/app/provider.tsx`: App providers and context
- `src/middleware.ts`: Request middleware

## Development Tools

```bash
# Start development server
pnpm dev

# Run tests
pnpm test:unit
pnpm e2e
```

### Key Entry Points
- `src/app/layout.tsx` - Main application layout
- `src/middleware.ts` - Request middleware
- `src/env.mjs` - Environment configuration
- `src/app/provider.tsx` - App providers

### Documentation Resources
- Review inline comments and TypeScript types
- Check Confluence/internal documentation
- Study test files for behavior documentation

## Next Steps

1. **For Developers**
   Start with:
   - `src/app/[locale]/(protected)/home/<USER>/page.tsx`
   - `src/app/_common/store/*`
   - `src/app/_common/services/*`

2. **For Contributors**
   Review:
   - `src/app/_common/components/`
   - `src/app/_common/hooks/`
   - Test files in `__tests__/`

Remember to update this guide as you discover new insights about the codebase.
