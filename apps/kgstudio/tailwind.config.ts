import type { Config } from 'tailwindcss';

import sharedConfig from '@kryptogo/configs/tailwind/tailwind-2b.config';

const tailwindConfigs: Config = {
  presets: [sharedConfig],
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar-hide'),
    require('@tailwindcss/container-queries'),
  ],
  content: [
    './src/**/*.{js,jsx,ts,tsx,mdx}',
    // This is for the shared components, so that tailwind will be able to find the styles
    '../../packages/kryptogo-2b/src/**/*.{js,jsx,ts,tsx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        outfit: ['var(--font-outfit)'],
      },
      animation: {
        shake: 'shake 0.82s cubic-bezier(.36,.07,.19,.97) both',
      },
      keyframes: {
        shake: {
          '10%, 90%': { transform: 'translate3d(-1px, 0, 0)' },
          '20%, 80%': { transform: 'translate3d(2px, 0, 0)' },
          '30%, 50%, 70%': { transform: 'translate3d(-4px, 0, 0)' },
          '40%, 60%': { transform: 'translate3d(4px, 0, 0)' },
        },
      },
    },
  },
};
export default tailwindConfigs;
