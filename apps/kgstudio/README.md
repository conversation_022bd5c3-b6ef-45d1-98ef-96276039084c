# KG Studio

KG Studio is KryptoGO's comprehensive Web3 infrastructure platform for managing DApps, user engagement, and blockchain operations.

## 🚀 Quick Start

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Run tests
pnpm test:unit    # Unit tests
pnpm e2e          # E2E tests
```

## 🏗 Tech Stack

- **Framework**: Next.js with App Router
- **Language**: TypeScript
- **State Management**:
  - <PERSON>ust<PERSON> for global state
  - TanStack Query for server state
  - Wagmi for Web3 state
- **UI**: Combination of Chakra UI and Radix UI
- **Styling**: Tailwind CSS
- **Testing**: Cypress (E2E), Vitest (Unit)
- **Monitoring**: Sentry

## 🌟 Core Features

### 1. Wallet Management
- Project-based wallet organization
- Multi-chain support (Ethereum, Polygon, BSC, Arbitrum)
- Asset tracking and management

### 2. User360 Platform
- User analytics and engagement
- Audience management
- Data visualization
- Campaign management

### 3. Asset Management
- Financial operations
- Treasury management
- Order tracking
- Transaction monitoring

### 4. NFT Campaigns
- Campaign creation and management
- NFT distribution
- Analytics and tracking

### 5. Compliance Tools
- KYC/AML integration
- Regulatory compliance
- Review workflows

## 📦 Project Structure

```
src/
├── app/                 # Next.js app directory
│   ├── [locale]/       # Internationalization
│   ├── _common/        # Shared utilities
│   └── (protected)/    # Protected routes
├── assets/             # Static assets
└── styles/             # Global styles
```

## 🔑 Key Concepts

1. **State Management**
   - Global state: Authentication, organization context
   - Server state: API data through React Query
   - Persistent state: User preferences, organization settings

2. **Data Flow**
   ```
   User Action → Component → Store → API → Backend
   ```

3. **Feature Organization**
   ```
   feature/
   ├── _components/   # UI components
   ├── _services/     # API integration
   ├── _types/        # Type definitions
   └── _utils/        # Helpers
   ```

## 🌐 Internationalization

### Setup
1. Install Localizely CLI:
   ```bash
   # Download from https://localizely.com/cli/
   # Unzip and add to PATH
   localizely-cli --version
   ```

### Usage
```bash
# Pull latest translations
localizely-cli pull

# Push new translations
localizely-cli push

# Update CLI
localizely-cli update
```

### Configuration
- Main config: `localizely.config.js`
- Supported languages: en-US, zh-TW, zh-CN, es, ja, vi-VN
- Translation files: `src/locales/*`
- Integration: next-intl

### Development Workflow
1. Add new strings in source language (en-US)
2. Push to Localizely using CLI
3. Translators work on other languages
4. Pull updated translations before release

## 🔧 Common Tasks

1. **Adding New Features**
   - Follow the feature organization pattern
   - Use shared components from `_common`
   - Implement proper type safety
   - Add necessary translations

2. **Testing**
   - Write unit tests for business logic
   - Add E2E tests for critical flows
   - Run full test suite before PR

3. **Development Best Practices**
   - Use TypeScript strictly
   - Follow component patterns
   - Maintain proper error handling
   - Keep bundle size optimized

## 📚 Additional Resources

- [Investigation Guide](./INVESTIGATION_GUIDE.md) - Detailed codebase exploration
- [Confluence Documentation](https://kryptogo.atlassian.net/wiki) - Internal docs
- [API Documentation](https://api-docs.kryptogo.com) - API reference

## 🤝 Contributing

1. Branch naming: `feature/`, `fix/`, `chore/`
2. Follow TypeScript best practices
3. Add tests for new features
4. Update documentation as needed

For detailed contribution guidelines, see our [Contributing Guide](./CONTRIBUTING.md).

## Google Ads Conversion Tracking

### 設置與使用方法

1. **在根 Layout 添加 Google Ads Tags**

```tsx
// src/app/layout.tsx
import GoogleAdsTags from './_common/components/GoogleAdsTags';

export default function RootLayout({ children }) {
  return (
    <html>
      <head>
        {/* 可以傳入您的 Google Ads ID 和轉換 ID */}
        <GoogleAdsTags 
          id="AW-17057468787" 
          conversionId="t75RCKjsicgaEPOi0cU_" 
        />
      </head>
      <body>{children}</body>
    </html>
  );
}
```

2. **在組件中追蹤轉換事件**

```tsx
'use client';

import { useGoogleAds } from '@/app/_common/hooks/useGoogleAds';

export default function RegisterButton() {
  const { trackConversion } = useGoogleAds();

  const handleClick = () => {
    // 追蹤點擊註冊的轉換事件
    trackConversion({
      url: '/register/success', // 可選：轉換後重定向 URL
      // sendTo: 'AW-17057468787/t75RCKjsicgaEPOi0cU_', // 可選：特定轉換 ID，默認使用 GoogleAdsTags 設定的值
      value: 1.0, // 可選：轉換價值
      currency: 'USD', // 可選：幣別
    });
  };

  return <button onClick={handleClick}>註冊</button>;
}
```

3. **參數說明**

- **GoogleAdsTags 組件**:
  - `id`: Google Ads 追蹤 ID (格式: AW-XXXXXXXXX)
  - `conversionId`: 轉換 ID (在 Google Ads 後台獲取的特定轉換 ID)

- **trackConversion 方法**:
  - `url`: 轉換後重定向的 URL (可選)
  - `sendTo`: 特定的轉換目標 ID (可選，預設使用 GoogleAdsTags 設定的值)
  - `value`: 轉換價值 (可選，預設為 1.0)
  - `currency`: 幣別 (可選，預設為 'USD')
