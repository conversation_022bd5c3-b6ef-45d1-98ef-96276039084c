import { defineConfig } from 'cypress';

export default defineConfig({
  video: true,
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    charts: true,
    reportPageTitle: 'KG Studio',
    embeddedScreenshots: true,
    inlineAssets: true,
    saveAllAttempts: false,
    reportFilename: '[status]_[datetime]-[name]-report',
  },
  fixturesFolder: 'cypress/fixtures',
  env: {
    apiBaseUrl: 'https://wallet-dev.kryptogo.app/v1',
    admin: {},
    member: {},
  },
  e2e: {
    setupNodeEvents(on, config) {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      require('cypress-mochawesome-reporter/plugin')(on);
    },
    baseUrl: 'http://localhost:3001',
    specPattern: './cypress/integration/**/*.{js,jsx,ts,tsx}',
    experimentalStudio: true,
  },
  watchForFileChanges: false,
});
