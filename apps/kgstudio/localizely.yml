# For more configuration details, see https://localizely.com/configuration-file/
config_version: 1.0 # Required. Only 1.0 available
project_id: 7bbf0447-393e-4381-b95c-596afeda0b7b # Required. Your project ID from: https://app.localizely.com/projects
file_type: json # Required. Available values : android_xml, ios_strings, ios_stringsdict, java_properties, rails_yaml, angular_xlf, flutter_arb, dotnet_resx, po, pot, json, csv, xlsx
upload: # Required.
  files: # Required. List of files for upload to Localizely. Usually, it is just one file used for the main locale
    - file: src/locales/en-US.json # Required. Path to the translation file
      locale_code: en-US # Required. Locale code for the file. Examples: en, de-DE, zh-Hans-CN
download: # Required.
  files: # Required. List of files for download from Localizely.
    - file: src/locales/en-US.json # Required. Path to the translation file
      locale_code: en-US # Required. Locale code for the file. Examples: en, de-DE, zh-Hans-CN
    - file: src/locales/zh-TW.json # Required. Path to the translation file
      locale_code: zh-TW # Required. Locale code for the file. Examples: en, de-DE, zh-Hans-CN
    - file: src/locales/zh-CN.json # Required. Path to the translation file
      locale_code: zh-CN # Required. Locale code for the file. Examples: en, de-DE, zh-Hans-CN
    - file: src/locales/vi-VN.json # Required. Path to the translation file
      locale_code: vi-VN # Required. Locale code for the file. Examples: en, de-DE, zh-Hans-CN
    - file: src/locales/ja.json # Required. Path to the translation file
      locale_code: ja # Required. Locale code for the file. Examples: en, de-DE, zh-Hans-CN
    - file: src/locales/es.json # Required. Path to the translation file
      locale_code: es # Required. Locale code for the file. Examples: en, de-DE, zh-Hans-CN
