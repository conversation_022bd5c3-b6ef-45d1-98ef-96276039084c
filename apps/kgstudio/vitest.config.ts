import { defineConfig } from 'vitest/config';

import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    include: ['./**/*.{test,spec}.{ts,tsx}'],
    reporters: process.env.CI ? ['dot', 'github-actions'] : ['default'],
    coverage: {
      provider: 'istanbul',
      reporter: ['lcov', 'text-summary', 'json'],
    },
  },
});
