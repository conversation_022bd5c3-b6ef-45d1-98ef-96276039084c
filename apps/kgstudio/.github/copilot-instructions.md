# KG Studio Copilot Instructions

KG Studio is a comprehensive Web3 infrastructure platform built with Next.js 15, TypeScript, and the App Router pattern. This guide provides essential knowledge for AI agents to be immediately productive in this codebase.

## Architecture Overview

**Tech Stack**: Next.js with App Router, TypeScript, Zustand + TanStack Query, Chakra UI + Radix UI, Tailwind CSS
**Key Features**: Multi-chain wallet management, User360 platform, asset management, NFT campaigns, compliance tools

## Project Structure & Key Patterns

### App Router Layout Hierarchy

```
src/app/
├── [locale]/                    # Internationalization wrapper
│   ├── (protected)/            # Protected routes with auth middleware
│   │   └── layout.tsx          # Main app layout with sidebar/navbar
│   └── auth/                   # Authentication routes
└── _common/                    # Shared utilities and components
```

### State Management Pattern

**Global State**: Zustand stores with persistence

- `useAuthStore`: Authentication state and user info
- `useOrganizationStore`: Organization context (persisted)

**Server State**: TanStack Query with Zodios API clients

- Auto-generated hooks: `apiOrganizationHooks.useGetCurrentUserInfo()`
- API definitions in `src/app/_common/services/*/index.ts`

### Component Architecture

**Feature Organization**:

```
feature/
├── _components/        # UI components
├── _services/          # API integration (queries/mutations)
├── _types/            # Type definitions
└── _utils/            # Helpers
```

**Common Components**: Located in `src/app/_common/components/`

- Form components with react-hook-form + zod validation
- Shared UI components (FormattedMessage, Loading, etc.)

## Authentication & Authorization

**Authentication Flow**:

1. Google OAuth or email/OTP login
2. Token exchange for studio tokens
3. Organization selection and context setting
4. RBAC permission checks throughout the app

**Key Files**:

- `src/middleware.ts`: Route protection and locale handling
- `src/app/_common/lib/axios/instances/`: API clients with token management
- `src/app/_common/store/useAuthStore.ts`: Auth state management

## Internationalization

**Setup**: next-intl with locale prefix routing
**Supported Locales**: en-US, es, ja, vi-VN, zh-CN, zh-TW (default: zh-TW)
**Usage**: `const t = useTranslations()` for translations
**CLI**: `pnpm i18n:pull/push` for Localizely integration

## Development Workflows

### Essential Commands

```bash
pnpm dev                # Development server
pnpm test:unit         # Unit tests (Vitest)
pnpm e2e               # End-to-end tests (Cypress)
pnpm typecheck         # TypeScript validation
pnpm i18n:pull         # Update translations
```

### Testing Patterns

**API Mocking**: Cypress intercepts with fixture files

```typescript
cy.intercept('GET', '/studio/organization/*/accounts', {
  fixture: '/treasury/accounts.json',
}).as('getKGAccounts');
```

**Authentication in Tests**: `cy.setLoggedIn()` helper with module permissions
**Selectors**: Use `data-cy` attributes with `cy.getBySel()` helper

## Critical Integration Points

### API Integration

- **Zodios**: Type-safe API clients with auto-generated hooks
- **Base URLs**: Configured per environment in `env.mjs`
- **Error Handling**: Centralized in `src/app/_common/lib/error`

### Permission System

- **RBAC**: Role-based access with resource-action permissions
- **Hooks**: `usePermissions(['asset_pro_order', 'read'])` for checks
- **Route Guards**: Automatic in protected layout based on user modules

### Multi-Chain Support

- **Wagmi**: Web3 state management for wallet connections
- **Chains**: Ethereum, Polygon, BSC, Arbitrum support
- **RPC**: Alchemy integration for blockchain interactions

## Common Anti-Patterns to Avoid

1. **Don't** call API endpoints directly - use generated hooks
2. **Don't** manage loading states manually - TanStack Query handles this
3. **Don't** hardcode organization IDs - use `useOrganizationStore`
4. **Don't** forget locale prefixes in navigation - use `@/i18n/navigation`
5. **Don't** skip error boundaries - wrap async operations properly

## Key Files for Quick Orientation

- `src/app/[locale]/(protected)/layout.tsx`: Main app shell and auth logic
- `src/app/_common/services/`: All API definitions and hooks
- `src/app/_common/lib/route.ts`: Sidebar navigation and permissions
- `cypress/support/commands.ts`: Test helpers and mocking utilities
- `src/middleware.ts`: Route protection and internationalization

## Development Tips

- Use `FormattedMessage` component for translations
- Leverage `apiHooks` pattern for consistent data fetching
- Follow the feature-based folder structure for new modules
- Always include proper TypeScript types with Zod schemas
- Test critical flows with both unit and E2E tests using realistic fixtures

This codebase emphasizes type safety, proper separation of concerns, and comprehensive testing. The modular architecture allows for easy feature development while maintaining consistency across the platform.
