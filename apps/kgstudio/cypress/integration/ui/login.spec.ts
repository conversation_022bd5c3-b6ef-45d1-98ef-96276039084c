/// <reference types="cypress" />
import { mockReCaptchaToken } from '../../fixtures/login/recaptcha';

describe('[UI] Login to Studio', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.visit('/auth/login');
    cy.location('pathname').should('eq', '/auth/login');
  });

  it(`Should login successfully`, () => {
    cy.intercept('POST', `https://www.google.com/recaptcha/enterprise/reload*`, {
      statusCode: 200,
      body: mockReCaptchaToken,
    }).as('getRecaptchaToken');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/email`, { fixture: 'login/email-login.json' }).as('getEmailOtp');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/login`, { fixture: 'login/login-email-otp.json' }).as(
      'loginEmailOtp',
    );
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/login_v2`, { fixture: 'login/studio-login.json' }).as(
      'loginStudio',
    );
    cy.wait('@getRecaptchaToken');
    cy.get('input[name="email"]').should('be.visible').type('<EMAIL>');
    cy.getBySel('email-login').click();
    cy.wait('@getEmailOtp').its('response.statusCode').should('eq', 200);
    cy.location('pathname', { timeout: 20000 }).should('match', /\/auth\/login\/verify-account.*/);
    cy.getBySel('otp-input').find('input').first().should('be.visible').type('654321');
    cy.wait('@loginEmailOtp', { timeout: 20000 })
      .then((interception) => {
        expect(interception.response?.statusCode).to.eq(200);
        expect(interception.request?.body).to.deep.eq({ email: '<EMAIL>', verification_code: '654321' });
      })
      .wait('@loginStudio', { timeout: 20000 });
    cy.getCookie('Kg-Studio-Token-V2').should('exist');
    // pathname may contain locale, so we use regex to match, instead of should.eq /asset/transfer
    cy.location('pathname', { timeout: 60000 }).should('match', /.*\/home\/overview/);
  });

  it(`
      Given: I am on the login page
      When:  I choose to login with Google, and completed the Google login
      Then:  I should be redirected to home page
    `, () => {
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/login_v2`, { fixture: 'login/studio-login.json' }).as(
      'loginStudio',
    );

    // Pretend that we've completed the google login
    cy.visit('/auth/login/oauth-callback?kg_token=random-code');
    cy.location('pathname', { timeout: 60000 }).should('match', /.*\/home\/overview/);
  });

  it(`
      Given: I am on the login page
      When:  I choose to login with Google, and fail the Google login
      Then:  I should be redirected to dedicate error page with error message
    `, () => {
    const ERROR = 'something_kind_of_error';
    const ERROR_CODE = '9999';

    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/login_v2`, { fixture: 'login/google-login.json' }).as(
      'getStudioToken',
    );

    // Pretend that we are logged in with Google
    cy.visit(`/auth/login/oauth-callback?error=${ERROR}&error_code=${ERROR_CODE}`);

    cy.getBySel('error-message-block').should('exist');
    cy.getBySel('error-message-block').should('contain.text', ERROR);
    cy.getBySel('error-message-block').should('contain.text', ERROR_CODE);
  });

  it(`
      Given: I am already logged in
      When:  I visit the login page
      Then:  I should be redirected to home page
    `, () => {
    cy.setLoggedIn();
    cy.visit('/auth/login');
    cy.location('pathname', { timeout: 60000 }).should('match', /.*\/home\/overview/);
    cy.wait('@getMe');
  });

  it(`
      Given: A user is already logged in
      When:  Logout and change user to login
      Then:  Should be able to login successfully with new user
    `, () => {
    cy.setLoggedIn();
    cy.getBySel('user-email').should('contain.text', '<EMAIL>');
    cy.getBySel('user-email').click();
    cy.getBySel('user-logout').click();
    cy.setLoggedIn({
      name: 'arron',
      email: '<EMAIL>',
    });
    cy.getBySel('user-email').should('contain.text', '<EMAIL>');
  });
});
