describe('[UI] Finance', () => {
  beforeEach(() => {
    cy.setLoggedIn({
      modules: {
        asset_pro: ['revenue'],
      },
      permissions: [
        {
          resource: 'asset_pro_revenue',
          action: 'read',
        },
        {
          resource: 'asset_pro_liquidity',
          action: 'edit',
        },
        {
          resource: 'asset_pro_liquidity',
          action: 'read',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/profit_rates`, {
        fixture: '/finance/profit-rates.json',
      }).as('getProfitRates');
      cy.intercept('PUT', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/profit_rates`, {
        fixture: '/finance/edit-profit-rate.json',
      }).as('editProfitRate');
    });
  });

  it(`Given:
    - As a studio user that has access to AssetPro Finance
    When:
    - Visit /asset/finance and click on filter button
    Then:
    - Should display correct date range`, () => {
    cy.visit('/en-US/asset/finance');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/*/grafana_login`, {
      fixture: '/grafana/login.json',
    }).as('getGrafanaLogin');
    cy.wait('@getGrafanaLogin');
    cy.getBySel('finance-filter-group').should('exist');
    cy.getBySel('date-range-date').should('contain', 'Date2023/10/18');
  });

  it(`Given:
    - As a studio user that has access to Finance Profit Rate Setting
    When:
    - Visit /asset/finance/settings and edit profit rate
    Then:
    - Should display correct profit rate & edit profit rate successfully`, () => {
    cy.visit('/en-US/asset/finance/settings');

    cy.get(`[data-trow]`).first().find('td').first().should('contain', 'Buy');
    cy.get(`[data-trow]`)
      .first()
      .find('td')
      .eq(1)
      .should('contain', '(the 1% is your profit margin, and the 7% is the market cost of Buy service)');
    cy.get(`[data-trow]`).first().find('td').eq(2).find('p').should('contain', '1.68 %');

    cy.getBySel('edit-profit-rate-btn').first().click();
    cy.getBySel('profit-rate-input').type('99');
    cy.getBySel('profit-rate-input').should('have.class', 'aria-[invalid=true]:text-error');
    cy.getBySel('profit-rate-input').clear();
    cy.getBySel('profit-rate-input').type('9.9');
    cy.getBySel('update-profit-rate-btn').click();
    cy.getBySel('confirm-modal-btn').click();

    cy.wait('@editProfitRate').its('request.body').should('deep.equal', {
      service: 'buy',
      profit_rate: 0.099,
    });
    cy.get('body').should('contain', 'Successfully Updated!');
  });
});
