import publishedData from '../../fixtures/products/products-list-published.json';

describe('[UI] Product Management', () => {
  beforeEach(() => {
    cy.setLoggedIn({
      modules: {
        asset_pro: ['market'],
      },
      permissions: [
        {
          resource: 'asset_pro_product',
          action: 'read',
        },
        {
          resource: 'asset_pro_product',
          action: 'edit',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/products?is_published=true*`,
        {
          fixture: '/products/products-list-published.json',
        },
      ).as('getPublishedProductsList');
      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/products?is_published=false*`,
        {
          fixture: '/products/products-list-unpublished.json',
        },
      ).as('getUnpublishedProductsList');
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/asset_pro/transfer/tokens`, {
        fixture: '/transfer/tokens.json',
      }).as('getTokensList');
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/accounts`, {
        fixture: '/treasury/accounts.json',
      }).as('getAccounts');

      cy.visit('/en-US/asset/products');
      cy.wait('@getTokensList');
    });
  });

  it(`Given:
        - Logged in as AssetPro Admin
      When:
        - Visit Products page
      Then:
        - Should display published and unpublished products list
      `, () => {
    cy.wait('@getPublishedProductsList');
    cy.wait('@getUnpublishedProductsList');

    const columns = ['Type', 'Image', 'Product Name', 'Price', 'Available', 'Fee', 'Updated', 'Action'];
    cy.get('[data-table]').first().find('[data-thead]').should('have.length', columns.length);
    cy.get('[data-table]')
      .first()
      .find('[data-thead]')
      .each((thead, i) => {
        cy.wrap(thead).should('contain', columns[i]);
      });

    cy.get('[data-table]').first().find('[data-trow]').should('have.length', publishedData.data.length);
    cy.get('[data-trow="0"]').find('td').eq(0).should('contain', 'Buy Crypto');
    cy.get('[data-trow="0"]').find('td').eq(1).find('img').should('exist');
    cy.get('[data-trow="0"]').find('td').eq(2).should('contain', 'USDT/TWD').and('contain', '(Polygon)');
    cy.get('[data-trow="0"]').find('td').eq(3).should('contain', '$31.15TWD');
    cy.get('[data-trow="0"]').find('td').eq(4).should('contain', '123,456.78').and('contain', 'USDT(Ethereum)');
    cy.get('[data-trow="0"]').find('td').eq(5).should('contain', '0.5 %').and('contain', 'Minimum: $10TWD');
    cy.get('[data-trow="0"]').find('td').eq(6).should('contain', '2024/01/09').and('contain', '13:26:54');
    cy.get('[data-trow="0"]').find('td').eq(7).find('svg').should('exist');
  });
  it(`Given:
        - Logged in as AssetPro Admin
      When:
        - Click Action Bution to edit product
      Then:
        - Should popup Edit Product Modal showing product details
      `, () => {
    cy.get('[data-trow="0"]').find('td').eq(7).find('svg').click();
    cy.wait('@getAccounts');
    cy.getBySel('product-modal-name-input').should('have.value', 'USDT/TWD (Polygon)');
    cy.getBySel('product-modal-price-input').should('have.value', '31.15');
    cy.getBySel('product-modal-publish-input').should('have.value', 'true');
    cy.getBySel('product-modal-limits-from-input').should('have.value', '5000');
    cy.getBySel('product-modal-limits-to-input').should('have.value', '10000');
    cy.getBySel('product-modal-fee-percentage-input').should('have.value', '0.5');
    cy.getBySel('product-modal-min-fee-input').should('have.value', '10');
  });
  it(`Given:
        - Logged in as AssetPro Admin
      When:
        - Click Cancel Bution on Edit Product Modal
      Then:
        - Should popup hint modal to confirm cancel
      `, () => {
    cy.get('[data-table]').eq(1).find('td').eq(7).find('svg').click();
    cy.wait('@getAccounts');
    cy.getBySel('product-modal').should('exist');
    cy.getBySel('product-modal-cancel-button').click();
    cy.getBySel('confirm-modal').should('exist');
  });
  it(`Given:
        - Logged in as AssetPro Admin
      When:
        - Fill in the product details and submit
      Then:
        - Should update successfully
      `, () => {
    cy.intercept('PUT', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/products/*`, {
      statusCode: 200,
      fixture: '/products/edit-product.json',
      delay: 500,
    }).as('interceptedEditProduct');
    cy.get('[data-table]').eq(1).find('td').eq(7).find('svg').click();
    cy.wait('@getAccounts');
    cy.getBySel('product-modal-price-input').type('30.4');
    cy.getBySel('product-modal-limits-from-input').type('10');
    cy.getBySel('product-modal-limits-to-input').type('100');
    cy.getBySel('product-modal-update-button').click();

    cy.wait('@interceptedEditProduct').its('request.body').should('deep.equal', {
      base_currency: 'USDT',
      chain_id: 'eth',
      fee_type: 'fee_included',
      image: 'https://assets.coingecko.com/coins/images/325/large/Tether-logo.png?**********',
      is_published: false,
      logo_url: 'https://assets.coingecko.com/coins/images/325/large/Tether-logo.png?**********',
      name: 'USDT/TWD (Polygon)',
      order_limits_from: '10',
      order_limits_to: '100',
      organization_id: 1,
      price: '30.4',
      product_id: 1,
      proportional_fee_percentage: '0.5',
      proportional_minimum_fee: '10',
      quote_currency: 'TWD',
      type: 'buy_crypto',
      stock: '123456.78',
    });
    cy.get('[data-sonner-toast]').should('contain.text', '“USDT/TWD (Ethereum)” successfully updated.');
  });
  it(`Given:
        - Logged in as AssetPro Admin
      When:
        - Visit Products page
        - Click the publish switch button on list item
        - User has done the product setting
      Then:
        - Can publish/unpublish product
        - Toast message should display after publish/unpublish product
      `, () => {
    cy.intercept('PUT', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/products/*`, {
      statusCode: 200,
      fixture: '/products/edit-product.json',
      delay: 500,
    }).as('interceptedEditProduct');
    cy.get('[data-table]').eq(0).find('td').eq(7).find('button').first().click();
    cy.wait('@interceptedEditProduct').its('request.body').should('deep.equal', {
      base_currency: 'USDT',
      chain_id: 'eth',
      fee_type: 'fee_included',
      image: 'https://assets.coingecko.com/coins/images/325/large/Tether-logo.png?**********',
      is_published: false,
      logo_url: 'https://assets.coingecko.com/coins/images/325/large/Tether-logo.png?**********',
      name: 'USDT/TWD (Polygon)',
      order_limits_from: '5000',
      order_limits_to: '10000',
      organization_id: 1,
      price: '31.15',
      product_id: 3,
      proportional_fee_percentage: '0.5',
      proportional_minimum_fee: '10',
      quote_currency: 'TWD',
      type: 'buy_crypto',
      stock: '123456.78',
    });
  });
  it(`Given:
        - Logged in as AssetPro Admin
      When:
        - Visit Products page
        - Click the publish switch button on list item
        - User has not done the product setting yet
      Then:
        - Popup hint modal to remind user to complete the product setting
      `, () => {
    cy.get('[data-table]').eq(1).find('td').eq(7).find('button').first().click();
    cy.wait('@getAccounts');
    cy.getBySel('product-not-ready-modal').should('exist');
    cy.getBySel('product-not-ready-modal').find('button').eq(1).click();
    cy.getBySel('product-modal').click();
  });
});
