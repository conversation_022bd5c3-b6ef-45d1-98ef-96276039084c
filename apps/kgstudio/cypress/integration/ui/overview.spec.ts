/// <reference types="cypress" />

describe('Inspect homepage access and link to module page behavior', () => {
  it(`Given:
        - As a studio user that don't have permission to view compliance stat and pending order count
      When:
        - Visit Overview page
      Then:
        - Should not display KYC Pending Review and Pending Orders sections`, () => {
    cy.setLoggedIn({
      modules: {
        asset_pro: ['treasury', 'send_token'],
      },
      permissions: [],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
    });
    cy.getBySel('review-data-section-comp-stat').should('not.exist');
    cy.getBySel('review-data-section-pending-order-count').should('not.exist');
  });

  it.skip(`Given:
        - As a studio user that has access to read asset_pro_order and user_360_statistics_compliance
      When:
        - Visit Overview page
      Then:
        - Should display compliance data
        - Should display pending order count card and redirect to correct route
        `, () => {
    cy.setLoggedIn({
      modules: {
        user_360: ['data', 'engage', 'audience'],
        asset_pro: ['market'],
      },
      permissions: [
        {
          resource: 'user_360_statistics_compliance',
          action: 'read',
        },
        {
          resource: 'asset_pro_order',
          action: 'read',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/pending_order_count`, {
        fixture: '/orders/pending-order-count.json',
      }).as('getPendinfOrderCount');
    });
    cy.wait('@getPendinfOrderCount');

    cy.getBySel('review-data-section-comp-stat').should('contain.text', '17');
    cy.getBySel('review-data-section-pending-order-count').contains('2');
    cy.getBySel('review-data-section-pending-order-count')
      .find('a')
      .should('have.attr', 'href', '/en-US/asset/orders?status=pending');
  });
  it.skip(`Given:
          - As a studio user that can access to AssetPro module
          When:
          - Click the AssetPro module card
          Then:
          - Should direct to AssetPro send_token page`, () => {
    cy.interceptedGetTokens();
    cy.setLoggedIn({
      modules: {
        asset_pro: ['treasury', 'send_token'],
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/accounts`, {
        fixture: '/treasury/accounts.json',
      }).as('getAccounts');
    });

    cy.getBySel('module-card-AssetPro').click();
    cy.wait('@getAccounts');
    cy.location('pathname').should('eq', '/en-US/asset/transfer', { timeout: 10000 });
  });

  it.skip(`Given:
          - As a studio user that has no access to AssetPro module
          When:
          - Click the AssetPro module card
          Then:
          - Should display error toast`, () => {
    cy.setLoggedIn({
      modules: {
        user_360: ['engage', 'audience'],
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
    });

    cy.getBySel('module-card-AssetPro').click();
    cy.get('[data-sonner-toast]').should(
      'contain.text',
      "It seems you don't have permission for this module. Please contact your system owner to help you activate it if you want to use it.",
    );
  });

  describe('Check Overview Stats Card Display', () => {
    beforeEach(() => {
      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/transfer/pending_history_count`,
        {
          fixture: '/transactions/pending-history-count.json',
        },
      );
    });

    it.skip(`Given:
          - As a studio user that has read:transasction permission(Trader)
          When:
          - Visit Overview page
          Then:
          - Should display Awaiting Approval + Awaiting Release + Sending order count(Send by self) card and redirect to correct route`, () => {
      cy.setLoggedIn({
        modules: {
          asset_pro: ['transaction_history'],
        },
        permissions: [
          {
            resource: 'transaction',
            action: 'read',
          },
        ],
      });
      cy.getBySel('review-data-section-my-pending-txs').contains('3');
      cy.getBySel('review-data-section-my-pending-txs')
        .find('a')
        .should(
          'have.attr',
          'href',
          '/en-US/asset/transactions?status=awaiting_approval%2Csending%2Cawaiting_release&submitter=5f7b9c9cb5a9c9c',
        );
    });
    it.skip(`Given:
          - As a studio user that has approve:transasction permission(Approver)
          When:
          - Visit Overview page
          Then:
          - Should display Awaiting Approval tx count card and redirect to correct route`, () => {
      cy.setLoggedIn({
        modules: {
          asset_pro: ['transaction_history'],
        },
        permissions: [
          {
            resource: 'transaction',
            action: 'approve',
          },
          {
            resource: 'transaction',
            action: 'read',
          },
        ],
      });

      cy.getBySel('review-data-section-awaiting-approval-txs').contains('2');
      cy.getBySel('review-data-section-awaiting-approval-txs')
        .find('a')
        .should('have.attr', 'href', '/en-US/asset/transactions?status%5B%5D=awaiting_approval');
    });
    it.skip(`Given:
          - As a studio user that has release:transasction permission(Finance Manager)
          When:
          - Visit Overview page
          Then:
          - Should display Awaiting Release tx count card and redirect to correct route`, () => {
      cy.setLoggedIn({
        modules: {
          asset_pro: ['transaction_history'],
        },
        permissions: [
          {
            resource: 'transaction',
            action: 'release',
          },
          {
            resource: 'transaction',
            action: 'read',
          },
        ],
      });
      cy.getBySel('review-data-section-awaiting-release-txs').contains('1');
      cy.getBySel('review-data-section-awaiting-release-txs')
        .find('a')
        .should('have.attr', 'href', '/en-US/asset/transactions?status%5B%5D=awaiting_release');
    });
  });
});
