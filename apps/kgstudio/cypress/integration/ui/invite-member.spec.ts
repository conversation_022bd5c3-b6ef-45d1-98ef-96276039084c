import memberData from '../../fixtures/organization/members.json';

describe('[UI] Invite Member - As Owner', () => {
  beforeEach(() => {
    cy.setLoggedIn().then((orgId) => {
      Cypress.env('orgId', orgId);

      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/users*`, {
        statusCode: 200,
        fixture: '/organization/members.json',
        delay: 500,
      }).as('interceptedMembers');

      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/roles`, {
        statusCode: 200,
        fixture: '/organization/roles.json',
        delay: 500,
      }).as('interceptedRoles');
    });
    cy.visit('/en-US/setting/team');
  });

  // only owner can invite/edit/delete member
  it(`Given:
        - Logged in as organization Owner
      When:
        - Visit Team page
      Then:
        - Should display organization member list
        - Should display invite and action buttons(edit/remove/reinvite)
      `, () => {
    cy.wait('@interceptedMembers');

    // check member list data and action button render
    const column = ['Name', 'Email', 'Member ID', 'Roles', 'Status', ''];
    cy.get('[data-table]').find('[data-thead]').should('have.length', column.length);
    cy.get('[data-table]')
      .find('[data-thead]')
      .each((thead, i) => {
        cy.wrap(thead).should('contain', column[i]);
      });
    cy.get('[data-table]')
      .find('[data-trow="0"]')
      .first()
      .should('contain', memberData.data[0].name)
      .and('contain', 'Owner');
    cy.get('[data-table]').find('[data-trow="0"] td').eq(1).should('contain', memberData.data[0].email);
    cy.get('[data-table]').find('[data-trow="0"] td').eq(2).should('contain', memberData.data[0].member_id);
    cy.get('[data-table]')
      .find('[data-trow="0"] td')
      .eq(3)
      .should('contain', 'User 360 - Admin')
      .and('contain', 'Wallet - Admin')
      .and('contain', 'AssetPro - Admin')
      .and('contain', 'AssetPro - Approver')
      .and('contain', 'AssetPro - Trader')
      .and('contain', 'NFT Boost - Admin')
      .and('contain', 'Compliance - Admin');
    cy.get('[data-table]').find('[data-trow="0"] td').eq(4).should('contain', 'Active');
    cy.get('[data-table]').find('[data-trow="0"] td').eq(5).find('button').should('exist');

    // check invite buttons
    cy.getBySel('invite-member-button').should('exist');
  });
  // invite success
  it(`Given:
        - Logged in as organization Owner
      When:
        - Click invite button
        - Fill in form with not existed email and submit
        Then:
        - Should invite member successfully
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/user_exist?email=*`, {
      statusCode: 200,
      fixture: '/organization/user-not-exist.json',
      delay: 500,
    }).as('interceptedCheckNotExist');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/users`, {
      statusCode: 200,
      fixture: '/organization/invite-member.json',
      delay: 500,
    }).as('interceptedInvite');

    cy.getBySel('invite-member-button').click();
    cy.get('input[name="name"]').should('be.visible').type('Tim');
    cy.get('input[name="email"]').should('be.visible', { timeout: 10000 }).type('<EMAIL>', { force: true });
    cy.get('input[name="email"]').should('have.value', '<EMAIL>', { timeout: 10000 });
    cy.get('input[name="email"]').blur({ force: true });
    cy.wait('@interceptedCheckNotExist');

    cy.getBySel('invite-member-form').find('button').eq(3).click();

    cy.get('button[role="checkbox"]').first().click();
    cy.get('button[role="checkbox"]').eq(1).click();
    cy.get('button[role="checkbox"]').eq(2).click();
    cy.getBySel('invite-member-form').find('button').eq(3).click({ force: true });
    cy.getBySel('invite-member-form').find('button').eq(3).find('svg').first().click();

    cy.getBySel('invite-member-confirm').should('be.enabled', { timeout: 10000 });
    cy.getBySel('invite-member-confirm').click();
    cy.wait('@interceptedInvite')
      .its('request.body')
      .should('deep.equal', {
        email: '<EMAIL>',
        member_id: '',
        name: 'Tim',
        roles: ['asset_pro:approver', 'asset_pro:trader'],
      });
    cy.get('[data-sonner-toast]').should('contain.text', 'Invitation sent!');
  });
  // invite - email existed
  it(`Given:
        - Logged in as organization Owner
      When:
        - Click invite button
        - Fill in form with existed email and submit
        Then:
        - Should not invite member
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/user_exist?email=*`, {
      statusCode: 200,
      fixture: '/organization/user-exist.json',
      delay: 500,
    }).as('interceptedCheckExist');

    cy.getBySel('invite-member-button').click();
    cy.get('input[name="name"]').type('Tim');
    cy.get('input[name="member_id"]').type('00001');
    cy.get('input[name="email"]').type('<EMAIL>', { force: true });
    cy.get('input[name="email"]').blur({ force: true });

    cy.wait('@interceptedCheckExist');
    cy.contains('User already exists');
    cy.getBySel('invite-member-confirm').should('be.disabled');
  });

  // edit success
  it(`Given:
        - Logged in as organization Owner
      When:
        - Edit a memebr
        Then:
        - Should edit member successfully
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('PUT', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/users/${memberData.data[0].uid}`, {
      statusCode: 200,
      fixture: '/organization/edit-member.json',
      delay: 500,
    }).as('interceptedEdit');
    cy.getBySel('team-row-action-btn').first().click();
    cy.get('div[role="menu"]').first().should('contain', 'Edit').click({ force: true });
    cy.get('input[name="member_id"]').clear({ force: true });
    cy.get('input[name="member_id"]').type('00002', { force: true });
    cy.get('input[name="name"]').type('Harry Potter02', { force: true });

    cy.getBySel('invite-member-confirm').should('be.enabled').click();
    cy.wait('@interceptedEdit')
      .its('request.body')
      .should('deep.equal', {
        member_id: '00002',
        name: 'Harry Potter02',
        roles: ['owner'],
      });
    cy.get('[data-sonner-toast]').should('contain.text', 'Changes saved!');
  });
  // remove success
  it(`Given:
        - Logged in as organization Owner
      When:
        - Remove a memebr
        Then:
        - Should remove member successfully
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept(
      'DELETE',
      `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/users/${memberData.data[0].uid}`,
      {
        statusCode: 200,
        fixture: '/organization/delete-member.json',
        delay: 500,
      },
    ).as('interceptedDelete');
    cy.getBySel('team-row-action-btn').first().click();
    cy.get('div[role="menu"]').contains('Remove').click();
    cy.getBySel('member-delete-button').click();

    cy.get('[data-sonner-toast]').should(
      'contain.text',
      '"Harry Potter (<EMAIL>)" has been removed from your team',
    );
  });
  // reinvite success
  it(`Given:
        - Logged in as organization Owner
      When:
        - Reinvite a memebr
        Then:
        - Should Reinvite member successfully
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/reinvite`, {
      statusCode: 200,
      fixture: '/organization/reinvite-member.json',
      delay: 500,
    }).as('interceptedReinvite');

    cy.get('[data-table]').find('[data-trow="1"] td').eq(5).find('button').click();
    cy.get('div[role="menu"]').contains('Resend Invitation').click();

    cy.wait('@interceptedReinvite').its('request.body').should('deep.equal', {
      uid: memberData.data[1].uid,
    });
    cy.get('[data-sonner-toast]').should('contain.text', 'Invitation sent!');
  });
  // reinvite rate limit
  it(`Given:
        - Logged in as organization Owner
      When:
        - Reinvite a memebr in 1 minute
        Then:
        - Should failed to reinvite since rate limit
      `, () => {
    const orgId = Cypress.env('orgId');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/reinvite`, {
      statusCode: 429,
      fixture: '/organization/reinvite-member-rate-limit.json',
      delay: 500,
    }).as('interceptedReinviterateLimit');

    cy.get('[data-table]').find('[data-trow="1"] td').eq(5).find('button').click();
    cy.get('div[role="menu"]').contains('Resend Invitation').click();

    cy.wait('@interceptedReinviterateLimit').its('request.body').should('deep.equal', {
      uid: memberData.data[1].uid,
    });
  });
});

describe('[UI] Invite Member - As NOT organization Owner', () => {
  beforeEach(() => {
    cy.setLoggedIn({
      permissions: [
        {
          resource: 'transaction',
          action: 'apply',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);

      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/users*`, {
        statusCode: 200,
        fixture: '/organization/members.json',
        delay: 500,
      }).as('interceptedMembers');
    });
    cy.visit('/en-US/setting/team');
  });
  it(`Given:
        - Logged in as NOT organization Owner
      When:
        - Visit Team page
        Then:
        - Should not display invite and action buttons(edit/remove/reinvite)
      `, () => {
    cy.getBySel('invite-member-button').should('not.exist');
    cy.get('[data-table]').find('[data-trow="0"] td').eq(5).should('not.exist');
  });
});
