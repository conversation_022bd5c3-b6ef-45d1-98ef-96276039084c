describe('[UI] Order Management', () => {
  beforeEach(() => {
    cy.setLoggedIn({
      modules: {
        asset_pro: ['market'],
      },
      permissions: [
        {
          resource: 'user_360_statistics_compliance',
          action: 'read',
        },
        {
          resource: 'asset_pro_order',
          action: 'read',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/orders*`, {
        fixture: '/orders/orders-list.json',
      }).as('getOrdersList');

      cy.visit('/en-US/asset/orders');
    });
  });

  it(`Given:
            - Logged in as AssetPro Admin
          When:
            - Visit Order page
          Then:
            - Should display order list
          `, () => {
    cy.clock(new Date(2024, 4, 9), ['Date']).then(() => {
      cy.wait('@getOrdersList');

      const columns = [
        'Order Created',
        'Order ID',
        'Customer',
        'Purchase',
        'Total Price',
        'Payment',
        'Shipment',
        'Order Status',
        '',
      ];
      cy.get('[data-table]').first().find('[data-thead]').should('have.length', columns.length);
      cy.get('[data-table]')
        .first()
        .find('[data-thead]')
        .each((thead, i) => {
          cy.wrap(thead).should('contain', columns[i]);
        });
      cy.get('[data-trow="0"]').find('td').eq(0).should('contain', '2024/01/09').and('contain', '13:26:54');
      cy.get('[data-trow="0"]').find('td').eq(1).should('contain', 'OD-123');
      cy.get('[data-trow="0"]')
        .find('td')
        .eq(2)
        .should('contain', '林美華')
        .and('contain', '+886912345678')
        .and('contain', '<EMAIL>');
      cy.get('[data-trow="0"]')
        .find('td')
        .eq(3)
        .should('contain', '100')
        .and('contain', 'USDT(Polygon)')
        .and('contain', '≈ 100 USD');
      cy.get('[data-trow="0"]')
        .find('td')
        .eq(4)
        .should('contain', '3,182')
        .and('contain', 'TWD')
        .and('contain', '≈ 102.16 USD');
      cy.get('[data-trow="0"]').find('td').eq(5).should('contain', 'Paid');

      cy.get('[data-trow="0"]').find('td').eq(6).should('contain', '0x095b...89ab').and('contain', '3 months ago');
      cy.get('[data-trow="0"]').find('td').eq(7).should('contain', 'Delivered');
    });
  });

  it.skip(`View unpaid order detail`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/unpaid.json',
    }).as('getOrderDetail');
    cy.wait('@getOrderDetail');

    // Check Steps display
    const stepsData = ['Order Created', 'Awaiting Payment', 'Awaiting Shipment', 'Shipping', 'Order Completed'];
    stepsData.forEach((step) => {
      cy.getBySel('order-steps').contains(step);
    });
    cy.getBySel('order-steps').contains('Awaiting Payment').should('have.class', 'text-highlight');
    cy.getBySel('order-steps').contains('Awaiting Shipment').should('have.class', 'text-disabled');

    // Check Order Info diaplay
    const orderInfoData = [
      { cy: 'order-info-transaction-id', values: ['OD-1-1712574843-1'] },
      { cy: 'order-info-order-time', values: ['2024/04/08 19:14 (GMT+8)'] },
      { cy: 'order-info-purchase', values: ['0.01', 'USDT', '(Sepolia Testnet)', '≈ 0.01 USD'] },
      { cy: 'order-info-total-price', values: ['NT$1', '≈ 0.01 USD'] },
      {
        cy: 'order-info-customer',
        values: [
          '艾倫',
          'Verified',
          '******************************************',
          '<EMAIL>',
          'This user has passed KYC verification!',
          'Wallet Risk Check',
        ],
      },
    ];
    // TODO: add multiple contains helper
    orderInfoData.forEach((data) => {
      data.values.forEach((value) => {
        cy.getBySel(`${data.cy}`).contains(value);
      });
    });

    // Check Actions Card display
    cy.getBySel('order-actions-card').within(() => {
      cy.getBySel('payment-deadline').should('exist').contains('2024/04/08 20:14 (GMT+8)');
    });
    cy.getBySel('actions-card-reminder-block-awaiting-confirmation').should('not.exist');

    cy.getBySel('order-actions-cancel').should('exist'); // 取消
    cy.getBySel('order-actions-mark-as-paid').should('exist'); // mark as paid

    // Payment Details
    const paymentData = [
      { cy: 'order-payment-status', values: ['unpaid'] },
      { cy: 'order-payment-transfer-amount', values: ['NT$0'] },
      { cy: 'order-payment-method', values: ['Bank Transfer'] },
      {
        cy: 'order-payment-transfer-to',
        values: [
          'Bank Name: 富貴銀行 Branch Name: 信義分行(888) Account Number: ************ Account Holder Name: 重量數位有限公司',
        ],
      },
      { cy: 'order-payment-customer-transfer-time', values: ['N/A'] },
      { cy: 'order-payment-account-five-digits', values: ['N/A'] },
      { cy: 'order-payment-note-attachments', values: ['N/A'] },
    ];
    paymentData.forEach((data) => {
      data.values.forEach((value) => {
        cy.getBySel(`${data.cy}`).contains(value);
      });
    });
    cy.getBySel('order-payment-card-actions').find('button').first().contains('Edit');
    cy.getBySel('order-payment-card-actions').find('button').eq(1).contains('Mark as Paid');
    cy.getBySel('payment-detail-reminder-block-awaiting-confirmation').should('not.exist');

    // Check Summary Card display
    const summaryData = [
      { cy: 'order-summary-order-status', value: 'Unpaid' },
      { cy: 'order-summary-payment-status', value: 'Unpaid' },
      { cy: 'order-summary-shipment-status', value: 'Not Shipped' },
      { cy: 'order-summary-shipping-proof', value: 'N/A' },
      { cy: 'order-summary-process-by', value: 'N/A' },
      { cy: 'order-summary-internal-note', value: 'N/A' },
    ];
    summaryData.forEach((data) => {
      cy.getBySel(data.cy).contains(data.value);
    });

    // Check Shipment Detail Card display
    cy.getBySel('order-shipment-details-card').should('not.exist');
  });

  it.skip(`Cancel Order(Actions Card)`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/unpaid.json',
    }).as('getOrderDetail');
    cy.intercept('PUT', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*/cancel`, {
      fixture: '/orders/detail/mutate-success.json',
    }).as('cancelOrder');
    cy.wait('@getOrderDetail');

    cy.getBySel('order-actions-cancel').click();
    cy.getBySel('cancel-order-modal').find('textarea').type('Test cancel order!!!');
    cy.getBySel('cancel-order-modal').find('button').eq(1).click();

    cy.wait('@cancelOrder').its('request.body').should('deep.equal', {
      internal_note: 'Test cancel order!!!',
    });
  });
  it.skip(`Edit internal note(Summary Card)`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/unpaid.json',
    }).as('getOrderDetail');
    cy.intercept('PATCH', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/mutate-success.json',
    }).as('editOrderInterNote');
    cy.wait('@getOrderDetail');

    cy.getBySel('order-summary-internal-note').find('button').click();
    cy.getBySel('edit-internal-note-modal').find('textarea').type('Test edit internal note!!!');
    cy.getBySel('edit-internal-note-modal').find('button').eq(1).click();

    cy.wait('@editOrderInterNote').its('request.body').should('deep.equal', {
      internal_note: 'Test edit internal note!!!',
    });
  });

  it.skip(`Edit unpaid order`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/unpaid.json',
    }).as('getOrderDetail');
    cy.intercept('PATCH', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/mutate-success.json',
    }).as('editOrder');
    cy.wait('@getOrderDetail');

    cy.getBySel('order-payment-card-actions').find('button').contains('Edit').click();
    cy.getBySel('edit-payment-note-textarea').type('Test payment note!!!');
    cy.getBySel('payment-update-button').click();

    cy.wait('@editOrder')
      .its('request.body')
      .should('deep.equal', {
        payment_details: {
          note: 'Test payment note!!!',
        },
      });
  });
  it.skip(`Mark unpaid order as paid`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/unpaid.json',
    }).as('getOrderDetail');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*/confirm_payment`, {
      fixture: '/orders/detail/mutate-success.json',
    }).as('confirmPayment');
    cy.wait('@getOrderDetail');

    cy.getBySel('order-payment-card-actions').find('button').contains('Mark as Paid').click();
    cy.getBySel('edit-payment-transfer-time-input').type('2024-04-16T08:30');
    cy.getBySel('edit-payment-last-five-digits-input').type('12345');

    cy.getBySel('payment-update-button').click();

    cy.wait('@confirmPayment').its('request.body').should('deep.equal', {
      attachments: null,
      customer_transfer_time: 1713227400,
      last_five_digits: '12345',
      note: '',
      transfer_amount: '0.3',
    });
  });

  it.skip(`view unpaid & awaiting confirmation order detail`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/awaiting-confirmation.json',
    }).as('getOrderDetail');
    cy.wait('@getOrderDetail');

    cy.getBySel('order-payment-card-actions').find('button').eq(0).contains('Mark as Paid');

    cy.getBySel('payment-detail-reminder-block-awaiting-confirmation').should('exist');
  });
  it.skip(`View awaiting shippment order(validation passed)`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/awaiting-shipment.json',
    }).as('getOrderDetail');
    cy.wait('@getOrderDetail');

    cy.getBySel('order-payment-card-actions').find('button').first().contains('Edit');
    cy.getBySel('payment-detail-reminder-block-success').should(
      'contain',
      'This order has been confirmed as paid.  2024/01/04 12:17 (GMT+8)',
    );

    cy.getBySel('order-actions-card').should(
      'contain',
      'Remaining balance today : $12,345 (Daily Transfer Limit: $100,000)',
    );
  });

  it(`Send token successfully`, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/awaiting-shipment.json',
    }).as('getOrderDetail');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*/transfer`, {
      fixture: '/orders/detail/mutate-success.json',
    }).as('transferOrderAssets');
    cy.wait('@getOrderDetail');

    cy.getBySel('order-actions-send-now').click();
    cy.getBySel('order-submit-request-btn').click();

    cy.wait('@transferOrderAssets').its('request.body').should('be.null');
  });

  it(`出貨失敗，庫存不足 `, () => {
    cy.visit('/en-US/asset/orders/1-123-1');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*`, {
      fixture: '/orders/detail/awaiting-shipment.json',
    }).as('getOrderDetail');
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/orders/*/transfer`, {
      statusCode: 400,
      fixture: '/orders/detail/transfer-balance-not-enough.json',
    }).as('transferOrderAssets');

    cy.wait('@getOrderDetail');
    cy.getBySel('order-actions-send-now').click();
    cy.getBySel('order-submit-request-btn').click();

    cy.contains('Balance not enough').should('exist');
  });
});
