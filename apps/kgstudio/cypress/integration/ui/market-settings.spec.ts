describe('Market Settings', () => {
  it(`
      Given: As AssetPro Admin/Owner
      When: I visit Studio page 
      Then: I can see the Market Settings tab exists in the sidebar
    `, () => {
    cy.setLoggedIn({
      modules: {
        asset_pro: ['market'],
      },
      roles: {
        asset_pro: ['asset_pro:admin'],
      },
      permissions: [
        {
          resource: 'asset_pro_market_info',
          action: 'read',
        },
      ],
    });
    cy.visit('/home/<USER>');
    cy.getBySel('sidebar').should('contain.text', 'Market Settings');
  });

  it(`
  Given: As an AssetPro Admin, and my market settings are <My_Market_Settings>
  When: I visit Market Settings tab
  Then: I can see <My_Market_Settings> and any null field will be displayed as "未設定"
  `, () => {
    cy.setLoggedIn({
      modules: {
        asset_pro: ['market'],
      },
      roles: {
        asset_pro: ['asset_pro:admin'],
      },
      permissions: [
        {
          resource: 'asset_pro_market_info',
          action: 'read',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);

      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/market`, {
        fixture: 'market/market-settings.json',
      }).as('getMarketSettings');
    });
    cy.visit('/asset/market-settings');
    cy.wait('@getMarketSettings');

    cy.fixture('market/market-settings.json').then((marketSettings) => {
      const marketProfileKeys = ['market_url', 'title', 'logo', 'introduction', 'email', 'phone', 'line_id'];
      const orderSettingsKeys = ['payment_expiration_sec'];
      const paymentInfoKeys = [
        'bank_name',
        'branch_name',
        'bank_account_holder_name',
        'bank_account',
        'payment_method',
        'payment_currency',
      ];
      Object.entries(marketSettings).forEach(([key, value]) => {
        if (value === null) value = 'unset';

        if (marketProfileKeys.includes(key)) {
          if (key === 'logo') return;

          cy.getBySel('market-profile-section').should('contain.text', value);
        }

        if (orderSettingsKeys.includes(key)) {
          cy.getBySel('order-settings-section').should('contain.text', '24 Hours');
        }

        if (paymentInfoKeys.includes(key)) {
          cy.getBySel('payment-info-section').should('contain.text', value);
        }
      });
    });
  });
});
