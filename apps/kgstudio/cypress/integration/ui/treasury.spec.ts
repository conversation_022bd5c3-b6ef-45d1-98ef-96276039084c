/// <reference types="cypress" />

describe('[UI] Treasury', () => {
  beforeEach(() => {
    cy.setLoggedIn({
      modules: {
        asset_pro: ['treasury'],
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/accounts`, {
        fixture: '/treasury/accounts.json',
      }).as('getKGAccounts');
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/liquidity`, {
        fixture: '/treasury/liquidity.json',
      }).as('getLiquidity');
      cy.intercept('PUT', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/liquidity`, {
        fixture: '/treasury/edit-liquidity.json',
      }).as('editLiquidity');
    });
    cy.interceptedGetTokens();

    cy.intercept('POST', 'https://eth-sepolia.g.alchemy.com/v2/*', {
      delay: 100,
      statusCode: 200,
      fixture: '/rpc/token-balance-sepolia-usdc.json',
    }).as('getSepoliaUsdcBalance');

    cy.intercept('POST', 'https://polygon-mainnet.g.alchemy.com/v2/*', {
      delay: 100,
      statusCode: 200,
      fixture: '/rpc/token-balance-usdt-polygon.json',
    }).as('getPolygonUsdtBalance');

    cy.intercept('POST', 'https://eth-mainnet.g.alchemy.com/v2/*', {
      delay: 100,
      statusCode: 200,
      fixture: '/rpc/token-balance.json',
    }).as('getEthTokenBalance');

    cy.intercept('POST', 'https://bnb-mainnet.g.alchemy.com/v2/*', {
      delay: 100,
      statusCode: 200,
      fixture: '/rpc/token-balance.json',
    }).as('getBscTokenBalance');

    cy.intercept('GET', 'https://apilist.tronscanapi.com/api/account/tokens*', {
      delay: 100,
      statusCode: 200,
      fixture: '/rpc/token-balance-tron.json',
    }).as('getTronTokenBalance');

    cy.intercept('GET', 'https://shastapi.tronscan.org/api/account/tokens*', {
      delay: 100,
      statusCode: 200,
      fixture: '/rpc/token-balance-shasta.json',
    }).as('getShastaTokenBalance');
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/asset_prices*`, {
      delay: 100,
      statusCode: 200,
      fixture: '/treasury/price.json',
    }).as('getPrice');
    cy.visit('/en-US/asset/treasury');
  });

  it(`Given:
        - As a studio user with asset_pro/treasury permission
      When:
        - Login successfully
        - Visit /asset/treasury
      Then:
        - Should display treasury data correctly
      `, () => {
    const column = ['Asset', 'Price', 'Balance', 'Value', ''];
    cy.get('[data-table]').find('[data-thead]').should('have.length', column.length);
    cy.get('[data-table]')
      .find('[data-thead]')
      .each((thead, i) => {
        cy.wrap(thead).should('contain', column[i]);
      });
    cy.wait('@getKGAccounts');
    cy.wait('@getPrice');
    cy.wait('@getSepoliaUsdcBalance');
    cy.wait('@getEthTokenBalance');
    cy.wait('@getPolygonUsdtBalance');
    cy.wait('@getBscTokenBalance');
    cy.wait('@getTronTokenBalance');
    cy.wait('@getShastaTokenBalance');

    // Check if native tokens display correctly
    const bnbNativeToken = ['BNB', '$300', '246.2816', '73,884.483'];
    cy.getBySel('treausry-token-filter').type('BNB');

    bnbNativeToken.forEach((value, i) => {
      cy.get(`[data-trow]`).find('td').eq(i).should('contain', value);
    });

    // Check if USDC tokens display correctly
    const ethUsdcToken = ['USDC', '$0.9988', '246,281,609,999,996', '245,975,728,240,376'];
    cy.getBySel('treausry-token-filter').clear();
    cy.getBySel('treausry-token-filter').type('USDC');

    ethUsdcToken.forEach((value, i) => {
      cy.get(`[data-trow]`).find('td').eq(i).should('contain', value);
    });

    cy.get(`[data-trow]`).each((row) => {
      cy.wrap(row).find('td').eq(0).should('contain', 'USDC');
    });

    // Check if USDT tokens display correctly
    const ethUsdtToken = ['USDT', '$0.9993', '246,281,609,999,996', '246,112,907,097,146'];

    cy.getBySel('treausry-token-filter').clear();
    cy.getBySel('treausry-token-filter').type('USDT');

    ethUsdtToken.forEach((value, i) => {
      cy.get(`[data-trow]`).find('td').eq(i).should('contain', value);
    });

    cy.get(`[data-trow]`).each((row) => {
      cy.wrap(row).find('td').eq(0).should('contain', 'USDT');
    });

    cy.get('button').contains('Transfer').should('exist');
  });

  it(`Given:
        - As an asset_pro admin
      When:
        - Visit /asset/treasury
      Then:
        - Can switch the tab to Liquidity and edit the liquidity margin
        - Can refresh the liquidity data
    `, () => {
    cy.wait('@getKGAccounts');
    cy.wait('@getPrice');
    cy.wait('@getSepoliaUsdcBalance');
    cy.wait('@getEthTokenBalance');
    cy.wait('@getPolygonUsdtBalance');
    cy.wait('@getBscTokenBalance');
    cy.wait('@getTronTokenBalance');
    cy.wait('@getShastaTokenBalance');

    cy.clock(new Date().getTime());
    cy.getBySel('table-tab').contains('Liquidity').click();
    cy.wait('@getLiquidity');

    cy.get(`[data-trow]`).first().find('td').first().should('contain', 'Gas Swap');
    cy.get(`[data-trow]`).first().find('td').eq(1).should('contain', 'TRX');
    cy.get(`[data-trow]`).first().find('td').eq(4).find('p').should('have.class', 'text-error');
    cy.get(`[data-trow]`).eq(1).find('td').first().should('contain', 'Buy Crypto');
    cy.get(`[data-trow]`).eq(1).find('td').eq(1).should('contain', 'USDT');
  });
});
