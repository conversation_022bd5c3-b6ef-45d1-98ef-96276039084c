/// <reference types="cypress" />
import rowData from '../../fixtures/review/cases.json';

describe('[UI] Review Case', () => {
  beforeEach(() => {
    cy.setLoggedIn().then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/comply_flow/cases?*`, {
        fixture: '/review/cases.json',
      }).as('getReviewCases');
      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/comply_flow/cases/filter_options`,
        {
          fixture: '/review/filter-options.json',
        },
      ).as('getReviewCasesFilterOptions');
      cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/*/comply_flow/*/audit`, {
        statusCode: 200,
        fixture: '/review/review-success.json',
        delay: 500,
      }).as('interceptedReviewCase');
    });
    cy.visit('/en-US/compliance/review');
  });

  it(`Given:
          - As a compliance_admin
        When:
          - Visit /compliance/review
        Then:
          - Table should display case list data correctly
        `, () => {
    cy.wait('@getReviewCases');
    cy.wait('@getReviewCasesFilterOptions');

    // check filter buttons
    const filterButtons = ['kyc-status', 'date-range-submit-time', 'select-risk', 'select-sanctioned', 'select-idv'];
    cy.getBySel('cases-filter-group').children().should('have.length', filterButtons.length);
    filterButtons.forEach((button) => {
      cy.getBySel(button).should('exist');
    });

    // check table column title
    const column = ['Name', 'Personal Information', 'Latest Submit Time', 'Updated', 'Summary', 'KYC Status', 'Action'];
    cy.get('[data-table]').find('[data-thead]').should('have.length', column.length);
    cy.get('[data-table]')
      .find('[data-thead]')
      .each((thead, i) => {
        cy.wrap(thead).should('contain', column[i]);
      });

    cy.get('[data-table]').find('[data-trow]').should('have.length', rowData.data.length);

    // first row data (processing)
    const data1 = rowData.data[0];
    cy.get('[data-trow="0"]').find('td').eq(0).should('contain', data1.name).and('contain', data1.national_id);
    cy.get('[data-trow="0"]')
      .find('td')
      .eq(1)
      .should('contain', data1.phone)
      .and('contain', data1.email)
      .and('contain', data1.line_id);
    cy.get('[data-trow="0"]').find('td').eq(2).should('contain', '2024/01/04, 12:17');
    cy.get('[data-trow="0"]').find('td').eq(3).should('contain', '2024/01/04, 12:17');
    cy.get('[data-trow="0"]').find('td').eq(4).should('contain', '');
    cy.get('[data-trow="0"]').find('td').eq(5).should('contain', 'Processing');
    cy.get('[data-trow="0"]').find('td').eq(6).should('contain', '');

    // second row data (pending)
    const data2 = rowData.data[1];
    cy.get('[data-trow="1"]')
      .find('td')
      .eq(1)
      .should('contain', data2.phone)
      .and('not.contain', data2.email)
      .and('contain', data2.line_id);
    cy.get('[data-trow="1"]')
      .find('td')
      .eq(4)
      .should('contain', 'High Risk')
      .and('contain', 'Sanctioned')
      .and('contain', 'Check ID');
    cy.get('[data-trow="1"]').find('td').eq(5).should('contain', 'Pending');
    cy.get('[data-trow="1"]').find('td').eq(6).should('contain', 'Review');

    // third row data (verified)
    const data3 = rowData.data[2];
    cy.get('[data-trow="2"]')
      .find('td')
      .eq(1)
      .should('not.contain', data3.phone)
      .and('contain', data3.email)
      .and('contain', data3.line_id);
    cy.get('[data-trow="2"]').find('td').eq(4).should('contain', 'Sanctioned');
    cy.get('[data-trow="2"]').find('td').eq(5).should('contain', 'Verified');
    cy.get('[data-trow="2"]').find('td').eq(6).find('button').find('svg').should('exist');

    // fourth row data (rejected)
    const data4 = rowData.data[3];
    cy.get('[data-trow="3"]').find('td').eq(4).should('contain', 'Mid Risk').and('contain', 'Sanctioned');
    cy.get('[data-trow="3"]').find('td').eq(5).should('contain', 'Rejected');
    cy.get('[data-trow="3"]').find('td').eq(6).find('button').find('svg').should('exist');

    // check hover state
    cy.get('[data-trow="3"]').find('td').eq(4).realHover();
    cy.get('[data-test-id="tooltip-summary"]')
      .should('contain', `FORM-${data4.form_id}`)
      .and('contain', `CDD-${data4.cdd_id}`)
      .and('contain', data4.risk_score)
      .and('contain', 'Sanctioned')
      .and('contain', `IDV-${data4.idv_id}`)
      .and('contain', 'Pass');
  });

  it(`Given:
        - As a studio user with compliance permission
      When:
        - Visit Compliance Review page
        - Click on Review Button and see Review Modal
      Then:
        - Should open Review modal and display correct information
      `, () => {
    cy.get('[data-trow="1"]').find('td').last().find('button').click();
    // header
    cy.get('h2').should('contain.text', 'Review');
    cy.getBySel('review-modal-kyc-status-badge').should('contain.text', 'Pending');

    // PII
    const personalInfoValues = ['林美華', 'F000333224', '2003/08/08', 'taiwan', '09884566456', 'AAA234'];
    personalInfoValues.forEach(function (value) {
      cy.getBySel('review-modal-personal-info').should('contain', value);
    });

    // Risk Screening
    const riskScreeningValues = [
      '2024/01/04 12:17 (GMT+8)',
      'FORM-2235',
      'CDD-996',
      'Check ID',
      'High Risk (80)',
      'Sanctioned',
    ];
    riskScreeningValues.forEach(function (value) {
      cy.getBySel('review-modal-risk-screening').should('contain', value);
    });

    // Review Panel
    cy.getBySel('review-modal-panel')
      .find('button')
      .first()
      .should('contain.text', 'Reject')
      .should('have.css', 'background-color', 'rgb(227, 26, 26)')
      .should('be.enabled');
    cy.getBySel('review-modal-panel')
      .find('button')
      .eq(1)
      .should('contain.text', 'Accept')
      .should('have.css', 'background-color', 'rgb(1, 181, 116)')
      .should('be.enabled');
  });

  it(`Given:
        - As a studio user with compliance permission
      When:
        - Visit Compliance Review page
        - Click on View Deatil icon Button and see Detail Modal
      Then:
        - Should open Review Detail Modal and display correct information
    `, () => {
    cy.get('[data-trow="2"]').find('td').last().find('button').click();

    // header
    cy.get('h2').should('contain.text', 'Review Details');
    cy.getBySel('review-modal-kyc-status-badge').should('contain.text', 'Verified');

    // PII
    const personalInfoValues = ['江美華', 'F000333224', '2003/08/08', 'taiwan', '<EMAIL>', 'AAA234'];
    personalInfoValues.forEach(function (value) {
      cy.getBySel('review-modal-personal-info').should('contain', value);
    });

    // Review Result
    const reviewResultValues = [
      'Review result: Verified',
      'Verification rejected due to excessive file uploads.',
      'Andrew Liu',
      '2024/01/04 12:17 (GMT+8)',
    ];
    reviewResultValues.forEach(function (value) {
      cy.getBySel('review-modal-result').should('contain', value);
    });
    cy.getBySel('review-modal-result').should('have.css', 'border-color', 'rgb(1, 181, 116)');
    cy.getBySel('review-modal-result-edit-btn').should('contain', 'Edit').should('be.enabled');

    // Risk Screening
    const riskScreeningValues = ['2024/01/04 12:17 (GMT+8)', 'FORM-2235', 'CDD-455', 'IDV-257', 'Pass', 'Sanctioned'];
    riskScreeningValues.forEach(function (value) {
      cy.getBySel('review-modal-risk-screening').should('contain', value);
    });

    // No Review Panel
    cy.getBySel('review-modal-panel').should('not.exist');
  });

  // Review(verified)
  it(`Given:
        - As a studio user with compliance permission
      When:
        - Review(Verify) KYC status within Review Modal
        - Click on Accept button
      Then:
        - Should display field "Status will change" correctly
        - Internal note is not required
        - Should review kyc status successfully
    `, () => {
    cy.get('[data-trow="1"]').find('td').last().find('button').click();
    cy.getBySel('review-modal-panel-accept-button').click();
    cy.getBySel('review-modal-form').scrollIntoView();
    cy.getBySel('review-modal-form').should('be.visible').should('have.css', 'border-color', 'rgb(1, 181, 116)');
    cy.getBySel('review-modal-form')
      .should('contain', 'Status will change')
      .and('contain', 'Pending')
      .and('contain', 'Verified');
    cy.getBySel('review-modal-panel-cancel-button').click();
    cy.getBySel('review-modal-form').should('not.exist');
    cy.getBySel('review-modal-panel-accept-button').click();
    cy.getBySel('review-modal-panel-confirm-button').click();
    cy.wait('@interceptedReviewCase').its('request.body').should('deep.equal', {
      kyc_status: 'verified',
      internal_notes: '',
    });
  });

  // Review(rejected)
  it(`Given:
        - As a studio user with compliance permission
      When:
        - Review(Reject) KYC status within Review Modal
        - Click on Reject button
      Then:
        - Should display field "Status will change" correctly
        - Internal note is required and limited to 50 characters
        - Should review kyc status successfully
    `, () => {
    cy.get('[data-trow="1"]').find('td').last().find('button').click();
    cy.getBySel('review-modal-panel-reject-button').click();
    cy.getBySel('review-modal-form').scrollIntoView();
    cy.getBySel('review-modal-form').should('be.visible').should('have.css', 'border-color', 'rgb(227, 26, 26)');
    cy.getBySel('review-modal-form')
      .should('contain', 'Status will change')
      .and('contain', 'Pending')
      .and('contain', 'Rejected');

    // validation: check internal note is required
    cy.getBySel('review-modal-panel-confirm-button').click();
    cy.getBySel('review-modal-form').should('contain', "Internal notes are required when status is 'rejected'");
    // validation: internal note is limited to 50 characters
    cy.getBySel('review-modal-form-internal-notes').type('111111111122222222223333333333444444444455555555556');
    cy.getBySel('review-modal-form-internal-notes').blur();
    cy.getBySel('review-modal-form').should('contain', 'Internal notes can have a maximum of 50 characters');
    // // 送出，檢查 request body
    cy.getBySel('review-modal-form-internal-notes').clear();
    cy.getBySel('review-modal-form-internal-notes').type('reject note!');
    cy.getBySel('review-modal-form-internal-notes').blur();
    cy.getBySel('review-modal-panel-confirm-button').click();
    cy.wait('@interceptedReviewCase').its('request.body').should('deep.equal', {
      kyc_status: 'rejected',
      internal_notes: 'reject note!',
    });
  });

  // Edit Review Result
  it(`Given:
        - As a studio user with compliance permission
      When:
        - Edit review result within Review Detail Modal
      Then:
        - Should NOT display field "Status will change" if state is the same
        - Should edit kyc status successfully
    `, () => {
    cy.get('[data-trow="2"]').find('td').last().find('button').click();

    cy.getBySel('review-modal-result-edit-btn').click();
    cy.getBySel('review-modal-form-status-changed').should('not.exist');
    cy.getBySel('review-modal-form-internal-notes').should(
      'contain.value',
      'Verification rejected due to excessive file uploads.',
    );

    cy.getBySel('review-modal-panel-reject-button').click();
    cy.getBySel('review-modal-form-status-changed').should('contain', 'Verified').and('contain', 'Rejected');

    cy.getBySel('review-modal-form-internal-notes').clear({ force: true });
    cy.getBySel('review-modal-form-internal-notes').type('update note!');
    cy.getBySel('review-modal-form-internal-notes').blur();
    cy.getBySel('review-modal-panel-confirm-button').click();
    cy.wait('@interceptedReviewCase').its('request.body').should('deep.equal', {
      kyc_status: 'rejected',
      internal_notes: 'update note!',
    });
  });
});
