/// <reference types="cypress" />
import txsData from '../../fixtures/transfer/histories.json';

describe('[UI] Histories', () => {
  beforeEach(() => {
    cy.setLoggedIn().then((orgId) => {
      Cypress.env('orgId', orgId);

      cy.intercept(
        `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer/filter_options`,
        {
          method: 'GET',
        },
        {
          statusCode: 200,
          fixture: '/transfer/filter-options.json',
        },
      ).as('interceptedFilterOptions');
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/asset_prices*`, {
        delay: 100,
        statusCode: 200,
        fixture: '/treasury/price.json',
      }).as('getPrice');
    });
    cy.interceptedGetTokens();
  });
  it.skip(`Given:
        - As a assetpro_admin
      When:
        - Visit Transaction Histories page
      Then:
        - Should see filter options group
        - Should see table display data correctly
      `, () => {
    cy.intercept(
      {
        method: 'GET',
        url: `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/transfer/histories*`,
      },
      (req) => {
        const url = new URL(req.url);
        const pageNumber = url.searchParams.get('page_number');

        if (pageNumber === '1') {
          req.reply({ fixture: `/transfer/histories.json` });
        } else {
          req.reply({ fixture: `/transfer/histories2.json` });
        }
      },
    ).as('interceptedHistories');
    cy.visit('/asset/transactions');
    cy.wait('@getPrice');

    // check table column title
    const column = [
      'Submit Time',
      'Send Token',
      'Quantity',
      'Recipient',
      'Status',
      'Tx hash',
      'Latest Update',
      'Action',
    ];
    cy.wait('@interceptedHistories');
    cy.get('[data-table]').find('[data-thead]').should('have.length', column.length);
    cy.get('[data-table]')
      .find('[data-thead]')
      .each((thead, i) => {
        cy.wrap(thead).should('contain', column[i]);
      });

    // check data display
    const txsData2 = [
      '2023/05/30',
      txsData.data[0].token_name,
      '$1',
      ['estelle-admin', '<EMAIL>', '886999590578', '0xc881...4777'],
      'Send Success',
      '0xbbd6...43f3',
      '2023/05/30',
    ];
    txsData2.forEach((data, index) => {
      if (typeof data === 'string') {
        cy.get('[data-table]').find('[data-trow="0"]').children().eq(index).should('contain', data);
      } else if (Array.isArray(data)) {
        data.forEach((item) => {
          cy.get('[data-table]').find('[data-trow="0"]').children().eq(index).should('contain', item);
        });
      }
    });

    cy.get('[data-table]')
      .find('[data-trow="0"]')
      .children()
      .eq(3)
      .find(`a[href="https://sepolia.etherscan.io/address/${txsData.data[0].recipient.wallet_address}"]`);

    cy.get('[data-table]')
      .find('[data-trow="0"]')
      .children()
      .eq(5)
      .find(`a[href="https://sepolia.etherscan.io/tx/${txsData.data[0].tx_hash}"]`);
    cy.get('[data-table]').find('[data-trow="0"]').children().last().find(`svg`);

    // check pagination is work correctly
    cy.getBySel('previous-page-btn').should('be.disabled');
    cy.getBySel('next-page-btn').should('not.be.disabled');
    cy.getBySel('next-page-btn').click();
    cy.getBySel('previous-page-btn').should('not.be.disabled');
    cy.getBySel('next-page-btn').should('be.disabled');

    // check filter
    cy.wait('@interceptedFilterOptions');
    cy.getBySel('txs-filter-group').should('exist');
    cy.getBySel('select-submitted-by').click();
    cy.getBySel('select-submitted-by-option').should('contain.text', 'Alliu2FadddDKrodan Ou');
    cy.getBySel('select-submitted-by-option')
      .first()
      .find('[data-cy="option-selected-icon"]')
      .then(($icon) => {
        cy.wrap($icon).should('exist');
      });
  });

  it(`Given:
        - As a studio user, no transaction histories
      When:
        - Visit Transaction Histories page
      Then:
        - Should see table display no data hint
      `, () => {
    cy.intercept(
      `${Cypress.env('apiBaseUrl')}/studio/organization/1/asset_pro/transfer/histories?*`,
      {
        method: 'GET',
        query: { page_number: '1' },
      },
      {
        statusCode: 200,
        fixture: '/transfer/histories-no-data.json',
        delay: 1000,
      },
    ).as('interceptedHistories');

    cy.visit('/en-US/asset/transactions');
    cy.wait('@getPrice');
    cy.wait('@interceptedHistories');
    cy.contains('No Data Available');
  });
});

describe('[UI] Operator Filter Dropdown', () => {
  beforeEach(() => {
    cy.setLoggedIn({
      permissions: [
        {
          resource: 'transaction',
          action: 'read',
        },
      ],
    }).then((orgId) => {
      Cypress.env('orgId', orgId);

      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/transfer/histories*`, {
        fixture: '/transfer/histories.json',
      }).as('interceptedHistories');

      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/asset_pro/transfer/filter_options`,
        {
          fixture: '/transfer/filter-options.json',
        },
      ).as('interceptedFilterOptions');

      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/asset_prices*`, {
        delay: 100,
        statusCode: 200,
        fixture: '/treasury/price.json',
      }).as('getPrice');
    });
    cy.interceptedGetTokens();
  });

  it.skip(`Given:
        - As a assetpro_trader
      When:
        - Visit Transaction Histories page
      Then:
        - Should see Submitted By filter option only contain「Me」
      `, () => {
    cy.visit('/en-US/asset/transactions');
    cy.wait(['@interceptedFilterOptions', '@interceptedHistories', '@getPrice']);
    cy.getBySel('txs-filter-group').should('exist');
    cy.getBySel('select-submitted-by').click();
    cy.getBySel('select-submitted-by-option').should('contain.text', 'Me');
    cy.getBySel('select-submitted-by-option').should('not.contain.text', 'All');
  });
});
