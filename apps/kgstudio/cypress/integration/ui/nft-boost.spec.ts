/// <reference types="cypress" />
import nftDetail from '../../fixtures/nft/detail.json';
import nftData from '../../fixtures/nft/projects.json';

describe('NFT-Boost Collection', () => {
  beforeEach(() => {
    cy.setLoggedIn();

    cy.intercept(
      {
        method: 'GET',
        url: `${Cypress.env('apiBaseUrl')}/studio/organization/1/nft/projects*`,
      },
      {
        statusCode: 200,
        fixture: '/nft/projects.json',
      },
    ).as('interceptedNftProjects');
  });

  it(`Given:
        - As a nft_boost:admin
      When:
        - Visit NFT-Boost Collection page
      Then:
        - Should see table display data correctly
      `, () => {
    cy.visit('/nft/campaign/overview');
    cy.wait('@interceptedNftProjects');

    // check table data display
    const column = ['NFT Collection', 'Claimed / Total', 'Mint Time', 'NFT Campaign Website', 'Status', 'Action'];
    cy.get('[data-table]').find('[data-thead]').should('have.length', column.length);
    cy.get('[data-table]')
      .find('[data-thead]')
      .each((thead, i) => {
        cy.wrap(thead).should('contain', column[i]);
      });
    cy.get('[data-table]')
      .find('[data-trow] td')
      .should('have.length', nftData.data.length * column.length);

    // check published nft display
    cy.get('[data-table]')
      .find('[data-trow="0"]')
      .first()
      .find('a')
      .should('have.attr', 'href', `https://polygonscan.com/address/${nftData.data[0].contract_address}`);
    cy.get('[data-table]').find('[data-trow="0"] td').eq(4).should('contain', 'Published');
    cy.get('[data-table]').find('[data-trow="0"] td').eq(5).find('button').should('have.length', 1);

    // check draft nft display
    cy.get('[data-table]').find('[data-trow="1"]').first().find('a').should('not.be.visible');
    cy.get('[data-table]').find('[data-trow="1"] td').eq(4).should('contain', 'Draft');
    cy.get('[data-table]').find('[data-trow="1"] td').eq(5).find('button').should('have.length', 1);
  });

  it(`Given:
        - As a nft_boost:admin
      When:
        - Visit NFT-Boost Detail page
      Then:
        - Should see detail data display correctly
      `, () => {
    cy.intercept(
      {
        method: 'GET',
        url: `${Cypress.env('apiBaseUrl')}/studio/organization/1/nft/projects/*`,
      },
      {
        statusCode: 200,
        fixture: '/nft/detail.json',
      },
    ).as('interceptedNftDetail');
    cy.intercept(
      {
        method: 'GET',
        url: `${Cypress.env('apiBaseUrl')}/studio/organization/1/accounts`,
      },
      {
        statusCode: 200,
        fixture: '/treasury/accounts.json',
      },
    ).as('interceptedAccounts');

    cy.visit('/nft/campaign/overview');
    cy.wait('@interceptedNftProjects');
    cy.get('[data-table]').find('[data-trow="0"] td').eq(5).find('button').click();

    cy.wait('@interceptedNftDetail');
    cy.wait('@interceptedAccounts');

    cy.get('h1').should('contain', nftData.data[0].collection_name);
    cy.getBySel('nft-detail-collection-image').should('have.attr', 'src', nftDetail.data.collection_image_url);
    cy.getBySel('nft-status-cards').find('span').first().should('contain', nftDetail.data.total_supply);
    cy.getBySel('nft-status-cards').find('span').eq(1).should('contain', nftDetail.data.max_supply);
    cy.getBySel('nft-status-cards').find('span').eq(2).should('contain', '0 %');

    cy.getBySel('nft-qr-code-download').click();
    cy.readFile('cypress/downloads/imgName-QR.png').should('exist');

    cy.getBySel('edit-nft-website-button').click();
    cy.getBySel('nft-website-title').should('have.value', nftDetail.data.title);
    cy.getBySel('nft-website-subTitle').should('have.value', nftDetail.data.subtitle);
    cy.getBySel('nft-website-favicon').find('img').should('have.attr', 'src', nftDetail.data.favicon_image_url);
    cy.getBySel('form-upload-remove-button').click();
    cy.getBySel('form-upload-error-msg').should('contain.text', 'Please upload image');
    cy.getBySel('nft-website-favicon').find('img').should('not.exist');
  });
});
