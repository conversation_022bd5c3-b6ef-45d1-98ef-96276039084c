/// <reference types="cypress" />

describe('Traders cannot access /operators, but others can', () => {
  it(`Given:
        - As a studio user that has no access to AssetPro Operators page
        When:
        - Visit /asset/operators
        Then:
        - Should redirect to not found page`, () => {
    cy.setLoggedIn({
      modules: {
        user_360: ['engage', 'audience'],
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
    });

    cy.visit('/asset/operators');
    cy.getBySel('not-found').should('contain.text', 'Not Found');
  });

  it(`Given:
        - As a studio user that can access to AssetPro Operators page
        When:
        - Visit /asset/operators
        Then:
        - Should show operators list`, () => {
    cy.setLoggedIn().then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/${Cypress.env('orgId')}/asset_pro/operators?*`,
        {
          fixture: '/operators/operators-list.json',
        },
      ).as('getOperators');
    });

    cy.visit('/asset/operators');
    cy.wait('@getOperators');
    cy.getBySel('operator-members').should('contain.text', 'AssetPro Operators (6)');
  });
});

describe('Admin and owner can set daily transfer limit', () => {
  beforeEach(() => {
    const uid = 'sqT0Lk5n27UlIe6Irng0YLTpmPR2';

    cy.setLoggedIn({
      modules: {
        asset_pro: ['operators'],
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/${Cypress.env('orgId')}/asset_pro/operators?*`,
        {
          fixture: '/operators/operators-list.json',
        },
      ).as('getOperators');
      cy.intercept(
        'PUT',
        `${Cypress.env('apiBaseUrl')}/studio/organization/${Cypress.env('orgId')}/asset_pro/operators/${uid}`,
        {
          fixture: '/operators/edit-operator.json',
        },
      ).as('editOperator');
    });
  });

  it(`Given:
        - An admin of asset_pro 
        When:
        - open edit transfer limit modal and set daily transfer limit
        Then:
        - Should close modal and show success toast`, () => {
    cy.visit('/asset/operators');

    cy.getBySel('edit-transfer-btn').first().click();
    cy.getBySel('daily-transfer-limit-input').type('12345');
    cy.getBySel('transfer-approval-threshold-input').type('12346');
    cy.getBySel('edit-transfer-modal-content').find('p.text-error').should('exist');
    cy.getBySel('edit-transfer-limit-confirm').should('be.disabled');

    cy.getBySel('transfer-approval-threshold-input').clear();
    cy.getBySel('transfer-approval-threshold-input').type('1000');
    cy.getBySel('edit-transfer-limit-confirm').click();
    cy.wait('@editOperator');

    cy.getBySel('edit-transfer-limit-modal').should('not.exist');
    cy.get('[data-sonner-toast]').should('contain.text', 'Changes saved!');
  });
});
