/// <reference types="cypress" />

describe('Validate Invite Link Flow', () => {
  beforeEach(() => {
    cy.setLoggedIn().then((orgId) => {
      Cypress.env('orgId', orgId);
    });
  });

  afterEach(() => {
    cy.clearCookies();
  });

  it(`Should join the orgnization successfully`, () => {
    // same with the token of invite email
    const studio_token =
      'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************.PZdgMnMoa16mJHV-gR5slsmt27f9R4fM3_iP65oeTpCB8Xc4Ggf5jKxymFI1i5mJGKWPbWQ4CBdCptxXWtzCfw';
    cy.validateInviteLink(studio_token, false);
    cy.location('pathname').should('eq', '/en-US/home/<USER>');
    cy.getBySel('owner-list').should('contain.text', 'Kuan, testt, 凹凹');
  });

  it(`Should show change account feedback`, () => {
    const orgId = Cypress.env('orgId');
    // different with the token of invite email
    const studio_token =
      'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************.18z6aX1fBBHIvLyLV8l7wCjzUEwOeWaN9psBUGubw5-mXwp5VU1eN7ibmWkF_IhrqppY9HPNNarxYk5xe7Xyow';
    cy.validateInviteLink(studio_token, false, orgId);

    cy.wait('@getMe', { timeout: 20000 }).wait('@getMe', { timeout: 20000 });
    cy.getBySel('feedback-message').should('contain', 'Please change your login account to join the team.');
    cy.getBySel('feedback-button').click();
    cy.location('pathname').should('eq', '/en-US/auth/welcome');
  });
  it(`Should show expired link feedback`, () => {
    const studio_token =
      'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************.18z6aX1fBBHIvLyLV8l7wCjzUEwOeWaN9psBUGubw5-mXwp5VU1eN7ibmWkF_IhrqppY9HPNNarxYk5xe7Xyow';
    cy.validateInviteLink(studio_token, true);
    cy.getBySel('feedback-message').should('contain', 'Sorry, this link has expired or the page cannot be found');
    cy.getBySel('feedback-button').click();
    cy.location('pathname').should('eq', '/en-US/auth/login');
  });
  it(`Given:
      - Click the invitation link in the email
    When:
      - Click on the accept invitation button twice or more
    Then:
      - Should display feedback to tell user they have succesfully joined the organization
    `, () => {
    cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/*/accept_invitation`, {
      statusCode: 409,
      fixture: '/invitation/accept-invitation-error.json',
    }).as('acceptInvitationError');
    const orgId = Cypress.env('orgId');
    cy.visit(`/en-US/check?kg_token=mytoken&organization_id=${orgId}`);
    cy.getBySel('feedback-message').should('contain', 'Congratulations. You have successfully registered.');
    cy.getBySel('feedback-button').should('have.attr', 'href', '/auth/login');
  });
});
