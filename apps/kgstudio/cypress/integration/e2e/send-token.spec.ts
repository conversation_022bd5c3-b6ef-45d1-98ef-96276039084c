/// <reference types="cypress" />

describe('[e2e] Send Token', () => {
  beforeEach(() => {
    cy.resetDB();
    cy.visit('/en-US/auth/login');
    cy.url({ timeout: 20000 }).should('not.eq', `${Cypress.config().baseUrl}/`);
  });

  it('Should send token successfully and the record is shown in transaction history', () => {
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.getBySel('email-login').click();
    cy.getBySel('otp-input').find('input').first().type('123456');
    cy.location('pathname', { timeout: 60000 }).should('not.match', /.*\/auth\/login\/verify-account/);
    cy.visit('/asset/transfer');

    cy.getBySel('send-input-blockchain').click();
    cy.get('[role=option]').contains('Sepolia Testnet').click();
    cy.getBySel('send-input-token').click();
    cy.contains('USDC').click();
    cy.getBySel('send-input-amount').type('0.001');
    cy.getBySel('send-type-radio').get('button[value="phone"]').click();
    cy.getBySel('send-input-phone').type('*********');
    cy.getBySel('send-input-phone').blur();
    cy.getBySel('risk-scan-button').click();
    cy.contains('Potential Risk Of Address', { timeout: 10000 });
    cy.getBySel('send-submit-button').click();

    cy.getBySel('send-confirm-dialog').should('exist');
    cy.getBySel('send-confirm-dialog-confirm').click();
    cy.getBySel('send-token-status-dialog', { timeout: 10000 }).should('exist');
    cy.contains('label', 'Transaction Success!', { timeout: 180000 });

    // check transaction history
    // FIXME: not working now
    // cy.visit('/asset/transactions');
    // cy.get('[data-table]').find('[data-trow="0"]').first().should('contain', 'member1');
    // cy.get('[data-table]').find('[data-trow="0"]').eq(1).should('contain', 'Sepolia');
    // cy.get('[data-table]').find('[data-trow="0"]').eq(2).should('contain', 'USDC');
    // cy.get('[data-table]').find('[data-trow="0"]').eq(3).should('contain', '$0.001');
    // cy.get('[data-table]').find('[data-trow="0"]').eq(5).should('contain', '+886*********');
    // cy.get('[data-table]').find('[data-trow="1"]').should('not.exist');
  });
});
