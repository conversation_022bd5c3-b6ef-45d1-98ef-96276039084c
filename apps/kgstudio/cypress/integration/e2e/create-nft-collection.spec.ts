/// <reference types="cypress" />

describe('[e2e] Create NFT Collection', () => {
  beforeEach(() => {
    cy.resetDB();
    cy.visit('/en-US/auth/login');

    cy.get('input[name="email"]').type('<EMAIL>');
    cy.getBySel('email-login').click();
    cy.getBySel('otp-input').find('input').first().type('123456');

    cy.location('pathname', { timeout: 60000 }).should('not.match', /.*\/auth\/login\/verify-account/);
  });

  it('Should save NFT collection successfully and the collection is shown in overview', () => {
    const id = Date.now();
    const NEW_COLLECTION_DRAFT_NAME = `Draft ${id}`;
    cy.getBySel('sidebar-menu').click();
    cy.get('[href="/en-US/nft/campaign/overview"]').click();
    cy.getBySel('create-nft-collection-button').click();
    cy.getBySel('create-nft-input-name').type(NEW_COLLECTION_DRAFT_NAME);
    cy.getBySel('create-nft-save-button').click();

    cy.contains(`Draft for NFT Project ${NEW_COLLECTION_DRAFT_NAME} has been saved!`).should('be.visible');
    cy.contains(`Draft for NFT Project ${NEW_COLLECTION_DRAFT_NAME} has been saved!`).should('not.be.visible');
    cy.location('pathname').should('contain', '/edit');

    cy.getBySel('sidebar-menu').click();
    cy.get('[href="/en-US/nft/campaign/overview"]').click();
    cy.get('[data-table]')
      .find('[data-trow="0"]')
      .should('contain', NEW_COLLECTION_DRAFT_NAME)
      .and('contain', '0/0')
      .and('contain', 'Draft');

    cy.get('[data-table]').find('[data-trow="0"]').last().find('button').last().click();
    cy.contains(NEW_COLLECTION_DRAFT_NAME).should('be.visible');
    cy.contains('Draft').should('be.visible');
  });

  it('Should publish NFT collection successfully and the collection is shown in overview', function () {
    const NEW_COLLECTION_NAME = `Test E2E ${Date.now()}`;
    const NEW_COLLECTION_SYMBOL = 'TE';
    const NEW_COLLECTION_DESCRIPTION = 'Test NFT Collection Description';
    const NEW_COLLECTION_MAX_SUPPLY = '10';

    cy.getBySel('sidebar-menu').click();
    cy.get('[href="/en-US/nft/campaign/overview"]').click();
    cy.getBySel('create-nft-collection-button').click();
    cy.url({ timeout: 20000 }).should('contain', '/create');
    cy.getBySel('create-nft-input-name').type(NEW_COLLECTION_NAME);
    cy.get('input[type=file]:first').selectFile('cypress/images/logo-square.png', { force: true });
    cy.getBySel('create-nft-input-symbol').type(NEW_COLLECTION_SYMBOL);
    cy.getBySel('create-nft-textarea-description').type(NEW_COLLECTION_DESCRIPTION);
    cy.get('input[type=file]').eq(1).selectFile('cypress/images/logo.png', { force: true });
    cy.getBySel('create-nft-input-max-supply').type(NEW_COLLECTION_MAX_SUPPLY);

    cy.getBySel('create-nft-confirm-button').click();
    cy.getBySel('create-nft-confirm-button', { timeout: 500 }).click();

    cy.get('input[type=file]:last').selectFile('cypress/images/logo-square.png', { force: true });
    cy.getBySel('create-nft-confirm-button', { timeout: 500 }).click();

    cy.getBySel('create-nft-status-dialog', { timeout: 2000 }).should('contain', 'Processing, please wait');

    cy.getBySel('create-nft-status-go-to-project-detail').should('be.visible');
    cy.getBySel('create-nft-status-back-to-overview-button').click();
    cy.get('[data-table]').find('[data-trow="0"]').should('contain', NEW_COLLECTION_NAME).and('contain', 'Processing');

    cy.get('[data-table]').find('[data-trow="0"]').last().find('button').last().click();
    cy.contains(NEW_COLLECTION_NAME).should('be.visible');
    cy.contains(NEW_COLLECTION_DESCRIPTION).should('be.visible');
  });

  it('Should toast error if create NFT collection with existed name', () => {
    const EXISTED_COLLECTION_NAME = 'Test E2E';

    // first time create
    cy.getBySel('sidebar-menu').click();
    cy.get('[href="/en-US/nft/campaign/overview"]').click();
    cy.getBySel('create-nft-collection-button').click();
    cy.getBySel('create-nft-input-name').type(EXISTED_COLLECTION_NAME);
    cy.getBySel('create-nft-save-button').click();
    cy.contains(`Draft for NFT Project ${EXISTED_COLLECTION_NAME} has been saved!`).should('be.visible');
    cy.contains(`Draft for NFT Project ${EXISTED_COLLECTION_NAME} has been saved!`).should('not.be.visible');

    // second time create
    cy.getBySel('sidebar-menu').click();
    cy.get('[href="/en-US/nft/campaign/overview"]').click();
    cy.getBySel('create-nft-collection-button').click();
    cy.getBySel('create-nft-input-name').type(EXISTED_COLLECTION_NAME);
    cy.getBySel('create-nft-save-button').click();

    cy.contains('Collection name already exists').should('be.visible');
    cy.contains('Collection name already exists').should('not.be.visible');
  });

  it('Should display detail page correctly', () => {
    cy.getBySel('sidebar-menu').click();
    cy.get('[href="/en-US/nft/campaign/overview"]').click();
    cy.getBySel('create-nft-collection-button').click();
    cy.getBySel('create-nft-input-name').type('Test Display');
    cy.getBySel('create-nft-save-button').click();
    cy.contains(`Draft for NFT Project Test Display has been saved!`).should('be.visible');
    cy.contains(`Draft for NFT Project Test Display has been saved!`).should('not.be.visible');

    cy.getBySel('sidebar-menu').click();
    cy.get('[href="/en-US/nft/campaign/overview"]').click();
    cy.get('[data-table]').find('[data-trow="0"]').last().find('button').last().click();
    cy.getBySel('create-nft-detail-notification').should('be.visible');
    cy.getBySel('create-nft-detail-basic-info-card').should('be.visible');
    cy.getBySel('create-nft-detail-mint-page-card').should('be.visible');
  });
});
