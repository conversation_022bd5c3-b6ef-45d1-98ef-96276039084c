/// <reference types="cypress" />

Cypress.Commands.add(
  'getBySel',
  (
    selector: string,
    options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Withinable & Cypress.Shadow>,
  ) => {
    return cy.get(`[data-cy=${selector}]`, options);
  },
);

Cypress.Commands.add(
  'getBySelLike',
  (
    selector: string,
    options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Withinable & Cypress.Shadow>,
  ) => {
    return cy.get(`[data-cy*=${selector}]`, options);
  },
);

Cypress.Commands.add('resetDB', () => {
  const baseUrl = new URL(Cypress.env('apiBaseUrl'));
  const domainUrl = baseUrl.port
    ? `${baseUrl.protocol}//${baseUrl.hostname}:${baseUrl.port}`
    : `${baseUrl.protocol}//${baseUrl.hostname}`;
  cy.request({
    method: 'POST',
    // remove `/v1` from the url
    url: `${domainUrl}/_v/database/reset`,
    timeout: 30000,
  })
    .its('body')
    .then((res) => {
      expect(res).to.has.property('code', 0);
    });
});
Cypress.Commands.add('interceptedLoginFailed', () => {
  cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/login`, {
    statusCode: 401,
    fixture: 'login-failed.json',
  }).as('interceptedLoginFailed');
});
Cypress.Commands.add('interceptedAccount', () => {
  cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/account`, {
    statusCode: 200,
    fixture: 'account.json',
  }).as('interceptedAccount');
});
Cypress.Commands.add('interceptedGetLimit', () => {
  cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/asset_pro/transfer/limit`, {
    fixture: '/transfer/limit.json',
  }).as('interceptedGetLimit');
});
Cypress.Commands.add('interceptedGetTokens', () => {
  cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/asset_pro/transfer/tokens`, {
    fixture: '/transfer/tokens.json',
  }).as('interceptedGetTokens');
});
Cypress.Commands.add('interceptedTxStatusLoadingThenRespond', (fixture: string) => {
  let counter = 1;
  cy.intercept(
    {
      method: 'GET',
      url: 'https://api-sepolia.etherscan.io/api*',
      query: {
        module: 'transaction',
        action: 'gettxreceiptstatus',
        txHash: '0x37c3f8e1d9cd132635de8b5baaf85b3665010f74f63b30e31346037e5b9af45c',
        apikey: '*',
      },
    },
    (req) => {
      const response = {
        statusCode: 200,
        fixture: counter < 2 ? '/etherscan/tx-status-loading.json' : fixture,
      };
      req.reply(response);
      counter++;
    },
  ).as('interceptedSendStatus');
});
Cypress.Commands.add('interceptedTxStatusLoadingThenSuccess', () => {
  cy.interceptedTxStatusLoadingThenRespond('/etherscan/tx-status-success.json');
});
Cypress.Commands.add('interceptedTxStatusLoadingThenFailed', () => {
  cy.interceptedTxStatusLoadingThenRespond('/etherscan/tx-status-failed.json');
});
Cypress.Commands.add('interceptedLogin', (role) => {
  const fixture = `login-${role}.json`;
  cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/login`, {
    statusCode: 200,
    fixture: fixture,
  }).as('interceptedLogin');
  return cy.wrap(fixture);
});

Cypress.Commands.add('setLoggedIn', (userData) => {
  cy.setCookie('Kg-Studio-Token-V2', 'random-token');

  cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organizations`, {
    fixture: 'organization/organizations.json',
  }).as('getOrgs');

  return cy.fixture('organization/organizations.json').then((data) => {
    const orgId = data.data[0].id; // Declare and assign orgId here

    cy.fixture('organization/me.json').then((defaultUserData) => {
      cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/me`, (req) => {
        req.on('response', (res) => {
          defaultUserData.data.name = userData?.name || defaultUserData.data.name;
          defaultUserData.data.email = userData?.email || defaultUserData.data.email;
          defaultUserData.data.roles = userData?.roles || defaultUserData.data.roles;
          defaultUserData.data.modules = userData?.modules || defaultUserData.data.modules;
          defaultUserData.data.asset_pro = userData?.asset_pro || defaultUserData.data.asset_pro;
          defaultUserData.data.permissions = userData?.permissions || defaultUserData.data.permissions;

          res.send(200, defaultUserData);
        });
      }).as('getMe');
    });

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/info`, {
      fixture: '/organization/info.json',
    }).as('getOrgInfo');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/pending_order_count`, {
      fixture: '/orders/pending-order-count.json',
    }).as('getPendinfOrderCount');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/transfer/pending_history_count`, {
      fixture: '/transactions/pending-history-count.json',
    }).as('getPendingTxCount');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/*/user_360/compliance`, {
      fixture: '/compliance/compliance.json',
    }).as('getComplianceStats');

    // This is to let the page load the data to set proper state for storage
    cy.visit(`/en-US/home/<USER>
    cy.wait('@getOrgs').wait('@getMe').wait('@getOrgInfo');

    return cy.wrap(orgId);
  });
});

Cypress.Commands.add('validateInviteLink', (studioToken: string, linkExpired: boolean, orgId: number = 1) => {
  cy.setCookie('Kg-Studio-Token-V2', studioToken);
  cy.intercept('POST', `${Cypress.env('apiBaseUrl')}/studio/organization/${orgId}/accept_invitation`, {
    statusCode: linkExpired ? 401 : 200,
    fixture: '/invitation/accept-invitation-success.json',
  }).as('acceptInvitation');
  cy.visit(`/en-US/check?kg_token=mytoken&organization_id=${orgId}`);
});
