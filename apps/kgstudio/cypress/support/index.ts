/// <reference types="cypress" />
import * as chaiSubset from 'chai-subset';

import { AssetProLimit, ModuleRole } from '@/app/[locale]/(protected)/setting/team/_types';
import { Module } from '@/app/_common/services/organization/model';

import './commands';

chai.use(chaiSubset.default);

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace, no-redeclare
  namespace Cypress {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    interface Chainable<Subject> {
      getBySel(selector: string, ...args: any[]): Chainable<JQuery<HTMLElement>>;
      getBySelLike(selector: string, ...args: any[]): Chainable<JQuery<HTMLElement>>;
      resetDB(): Chainable<void>;
      interceptedLogin(role: string): Chainable<string>;
      interceptedLoginFailed(): Chainable<void>;
      interceptedAccount(): Chainable<void>;
      interceptedGetLimit(): Chainable<void>;
      interceptedGetTokens(): Chainable<void>;
      interceptedTxStatusLoadingThenRespond(fixture: string): Chainable<void>;
      interceptedTxStatusLoadingThenSuccess(): Chainable<void>;
      interceptedTxStatusLoadingThenFailed(): Chainable<void>;
      setAuthState(state: any): Chainable<void>;
      setLoggedInAs(role: string): Chainable<void>;
      setLoggedIn(userData?: {
        name?: string;
        email?: string;
        roles?: ModuleRole;
        modules?: Module;
        asset_pro?: AssetProLimit;
        permissions?: {
          resource: string;
          action: string;
        }[];
      }): Chainable<JQuery<number>>;
      validateInviteLink(studioToken: string, linkExpired: boolean, orgId?: number): Chainable<void>;
    }
  }
  // Reference: https://stackoverflow.com/a/********/********
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Chai {
    interface Assertion {
      containSubset(arg: any): Promise<void>;
    }
  }
}
