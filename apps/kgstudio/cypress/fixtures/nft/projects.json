{"code": 0, "data": [{"project_id": 161, "event_id": "monkemonkemonkemonkemonkemonkemonke", "collection_name": "monkemonkemonkemonkemonkemonkemonke", "status": "publish", "start_time": 1702402119, "end_time": 2145888000, "max_supply": 1, "total_supply": 0, "publish_status": "success", "contract_address": "******************************************", "collection_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/fa76a4b7-a972-4d0b-aae5-31367f740bcb-Screenshot%25202023-11-21%2520at%25202.42.46%25E2%2580%25AFAM.png"}, {"project_id": 153, "event_id": "draft-1694155700039", "collection_name": "Draft 1694155700039", "status": "draft", "start_time": 1694155702, "end_time": 2145888000, "max_supply": 0, "total_supply": 0, "publish_status": "pending", "contract_address": "", "collection_image_url": ""}, {"project_id": 160, "event_id": "monkemonkemonkemonkemonkemonke", "collection_name": "monkemonkemonkemonkemonkemonke", "status": "publish", "start_time": 1702401900, "end_time": 1706716740, "max_supply": 20, "total_supply": 0, "publish_status": "success", "contract_address": "******************************************", "collection_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/8293c3d4-4419-4242-be31-415fd250db06-108d518c-13ff-4a15-8761-59831c6935fa-0_A%2520monkey%2520riding%2520a%2520racing%2520motorcycle%2520presses%2520the%2520br_esrgan-v1-x2plus.png"}, {"project_id": 159, "event_id": "testttt", "collection_name": "Testttt", "status": "publish", "start_time": 1702359541, "end_time": 2145888000, "max_supply": 10, "total_supply": 0, "publish_status": "success", "contract_address": "******************************************", "collection_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/d903e424-5b09-4901-99a6-c92d683d569a-Screenshot%25202023-12-12%2520at%252012.49.35%25E2%2580%25AFPM.png"}, {"project_id": 158, "event_id": "test-123", "collection_name": "Test 123", "status": "publish", "start_time": 1699356411, "end_time": 2145888000, "max_supply": 100, "total_supply": 3, "publish_status": "success", "contract_address": "******************************************", "collection_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/e29dd1f5-f242-4f71-a174-3073882412c5-15567-koda.png"}, {"project_id": 157, "event_id": "ascend", "collection_name": "Ascend", "status": "publish", "start_time": 1697426359, "end_time": 1698767940, "max_supply": 3, "total_supply": 1, "publish_status": "success", "contract_address": "******************************************", "collection_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/c927e859-8c78-47fe-8ffb-a242c40d3cd2-idle02.png"}], "paging": {"page_number": 1, "page_size": 7, "total_count": 7}}