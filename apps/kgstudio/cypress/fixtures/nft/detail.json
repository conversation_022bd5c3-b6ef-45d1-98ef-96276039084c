{"code": 0, "data": {"project_id": 161, "status": "publish", "collection_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/fa76a4b7-a972-4d0b-aae5-31367f740bcb-Screenshot%25202023-11-21%2520at%25202.42.46%25E2%2580%25AFAM.png", "collection_name": "monkemonkemonkemonkemonkemonkemonke", "symbol_name": "tt", "collection_description": "ttt", "banner_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/b55f0f89-9b03-4393-b2cb-0b88848d9393-Screenshot%25202023-11-21%2520at%25202.42.46%25E2%2580%25AFAM.png", "contract_schema_name": "ERC721", "total_supply": 0, "max_supply": 1, "start_time": 1702402119, "end_time": 2145888000, "title": "Claim NFT for Free", "subtitle": "monkemonkemonkemonkemonkemonkemonke", "favicon_image_url": "https://wallet-static-dev.kryptogo.com/public/studio/nfts/kryptogo/6813c2e1-ffda-40bf-ac5b-019b9e56d4ad-Screenshot%25202023-11-21%2520at%25202.42.46%25E2%2580%25AFAM.png", "msg_content": "【NFT領取通知】~歡迎領取 {{.collection_name}} NFT，請開啟 KryptoGO Wallet App 並使用手機號碼：{{.phone}} 登入查看。（App 下載連結：https://kryptogo.page.link/q9gD）", "publish_status": "success", "event_id": "monkemonkemonkemonkemonkemonkemonke", "contract_address": "******************************************"}}