{"state": {"data": {"account": "******************************************", "chain": {"id": 1, "unsupported": false}}, "chains": [{"id": 1, "network": "homestead", "name": "Ethereum", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "rpcUrls": {"alchemy": {"http": ["https://eth-mainnet.g.alchemy.com/v2"], "webSocket": ["wss://eth-mainnet.g.alchemy.com/v2"]}, "infura": {"http": ["https://mainnet.infura.io/v3"], "webSocket": ["wss://mainnet.infura.io/ws/v3"]}, "default": {"http": ["https://cloudflare-eth.com"]}, "public": {"http": ["https://cloudflare-eth.com"]}}, "blockExplorers": {"etherscan": {"name": "Etherscan", "url": "https://etherscan.io"}, "default": {"name": "Etherscan", "url": "https://etherscan.io"}}, "contracts": {"ensRegistry": {"address": "******************************************"}, "ensUniversalResolver": {"address": "******************************************", "blockCreated": ********}, "multicall3": {"address": "******************************************", "blockCreated": 14353601}}}, {"id": 137, "name": "Polygon", "network": "matic", "nativeCurrency": {"name": "MATIC", "symbol": "MATIC", "decimals": 18}, "rpcUrls": {"alchemy": {"http": ["https://polygon-mainnet.g.alchemy.com/v2"], "webSocket": ["wss://polygon-mainnet.g.alchemy.com/v2"]}, "infura": {"http": ["https://polygon-mainnet.infura.io/v3"], "webSocket": ["wss://polygon-mainnet.infura.io/ws/v3"]}, "default": {"http": ["https://polygon-rpc.com"]}, "public": {"http": ["https://polygon-rpc.com"]}}, "blockExplorers": {"etherscan": {"name": "PolygonScan", "url": "https://polygonscan.com"}, "default": {"name": "PolygonScan", "url": "https://polygonscan.com"}}, "contracts": {"multicall3": {"address": "******************************************", "blockCreated": 25770160}}}]}, "version": 2}