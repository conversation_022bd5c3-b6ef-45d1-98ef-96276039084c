{"code": 0, "data": [{"id": "1", "chain_id": "sepolia", "token_name": "USDT", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "1", "transfer_time": 1685443967, "submit_time": 1685443967, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA1"}, "status": "send_success", "tx_hash": "0xbbd6f2ef4a48ffc762fe2bbb9e13fbbc4f585d6b72b4bb53dc49292c84b943f3", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "tether"}, {"id": "2", "chain_id": "sepolia", "token_name": "USDT", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "1", "transfer_time": 1685443994, "submit_time": 1685443994, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": null}, "status": "awaiting_release", "tx_hash": "", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "tether"}, {"id": "3", "chain_id": "sepolia", "token_name": "USDC", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "2", "transfer_time": 1685444048, "submit_time": 1685444048, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA3"}, "status": "send_failed", "tx_hash": "", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "usd-coin"}, {"id": "4", "chain_id": "shasta", "token_name": "USDT", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "2", "transfer_time": 1685444089, "submit_time": 1685444089, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "TMLttvoydtCBLWo48HdM5eYyKmJyutkHQn", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA4"}, "status": "sending", "tx_hash": "20a22fdee792074d6ff81626a5ed281aee8761149416785a48eed3448073311a", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "tether"}, {"id": "5", "chain_id": "sepolia", "token_name": "USDC", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "2", "transfer_time": 1685444118, "submit_time": 1685444118, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA5"}, "status": "awaiting_approval", "tx_hash": "", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "usd-coin"}, {"id": "6", "chain_id": "sepolia", "token_name": "USDT", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "1", "transfer_time": 1685445405, "submit_time": 1685445405, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA6"}, "status": "sending", "tx_hash": "", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "tether"}, {"id": "7", "chain_id": "sepolia", "token_name": "USDT", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "1", "transfer_time": 1685445461, "submit_time": 1685445461, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA7"}, "status": "rejected", "tx_hash": "0xea6ae3d4ed88a8a722e0b01c3ec1942ae59fe021e56a77c5482da8dd220c0c75", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "tether"}, {"id": "8", "chain_id": "sepolia", "token_name": "USDT", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "1", "transfer_time": 1685445897, "submit_time": 1685445897, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA8"}, "status": "sending", "tx_hash": "", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "tether"}, {"id": "9", "chain_id": "shasta", "token_name": "USDT", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "1", "transfer_time": 1685445922, "submit_time": 1685445922, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "TMLttvoydtCBLWo48HdM5eYyKmJyutkHQn", "uid": "mfOfXCo39GQ1piCaNRbQ0G0zINA9"}, "status": "sending", "tx_hash": "766a40c4744f4d21f54d675109f3379ef900fb4a2856b5d19cf9172a8a088ed8", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "tether"}, {"id": "10", "chain_id": "sepolia", "token_name": "USDC", "token_logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "amount": "1", "transfer_time": 1685445958, "submit_time": 1685445958, "recipient": {"name": "estelle-admin", "kyc_status": "verified", "email": "<EMAIL>", "phone": "+************", "wallet_address": "******************************************", "uid": null}, "status": "sending", "tx_hash": "", "updated_by": "<PERSON>", "update_time": 1685445958, "token_coingecko_id": "usd-coin"}], "paging": {"page_number": 1, "page_size": 10, "total_count": 11, "page_sort": ""}}