{"code": 0, "data": [{"chain_id": "eth", "chain_name": "Ethereum", "contract_address": "******************************************", "name": "Tether USD", "symbol": "USDT", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "tether"}, {"chain_id": "eth", "chain_name": "Ethereum", "contract_address": "******************************************", "name": "USD Coin", "symbol": "USDC", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "usd-coin"}, {"chain_id": "matic", "chain_name": "Polygon", "contract_address": "******************************************", "name": "Tether USD", "symbol": "USDT", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "tether"}, {"chain_id": "matic", "chain_name": "Polygon", "contract_address": "******************************************", "name": "USD Coin", "symbol": "USDC", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "usd-coin"}, {"chain_id": "bsc", "chain_name": "BNB Chain", "contract_address": "******************************************", "name": "Tether USD", "symbol": "USDT", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 18, "coingecko_id": "tether"}, {"chain_id": "bsc", "chain_name": "BNB Chain", "contract_address": "******************************************", "name": "USD Coin", "symbol": "USDC", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 18, "coingecko_id": "usd-coin"}, {"chain_id": "tron", "chain_name": "Tron", "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "name": "Tether USD", "symbol": "USDT", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "tether"}, {"chain_id": "tron", "chain_name": "Tron", "contract_address": "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8", "name": "USD Coin", "symbol": "USDC", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "usd-coin"}, {"chain_id": "sepolia", "chain_name": "Sepolia Testnet", "contract_address": "******************************************", "name": "Tether USD", "symbol": "USDT", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "tether"}, {"chain_id": "sepolia", "chain_name": "Sepolia Testnet", "contract_address": "******************************************", "name": "USD Coin", "symbol": "USDC", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6, "coingecko_id": "usd-coin"}, {"chain_id": "shasta", "chain_name": "<PERSON><PERSON><PERSON>net", "contract_address": "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs", "name": "Tether USD", "symbol": "USDT", "coingecko_id": "usd-coin", "logo_url": "https://token-icons.s3.amazonaws.com/******************************************.png", "decimals": 6}]}