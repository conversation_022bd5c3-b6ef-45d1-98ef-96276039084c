<svg width="308" height="186" viewBox="0 0 308 186" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M248.805 92.9027C248.805 143.659 207.659 184.805 156.903 184.805C106.146 184.805 65 143.659 65 92.9027M248.805 92.9027C248.805 42.1462 207.659 1 156.903 1C106.146 1 65 42.1462 65 92.9027M248.805 92.9027C248.805 130.023 207.659 160.115 156.903 160.115C106.146 160.115 65 130.023 65 92.9027M248.805 92.9027C248.805 55.7823 207.659 25.6903 156.903 25.6903C106.146 25.6903 65 55.7823 65 92.9027M248.805 92.9027C248.805 112.599 207.659 128.566 156.903 128.566C106.146 128.566 65 112.599 65 92.9027M248.805 92.9027C248.805 73.2061 207.659 57.2389 156.903 57.2389C106.146 57.2389 65 73.2061 65 92.9027" stroke="#FFC211" stroke-width="2" stroke-linecap="round" stroke-dasharray="2.74 5.49"/>
<path d="M216 93L186.699 93M138.69 93H108.5" stroke="#FFC211" stroke-width="2" stroke-linecap="round" stroke-dasharray="2.74 5.49"/>
<rect x="2" y="44.4469" width="106.991" height="97.416" rx="12.3451" fill="white"/>
<rect x="2" y="44.4469" width="106.991" height="97.416" rx="12.3451" fill="url(#paint0_linear_130_11289)"/>
<rect x="2" y="44.4469" width="106.991" height="97.416" rx="12.3451" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2 59.9412C2 54.1988 2 51.3275 3.00595 48.942C3.89347 46.8374 5.32792 45.0085 7.1605 43.645C9.23763 42.0997 12.0262 41.4156 17.6032 40.0473L33.2018 36.2205C44.6295 33.417 50.3434 32.0152 54.8409 33.4204C58.7829 34.652 62.1381 37.2836 64.2735 40.8186C66.7099 44.8517 66.7099 50.7351 66.7099 62.5017V123.808C66.7099 135.575 66.7099 141.458 64.2735 145.491C62.1381 149.026 58.7829 151.658 54.8409 152.889C50.3434 154.295 44.6295 152.893 33.2017 150.089L17.6032 146.262C12.0262 144.894 9.23763 144.21 7.1605 142.665C5.32792 141.301 3.89347 139.472 3.00595 137.368C2 134.982 2 132.111 2 126.369V59.9412Z" fill="#FFC211" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2 60.3761C2 54.5781 2 51.6791 3.02112 49.278C3.92192 47.1598 5.37712 45.3237 7.23352 43.9629C9.33789 42.4203 12.1602 41.7582 17.805 40.4338L32.9193 36.8877C41.5113 34.8719 45.8073 33.864 49.1841 34.9454C52.1444 35.8934 54.6586 37.886 56.2578 40.5515C58.0819 43.5921 58.0819 48.0047 58.0819 56.83V129.48C58.0819 138.305 58.0819 142.718 56.2578 145.758C54.6586 148.424 52.1444 150.416 49.1841 151.364C45.8073 152.446 41.5113 151.438 32.9193 149.422L17.805 145.876C12.1602 144.551 9.33789 143.889 7.23352 142.347C5.37712 140.986 3.92192 139.15 3.02112 137.032C2 134.631 2 131.732 2 125.934V60.3761Z" fill="white"/>
<path d="M2 60.3761C2 54.5781 2 51.6791 3.02112 49.278C3.92192 47.1598 5.37712 45.3237 7.23352 43.9629C9.33789 42.4203 12.1602 41.7582 17.805 40.4338L32.9193 36.8877C41.5113 34.8719 45.8073 33.864 49.1841 34.9454C52.1444 35.8934 54.6586 37.886 56.2578 40.5515C58.0819 43.5921 58.0819 48.0047 58.0819 56.83V129.48C58.0819 138.305 58.0819 142.718 56.2578 145.758C54.6586 148.424 52.1444 150.416 49.1841 151.364C45.8073 152.446 41.5113 151.438 32.9193 149.422L17.805 145.876C12.1602 144.551 9.33789 143.889 7.23352 142.347C5.37712 140.986 3.92192 139.15 3.02112 137.032C2 134.631 2 131.732 2 125.934V60.3761Z" fill="url(#paint1_linear_130_11289)" fill-opacity="0.33"/>
<path d="M2 60.3761C2 54.5781 2 51.6791 3.02112 49.278C3.92192 47.1598 5.37712 45.3237 7.23352 43.9629C9.33789 42.4203 12.1602 41.7582 17.805 40.4338L32.9193 36.8877C41.5113 34.8719 45.8073 33.864 49.1841 34.9454C52.1444 35.8934 54.6586 37.886 56.2578 40.5515C58.0819 43.5921 58.0819 48.0047 58.0819 56.83V129.48C58.0819 138.305 58.0819 142.718 56.2578 145.758C54.6586 148.424 52.1444 150.416 49.1841 151.364C45.8073 152.446 41.5113 151.438 32.9193 149.422L17.805 145.876C12.1602 144.551 9.33789 143.889 7.23352 142.347C5.37712 140.986 3.92192 139.15 3.02112 137.032C2 134.631 2 131.732 2 125.934V60.3761Z" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M33.5487 85.6106C33.5487 90.156 30.478 93.8407 26.6903 93.8407C22.9025 93.8407 19.8318 90.156 19.8318 85.6106C19.8318 81.0653 22.9025 77.3806 26.6903 77.3806C30.478 77.3806 33.5487 81.0653 33.5487 85.6106Z" fill="#FFC211"/>
<path d="M24.4041 92.812C24.4041 91.6756 25.4277 90.7544 26.6903 90.7544C27.9529 90.7544 28.9764 91.6756 28.9764 92.812V108.243C28.9764 109.38 27.9529 110.301 26.6903 110.301C25.4277 110.301 24.4041 109.38 24.4041 108.243V92.812Z" fill="#FFC211"/>
<path d="M296 65.872C296 60.9532 296 58.4937 295.096 56.5143C294.299 54.7699 293.017 53.2915 291.403 52.2553C289.572 51.0795 287.137 50.7308 282.268 50.0336L230 42.5487C228.607 42.3492 227.911 42.2495 227.216 42.2471C226.536 42.2447 225.857 42.3118 225.19 42.4473C224.509 42.5857 223.846 42.8198 222.519 43.288C218.827 44.5909 216.981 45.2424 215.602 46.3902C214.258 47.5088 213.231 48.9602 212.624 50.5995C212 52.2816 212 54.2393 212 58.1546L212 129.845C212 133.761 212 135.718 212.624 137.4C213.231 139.04 214.258 140.491 215.602 141.61C216.981 142.758 218.827 143.409 222.519 144.712C223.846 145.18 224.509 145.414 225.19 145.553C225.857 145.688 226.536 145.755 227.216 145.753C227.911 145.751 228.607 145.651 230 145.451L282.268 137.966C287.137 137.269 289.572 136.921 291.403 135.745C293.017 134.708 294.299 133.23 295.096 131.486C296 129.506 296 127.047 296 122.128L296 65.872Z" fill="#FFC211" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M306 61.7692C306 57.2501 306 54.9905 305.161 53.1859C304.421 51.596 303.234 50.2567 301.744 49.3325C300.052 48.2834 297.809 48.0132 293.322 47.4726L237.958 40.8022C232.786 40.1791 230.2 39.8675 228.196 40.6849C226.437 41.4027 224.974 42.6993 224.051 44.3605C223 46.2522 223 48.8569 223 54.0663L223 130.675C223 136.665 223 139.661 224.209 141.836C225.27 143.746 226.952 145.237 228.975 146.063C231.28 147.002 234.253 146.644 240.2 145.928L293.323 139.527C297.809 138.987 300.052 138.717 301.744 137.668C303.234 136.743 304.421 135.404 305.161 133.814C306 132.009 306 129.75 306 125.231L306 61.7692Z" fill="white"/>
<path d="M306 61.7692C306 57.2501 306 54.9905 305.161 53.1859C304.421 51.596 303.234 50.2567 301.744 49.3325C300.052 48.2834 297.809 48.0132 293.322 47.4726L237.958 40.8022C232.786 40.1791 230.2 39.8675 228.196 40.6849C226.437 41.4027 224.974 42.6993 224.051 44.3605C223 46.2522 223 48.8569 223 54.0663L223 130.675C223 136.665 223 139.661 224.209 141.836C225.27 143.746 226.952 145.237 228.975 146.063C231.28 147.002 234.253 146.644 240.2 145.928L293.323 139.527C297.809 138.987 300.052 138.717 301.744 137.668C303.234 136.743 304.421 135.404 305.161 133.814C306 132.009 306 129.75 306 125.231L306 61.7692Z" fill="url(#paint2_linear_130_11289)" fill-opacity="0.6"/>
<path d="M306 61.7692C306 57.2501 306 54.9905 305.161 53.1859C304.421 51.596 303.234 50.2567 301.744 49.3325C300.052 48.2834 297.809 48.0132 293.322 47.4726L237.958 40.8022C232.786 40.1791 230.2 39.8675 228.196 40.6849C226.437 41.4027 224.974 42.6993 224.051 44.3605C223 46.2522 223 48.8569 223 54.0663L223 130.675C223 136.665 223 139.661 224.209 141.836C225.27 143.746 226.952 145.237 228.975 146.063C231.28 147.002 234.253 146.644 240.2 145.928L293.323 139.527C297.809 138.987 300.052 138.717 301.744 137.668C303.234 136.743 304.421 135.404 305.161 133.814C306 132.009 306 129.75 306 125.231L306 61.7692Z" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M259 87.0671C259 84.2163 259 82.7909 258.462 81.6675C257.989 80.6782 257.229 79.8535 256.283 79.2999C255.207 78.6712 253.787 78.5536 250.946 78.3183L223.984 76.0854C223.598 76.0534 223.405 76.0374 223.212 76.0351C223.04 76.033 222.868 76.0389 222.697 76.053C222.505 76.0687 222.313 76.0981 221.93 76.1568L212.023 77.6753C211.305 77.7853 210.946 77.8403 210.676 78.0161C210.439 78.1711 210.25 78.3907 210.133 78.6492C210 78.9424 210 79.3055 210 80.0318V102.479C210 105.153 210 106.49 210.489 107.57C210.921 108.522 211.615 109.331 212.49 109.901C213.484 110.549 214.805 110.751 217.449 111.156L221.93 111.843C222.313 111.902 222.505 111.931 222.697 111.947C222.868 111.961 223.04 111.967 223.212 111.965C223.405 111.963 223.598 111.947 223.984 111.915L250.946 109.682C253.787 109.446 255.207 109.329 256.283 108.7C257.229 108.147 257.989 107.322 258.462 106.333C259 105.209 259 103.784 259 100.933V87.0671Z" fill="white"/>
<path d="M259 87.0671C259 84.2163 259 82.7909 258.462 81.6675C257.989 80.6782 257.229 79.8535 256.283 79.2999C255.207 78.6712 253.787 78.5536 250.946 78.3183L223.984 76.0854C223.598 76.0534 223.405 76.0374 223.212 76.0351C223.04 76.033 222.868 76.0389 222.697 76.053C222.505 76.0687 222.313 76.0981 221.93 76.1568L212.023 77.6753C211.305 77.7853 210.946 77.8403 210.676 78.0161C210.439 78.1711 210.25 78.3907 210.133 78.6492C210 78.9424 210 79.3055 210 80.0318V102.479C210 105.153 210 106.49 210.489 107.57C210.921 108.522 211.615 109.331 212.49 109.901C213.484 110.549 214.805 110.751 217.449 111.156L221.93 111.843C222.313 111.902 222.505 111.931 222.697 111.947C222.868 111.961 223.04 111.967 223.212 111.965C223.405 111.963 223.598 111.947 223.984 111.915L250.946 109.682C253.787 109.446 255.207 109.329 256.283 108.7C257.229 108.147 257.989 107.322 258.462 106.333C259 105.209 259 103.784 259 100.933V87.0671Z" fill="url(#paint3_linear_130_11289)"/>
<path d="M259 87.0671C259 84.2163 259 82.7909 258.462 81.6675C257.989 80.6782 257.229 79.8535 256.283 79.2999C255.207 78.6712 253.787 78.5536 250.946 78.3183L223.984 76.0854C223.598 76.0534 223.405 76.0374 223.212 76.0351C223.04 76.033 222.868 76.0389 222.697 76.053C222.505 76.0687 222.313 76.0981 221.93 76.1568L212.023 77.6753C211.305 77.7853 210.946 77.8403 210.676 78.0161C210.439 78.1711 210.25 78.3907 210.133 78.6492C210 78.9424 210 79.3055 210 80.0318V102.479C210 105.153 210 106.49 210.489 107.57C210.921 108.522 211.615 109.331 212.49 109.901C213.484 110.549 214.805 110.751 217.449 111.156L221.93 111.843C222.313 111.902 222.505 111.931 222.697 111.947C222.868 111.961 223.04 111.967 223.212 111.965C223.405 111.963 223.598 111.947 223.984 111.915L250.946 109.682C253.787 109.446 255.207 109.329 256.283 108.7C257.229 108.147 257.989 107.322 258.462 106.333C259 105.209 259 103.784 259 100.933V87.0671Z" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<ellipse cx="4.5" cy="5.5" rx="4.5" ry="5.5" transform="matrix(-1 0 0 1 253 88)" fill="#FFC211"/>
<rect x="134" y="70" width="46.6372" height="46.6372" rx="23.3186" fill="white"/>
<rect x="134" y="70" width="46.6372" height="46.6372" rx="23.3186" fill="url(#paint4_linear_130_11289)" fill-opacity="0.72"/>
<rect x="134" y="70" width="46.6372" height="46.6372" rx="23.3186" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M157.215 99.9774L155.629 101.457C153.44 103.5 149.891 103.5 147.702 101.457C145.513 99.4141 145.513 96.1015 147.702 94.0584L149.288 92.5787M163.557 94.0584L165.142 92.5786C167.331 90.5355 167.331 87.223 165.142 85.1799C162.953 83.1367 159.404 83.1367 157.215 85.1799L155.629 86.6596M152.498 96.9807L160.346 89.6563" stroke="#FFC211" stroke-width="2.74336" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<linearGradient id="paint0_linear_130_11289" x1="55" y1="115.5" x2="101" y2="53" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC211" stop-opacity="0.7"/>
<stop offset="1" stop-color="#FFC211" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_130_11289" x1="30" y1="133.5" x2="50" y2="46.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC211" stop-opacity="0.7"/>
<stop offset="1" stop-color="#FFC211" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_130_11289" x1="246.5" y1="120.774" x2="294.491" y2="63.1784" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC211" stop-opacity="0.7"/>
<stop offset="1" stop-color="#FFC211" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_130_11289" x1="223.825" y1="109.341" x2="264.881" y2="66.9938" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC211" stop-opacity="0.7"/>
<stop offset="1" stop-color="#FFC211" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_130_11289" x1="157.319" y1="116.637" x2="157.319" y2="70" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC211" stop-opacity="0.7"/>
<stop offset="1" stop-color="#FFC211" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
