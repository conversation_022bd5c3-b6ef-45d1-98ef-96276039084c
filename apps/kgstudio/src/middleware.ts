import createIntlMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';

import { AUTH_URLS, HOME_PAGE_URL, PUBLIC_PAGES, StudioCookieKey } from '@/app/_common/constant';
import { routing } from '@/i18n/routing';

const intlMiddleware = createIntlMiddleware(routing);

const getIsAuthPath = (request: NextRequest): boolean => {
  const AUTH_URLSRegex = RegExp(`^(/(${routing.locales.join('|')}))?(${AUTH_URLS.join('|')})/?$`, 'i');

  return AUTH_URLSRegex.test(request.nextUrl.pathname);
};

const getMatchedLocale = (request: NextRequest): string | undefined => {
  const matchedLocaleRegex = RegExp(`^/(${routing.locales.join('|')})`, 'i');
  const result = matchedLocaleRegex.exec(request.nextUrl.pathname);

  if (!result) {
    return undefined;
  }

  return result[1];
};

const getLocalizedPathname = (pathname: string, locale: string | undefined): string => {
  if (!locale) return pathname;

  const matchedLocaleRegex = RegExp(`^/(${routing.locales.join('|')})`, 'i');
  const result = matchedLocaleRegex.exec(pathname);

  if (!result) {
    return `/${locale}${pathname}`;
  }

  return pathname.replace(result[1], locale);
};

const authMiddleware = (request: NextRequest) => {
  const authenticated = !!request.cookies.get(StudioCookieKey.STUDIO_OAUTH_TOKEN);

  const isAuthPath = getIsAuthPath(request);
  const matchedLocale = getMatchedLocale(request);

  // Special handling for the create-organization path
  if (request.nextUrl.pathname.includes('/auth/login/create-organization')) {
    console.log('Middleware: Allowing direct access to create-organization path');
    return intlMiddleware(request);
  }

  if (authenticated) {
    if (isAuthPath) {
      return NextResponse.redirect(new URL(getLocalizedPathname(HOME_PAGE_URL, matchedLocale), request.url));
    }
    return intlMiddleware(request);
  } else {
    if (isAuthPath) {
      return intlMiddleware(request);
    }

    const loginUrl = new URL(getLocalizedPathname('/auth/login', matchedLocale), request.url);
    loginUrl.searchParams.append('next', request.nextUrl.pathname);
    return NextResponse.redirect(loginUrl);
  }
};

export default function middleware(req: NextRequest) {
  const publicPathnameRegex = RegExp(`^(/(${routing.locales.join('|')}))?(${PUBLIC_PAGES.join('|')})?/?$`, 'i');
  const isPublicPage = publicPathnameRegex.test(req.nextUrl.pathname);

  if (isPublicPage) {
    return intlMiddleware(req);
  } else {
    return authMiddleware(req);
  }
}

export const config = {
  /*
   * Match all request paths except for the ones starting with:
   * - api (API routes)
   * - _next/static (static files)
   * - _next/image (image optimization files)
   * - xxx.xxx (all static files with file extension)
   * - url extensions (files in public folder)
   */
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)'],

  // https://github.com/vercel/next.js/issues/51401#issuecomment-**********
  unstable_allowDynamic: ['**/.pnpm/**/node_modules/lodash-es/*.js'],
};
