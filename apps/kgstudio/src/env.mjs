import { z } from 'zod';

import { createEnv } from '@t3-oss/env-nextjs';

export const env = createEnv({
  server: {
    NODE_ENV: z.enum(['development', 'test', 'production']),
    NEXT_PUBLIC_SENTRY_DSN: z.string().min(1),
    NEXT_PUBLIC_SENTRY_ENVIRONMENT: z.string().min(1),
    SENTRY_PROJECT: z.string().min(1),
    LOCALIZELY_API_KEY: z.string().min(1),
  },
  client: {
    NEXT_PUBLIC_CI: z.string().default('false'),
    NEXT_PUBLIC_API_ENDPOINT: z.string().min(1),
    NEXT_PUBLIC_ETHERSCAN_API_KEY: z.string().min(1),
    NEXT_PUBLIC_POLYGONSCAN_API_KEY: z.string().min(1),
    NEXT_PUBLIC_BSCSCAN_API_KEY: z.string().min(1),
    NEXT_PUBLIC_ARBISCAN_API_KEY: z.string().min(1),
    NEXT_PUBLIC_APP_DEEP_LINK: z.string().min(1),
    NEXT_PUBLIC_ALCHEMY_API_KEY: z.string().min(1),
    NEXT_PUBLIC_WALLET_WEB: z.string().min(1),
    NEXT_PUBLIC_KG_DASHBOARD: z.string().min(1),
    NEXT_PUBLIC_COMPLIANCE_URL: z.string().min(1),
    NEXT_PUBLIC_RECAPTCHA_KEY: z.string().min(1),
    NEXT_PUBLIC_GRAFANA_BASE_URL: z.string().min(1),
  },
  runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    SENTRY_PROJECT: process.env.SENTRY_PROJECT,
    // eslint-disable-next-line turbo/no-undeclared-env-vars
    LOCALIZELY_API_KEY: process.env.LOCALIZELY_API_KEY,
    NEXT_PUBLIC_CI: process.env.NEXT_PUBLIC_CI,
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
    NEXT_PUBLIC_SENTRY_ENVIRONMENT: process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT,
    NEXT_PUBLIC_API_ENDPOINT: process.env.NEXT_PUBLIC_API_ENDPOINT,
    NEXT_PUBLIC_ETHERSCAN_API_KEY: process.env.NEXT_PUBLIC_ETHERSCAN_API_KEY,
    NEXT_PUBLIC_ARBISCAN_API_KEY: process.env.NEXT_PUBLIC_ARBISCAN_API_KEY,
    NEXT_PUBLIC_POLYGONSCAN_API_KEY: process.env.NEXT_PUBLIC_POLYGONSCAN_API_KEY,
    NEXT_PUBLIC_BSCSCAN_API_KEY: process.env.NEXT_PUBLIC_BSCSCAN_API_KEY,
    NEXT_PUBLIC_APP_DEEP_LINK: process.env.NEXT_PUBLIC_APP_DEEP_LINK,
    NEXT_PUBLIC_ALCHEMY_API_KEY: process.env.NEXT_PUBLIC_ALCHEMY_API_KEY,
    NEXT_PUBLIC_WALLET_WEB: process.env.NEXT_PUBLIC_WALLET_WEB,
    NEXT_PUBLIC_KG_DASHBOARD: process.env.NEXT_PUBLIC_KG_DASHBOARD,
    NEXT_PUBLIC_COMPLIANCE_URL: process.env.NEXT_PUBLIC_COMPLIANCE_URL,
    NEXT_PUBLIC_RECAPTCHA_KEY: process.env.NEXT_PUBLIC_RECAPTCHA_KEY,
    NEXT_PUBLIC_GRAFANA_BASE_URL: process.env.NEXT_PUBLIC_GRAFANA_BASE_URL,
  },

  // eslint-disable-next-line turbo/no-undeclared-env-vars
  skipValidation: process.env.CI === 'true',
});
