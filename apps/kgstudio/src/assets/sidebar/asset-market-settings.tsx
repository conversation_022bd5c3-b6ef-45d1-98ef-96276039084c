import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7M2 7h20M2 7v3a2 2 0 0 0 2 2m18-5v3a2 2 0 0 1-2 2M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8M4 12a2.7 2.7 0 0 0 1.59-.63.7.7 0 0 1 .82 0A2.7 2.7 0 0 0 8 12a2.7 2.7 0 0 0 1.59-.63.7.7 0 0 1 .82 0A2.7 2.7 0 0 0 12 12a2.7 2.7 0 0 0 1.59-.63.7.7 0 0 1 .82 0A2.7 2.7 0 0 0 16 12a2.7 2.7 0 0 0 1.59-.63.7.7 0 0 1 .82 0A2.7 2.7 0 0 0 20 12m-5 10v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4"
    />
  </svg>
);
export default SvgComponent;
