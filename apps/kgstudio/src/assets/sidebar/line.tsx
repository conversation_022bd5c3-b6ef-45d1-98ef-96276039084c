import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg width={12} height={12} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g clipPath="url(#clip0_7166_35182)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.00078 0.601562C9.14382 0.601562 11.7008 2.67662 11.7008 5.22717C11.7008 6.24653 11.3049 7.16759 10.4789 8.07316C9.28326 9.44995 6.6093 11.127 6.00078 11.3829C5.40857 11.6319 5.47771 11.2414 5.50498 11.0873C5.50573 11.0831 5.50645 11.079 5.50713 11.0752C5.52155 10.9897 5.58833 10.5874 5.58833 10.5874C5.60757 10.4421 5.62733 10.2161 5.56963 10.0718C5.50606 9.91315 5.25335 9.83087 5.06743 9.7908C2.33096 9.42911 0.300781 7.51379 0.300781 5.22717C0.300781 2.67662 2.85774 0.601562 6.00078 0.601562ZM4.44076 3.99463H4.84466C4.90485 3.99463 4.95364 4.04343 4.95364 4.10362V6.59113C4.95364 6.65132 4.90485 6.70011 4.84466 6.70011H4.44076C4.38056 6.70011 4.33177 6.65132 4.33177 6.59113V4.10362C4.33177 4.04343 4.38056 3.99463 4.44076 3.99463ZM3.88087 6.70012C3.90921 6.70012 3.93639 6.68886 3.95643 6.66882C3.97647 6.64879 3.98773 6.62161 3.98773 6.59327V6.18937C3.98773 6.16103 3.97647 6.13385 3.95643 6.11381C3.93639 6.09378 3.90921 6.08252 3.88087 6.08252H2.79259V4.10576C2.79259 4.07742 2.78133 4.05025 2.76129 4.03021C2.74126 4.01017 2.71408 3.99891 2.68574 3.99891H2.2797C2.25136 3.99891 2.22419 4.01017 2.20415 4.03021C2.18411 4.05025 2.17285 4.07742 2.17285 4.10576V6.59167C2.17285 6.62 2.18411 6.64718 2.20415 6.66722C2.22419 6.68726 2.25136 6.69852 2.2797 6.69852H3.88248L3.88087 6.70012ZM9.80416 6.70012H8.20139C8.17305 6.70012 8.14587 6.68886 8.12583 6.66882C8.10579 6.64878 8.09454 6.6216 8.09454 6.59327V4.10629C8.09454 4.07796 8.10579 4.05078 8.12583 4.03074C8.14587 4.0107 8.17305 3.99944 8.20139 3.99944H9.80416C9.8325 3.99944 9.85968 4.0107 9.87972 4.03074C9.89976 4.05078 9.91101 4.07796 9.91101 4.10629V4.51073C9.91101 4.53907 9.89976 4.56625 9.87972 4.58628C9.85968 4.60632 9.8325 4.61758 9.80416 4.61758H8.71588V5.03697H9.80416C9.8325 5.03697 9.85968 5.04823 9.87972 5.06827C9.89976 5.08831 9.91101 5.11549 9.91101 5.14382V5.552C9.91101 5.58034 9.89976 5.60751 9.87972 5.62755C9.85968 5.64759 9.8325 5.65885 9.80416 5.65885H8.71588V6.07878H9.80416C9.8325 6.07878 9.85968 6.09003 9.87972 6.11007C9.89976 6.13011 9.91101 6.15729 9.91101 6.18563V6.58953C9.91152 6.60387 9.90912 6.61817 9.90398 6.63157C9.89884 6.64496 9.89104 6.65719 9.88107 6.66751C9.8711 6.67783 9.85915 6.68604 9.84594 6.69164C9.83272 6.69724 9.81851 6.70013 9.80416 6.70012ZM7.59661 3.99463H7.19271C7.16437 3.99463 7.13719 4.00589 7.11715 4.02593C7.09711 4.04597 7.08586 4.07314 7.08586 4.10148V5.57924L5.94789 4.04218C5.94529 4.03811 5.94224 4.03435 5.9388 4.03096L5.93239 4.02455L5.92652 4.01974H5.92331L5.91743 4.01547H5.91423L5.90835 4.01226H5.90461H5.89873H5.89499H5.88858H5.88431H5.8779H5.87362H5.86775H5.45583C5.4275 4.01226 5.40032 4.02352 5.38028 4.04356C5.36024 4.0636 5.34898 4.09077 5.34898 4.11911V6.60662C5.34898 6.63496 5.36024 6.66214 5.38028 6.68217C5.40032 6.70221 5.4275 6.71347 5.45583 6.71347H5.86027C5.88861 6.71347 5.91578 6.70221 5.93582 6.68217C5.95586 6.66214 5.96712 6.63496 5.96712 6.60662V5.11444L7.10669 6.6531C7.11416 6.664 7.12358 6.67342 7.13447 6.68088L7.14088 6.68516H7.14409L7.14943 6.68783H7.15478H7.15851H7.16599C7.17544 6.69015 7.18512 6.69141 7.19484 6.69157H7.59661C7.62495 6.69157 7.65212 6.68031 7.67216 6.66027C7.6922 6.64023 7.70346 6.61305 7.70346 6.58471V4.10362C7.70374 4.08941 7.70119 4.07528 7.69595 4.06207C7.6907 4.04886 7.68288 4.03683 7.67293 4.02668C7.66298 4.01653 7.6511 4.00847 7.638 4.00296C7.62489 3.99746 7.61082 3.99463 7.59661 3.99463Z"
        fill="#68769F"
      />
    </g>
    <defs>
      <clipPath id="clip0_7166_35182">
        <rect width="11.4" height="11.4" fill="white" transform="translate(0.300781 0.300781)" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgComponent;
