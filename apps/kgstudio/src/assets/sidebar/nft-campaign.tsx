import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="none" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeOpacity={0.88}
      strokeWidth={2}
      d="M8 4h-.2c-1.68 0-2.52 0-3.162.327a3 3 0 0 0-1.311 1.311C3 6.28 3 7.12 3 8.8V10m5-6h8M8 4V2m0 2v2m8-2h.2c1.68 0 2.52 0 3.162.327a3 3 0 0 1 1.311 1.311C21 6.28 21 7.12 21 8.8V10m-5-6V2m0 2v2M3 10v7.2c0 1.68 0 2.52.327 3.162a3 3 0 0 0 1.311 1.311C5.28 22 6.12 22 7.8 22h3.7M3 10h18m0 0v1.5m-4.488 5.033-2.075.319c-.219.033-.328.05-.379.106a.23.23 0 0 0-.056.182c.01.076.089.157.247.319l1.501 1.534c.047.048.07.072.086.1a.23.23 0 0 1 .025.081c.003.033-.002.067-.013.134l-.354 2.167c-.038.228-.056.342-.021.41.03.06.085.1.148.113.072.014.17-.04.365-.148l1.856-1.024a.453.453 0 0 1 .117-.054.204.204 0 0 1 .082 0c.03.007.06.023.117.054l1.856 1.024c.195.108.293.162.365.148a.213.213 0 0 0 .148-.113c.035-.068.017-.182-.02-.41l-.355-2.167c-.011-.067-.017-.101-.013-.134a.23.23 0 0 1 .025-.081c.015-.029.039-.052.086-.1l1.501-1.534c.158-.162.237-.243.247-.32a.23.23 0 0 0-.056-.181c-.05-.056-.16-.073-.379-.106l-2.075-.319c-.065-.01-.097-.015-.125-.028a.215.215 0 0 1-.066-.05c-.021-.024-.036-.055-.065-.116l-.928-1.972c-.097-.208-.146-.312-.213-.345a.203.203 0 0 0-.182 0c-.067.033-.116.137-.213.345l-.928 1.971c-.03.062-.044.092-.065.116a.215.215 0 0 1-.066.05c-.028.014-.06.02-.125.03Z"
    />
  </svg>
);
export default SvgComponent;
