{"common.action": "Action", "common.actions": "Actions", "common.add": "Add", "common.admin": "Admin", "common.amount": "Quantity", "common.anonymous": "Anonymous", "common.any-chain": "Any Chain", "common.any-token": "Any Token", "common.blockchain": "Blockchain", "common.cancel": "Cancel", "common.checking": "Checking", "common.clear-all": "Clear All", "common.confirm": "Confirm", "common.confirmation": "Confirmation", "common.connect-wallet-to-login": "Please connect your wallet to login", "common.contact-us": "Contact Us", "common.copy": "Copy", "common.created-at": "Created At", "common.delete": "Delete", "common.edit": "Edit", "common.email": "Email", "common.enter-email": "Enter the email", "common.enter-phone-number": "Enter phone number", "common.error": "Error", "common.go-back": "Go Back", "common.hide-filter": "<PERSON><PERSON>lter", "common.learn-more-about": "Learn more about", "common.loading": "Loading", "common.member": "Member", "common.name": "Name", "common.no-access": "No Access", "common.no-data-available": "No Data Available", "common.no-image": "No image", "common.owner": "Owner", "common.owners": "Owner(s)", "common.phone-number": "phone number", "common.pick-date": "Pick a date", "common.please-check": "Please Check", "common.please-wait": "Please wait", "common.privacy-policy": "Privacy Policy", "common.recipient": "Recipient", "common.refresh": "Refresh", "common.remove": "Remove", "common.role": "Role", "common.save": "Save", "common.search-placeholder": "Quick Search", "common.select": "Select", "common.select-chain": "Select Chain", "common.select-status": "Select Status", "common.select-token": "Select Token", "common.send": "Send", "common.send-token": "Send Token", "common.show-filter": "Show Filter", "common.status.active": "Active", "common.status.expired": "Expired", "common.status.inactive": "deactivated", "common.status.pending": "Pending", "common.status.text": "Status", "common.status.title": "Status", "common.terms": "Terms", "common.time": "Time", "common.time-utc8": "Time(UTC+8)", "common.token": "Token", "common.tx-hash": "Tx hash", "common.tx-status-failed": "Failed", "common.tx-status-pending": "Pending", "common.tx-status-success": "Success", "common.update": "Update", "common.user": "User", "common.wallet": "Wallet Builder", "common.wallet-address": "Wallet Address", "common.welcome": "Welcome!", "error.cant-find-user": "Sorry, we can't find this user.", "error.general-error": "Some error happened. Code: ", "error.no-access": "You do not have access. Please use a verified wallet address to log in.", "error.try-again": "Some error occurred, please try again later.", "error.user-not-found": "User not found.", "kgauth.change.change-email": "Change Email", "kgauth.change.change-phone-number": "Change Phone Number", "kgauth.change.email-different": "New email must be different from the old one", "kgauth.change.email-exists": "Email already exists", "kgauth.change.email-update-failed": "Failed to update email. Please try again later.", "kgauth.change.new-email": "New Email", "kgauth.change.new-phone-number": "New Phone Number", "kgauth.change.old-email": "Your old email is", "kgauth.change.old-phone-number": "Your old phone number is", "kgauth.change.password-input": "Password", "kgauth.change.password-input2": "Confirm Password", "kgauth.change.password-input-hint1": "At least 12-digit", "kgauth.change.password-input-hint2": "Contain uppercase & lowercase, number & letter", "kgauth.change.password-mismatch": "Passwords must match", "kgauth.change.password-title": "Change Password", "kgauth.change.phone-duplicate": "New phone number must be different from the old one", "kgauth.change.phone-exists": "Phone number already exists", "kgauth.change.verify-email": "<PERSON><PERSON><PERSON>", "kgauth.change.verify-phone-number": "Verify Phone Number", "kgauth.common.accept": "Accept", "kgauth.common.change-email-address": "change email address", "kgauth.common.change-password": "change password", "kgauth.common.change-phone-number": "change phone number", "kgauth.common.continue": "Continue", "kgauth.common.decline": "Decline", "kgauth.common.done": "Done", "kgauth.common.link-line-account": "link LINE account", "kgauth.common.loading": "Data processing in progress. Please do not close this window.", "kgauth.common.login": "<PERSON><PERSON>", "kgauth.common.oauth-login": "oauth login", "kgauth.common.resend": "Resend", "kgauth.common.retry": "Retry", "kgauth.common.seconds": "seconds", "kgauth.common.verify-email-address": "verify email address", "kgauth.common.verify-phone-number": "verify phone number", "kgauth.errors.common": "Something went wrong!", "kgauth.errors.common-retry": "Something went wrong. Please try again later.", "kgauth.errors.invalid-email": "Invalid email", "kgauth.errors.invalid-otp": "Invalid Code. Please try again.", "kgauth.errors.invalid-phone": "Invalid phone number", "kgauth.errors.otp-digit": "OTP must be 6 digits", "kgauth.errors.password-required": "Wrong Password.", "kgauth.errors.rate-limit": "Rate limit exceeded. Please try again later.", "kgauth.errors.return-to-wallet-in": "Return to wallet in", "kgauth.errors.return-to-wallet-now": "Return to wallet now", "kgauth.errors.token-expired": "Your session has expired. Please log in again to continue.", "kgauth.errors.update-phone": "Failed to update phone number. Please try again later.", "kgauth.forgot.backup-seed": "Backup Seed Phrase", "kgauth.forgot.desc": "Important: If you click \"OK\" to reset password, we will erase your account's asset information at the same time. Don't worry, you can import your asset through seed phrase later. But we recommend you backup seed phrase first.", "kgauth.forgot.password": "Forgot password", "kgauth.forgot.reset": "OK. I will log out and reset password.", "kgauth.forgot.step1": "Log out of the app", "kgauth.forgot.step2": "Log in again", "kgauth.forgot.step3": "Click \"Forgot password\" and follow the guides", "kgauth.forgot.step-title": "Please click \"OK\" and follow the steps", "kgauth.forgot.sub-title": "To continue, please log out, so we can help you reset password.", "kgauth.login.email": "Email", "kgauth.login.input-required": "Login/ Register information is required.", "kgauth.login.or": "or", "kgauth.login.phone": "Phone", "kgauth.login.signin-sub-title": "to continue to", "kgauth.login.signin-title": "Sign in / Register with KryptoGO", "kgauth.login.with-google": "Login with Google", "kgauth.oauth-callback.authorize-description": "The following permissions are requested. Please review and make sure you agree to share the following info with this site or app", "kgauth.oauth-callback.authorize-item-0": "View your KryptoGO account information, including your email, phone number, and user handle.", "kgauth.oauth-callback.authorize-item-1": "Access your wallet addresses and view your digital assets.", "kgauth.oauth-callback.authorize-item-2": "View your transaction history.\n", "kgauth.oauth-callback.authorize-item-3": "", "kgauth.oauth-callback.authorize-scope-and": "and", "kgauth.oauth-callback.authorize-scope-asset": "assets, NFTs, transactions", "kgauth.oauth-callback.authorize-scope-chatroom": "chatroom", "kgauth.oauth-callback.authorize-scope-edit": "edit", "kgauth.oauth-callback.authorize-scope-notification": "notifications", "kgauth.oauth-callback.authorize-scope-order": "orders", "kgauth.oauth-callback.authorize-scope-read": "view", "kgauth.oauth-callback.authorize-scope-token": "token", "kgauth.oauth-callback.authorize-scope-transaction": "transactions", "kgauth.oauth-callback.authorize-scope-user": "User Information", "kgauth.oauth-callback.authorize-scope-vault": "vault", "kgauth.oauth-callback.authorize-scope-wallet": "wallets", "kgauth.oauth-callback.authorize-scope-your": "your", "kgauth.oauth-callback.subtitle": "An application wants access to your KryptoGO Account", "kgauth.oauth-callback.title": "Authorize App", "kgauth.password.enter-password": "Enter Your password", "kgauth.password.forget-password": "Forget password ?", "kgauth.password.invalid": "Invalid password. Please try again.", "kgauth.password.sub-title": "To access your wallet assets, please use your password to restore the wallet from backup.", "kgauth.success.email": "Email verified successfully!", "kgauth.success.email-updated": "Email updated successfully!", "kgauth.success.password-updated": "Password updated successfully!", "kgauth.success.phone": "Phone number verified successfully!", "kgauth.success.phone-updated": "Phone number updated successfully!", "kgauth.verify.enter-digit": "Enter the 6-digit code that you received to", "kgauth.verify.not-receive-code": "Didn't receive the code?", "kgauth.verify.title": "We've sent a code to", "kgform.common.back-to-home": "Back to Home", "kgform.common.change": "Change", "kgform.common.next-page": "Next Page", "kgform.common.previous-page": "Previous Page", "kgform.common.resend": "Resend", "kgform.common.start": "Start", "kgform.common.submit": "Submit", "kgform.common.verify": "Verify", "kgform.errors.addres.in-use": "The address is already in use.", "kgform.errors.address.in-use": "The address is already in use.", "kgform.errors.address.invalid-format": "Invalid address format.", "kgform.errors.birth-date": "You must be at least 18 years old.", "kgform.errors.code.invalid": "Invalid code.", "kgform.errors.code.invalid-format": "OTP code must be 6 digits.", "kgform.errors.email.in-use": "The email is already in use.", "kgform.errors.email.invalid-format": "Invalid email format.", "kgform.errors.file-upload.file-too-large": "File size is too large.", "kgform.errors.form-validation-error": "Some fields are not filled in correctly.", "kgform.errors.idIssue-date": "Please provide the complete issuance date.", "kgform.errors.id-number": "Please provide the correct ID number.", "kgform.errors.max-length": "The maximum length is {maxLength} characters", "kgform.errors.no-client-id": "OAuth client ID is invalid.", "kgform.errors.oauth-login-error": "<PERSON><PERSON> failed.", "kgform.errors.phone.in-use": "The phone number is already in use.", "kgform.errors.phone.invalid-format": "Invalid phone number format.", "kgform.errors.privacy-policy": "Please agree to the Personal Data Security and Privacy Policy", "kgform.errors.required": "This field is required", "kgform.errors.signature.empty": "Please sign your full name.", "kgform.errors.signature.invalid": "Invalid signature.", "kgform.errors.something-went-wrong": "Something went wrong, please try again later.", "kgform.errors.submission-error": "Submission error. Please try again later.", "kgform.errors.too-many-request": "Too many requests. Please try again later.", "kgform.errros.code.invalid": "Invalid code.", "kgform.form.address.connect": "Connect", "kgform.form.address.disconnect": "Disconnect", "kgform.form.email.dialog.description": "The verification code has been sent to {email}", "kgform.form.email.dialog.title": "Email Verification", "kgform.form.email.in-use": "The email is already in use.", "kgform.form.file-upload.choose-file": "Choose <PERSON>", "kgform.form.file-upload.start-over": "Start Over", "kgform.form.login-and-start": "Login and Start", "kgform.form.phone.dialog.description": "The verification code has been sent to {phone}", "kgform.form.phone.dialog.title": "Phone Verification", "kgform.form.signature.clear": "Clear", "kgform.form.signature.dialog.title": "Sign", "kgform.form.signature.placeholder": "Click here to sign your full name", "kgform.form.signature.save": "Save", "kgform.form.successfully-verirfied": "Successfully verified.", "kgform.form.upload-placeholder": "Click or drag to upload files", "kgform.index.buy-crypto": "Buy Crypto", "kgform.index.login": "<PERSON><PERSON>", "kgform.index.register": "Register", "kgstore.checkout.cta": "I’ve Made Payment", "kgstore.checkout.customer-info": "Customer Info", "kgstore.checkout.error-create-order": "Failed to create the order.", "kgstore.checkout.order-correct-text": "Once an order has been created, you may make payment within the specified time limit.", "kgstore.checkout.order-correct-title": "Order Correct?", "kgstore.checkout.order-summary": "Order Summary", "kgstore.checkout.rate-updated-text": "The item you selected has been updated with the new rate. Please try again.", "kgstore.checkout.rate-updated-title": "New Rate Updated", "kgstore.checkout.receiving-wallet": "Receiving Wallet", "kgstore.checkout.title": "Checkout", "kgstore.checkout.toast": "Your payment deadline will be {time} after confirming the order.", "kgstore.checkout.transfer-fund": "Transfer Funds to ", "kgstore.checkout.will-receive": "You will receive", "kgstore.common.about": "About", "kgstore.common.back": "Back", "kgstore.common.bank-transfer": "Bank Transfer", "kgstore.common.buy": "Buy", "kgstore.common.cancel": "Cancel", "kgstore.common.confirm": "Confirm", "kgstore.common.copy-success": "Copy successful!", "kgstore.common.email": "Email", "kgstore.common.error": "Something went wrong. Please try again later.", "kgstore.common.error-get-wallet": "Failed to get wallet information.", "kgstore.common.kyc-status.pending": "Pending", "kgstore.common.kyc-status.processing": "Processing", "kgstore.common.kyc-status.rejected": "Rejected", "kgstore.common.kyc-status.unverified": "Unverified", "kgstore.common.kyc-status.verified": "Verified", "kgstore.common.limit": "Limit", "kgstore.common.login-now": "Login Now", "kgstore.common.login-order-desc": "Login to place an order", "kgstore.common.login-order-title": "Login to place an order", "kgstore.common.next": "Next", "kgstore.common.payment-method": "Payment Method", "kgstore.common.phone": "Phone", "kgstore.common.rate": "Rate", "kgstore.common.recaptcha-error": "Failed to verify Google reCAPTCHA. Please refresh web page or contact our support team for assistance.", "kgstore.common.submit": "Submit", "kgstore.common.subtotal": "Subtotal", "kgstore.common.total": "Total", "kgstore.common.try-again": "Try Again", "kgstore.login.back": "Back", "kgstore.login.check-email-code": "Please check your email to receive the OTP verification code.", "kgstore.login.continue": "Continue", "kgstore.login.email-required": "Email is required", "kgstore.login.otp-message": "OTP must be 6 digits", "kgstore.login.phone-required": "Phone number is required", "kgstore.login.resend": "Resend", "kgstore.login.success": "Login successful. Redirecting, please wait.", "kgstore.login.title": "Sign in / Register with KryptoGO to continue", "kgstore.login.welcome": "Welcome!", "kgstore.menu.orders": "Orders", "kgstore.menu.product": "Product", "kgstore.menu.profile": "Profile", "kgstore.order.active-title": "Active", "kgstore.order.attachments.count": "{count} Files", "kgstore.order.attachments.title": "Attachments", "kgstore.order.cancelled-time": "Cancelled Time", "kgstore.order.cancel-toast": "The owner has canceled order {orderId}, with a total payment of {cost}.\n\nIf you have already made the payment, please contact the owner as soon as possible for a refund.", "kgstore.order.cancel-toast-cta": "Check Contact Info", "kgstore.order.delivered-time": "Delivered Time", "kgstore.order.error-get-order": "Failed to retrieve active orders", "kgstore.order.error-get-order-details": "Failed to retrieve order details", "kgstore.order.history-title": "History", "kgstore.order.no-order": "No active or completed orders yet", "kgstore.order.no-order-history": "No completed orders yet.", "kgstore.order.order-created": "Order Created", "kgstore.order.payment-time": "Payment Time", "kgstore.order.receiving-wallet": "Receiving Wallet", "kgstore.order.shipment-time": "Shipment Time", "kgstore.order.shipment-tx-hash": "Transaction Hash", "kgstore.order.shipping-time": "Shipping Time", "kgstore.order.status-awaiting-confirmation": "Awaiting Confirm", "kgstore.order.status-awaiting-shipment": "Awaiting Shipment", "kgstore.order.status-canceled": "Cancelled", "kgstore.order.status-delivered": "Delivered", "kgstore.order.status-shipping": "Shipping", "kgstore.order.status-unpaid": "Unpaid", "kgstore.order.unpaid-toast": "Please make payment for {order} before {time}.", "kgstore.order.view-more": "View More", "kgstore.order.view-order-detail": "View Order Details", "kgstore.order.view-receipt": "View Receipt", "kgstore.payment.desc": "Please upload at least 1 screenshot, image, or any other attachment to prove your payment.", "kgstore.payment.error-attachment": "Failed to upload attachments", "kgstore.payment.error-file-size": "File size is too large to upload.", "kgstore.payment.error-file-type": "Invalid file type. Only png, jpg, jpeg, and webp are allowed.", "kgstore.payment.error-max-upload": "Only 10 attachments are allowed.", "kgstore.payment.error-upload": "Failed to upload payment detail", "kgstore.payment.file-restrict": "Drop png/jpg/jpeg/heic/heif here\nFile size limit is 10MB\nImage ratio 300*8000px", "kgstore.payment.status-awaiting-refund": "Awaiting Refund", "kgstore.payment.status-paid": "Paid", "kgstore.payment.status-refunded": "Refunded", "kgstore.payment.success-upload": "Payment detail uploaded successfully!", "kgstore.payment.title": "Upload Attachment", "kgstore.payment.upload-and-inform": "Upload and Inform Seller", "kgstore.payment.x-files-uploaded": "Files Uploaded", "kgstore.product.account-name": "Account Name", "kgstore.product.account-number": "Account Number", "kgstore.product.bank-name": "Bank Name", "kgstore.product.branch-name": "Branch", "kgstore.product.buy-now": "Buy Now", "kgstore.product.cta-need-kyc": "Submit KYC to Continue", "kgstore.product.cta-sign-in": "Sign In to Buy", "kgstore.product.current-rate": "Current Rate", "kgstore.product.error-max-amount": "Max amount is", "kgstore.product.error-max-quantity": "Max quantity is", "kgstore.product.error-merchant": "Failed to retrieve merchant information.", "kgstore.product.error-min-amount": "Min amount is", "kgstore.product.error-min-quantity": "Min quantity is", "kgstore.product.fee-desc-1": "The total price includes all fees. The actual purchase quantity will be calculated as", "kgstore.product.fee-desc-2": "If the handling fee for your purchase quantity is lower than the minimum, the total price will be adjusted accordingly.", "kgstore.product.fee-formula-1": "total/exchange_rate.", "kgstore.product.fee-formula-2": "total/(exchange_rate*(1+proportional_fee).", "kgstore.product.handling-fee": "Handling Fee", "kgstore.product.include-fee": "Price Includes {fee_percentage}% Handling Fee", "kgstore.product.introduction": "Introduction", "kgstore.product.limit": "Limit", "kgstore.product.market-profile": "Market Profile", "kgstore.product.minimum-handling-fee": "Minimum Handling Fee", "kgstore.product.no-handling-fee": "No Handling Fee", "kgstore.product.no-product": "The owner did not publish any products.", "kgstore.product.receive": "and receive", "kgstore.product.spend": "I want to spend", "kgstore.product.title": "Products", "kgstore.product.transfer-funds": "Transfer Funds to", "kgstore.profile.title": "Profile", "kgstore.toast.guest-desc": "Log in immediately and verify your identity to ensure that you can place an order anytime you see a product you like!", "kgstore.toast.guest-title": "Enjoy the safest crypto transaction", "kgstore.toast.pending-title": "Your identity application is under review.", "kgstore.toast.rejected-desc": "Please contact our customer support to know more about the details.", "kgstore.toast.rejected-title": "Identity application has been rejected.", "kgstore.toast.unverified-title": "Verify your identity to start trading.", "kgstore.toast.verified-desc": "Your identity application has been verified! You may now place an order.", "kgstore.toast.verified-title": "Identity verified successfully!", "kgstore.unpaid.cta": "I've made the payment", "kgstudio.asset.amount-invalid-description-1": "Adjust amount or contact admin to deposit at least", "kgstudio.asset.amount-invalid-description-2": "and", "kgstudio.asset.amount-invalid-description-3": "to ensure the transaction can be made.", "kgstudio.asset.available-balance": "Available balance: ", "kgstudio.asset.balance": "Asset Balance", "kgstudio.asset.balance-checking": "Balance Checking...", "kgstudio.asset.balance-value": "Balance: {formattedTokenBalance}", "kgstudio.asset.checking-kya": "Checking potential risk of wallet address", "kgstudio.asset.checking-kyc": "Checking KYC status", "kgstudio.asset.checklist.contact-hint": "Remaining balance today : $0 (Daily Transfer Limit: $0) Seems like your transfer limit hasn't been set by your system Admin or Owner. Please contact them to help you with the transfer limit setup.", "kgstudio.asset.checklist.edit-hint": "Edit your transfer limit", "kgstudio.asset.checklist.exceed-hint": "Exceeds the amount that can be transferred out, please reduce the amount. (Remaining transfer limit today : ${remainLimit})", "kgstudio.asset.checklist.remain-hint": "Today's Remaining Balance: ${remainLimit} (Daily Transfer Limit: ${dailyTransferLimit})", "kgstudio.asset.checklist.reuqired-approval": "This transaction needs to be reviewed. (Approval threshold: ${threshold})", "kgstudio.asset.checklist.reuqired-hint": "Amount should be greater than 0. (Remaining balance today : ${remainLimit})", "kgstudio.asset.check-tx": "Please check the transaction", "kgstudio.asset.customer-transfer-time": "Payment Notice Time", "kgstudio.asset.deposit-now": "Deposit Now", "kgstudio.asset.edit-now": "Edit Now", "kgstudio.asset.edit-order-modal.accepted-file-types": "Format: png, jpg, jpeg, webp", "kgstudio.asset.edit-order-modal.cancel-transaction-instruction": "If you wish to stop the transaction, please cancel this order.", "kgstudio.asset.edit-order-modal.change-status-to": "Change Status to", "kgstudio.asset.edit-order-modal.edit-unpaid-himt": "Please fill in the following information based on the proof of payment provided by the customer", "kgstudio.asset.edit-order-modal.file-size-error": "File size exceeds 1MB", "kgstudio.asset.edit-order-modal.max-files-error": "Maximum number of files exceeded", "kgstudio.asset.edit-order-modal.max-file-upload-info": "Up to 10 files can be uploaded, each file size does not exceed 1MB", "kgstudio.asset.edit-order-modal.payment-amount-mismatch": "The actual payment amount differs from the order amount", "kgstudio.asset.edit-order-modal.payment-done-hint": "The customer has completed the payment; please arrange the shipment promptly. If you do not accept this transaction, you must refund promptly after cancelling the order.", "kgstudio.asset.edit-order-modal.payment-note": "Payment Note", "kgstudio.asset.edit-order-modal.title": "Edit Payment Detail", "kgstudio.asset.edit-order-modal.upload-attachments": "Upload Attachments", "kgstudio.asset.edit-tx-note-modal.attachments.title": "Attachments", "kgstudio.asset.edit-tx-note-modal.note.hint": "Enter the transaction note and upload necessary attachments for approval.", "kgstudio.asset.edit-tx-note-modal.note.placeholder": "Transaction Notes", "kgstudio.asset.edit-tx-note-modal.note.title": "Transaction Note", "kgstudio.asset.edit-tx-note-modal.title": "Edit", "kgstudio.asset.edit-tx-note-modal.update-failed-error": "Translation Placeholder", "kgstudio.asset.estimated-gas": "Estimated Gas fee:", "kgstudio.asset.estimated-gas-checking": "Estimated Gas Fee: Checking...", "kgstudio.asset.finance.loading": "Generating dashboard data...", "kgstudio.asset.gas-insufficient": "Insufficient gas balance.", "kgstudio.asset.kya-info.total-balance": "Asset Balance", "kgstudio.asset.kya-info.total-received": "Total Received", "kgstudio.asset.kya-info.total-spent": "Total Spent", "kgstudio.asset.kya-info.total-transactions": "Total Transactions", "kgstudio.asset.kya-status.atm.desc": "Cryptocurrency ATM operator.", "kgstudio.asset.kya-status.atm.name": "ATM", "kgstudio.asset.kya-status.child-exploitation.desc": "No additional information.", "kgstudio.asset.kya-status.child-exploitation.name": "Child Exploitation", "kgstudio.asset.kya-status.dark-market.desc": "An online marketplace which operates via darknets and is used for trading illegal products for cryptocurrency.", "kgstudio.asset.kya-status.dark-market.name": "Darknet Marketplace", "kgstudio.asset.kya-status.dark-service.desc": "An organization which operates via darknets and offers illegal services for cryptocurrency.", "kgstudio.asset.kya-status.dark-service.name": "Darknet Service", "kgstudio.asset.kya-status.enforcement-action.desc": "The entity is subject to legal proceedings. Jurisdictions will be annotated as a subtype.", "kgstudio.asset.kya-status.enforcement-action.name": "Enforcement Action", "'kgstudio.asset.kya-status.error'": "Currently unable to perform a risk scan for this address, please try again later.", "kgstudio.asset.kya-status.error": "Currently unable to perform a risk scan for this address, please try again later.", "kgstudio.asset.kya-status.exchange-fraudulent.desc": "An exchange that was involved in illegal activity.", "kgstudio.asset.kya-status.exchange-fraudulent.name": "Fraudulent Exchange", "kgstudio.asset.kya-status.exchange-licensed.desc": "The entity holds a business license specific to crypto assets, including custody, exchange, brokerage, or any other related financial service. It provides an exchange service where participants interact with a central party (the entity). It does not include non-specific financial service licenses and jurisdictions listed as non-cooperative with FATF.", "kgstudio.asset.kya-status.exchange-licensed.name": "Exchange: Licensed", "kgstudio.asset.kya-status.exchange-unlicensed.desc": "The entity does not hold any specific business license for crypto assets and provides an exchange service where participants interact with a central party (the entity). It includes licensed entities but in jurisdictions listed as non-cooperative with FATF.", "kgstudio.asset.kya-status.exchange-unlicensed.name": "Exchange: Unlicensed", "kgstudio.asset.kya-status.gambling.desc": "An online resource offering gambling services using cryptocurrency.", "kgstudio.asset.kya-status.gambling.name": "Gambling", "kgstudio.asset.kya-status.high": "High", "kgstudio.asset.kya-status.high-risk": "High Risk", "kgstudio.asset.kya-status.illegal-service.desc": "A resource offering illegal services or engaged in illegal activities.", "kgstudio.asset.kya-status.illegal-service.name": "Illegal Service", "kgstudio.asset.kya-status.liquidity-pools.desc": "No additional information.", "kgstudio.asset.kya-status.liquidity-pools.name": "Liquidity Pools", "kgstudio.asset.kya-status.low": "Low", "kgstudio.asset.kya-status.low-risk": "Low Risk", "kgstudio.asset.kya-status.marketplace.desc": "An entity offering legal services/trading goods for cryptocurrency.", "kgstudio.asset.kya-status.marketplace.name": "Online Marketplace", "kgstudio.asset.kya-status.medium": "Medium", "kgstudio.asset.kya-status.medium-risk": "Medium Risk", "kgstudio.asset.kya-status.miner.desc": "An organization which utilizes its computing power for mining cryptocurrency blocks.", "kgstudio.asset.kya-status.miner.name": "Miner", "kgstudio.asset.kya-status.mixer.desc": "A service for mixing funds from different sources to make tracing them back harder or almost impossible. It is mostly used for money laundering.", "kgstudio.asset.kya-status.mixer.name": "Mixing Service", "kgstudio.asset.kya-status.not-enough-info": "The address does not have sufficient information for risk analysis.", "kgstudio.asset.kya-status.other.desc": "No additional information.", "kgstudio.asset.kya-status.other.name": "Other trusted sources", "kgstudio.asset.kya-status.others.desc": "No additional information.", "kgstudio.asset.kya-status.others.name": "Others", "kgstudio.asset.kya-status.p2p-exchange-licensed.desc": "The entity holds a business license specific to crypto assets, including custody, exchange, brokerage, or any other related financial service. It provides an exchange service where participants exchange directly with each other. It does not include non-specific financial service licenses and jurisdictions listed as non-cooperative with FATF.", "kgstudio.asset.kya-status.p2p-exchange-licensed.name": "P2P Exchange: Licensed", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.desc": "The entity does not hold any specific business license for crypto assets and provides an exchange service where participants exchange directly. It includes licensed entities but in jurisdictions listed as non-cooperative with FATF.", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.name": "P2P Exchange: Unlicensed", "kgstudio.asset.kya-status.payment.desc": "A service which acts as an intermediary between customers and the company which provides services for making a payment.", "kgstudio.asset.kya-status.payment.name": "Payment Processor", "kgstudio.asset.kya-status.potential-risk": "Potential Risk Of Address", "kgstudio.asset.kya-status.ransom.desc": "Extortioners demanding payment in the form of cryptocurrency.", "kgstudio.asset.kya-status.ransom.name": "<PERSON><PERSON><PERSON>tioner", "kgstudio.asset.kya-status.sanctions.desc": "No additional information.", "kgstudio.asset.kya-status.sanctions.name": "Sanctions", "kgstudio.asset.kya-status.scam.desc": "Entities that have scammed their customers and taken possession of their cryptocurrency.", "kgstudio.asset.kya-status.scam.name": "Scam", "kgstudio.asset.kya-status.seized-assets.desc": "No additional information.", "kgstudio.asset.kya-status.seized-assets.name": "Seized Assets", "kgstudio.asset.kya-status.source.suspicious": "Suspicious Sources", "kgstudio.asset.kya-status.source.trusted": "Trusted Sources", "kgstudio.asset.kya-status.stolen-coins.desc": "The entities which have taken possession of someone else’s cryptocurrency by hacking.", "kgstudio.asset.kya-status.stolen-coins.name": "Stolen Coins", "kgstudio.asset.kya-status.terrorist-financing.desc": "No additional information.", "kgstudio.asset.kya-status.terrorist-financing.name": "Terrorist Financing", "kgstudio.asset.kya-status.view-potential-risk-details": "View Potential Risk Details", "kgstudio.asset.kya-status.wallet-address-risk": "Wallet Address Risk", "kgstudio.asset.kya-status.wallet.desc": "A service for storage and making payments with cryptocurrency.", "kgstudio.asset.kya-status.wallet.name": "Online Wallet", "kgstudio.asset.kya-status.wallet-risk-checkin": "Analyzing wallet risk...", "kgstudio.asset.kyc-status.scan": "<PERSON>", "kgstudio.asset.kyc-status.scan-address": "Address Risk", "kgstudio.asset.kyc-status.subtitle": "Please check the user information before you transfer.", "kgstudio.asset.market-profile.email": "E-mail", "kgstudio.asset.market-profile.intro": "Introduction", "kgstudio.asset.market-profile.intro-tooltip": "Shown at the top of the store.", "kgstudio.asset.market-profile.line-id": "LINE ID", "kgstudio.asset.market-profile.logo": "Logo", "kgstudio.asset.market-profile.phone": "Phone", "kgstudio.asset.market-profile.section-title": "Market Profile", "kgstudio.asset.market-profile.store-link": "Preview", "kgstudio.asset.market-profile.title": "Title", "kgstudio.asset.market-profile.title-tooltip": "Displayed in website title.", "kgstudio.asset.market-profile.url": "URL", "kgstudio.asset.market-profile.url-tooltip": "To customize your domain, please contact support.", "kgstudio.asset.mismatch-error": "This user hasn’t imported wallets from the selected chain ({selectedBlockchain}). They may not see the money you send to them.", "kgstudio.asset.note-attachments-required": "Enter the transaction note and upload necessary attachments for approval. If you want to send without approval, please lower the amount or edit your threshold.", "kgstudio.asset.order-detail.action-card.remain-balance": "Remaining balance today : {balance} (Daily Transfer Limit: {limit})", "kgstudio.asset.order-detail.action-card.tx-failed": "Transaction {txHash} send failed. Please try again. ({shippedAt})", "kgstudio.asset.order-detail.actions.awaiting-confirmation": "The customer has notified us of the payment, please review it as soon as possible.", "kgstudio.asset.order-detail.actions.cancel-order": "Cancel Order", "kgstudio.asset.order-detail.actions.confirmation-hint": "To prevent money laundry, please verify that the user's registered bank account matches the remittance account to ensure it's their transaction. If not, you may cancel and refund.", "kgstudio.asset.order-detail.actions.mark-as-paid": "<PERSON> as <PERSON><PERSON>", "kgstudio.asset.order-detail.actions.order-cancelled": "The order has been canceled.", "kgstudio.asset.order-detail.actions.order-done": "The order has been completed.", "kgstudio.asset.order-detail.actions.order-shipping": "The asset is currently in transit. Please check the real-time shipping progress through Shipping Proof.", "kgstudio.asset.order-detail.actions.payment-deadline": "The customer should pay before {deadline}, otherwise you can cancel this order.\n\nBefore the customer makes payment, if you do not accept the transaction, you can cancel the order without processing a refund.", "kgstudio.asset.order-detail.actions.title": "Actions", "kgstudio.asset.order-detail.cancel-modal.cancel": "Cancel this order", "kgstudio.asset.order-detail.cancel-modal.description": "Please fill in the internal note with the reason. The user has completed the payment. You need to process the refund as soon as possible after canceling this order.", "kgstudio.asset.order-detail.cancel-modal.title": "Are you sure you want to cancel this order?", "kgstudio.asset.order-detail.edit-internal-note": "Edit Internal Note", "kgstudio.asset.order-detail.order-details": "Order Details", "kgstudio.asset.order-detail.order-information": "Order Information", "kgstudio.asset.order-detail.order-information.customer": "Customer", "kgstudio.asset.order-detail.order-information.order-time": "Order Time", "kgstudio.asset.order-detail.order-information.payment-details": "Payment Details", "kgstudio.asset.order-detail.order-information.product": "Product", "kgstudio.asset.order-detail.order-information.qty": "Quantity", "kgstudio.asset.order-detail.order-information.title": "Order Information", "kgstudio.asset.order-detail.order-information.total-price": "Total Price", "kgstudio.asset.order-detail.order-information.tx-id": "Order ID", "kgstudio.asset.order-detail.payment-details.account-info": "Bank Name: {bankName}\nBranch Name: {branchName}\nAccount Number: {accountNumber}\nAccount Holder Name: {accountHolderName}", "kgstudio.asset.order-detail.payment-details.account-info-less-content": "{bankName} {branchName}\nAccount Number: {accountNumber}\nAccount Holder Name:{accountHolderName}", "kgstudio.asset.order-detail.payment-details.confirmed-as-paid": "This order has been confirmed as paid.", "kgstudio.asset.order-detail.payment-details.customer-account": "User's registered personal bank account information (see user KYC details for more):", "kgstudio.asset.order-detail.payment-details.info-from-customer": "Payment Info from Customer", "kgstudio.asset.order-detail.payment-details.last-five-digits": "Last five digits of actual payment account number", "kgstudio.asset.order-detail.payment-details.title": "Payment Details", "kgstudio.asset.order-detail.payment-details.unpaid": "The customer has not yet notified the payment.", "kgstudio.asset.order-detail.payment-details.unpaid-time-reminder": "The payment deadline is {deadline}, and there are still {timeLeft} left before the payment deadline.", "kgstudio.asset.order-detail.summary.internal-note": "Internal Note", "kgstudio.asset.order-detail.summary.internal-note-hint": "Fill in details such as order processing status, reasons for order cancellation, refund information, and progress.", "kgstudio.asset.order-detail.summary.payment-status": "Payment Status", "kgstudio.asset.order-detail.summary.process-by": "Processed by", "kgstudio.asset.order-detail.summary.shipment-status": "Shipment Status", "kgstudio.asset.order-detail.summary.shipping-proof": "Shipping Proof", "kgstudio.asset.order-detail.summary.title": "Summary", "kgstudio.asset.order-settings.payment-terms": "Payment Terms", "kgstudio.asset.order-settings.payment-terms-tooltip": "A payment deadline reminder will be automatically displayed after the order is established. You still need to cancel the expired order yourself.", "kgstudio.asset.order-settings.section-title": "Order Settings", "kgstudio.asset.orders.just now": "just now", "kgstudio.asset.orders.just-now": "just now", "kgstudio.asset.orders.list.customer": "Customer", "kgstudio.asset.orders.list.order-created": "Order Created", "kgstudio.asset.orders.list.order-id": "Order ID", "kgstudio.asset.orders.list.order-purchase": "Purchase", "kgstudio.asset.orders.list.order-status": "Order Status", "kgstudio.asset.orders.list.payment": "Payment", "kgstudio.asset.orders.list.shipment": "Shipment", "kgstudio.asset.orders.list.total-price": "Total Price", "kgstudio.asset.orders.search.placeholder": "Order ID, Customer", "kgstudio.asset.orders.steps.awaiting-payment": "Awaiting Payment", "kgstudio.asset.orders.steps.awaiting-shipment": "Awaiting Shipment", "kgstudio.asset.orders.steps.order-cancelled": "Order Cancelled", "kgstudio.asset.orders.steps.order-completed": "Order Completed", "kgstudio.asset.orders.steps.order-created": "Order Created", "kgstudio.asset.orders.steps.paid": "Paid", "kgstudio.asset.orders.steps.sent": "<PERSON><PERSON>", "kgstudio.asset.orders.steps.shipping": "Shipping", "kgstudio.asset.order-status.awaiting-confirmation": "Awaiting Confirmation", "kgstudio.asset.order-status.awaiting-shipment": "Awaiting Shipment", "kgstudio.asset.order-status.cancelled": "Cancelled", "kgstudio.asset.order-status.delivered": "Delivered", "kgstudio.asset.order-status.shipping": "Shipping", "kgstudio.asset.order-status.unpaid": "Unpaid", "kgstudio.asset.orders.title": "Orders", "kgstudio.asset.payment-info.account-holder-name": "Account Holder's Name", "kgstudio.asset.payment-info.account-number": "Account Number", "kgstudio.asset.payment-info.bank-name": "Bank Name", "kgstudio.asset.payment-info.bank-transfer": "Bank Transfer", "kgstudio.asset.payment-info.branch-name": "Branch Name", "kgstudio.asset.payment-info.currency": "Payment Currency", "kgstudio.asset.payment-info.payment-method": "Payment Method", "kgstudio.asset.payment-info.section-title": "Payment Information", "kgstudio.asset.payment-status.awaiting-refund": "Awaiting Refund", "kgstudio.asset.payment-status.paid": "Paid", "kgstudio.asset.payment-status.refunded": "Refunded", "kgstudio.asset.payment-status.unpaid": "Unpaid", "kgstudio.asset.products.action": "Action", "kgstudio.asset.products.available": "Available", "kgstudio.asset.products.cancel-modal-cancel": "Yes, <PERSON>cel", "kgstudio.asset.products.cancel-modal-hint": "Are you sure you want to cancel? The data you just edited will not be saved", "kgstudio.asset.products.cancel-modal-stay": "No, Stay", "kgstudio.asset.products.edit-product": "Edit Product", "kgstudio.asset.products.fee": "Fee", "kgstudio.asset.products.fee-free-reminder-description": "E.g.: Spend 10,000 {quoteCurrency} will receive (10000/{currentPrice}) = {quoteAmount} {baseCurrency} ({chain})", "kgstudio.asset.products.fee-free-reminder-title": "Total price = Price × Token Amount", "kgstudio.asset.products.fee-included-reminder-description": "E.g.: Spend 10,000 {quoteCurrency} will receive (10000/{quoteDenominator}/{currentPrice}) = {quoteAmount} {baseCurrency} ({chain})", "kgstudio.asset.products.fee-included-reminder-title": "Total price = (1 + “Handling Fee”) × Price × Token Amount", "kgstudio.asset.products.fee-included-reminder-with-min-fee-description": "E.g.: Spend 10,000 {quoteCurrency} will reveive (10000-{minFee})/{currentPrice} = {quoteAmount} {baseCurrency} ({chain})", "kgstudio.asset.products.fee-included-reminder-with-min-fee-title": "Total price = Minimum Fee+ Price × Token Amount", "kgstudio.asset.products.handling-fee": "Handling Fee", "kgstudio.asset.products.handling-fee-hint": "min: 0%, max: 100%", "kgstudio.asset.products.handling-fee-no": "No Handling Fee", "kgstudio.asset.products.handling-fee-proportional": "Handling Fee (Added to Price)", "kgstudio.asset.products.handling-fee-yes": "<PERSON><PERSON>e", "kgstudio.asset.products.image-size": "Size: 200*200 px", "kgstudio.asset.products.inventory-greater-than-zero": "Display inventory should be greater than zero.", "kgstudio.asset.products.inventory-less": "Display inventory should be less than 1,000,000,000,000.", "kgstudio.asset.products.limit-from-required": "Order Limit is required", "kgstudio.asset.products.limit-to-required": "Order Limit is required", "kgstudio.asset.products.limit-validation": "Order Limit range should be valid numbers", "kgstudio.asset.products.minimum-fee": "Minimum Fee", "kgstudio.asset.products.name-required": "Product Name is required", "kgstudio.asset.products.not-ready-for-publish-description": "Please complete editing the product settings before publishing it", "kgstudio.asset.products.not-ready-for-publish-title": "The product information has not been set so it cannot be published.", "kgstudio.asset.products.order-limit": "Order Limit", "kgstudio.asset.products.order-limits": "Order Limits", "kgstudio.asset.products.price": "Price", "kgstudio.asset.products.price-required": "Product Price is required", "kgstudio.asset.products.product-name": "Product Name", "kgstudio.asset.products.product-type": "Product Type", "kgstudio.asset.products.product-type-buy-crypto": "Buy Crypto", "kgstudio.asset.products.publish": "Publish", "kgstudio.asset.products.reset-all": "Rest<PERSON>", "kgstudio.asset.products.reset-all-title": "Restore information to previous settings", "kgstudio.asset.products.reset-image": "Reset Image", "kgstudio.asset.products.reset-to-default": "Reset to De<PERSON>ult", "kgstudio.asset.products.status-published": "Published", "kgstudio.asset.products.status-unpublished": "Unpublished", "kgstudio.asset.products.stock": "Display Inventory", "kgstudio.asset.products.stock-hint": "Available inventory in AssetPro Treasury: {tokenAmount}", "kgstudio.asset.products.trading-pair": "Trading Pair", "kgstudio.asset.products.updated": "Updated", "kgstudio.asset.products.update-failure-toast": "Failed to update “{baseCurrency}/{quoteCurrency} ({chain})” . Please try again. (Error Code: {code})", "kgstudio.asset.products.update-success-toast": "“{baseCurrency}/{quoteCurrency} ({chain})” successfully updated.", "kgstudio.asset.recipient-address": "Recipient (Wallet Address)", "kgstudio.asset.recipient-send-by": "Recipient (Send By {sendType})", "kgstudio.asset.remain-limit-checking": "Remaining daily transfer limit: Checking...", "kgstudio.asset.remain-limit-info": "Remaining daily transfer limit: ${remainLimit} of ${dailyTransferLimit}.", "kgstudio.asset.remain-limit-info-invalid": "You are not able to submit any transaction tody, or contact Admins to adjust your transafer limit. ", "kgstudio.asset.remain-limit-invalid-hint": "Adjust amount or contact Admins to adjust your transfer limit. ", "kgstudio.asset.threshold-info": "This transaction requires approval (approval threshold: below ${transferApprovalThreshold}).", "kgstudio.asset.token-and-gas-insufficient": "Insufficient token and gas token balance.", "kgstudio.asset.token-insufficient": "Insufficient token balance.", "kgstudio.asset.transfer-amount": "Transfer Amount", "kgstudio.asset.transfer-to": "Transfer To", "kgstudio.asset.transfer-validation": "Fix the issues before you submit:", "kgstudio.asset.transfer-validation-amount-invalid": "The transfer token amount is invalid.", "kgstudio.asset.transfer-validation-info-invalid": "Transaction Note and Attachment are necessary for approval", "kgstudio.asset.transfer-validation-recipient-invalid": "Recipient is required. Click \"Risk Scan\" before sending any transaction.", "kgstudio.asset.tx-action-card.approve": "Approve", "kgstudio.asset.tx-action-card.awaiting-approval.msg-approver": "Please review the transaction details and make a decision: approve or reject the transaction.", "kgstudio.asset.tx-action-card.awaiting-approval.msg-normal": "This transaction is in the queue for approval. If you'd like to accelerate the process, consider getting in touch with the approvers.", "kgstudio.asset.tx-action-card.awaiting-release.msg-finance-manager": "Please carefully consider the transaction details and proceed to release the transaction for execution or reject it.", "kgstudio.asset.tx-action-card.awaiting-release.msg-normal": "This transaction is in the queue for release. If you'd like to accelerate the process, consider getting in touch with the finance managers.", "kgstudio.asset.tx-action-card.reject": "Reject", "kgstudio.asset.tx-action-card.rejected.review-note.title": "Review note", "kgstudio.asset.tx-action-card.rejected.title": "This transaction has been rejected ❌", "kgstudio.asset.tx-action-card.release": "Release", "kgstudio.asset.tx-action-card.release-check-list.balance-enough": "Available balance: sufficient ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.balance-loading": "Checking remaining balance of Treasury", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.desc": "Please top-up at least {shortfallAmount} {tokenSymbol}({chainName}) before Approver approves this transaction.", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.title": "Available balance: Insufficient ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.fee-enough": "Est. transfer gas fee: {fee} {tokenSymbol}", "kgstudio.asset.tx-action-card.release-check-list.fee-loading": "Checking remaining native token of Treasury", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.desc": "Please top-up at least {shortfallAmount} {tokenSymbol}({chainName}) before Approver approves this transaction.", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.title": "Available native token balance (for gas fee payment): Insufficient. ", "kgstudio.asset.tx-action-card.send-failed.msg": "For more details, click the button below to be redirected to the blockchain explorer website.", "kgstudio.asset.tx-action-card.send-failed.title": "Transaction failed ❌", "kgstudio.asset.tx-action-card.sending.msg": "This transaction has been released and is being sent. Please note that it may take longer than expected if the blockchain is congested.\n\nFor more details, click the button below to be redirected to the blockchain explorer website.", "kgstudio.asset.tx-action-card.send-success.msg": "For more details, click the button below to be redirected to the blockchain explorer website.", "kgstudio.asset.tx-action-card.send-success.title": "This transaction is completed ✅", "kgstudio.asset.tx-action-card.title": "Action", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.desc": "Please retry after the Admin has deposited sufficient {shortfallAmount} {tokenSymbol} ({chainName})", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.title": "Error: Insufficient {tokenSymbol} ({chainName}) inventory", "kgstudio.asset.tx-approval-modal.success.title": "Successfully approved", "kgstudio.asset.tx-confirm-approval-modal.approve": "Confirm Approval", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.description": "Please retry after the administrator has deposited sufficient ${amount} ${tokenSymbol} (${chainName})", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.title": "Error: Insufficient ${tokenSymbol} (${chainName}) inventory", "kgstudio.asset.tx-confirm-approval-modal.send-token": "Send Token", "kgstudio.asset.tx-confirm-approval-modal.title": "Confirm Approval", "kgstudio.asset.tx-confirm-approval-modal.warning.description": "Please ensure you have carefully reviewed the transaction details and verified its legitimacy.", "kgstudio.asset.tx-confirm-approval-modal.warning.title": "Are you sure you want to mark this transaction as approved?", "kgstudio.asset.tx-confirm-release-modal.release": "Confirm Release", "kgstudio.asset.tx-confirm-release-modal.warning.description": "Please ensure you have carefully reviewed the transaction details and verified its legitimacy.", "kgstudio.asset.tx-confirm-release-modal.warning.title": "Are you sure you want to release this transaction?", "kgstudio.asset.tx-conform-release-modal.title": "Confirm Release", "kgstudio.asset.tx-detail.not-exist": "This order does not exist in this organization ({orgName}). Please check the corresponding organization instead.", "kgstudio.asset.tx-detail.steps.awaiting-approval": "Awaiting <PERSON><PERSON><PERSON><PERSON>", "kgstudio.asset.tx-detail.steps.awaiting-release": "Awaiting Release", "kgstudio.asset.tx-detail.steps.rejected": "Rejected", "kgstudio.asset.tx-detail.steps.send-failed": "Send Failed", "kgstudio.asset.tx-detail.steps.sending": "Sending", "kgstudio.asset.tx-detail.steps.send-success": "Send Success", "kgstudio.asset.tx-detail.steps.submitted": "Submitted", "kgstudio.asset.tx-detail.tx-info-card.tx-id": "Transaction ID", "kgstudio.asset.tx-error-generic": "An error occurred", "kgstudio.asset.tx-error-processing": "An error occurred while processing the request. Please try again later.", "kgstudio.asset.tx-failed": "Failed to send. Please try again later. ", "kgstudio.asset.tx-history.approved": "{name} approved", "kgstudio.asset.tx-history.approved-by": "Approved by", "kgstudio.asset.tx-history-card.approver": "Approver", "kgstudio.asset.tx-history-card.finance-manager": "Finance Manager", "kgstudio.asset.tx-history-card.submitted-by": "Submitted by", "kgstudio.asset.tx-history-card.title": "History", "kgstudio.asset.tx-history-card.tx-hashes": "Transaction Hash", "kgstudio.asset.tx-history.latest-update": "Latest Update", "kgstudio.asset.tx-history.rejected": "{name} rejected", "kgstudio.asset.tx-history.rejected-by": "Rejected by", "kgstudio.asset.tx-history.released": "{name} released", "kgstudio.asset.tx-history.released-by": "Released by", "kgstudio.asset.tx-history.submitted": "{name} submitted", "kgstudio.asset.tx-info-card.attachments": "Attachments", "kgstudio.asset.tx-info-card.blockchain": "Blockchain", "kgstudio.asset.tx-info-card.recipient": "Recipient", "kgstudio.asset.tx-info-card.send-token": "Send Token", "kgstudio.asset.tx-info-card.title": "Transaction Information", "kgstudio.asset.tx-info-card.tx-note": "Transaction Note", "kgstudio.asset.tx-info-card.tx-status": "Transaction Status", "kgstudio.asset.tx-insufficient-balance": "Balance not enough", "kgstudio.asset.tx-insufficient-balance-admin-recharge": "Please have the administrator deposit at least {insufficientAmount} {tokenName} ({chainName}) to Treasury and retry", "kgstudio.asset.tx-limit-exceeded": "Transfer limit exceeded", "kgstudio.asset.tx-limit-exceeded-contact-admin": "Transfer limit exceeded, please contact the administrator for assistance", "kgstudio.asset.tx-need-approval": "This transaction will be sent only after approval.", "kgstudio.asset.tx-need-approval-hint": "Please make sure that your transaction details are correct, as they cannot be modified after submission.", "kgstudio.asset.tx-rejection-modal.confirm-rejection": "Confirm Rejection", "kgstudio.asset.tx-rejection-modal.review-note.hint": "Rejection reason", "kgstudio.asset.tx-rejection-modal.review-note.required-error": "Please provide a reason for rejection", "kgstudio.asset.tx-rejection-modal.review-note.title": "Review note", "kgstudio.asset.tx-rejection-modal.success.title": "Successfully rejected", "kgstudio.asset.tx-rejection-modal.title": "Confirm Rejection", "kgstudio.asset.tx-rejection-modal.warning.desc": "Please ensure you have carefully reviewed the transaction details and provided a reason for rejection.", "kgstudio.asset.tx-rejection-modal.warning.title": "Are you sure you want to reject this transaction?", "kgstudio.asset.understand": "I Understand", "kgstudio.asset.wallet-risk-check": "Wallet Risk Check", "kgstudio.audience.compliance": "Compliance", "kgstudio.audience.country": "Country", "kgstudio.audience.email": "Email", "kgstudio.audience.kyc_status": "KYC", "kgstudio.audience.name": "Name", "kgstudio.audience.nft_projects": "NFT Projects", "kgstudio.audience.phone": "Phone", "kgstudio.audience.query-placeholder": "Name, Email or Phone", "kgstudio.audience.wallet_id": "Wallet ID: ", "kgstudio.auth.login.accept-crypto-for": "Accept crypto for", "kgstudio.auth.login.continue-with-email": "Continue with <PERSON>ail", "kgstudio.auth.login.module.integration-options.description": "Offers no-code solutions, SDK integration, and convenient API queries for various technical needs.", "kgstudio.auth.login.module.integration-options.title": "Flexible Integration（No-code, SDK, API）", "kgstudio.auth.login.module.low-fees.description": "Minimal fees that let creators keep more of their earnings.", "kgstudio.auth.login.module.low-fees.title": "Low Fees", "kgstudio.auth.login.module.no-kyc.description": "No KYC verification or business registration required. Start selling immediately.", "kgstudio.auth.login.module.no-kyc.title": "No Restrictions", "kgstudio.auth.login.module.payment-options.description": "Supports top-ups with credit card or direct wallet payments to meet various transaction needs.", "kgstudio.auth.login.module.payment-options.title": "Multiple Payment Options", "kgstudio.auth.login.rotator.artwork": "🎨 art work", "kgstudio.auth.login.rotator.business": "💼 business", "kgstudio.auth.login.rotator.community": "👥 community", "kgstudio.auth.login.rotator.content": "📝 content creation", "kgstudio.auth.login.rotator.digital-goods": "🎁 digital goods", "kgstudio.auth.login.rotator.website": "💻 website", "kgstudio.check.change_acc_apologize": "Sorry, you can't join this team.", "kgstudio.check.change_acc_desc": "Your current login email is {currentEmail}, please use the invited email instead: {inviteEmail} to log in.", "kgstudio.check.change_acc_link": "Logout", "kgstudio.check.change_acc_title": "Please change your login account to join the team.", "kgstudio.check.change_acc_toast": "Logged out of current account. You'll be redirected to the new account.", "kgstudio.check.invalid_desc": "Please check the URL or contact your organization administrator.", "kgstudio.check.invalid_link": "Back to homepage", "kgstudio.check.invalid_title": "Sorry, this link has expired or the page cannot be found", "kgstudio.check.invitation-accepted-cta": "<PERSON><PERSON>", "kgstudio.check.invitation-accepted-desc": "Log in and start your journey", "kgstudio.check.invitation-accepted-title": "Congratulations. You have successfully registered.", "kgstudio.check.loading_desc": "We are checking authorization...", "kgstudio.check.loading_title": "Please wait and do not leave or close this page.", "kgstudio.common.accept": "Accept", "kgstudio.common.account_setting": "Account <PERSON>ting", "kgstudio.common.add-fund": "Add Fund", "kgstudio.common.address": "Address", "kgstudio.common.address-copied": "Address copied!", "kgstudio.common.all-tasks": "Task Details", "kgstudio.common.app-notification": "App Notification", "kgstudio.common.app-publish": "App Publish", "kgstudio.common.approved": "Approved", "kgstudio.common.asset": "AssetPro", "kgstudio.common.assets": "Assets", "kgstudio.common.attachments": "Attachments", "kgstudio.common.back": "Back", "kgstudio.common.back-to-login": "Back to login", "kgstudio.common.billing": "Billing", "kgstudio.common.blockchain": "Blockchain", "kgstudio.common.cancel": "Cancel", "kgstudio.common.case-management": "Case Management", "kgstudio.common.cdd-tasks": "CDD Task", "kgstudio.common.change": "Change", "kgstudio.common.clear-filter": "Clear", "kgstudio.common.close": "Close", "kgstudio.common.community-links": "Community Links", "kgstudio.common.complete": "Done", "kgstudio.common.compliance": "Compliance", "kgstudio.common.configuration": "Wallet Settings", "kgstudio.common.confirm-publish": "Confirm Publish", "kgstudio.common.create": "Create", "kgstudio.common.create-a-task": "Create a Task", "kgstudio.common.created": "Created", "kgstudio.common.created_at": "Created At", "kgstudio.common.dapp-list": "DApp list", "kgstudio.common.data.analysis": "Data", "kgstudio.common.data.asset-pro": "AssetPro", "kgstudio.common.data.compliance": "Compliance", "kgstudio.common.data.nft-boost": "NFT Activity", "kgstudio.common.data.wallet": "Wallet", "kgstudio.common.description": "Description", "kgstudio.common.discord": "Discord", "kgstudio.common.edit": "Edit", "kgstudio.common.editor": "Editor", "kgstudio.common.engage": "Engage (coming soon)", "kgstudio.common.error": "Something went wrong. Please try again later or contact our support team for assistance.", "kgstudio.common.expired": "Expired", "kgstudio.common.explorer-banner": "Explorer Banner", "kgstudio.common.export-private-key": "Export Private Key", "kgstudio.common.finance": "Finance", "kgstudio.common.general": "General", "kgstudio.common.get-started": "Get Started", "kgstudio.common.idv-tasks": "IDV Task", "kgstudio.common.image": "Image", "kgstudio.common.in-app-message": "In-app Message", "kgstudio.common.insufficient_not_refunded": "Insufficient (Not Refunded)", "kgstudio.common.insufficient_refunded": "Insufficient (Refunded)", "kgstudio.common.invoice-pro": "Invoice (PRO)", "kgstudio.common.kyc-form": "KYC Form", "kgstudio.common.kyt-tasks": "KYA Task", "kgstudio.common.language.english": "English", "kgstudio.common.language.japanese": "日本語", "kgstudio.common.languages": "Languages", "kgstudio.common.language.simplified-chinese": "中文（简体）", "kgstudio.common.language.spanish": "Español", "kgstudio.common.language.traditional-chinese": "中文（繁體）", "kgstudio.common.language.vietnamese": "Tiếng <PERSON>", "kgstudio.common.last-edited-time": "Last Edited Time", "kgstudio.common.line-id": "LINE ID", "kgstudio.common.liquidity": "Liquidity", "kgstudio.common.logout": "Logout", "kgstudio.common.marketing-tools": "Marketing Tools", "kgstudio.common.market-settings": "Market Settings", "kgstudio.common.maxInputHint": "Max characters: 1,000", "kgstudio.common.member-id": "Member ID", "kgstudio.common.members": "Team Members", "kgstudio.common.minimum": "Minimum", "kgstudio.common.my-role": "My Role(s)", "kgstudio.common.my-shop": "My Shop", "kgstudio.common.next": "Next", "kgstudio.common.next-step": "Next Step", "kgstudio.common.nft-airdrop": "NFT Airdrop", "kgstudio.common.nft-boost": "NFT Boost", "kgstudio.common.note-and-attachments": "Note & Attachments", "kgstudio.common.notification": "Notification", "kgstudio.common.operators": "Operators", "kgstudio.common.optional": "Optional", "kgstudio.common.orders": "Orders", "kgstudio.common.organization-id-required": "Organization ID is required", "kgstudio.common.overview": "Overview", "kgstudio.common.page-desc": "Show rows per page", "kgstudio.common.payment-address": "Payment Address", "kgstudio.common.pending": "Pending", "kgstudio.common.prev-step": "Previous Step", "kgstudio.common.processing": "Loading...", "kgstudio.common.products": "Product", "kgstudio.common.profile": "Profile", "kgstudio.common.project": "Project", "kgstudio.common.project-updated": "Project Info Updated!", "kgstudio.common.push-notification": "Push Notification", "kgstudio.common.recaptcha-error": "Failed to verify Google reCAPTCHA. Please refresh web page or contact our support team for assistance.", "kgstudio.common.recipient": "Recipient", "kgstudio.common.reject": "Reject", "kgstudio.common.rejected": "Rejected", "kgstudio.common.reset": "Reset", "kgstudio.common.revenue": "Finance", "kgstudio.common.revert": "Reject and Cancel", "kgstudio.common.review": "Review Center", "kgstudio.common.roles": "Roles", "kgstudio.common.save": "Save", "kgstudio.common.save-changes": "Changes saved!", "kgstudio.common.save-draft": "Save Draft", "kgstudio.common.search": "Search", "kgstudio.common.send": "Send", "kgstudio.common.send-now": "Send Now", "kgstudio.common.send-to": "Send To", "kgstudio.common.send-token": "Send Token", "kgstudio.common.settings": "Settings", "kgstudio.common.start": "Start", "kgstudio.common.submit": "Submit", "kgstudio.common.submit-request": "Submit", "kgstudio.common.success": "Success", "kgstudio.common.successfully-copied": "Successfully copied!", "kgstudio.common.supported-chains": "Supported Blockchain(s)", "kgstudio.common.system-settings": "System Settings", "kgstudio.common.telegram": "Telegram", "kgstudio.common.top-up": "Top up", "kgstudio.common.total": "Total", "kgstudio.common.transaction-status": "Transaction Status", "kgstudio.common.transfer": "Transfer", "kgstudio.common.treasury": "Treasury", "kgstudio.common.twitter": "Twitter", "kgstudio.common.tx-hash": "Transaction Hash", "kgstudio.common.type": "Type", "kgstudio.common.type-some-description": "Type some description here.", "kgstudio.common.unset": "unset", "kgstudio.common.update": "Update", "kgstudio.common.updated_at": "Updated At", "kgstudio.common.update-time": "Update Time", "kgstudio.common.uploading": "Uploading...", "kgstudio.common.user": "User Settings", "kgstudio.common.user360": "User 360", "kgstudio.common.user-management": "All Users", "kgstudio.common.user-not-found": "Studio user not found. Please contact your organization administrator.", "kgstudio.common.users": "Users", "kgstudio.common.wallet": "Wallet Builder", "kgstudio.compliance.cdd-tasks": "CDD Tasks", "kgstudio.compliance.idv-tasks": "IDV Tasks", "kgstudio.compliance.kyt-tasks": "KYA Tasks", "kgstudio.compliance.title": "Compliance", "kgstudio.data.actions-section": "Actions", "kgstudio.data.active-users": "Active Users", "kgstudio.data.ai-generate": "AI Generate!", "kgstudio.data.ai-recommend": "AI Recommended", "kgstudio.data.all": "All", "kgstudio.data.app-store-info-language": "App Store Information Language", "kgstudio.data.asset-pro.date.all": "All", "kgstudio.data.asset-pro.date.last-14-days": "Last 14 Days", "kgstudio.data.asset-pro.date.last-30-days": "Last 30 Days", "kgstudio.data.asset-pro.date.last-7-days": "Last 7 Days", "kgstudio.data.asset-pro.date.title": "Date", "kgstudio.data.asset-pro.error": "Failed to generate data, please try again later.", "kgstudio.data.asset-pro.loading": "Generating dashboard for AssetPro data...", "kgstudio.data.asset-pro.retry": "Retry", "kgstudio.data.balance": "Balance", "kgstudio.data.balance-greater-than-zero": "Balance > 0", "kgstudio.data.budget-title": "Budget", "kgstudio.data.choose-plan": "Choose <PERSON>", "kgstudio.data.churn-rate": "Churn Rate", "kgstudio.data.churn-users": "Churn Users", "kgstudio.data.compliance.cdd-tasks": "Case Reviews", "kgstudio.data.compliance.form-submission": "Form Submissions", "kgstudio.data.compliance.idv-tasks": "ID Verifications", "kgstudio.data.compliance.kyc-status.title": "KYC Verification", "kgstudio.data.compliance.no-data": "No data available for analysis yet", "kgstudio.data.compliance.pending-review": "Pending Review", "kgstudio.data.compliance.personal-info.title": "Personal Info", "kgstudio.data.compliance.verified-customers": "Verified Customers", "kgstudio.data.compliance.v-wallet-usage.title": "Wallet Activation of Verified Customers", "kgstudio.data.compliance.v-wallet-usage.tooltip": "Wether the KYC-verified users have activated (download & login) their KryptoGO Wallet or not.", "kgstudio.data.create_nft_collection": "Create an NFT Collection", "kgstudio.data.discard": "Discard", "kgstudio.data.engage.compliance": "Compliance", "kgstudio.data.engage.country": "Country", "kgstudio.data.engage-create": "Create Engagement", "kgstudio.data.engage.email": "Email", "kgstudio.data.engage.kyc": "KYC", "kgstudio.data.engage-list": "Engagement Project List", "kgstudio.data.engage.name": "Name", "kgstudio.data.engage.nft-projects": "NFT Projects", "kgstudio.data.engage.phone": "Phone", "kgstudio.data.engage-title": "Engage", "kgstudio.data.engage.wallet-id": "Wallet ID", "kgstudio.data.estimated-gas-fee": "Estimated Gas Fee", "kgstudio.data.estimated-reach": "Estimated Reach", "kgstudio.data.events": "Events", "kgstudio.data.evm-wallet": "EVM Wallet", "kgstudio.data.file_types_supported": "Image supported: JPG, PNG, GIF, SVG. Max size: 10 MB (Renaming the file format won't work. Please use file convert services if you don't have supported image formats.)", "kgstudio.data.header": "Wallet Data", "kgstudio.data.increase-user-retention": "Increase user retention", "kgstudio.data.increase-wallet-user-activity": "Registered User Activation", "kgstudio.data.increase-wallet-user-retention": "Wallet User Retention", "kgstudio.data.invalid": "Please confirm if the information is entered correctly", "kgstudio.data.login": "<PERSON><PERSON>", "kgstudio.data.new-users": "New Users", "kgstudio.data.nft_opensea_banner_title": "NFT OpenSea Banner", "kgstudio.data.notification.1-button": "1 Button", "kgstudio.data.notification.2-buttons": "2 Buttons", "kgstudio.data.notification.body": "Body", "kgstudio.data.notification.buttons": "<PERSON><PERSON> (s)", "kgstudio.data.notification.enter-title": "Enter title", "kgstudio.data.notification.file-types-supported": "Image supported: JPG, PNG, GIF, SVG. Max size: 10 MB (Renaming the file format won't work. Please use file convert services if you don't have supported image formats.)", "kgstudio.data.notification.first-button": "The First Button", "kgstudio.data.notification.image": "Image", "kgstudio.data.notification.no-button": "No Button", "kgstudio.data.notification.recommended-size": "Recommended size : 1400 x 350 px", "kgstudio.data.notification.text-placeholder": "Text", "kgstudio.data.recommended_size": "Recommended size : 600 x 200 px", "kgstudio.data.recommended-target": "Recommended Target", "kgstudio.data.registered-in-7D": "Registered in 7-Day", "kgstudio.data.registered-users": "Registered Users", "kgstudio.data.retention": "Retention", "kgstudio.data.retention-rate": "Retention Rate", "kgstudio.data.select_from_nft_boost": "Select from NFT Boost", "kgstudio.data.select-multiple-actions": "You can set up more than one type of asset/information to your users.", "kgstudio.data.send-currency": "Send Currency", "kgstudio.data.send-nft": "Send NFT", "kgstudio.data.send-notification": "Send Notification", "kgstudio.data.shop-info-language": "Shop Information Language Version", "kgstudio.data.since-last-month": "Since last month", "kgstudio.data.specify-engage-time": "Schedule", "kgstudio.data.target-placeholder": "Your goal, e.g. Increase Retention", "kgstudio.data.target-section": "Target", "kgstudio.data.target-title": "Goal", "kgstudio.data.time-option-custom": "Custom", "kgstudio.data.time-option-immediately": "Immediately", "kgstudio.data.time-section": "Time", "kgstudio.data.top-users": "Top Users", "kgstudio.data.total-balance": "Total Balance", "kgstudio.data.tron-wallet": "Tron Wallet", "kgstudio.data.try-more": "Try More", "kgstudio.data.tx-events": "Transaction Events", "kgstudio.data.upload-branding-assets": "Upload Branding Assets", "kgstudio.data.upload-csv-list": "Upload .csv List", "kgstudio.data.use-assetpro": "Use AssetPro to send cryptocurrencies to your targeted users", "kgstudio.data.use-nft-boost": "Select from NFT Boost's existed NFT items", "kgstudio.data.user": "User", "kgstudio.data.user-activities": "User Activities", "kgstudio.data.users-balance-greater": "User Balance > 0", "kgstudio.data.use-wallet-notification": "Send notification & announcement to your wallet users", "kgstudio.data.wallet": "Wallet", "kgstudio.data.wallet-address": "Wallet Address", "kgstudio.data.wallet-balance-greater": "Wallet Balance > 0", "kgstudio.data.wallets": "Wallets", "kgstudio.data.your-prompt": "Your Prompt", "kgstudio.dna.create-at": "Created on", "kgstudio.dna.token-holdings": "Token Holdings", "kgstudio.engagement.actions-title": "Actions", "kgstudio.engagement.activity": "Activity", "kgstudio.engagement.all": "All", "kgstudio.engagement.asset-balance": "Asset Balance", "kgstudio.engagement.behavior": "Behavior", "kgstudio.engagement.engaged": "Engaged", "kgstudio.engagement.methods": "How to engage?", "kgstudio.engagement.name": "Project Name", "kgstudio.engagement.nft-claim-rate": "NFT Claim Rate", "kgstudio.engagement.notification-visit": "Notification Visit", "kgstudio.engagement.rewards-redeem-rate": "Rewards Redeem Rate", "kgstudio.engagement.send-currency": "Send Currency", "kgstudio.engagement.send-nft": "Send NFT", "kgstudio.engagement.send-notification": "Send Notification", "kgstudio.engagement.target-settings": "Target Settings", "kgstudio.engagement.time": "Time", "kgstudio.engagement.title": "Engagement", "kgstudio.engagement.users": "Users", "kgstudio.error.cant-find-customer": "Sorry, this user has not yet registered for your service. If you would like to transfer funds to this user, please ask them to register your KYC form or Wallet first.", "kgstudio.error.cant-find-user": "Sorry, we can't find this user.", "kgstudio.error.general-error": "Error. Code: ", "kgstudio.error.insufficient-balance": "Insufficient balance. Please add more funds in your organization's treasury pool.", "kgstudio.error.no-access": "You do not have access. Please use a verified wallet address to log in.", "kgstudio.error.no-organization": "Sorry, you haven't joined any organization yet", "kgstudio.error.no-organization-contact": "Please contact your organization administrator, or contact KryptoGO to start your business plan.", "kgstudio.error.out-of-range": "The input value is out of range.", "kgstudio.error.permission-denied.desc": "You do not have permission to access this page.", "kgstudio.error.permission-denied.title": "Permission denied.", "kgstudio.error.please-try-again": "Please try again later", "kgstudio.error.resource-not-found.desc": "The page you are looking for does not exist.", "kgstudio.error.resource-not-found.title": "Resource not found.", "kgstudio.error.something-went-wrong": "Sorry, something went wrong", "kgstudio.error.try-again": "Some error occurred, please try again later.", "kgstudio.error.upload-attachment": "Failed to upload attachment", "kgstudio.error.upload-image-failed": "Failed to upload image", "kgstudio.error.upload-max-files": "Maximum number of files exceeded.", "kgstudio.error.upload-max-file-size": "File size exceeds 10 MB.", "kgstudio.error.upload-unsupported-file": "Unsupported file format.", "kgstudio.error.user-not-found": "User not found.", "kgstudio.home.awaiting-approval-tx.title": "Awaiting Approval Transactions", "kgstudio.home.awaiting-release-tx.title": "Awaiting Release Transactions", "kgstudio.home.duplicate-org-name": "Organization name already exists", "kgstudio.home.edit-organization.icon-hint1": "Supported formats: JPG, PNG, WEBP, SVG.", "kgstudio.home.edit-organization.icon-hint2": "Recommended size: 100 x 100 px", "kgstudio.home.edit-organization.icon-hint3": "Max 10 MB", "kgstudio.home.edit-organization.icon-title": "Icon", "kgstudio.home.edit-organization.name-error1": "Name must be 2-20 characters", "kgstudio.home.edit-organization.name-error2": "Name contains invalid characters (@, #, $, /, *, ...)", "kgstudio.home.edit-organization.name-placeholder": "Organazation", "kgstudio.home.edit-organization.name-title": "Name", "kgstudio.home.edit-organization.success": "Organazation settings updated!", "kgstudio.home.edit-organization-title": "Edit Organization", "kgstudio.home.joyride.step1": "In the account settings page, you can use AI agent to create a crypto payment page or integrate payment functionality into your existing application, suitable for developers.", "kgstudio.home.joyride.step2": "In the product list page, you can create your own products and get a one-page payment link to share with anyone.", "kgstudio.home.joyride.step3": "In the Treasury page, you can view your wallet's current earnings and manage your Treasury pool.", "kgstudio.home.joyride.step4": "You can also click the button here to visit the above pages.", "kgstudio.home.joyride.step5": "Don't have any products yet? Click here to create your first product.", "kgstudio.home.kyc-pending.title": "KYC Pending Review", "kgstudio.home.kyc-pending.tooltip": "the number of KYC applications yet to be reviewed", "kgstudio.home.my-pending-tx.title": "My Pending Transactions", "kgstudio.home.orders-pending.title": "Pending Orders", "kgstudio.home.orders-pending.tooltip": "the number of orders yet to be processed", "kgstudio.home.payment.api-key-settings": "API Key Settings", "kgstudio.home.payment.create-product": "Create Product", "kgstudio.home.payment.data": "Payment Data", "kgstudio.home.payment.manage-wallet": "Manage Wallet", "kgstudio.home.payment.total-order": "Total Order", "kgstudio.home.payment.total-revenue": "Total Revenue", "kgstudio.home.payment.unique-customer": "Unique Customer", "kgstudio.image.upload-required": "Please upload image", "kgstudio.kyc-status.pending": "Pending", "kgstudio.kyc-status.rejected": "Rejected", "kgstudio.kyc-status.transfer-hint.pending": "Please note that the KYC review for this user is still being processed. Please complete the review as soon as possible and trade with caution.", "kgstudio.kyc-status.transfer-hint.rejected": "Please note that this user has failed the KYC review, please trade with caution.", "kgstudio.kyc-status.transfer-hint.unverified": "This user has not yet passed KYC verification, please trade with caution.", "kgstudio.kyc-status.transfer-hint.verified": "This user has passed KYC verification!", "kgstudio.kyc-status.unverified": "Unverified", "kgstudio.kyc-status.verified": "Verified", "kgstudio.login.with-google": "Sign in with Google", "kgstudio.message.input-message-content": "Please enter message content", "kgstudio.nft.airdrop": "Airdrop", "kgstudio.nft.back-to-list": "Back to Campaign List", "kgstudio.nft.balance-and-fee": "Your Polygon wallet balance is {balance} POL, this project will need approximately {createCollectionFee} POL to publish.", "kgstudio.nft.campaign": "Campaign", "kgstudio.nft.claimed": "CLAIMED", "kgstudio.nft.claimed-total": "Claimed / Total", "kgstudio.nft.collection-name-hint": "Please enter alphanumeric characters within 40 characters.", "kgstudio.nft.collection-symbol-hint": "Suggest within 10 letters, typically abbreviations for Collection Name", "kgstudio.nft.confirm-publish": "Confirm Publish", "kgstudio.nft.create-collection-fee": "Creating an NFT Collection requires approximately {createCollectionFee} POL.", "kgstudio.nft.delivery-method-title": "NFT Claiming Method", "kgstudio.nft.edit-button-text": "Edit", "kgstudio.nft.end-date-title": "End Date", "kgstudio.nft.error.collection-name-existed": "Collection name already exists", "kgstudio.nft.error.project-not-found": "Project not found", "kgstudio.nft.favicon-title": "Favicon", "kgstudio.nft.form.banner-file-types": "Image supported: JPG, PNG, GIF, SVG. Max size: 100 MB. (Renaming the file format won't work. Please use file convert services if you don't have supported image formats.)", "kgstudio.nft.form.banner-recommended-size": "Recommended size: 1400 x 350 px.", "kgstudio.nft.form.collection-description": "NFT Description", "kgstudio.nft.form.collection-description-hint": "Up to 1000 characters.", "kgstudio.nft.form.collection-name": "NFT Collection Name", "kgstudio.nft.form.collection-name-hint": "Please enter up to 40 characters of alphanumeric text.", "kgstudio.nft.form.contract-schema-name": "Contract schema name", "kgstudio.nft.form.favicon": "Favicon Image", "kgstudio.nft.form.favicon-image-title": "Favicon Image", "kgstudio.nft.form.max-supply": "How many NFTs are you going to issue?", "kgstudio.nft.form.max-supply-hint": "Publishing this NFT project may require Gas Fee: {mintFee} POL (varies based on the current network activity).", "kgstudio.nft.form.max-supply-label": "NFT Amount", "kgstudio.nft.form.mint-time-customized": "Custom Time", "kgstudio.nft.form.mint-time-end": "End Time", "kgstudio.nft.form.mint-time-end-hint": "<PERSON><PERSON> set a time later than 2038/01/01 00:00 (UTC).", "kgstudio.nft.form.mint-time-end-instant": "Never (end on 2038/01/01)", "kgstudio.nft.form.mint-time-instant": "Auto (NFTs can be claimed immediately)", "kgstudio.nft.form.mint-time-start": "Start Time", "kgstudio.nft.form.nft-image": "NFT Image", "kgstudio.nft.form.nft-opensea-banner": "NFT OpenSea Banner", "kgstudio.nft.form.placeholder.description": "Provide a detailed description of your item.", "kgstudio.nft.form.received-method": "Receiving Method", "kgstudio.nft.form.received-method-address": "Linked Wallet Address", "kgstudio.nft.form.received-method-email": "<PERSON><PERSON>", "kgstudio.nft.form.received-method-phone": "Enter Phone Number", "kgstudio.nft.form.subtitle": "Subtitle", "kgstudio.nft.form.symbol-name": "NFT Symbol Name", "kgstudio.nft.form.symbol-name-hint": "Max: 10 characters. English letters only. Cannot contain space. Often based on the Collection Name, e.g. KGYC", "kgstudio.nft.form.title": "Page Title", "kgstudio.nft.form.upload-ico-desc": "Please upload .ico file", "kgstudio.nft.form.upload-icon-file": "Please upload .ico file", "kgstudio.nft.free-claim": "Claim NFT for Free", "kgstudio.nft.go-to-project": "View Details", "kgstudio.nft.image-collection-item": "NFT Collection Image、NFT Item Image", "kgstudio.nft.image-file-types": "Image supported: JPG, PNG, GIF, SVG. Max size: 10 MB (Renaming the file format won't work. Please use file convert services if you don't have supported image formats.)", "kgstudio.nft.image-recommended-size": "Recommended size: 2000 x 2000 px", "kgstudio.nft.info.contract": "Contract address", "kgstudio.nft.info.creator": "NFT Creator", "kgstudio.nft.info.na": "N/A", "kgstudio.nft.insufficient-balance": "Insufficient balance!", "kgstudio.nft.label.success-sms-preview": "Success SMS Preview", "kgstudio.nft.mint-fee": "Every time when one NFT is claimed, it costs approximately {mintFee} POL (varies based on the current network activity).", "kgstudio.nft.mint-page.na": "No activity link available yet.", "kgstudio.nft.mint-page-name": "NFT Campaign Website", "kgstudio.nft.mint-page.pending": "Generating your campaign website...the campaign link will be available shortly!", "kgstudio.nft.mint-page-title": "NFT Campaign Website", "kgstudio.nft.mint-time": "Mint Time", "kgstudio.nft.modal.edit-mint-page": "Edit NFT Campaign Website", "kgstudio.nft.next-step": "Next Step", "kgstudio.nft.nft-collection": "NFT Collection", "kgstudio.nft.nft-collection-chain": "NFT will be issued on the Polygon chain.", "kgstudio.nft.nft-projects": "NFT Projects", "kgstudio.nft.notification.draft": "Draft Status: Your NFT has not been published yet!", "kgstudio.nft.notification.failed": "An error occurred while publishing your NFT!", "kgstudio.nft.notification.pending": "NFT contract deployment is in progress. Please check back on this page later.", "kgstudio.nft.notification.published": "Your NFT has been successfully published!", "kgstudio.nft.overview": "Overview", "kgstudio.nft.placeholder.success-sms": "Provide a detailed description of your item.", "kgstudio.nft.preview.collection": "NFT Collection Preview", "kgstudio.nft.preview.mint-page": "Preview - NFT Campaign Website", "kgstudio.nft.prev-step": "Previous Step", "kgstudio.nft.processing": "Processing, please wait", "kgstudio.nft.processing-description": "The NFT contract is being deployed, which may take a few minutes. While waiting for the NFT to be published, you can go to the NFT Project page for updates or return to the NFT Project List to continue browsing.", "kgstudio.nft.qr-code-title": "QR Code", "kgstudio.nft.recharge-balance": "You need to add at least {balanceDifference} POL to your wallet to pay the gas fee so you can publish the NFT Project.", "kgstudio.nft.reward": "<PERSON><PERSON>", "kgstudio.nft.save-draft": "Save Draft", "kgstudio.nft.saved-successfully-toast": "Draft for NFT Project {collectionName} has been saved!", "kgstudio.nft.scan-to-visit": "<PERSON>an and go", "kgstudio.nft.start-date-title": "Start Date", "kgstudio.nft.status.claimed": "Claimed", "kgstudio.nft.status.claim-rate": "Claim Rate", "kgstudio.nft.status.draft": "Draft", "kgstudio.nft.status.failed": "Failed", "kgstudio.nft.status.pending": "Processing", "kgstudio.nft.status.published": "Published", "kgstudio.nft.status.total": "Total", "kgstudio.nft.step.check": "Preview and Confirm", "kgstudio.nft.step.collection": "Fill in Basic Information for NFT Collection", "kgstudio.nft.step.mint": "Setup Claiming Details", "kgstudio.nft.subtitle-title": "Subtitle", "kgstudio.nft.success-sms-title": "Text Message Notification", "kgstudio.nft.text.success-sms": "【NFT Get!】Congrats on getting the {collectionName} NFT! Please open the KryptoGO Wallet App and log in using your phone number. (App download link: {appLink})", "kgstudio.nft.title-title": "Title", "kgstudio.nft.total": "TOTAL", "kgstudio.nft.validation.collection-desc": "Please enter NFT Collection description", "kgstudio.nft.validation.collection-name": "Please enter NFT Collection name", "kgstudio.nft.validation.collection-name-max": "NFT Collection name cannot exceed 40 characters", "kgstudio.nft.validation.collection-name-min": "Please enter NFT Collection name", "kgstudio.nft.validation.enter-collection-abbreviation": "Please enter NFT Collection abbreviation", "kgstudio.nft.validation.enter-total-supply": "Please enter NFT Collection total supply", "kgstudio.nft.validation.only-english-and-whitespace": "Only English characters and white spaces are allowed", "kgstudio.nft.wallet-balance": "Your Polygon wallet balance is", "kgstudio.nft.wallet-balance-matic": "POL. Please ensure that your balance is sufficient to cover the Gas Fee for creating and issuing NFTs.", "kgstudio.onboarding.category-arts": "Arts", "kgstudio.onboarding.category-arts-advice": "Music, Poetry, Screenplays, etc.", "kgstudio.onboarding.category-arts-description": "Instant-download kit! cinematic phone camera presets, royalty-free lo-fi loops, shareable poetry overlays, chat stickers, lock-screen wallpapers, and plug-and-play SFX—everything you need to level-up photos, videos, streams, and chats, royalty-free and ready for iOS, Android, or desktop editors.", "kgstudio.onboarding.category-arts-name": "Digital Art Package", "kgstudio.onboarding.category-educational": "Educational", "kgstudio.onboarding.category-educational-advice": "Recipes, Language Learning, Courses, etc.", "kgstudio.onboarding.category-educational-description": "A single download unlocks mini-courses! PDF + video tutorials that teaches you the ultimate cooking, Spanish speaking, investment and Fat burn in 30 days!", "kgstudio.onboarding.category-educational-name": "Learn anything in 30 Days!", "kgstudio.onboarding.category-other": "Other", "kgstudio.onboarding.category-products": "Products", "kgstudio.onboarding.category-products-advice": "Food, Clothes, etc.", "kgstudio.onboarding.category-products-description": "Happy afternoon in 3 minutes! Buttery croissant, crispy cookies & small-batch pour-over coffee Treat yourself (or surprise a friend) with a taste of slow, happy afternoons!", "kgstudio.onboarding.category-products-name": "The Happy Pack", "kgstudio.onboarding.check-store": "Check out My Store", "kgstudio.onboarding.close": "Close", "kgstudio.onboarding.copy-link": "Copy Store Link", "kgstudio.onboarding.create-error": "Failed to create product. Please try again.", "kgstudio.onboarding.currency": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.onboarding.fill-required-fields": "Please fill in all required fields", "kgstudio.onboarding.go": "GO", "kgstudio.onboarding.image": "Image", "kgstudio.onboarding.next": "Next", "kgstudio.onboarding.prev": "Prev", "kgstudio.onboarding.product-description": "Product Description", "kgstudio.onboarding.product-name": "Product Name", "kgstudio.onboarding.product-price": "Product Price", "kgstudio.onboarding.receive-address": "Wallet Address", "kgstudio.onboarding.revenue-over-3000": "More than $3000 USD", "kgstudio.onboarding.revenue-under-3000": "Under $3000 USD", "kgstudio.onboarding.skip": "<PERSON><PERSON>", "kgstudio.onboarding.step1.title": "Start your first sale in 30 seconds ⏳", "kgstudio.onboarding.step2.title": "What kind of store will you create?", "kgstudio.onboarding.step3.title": "What's your expected monthly revenue?", "kgstudio.onboarding.step4.title": "Just a few details to make your store better!", "kgstudio.onboarding.step6.title": "Which crypto address do you want to receive the customer's payment?", "kgstudio.onboarding.step7.title": "Awesome! Let's start your first sell!", "kgstudio.onboarding.step8.description": "Paste the purchase link to your IG, Tiktok, ... anywhere!", "kgstudio.onboarding.step8.title": "📋 Copied to clipboard!", "kgstudio.onboarding.supported-chains": "Supported Chains / Tokens", "kgstudio.operators.approval-threshold-desc": "Transfer requires approval once it reaches this specified amount.", "kgstudio.operators.daily-transfer-limit": "Daily Transfer Limit", "kgstudio.operators.edit-operator": "Edit Operator", "kgstudio.operators.page-title": "Operators", "kgstudio.operators.placeholder": "Search Name, Email, ID", "kgstudio.operators.threshold-amount": "A<PERSON>roval Threshold", "kgstudio.operators.title": "AssetPro Operators", "kgstudio.operators.transfer-approval-threshold": "Transfer Approval Threshold", "kgstudio.operators.transfer-approval-threshold-desc": "Transactions over this threshold require approval before sending.", "kgstudio.operators.transfer-limit-desc": "The maximum daily amount the operator can transfer in AssetPro, resetting daily at 00:00.", "kgstudio.operators.transfer-limit-error": "Threshold amount cannot exceed Daily Transfer Limit", "kgstudio.organization.create.back": "Back", "kgstudio.organization.create.back-to-login": "Back to Login", "kgstudio.organization.create.button": "Create Organization", "kgstudio.organization.create.email-description": "This email will be associated with your account in the organization.", "kgstudio.organization.create.email-placeholder": "Enter your email address", "kgstudio.organization.create.error.failed": "Failed to create organization. Please try again.", "kgstudio.organization.create.error.login-failed": "Failed to login after organization creation. Please try again.", "kgstudio.organization.create.error.missing-token": "Authentication token missing. Please try logging in again.", "kgstudio.organization.create.login-success": "Successfully logged in!", "kgstudio.organization.create.org-name-placeholder": "Enter organization name", "kgstudio.organization.create.subtitle.existing-user": "Create a new organization", "kgstudio.organization.create.subtitle.new-user": "Create your organization to get started", "kgstudio.organization.create.success": "Organization created successfully", "kgstudio.overview.applications": "Applications", "kgstudio.overview.assetpro-intro": "Light and secured crypto asset management system.", "kgstudio.overview.compliance-intro": "Global compliance that adapts to changes in regional regulations.", "kgstudio.overview.create-date": "Create Date", "kgstudio.overview.nft-intro": "Easily create NFT campaigns without any code.", "kgstudio.overview.no-access": "It seems you don't have permission for this module. Please contact your system owner to help you activate it if you want to use it.", "kgstudio.overview.user360-intro": "Your Web3 command center.", "kgstudio.overview.wallet-intro": "User-centric, brand-exclusive wallet.", "kgstudio.page.input-page-subtitle": "Please enter page subtitle", "kgstudio.page.input-page-title": "Please enter page title", "kgstudio.page.page-size-description": "Showing {pageSize} rows per page", "kgstudio.payment.accent-color": "Accent Color", "kgstudio.payment.add-field": "Add Field", "kgstudio.payment.aggregated-amount": "Aggregated Amount", "kgstudio.payment.all-clients": "All Clients", "kgstudio.payment.all": "All", "kgstudio.payment.amount": "Amount", "kgstudio.payment.approve": "Approve", "kgstudio.payment.attachment": "Attachment", "kgstudio.payment.awaiting-approval": "Awaiting <PERSON><PERSON><PERSON><PERSON>", "kgstudio.payment.batch-id": "Batch ID", "kgstudio.payment.button-preview": "Button Preview", "kgstudio.payment.callback-dashboard": "Callback Dashboard", "kgstudio.payment.callback-details": "Callback Details", "kgstudio.payment.callback-id": "Callback ID", "kgstudio.payment.callback-payload": "Callback Payload", "kgstudio.payment.callback-result": "Callback Result", "kgstudio.payment.callback-status": "Callback Status", "kgstudio.payment.callback-type": "Callback Type", "kgstudio.payment.callback-url": "Callback URL", "kgstudio.payment.callback-url-placeholder": "We will send the payment result to this URL", "kgstudio.payment.chain-id": "Chain", "kgstudio.payment.chain-id-desc": "All supported chains (Arbitrum, Base, Optimism)", "kgstudio.payment.client-id": "Client ID", "kgstudio.payment.column-setting": "Columns", "kgstudio.payment.copy-button": "Copy Payment Button", "kgstudio.payment.copy-link": "Copy Payment Link", "kgstudio.payment.create-item-error": "Failed to create payment item", "kgstudio.payment.create-item-success": "Payment item created successfully", "kgstudio.payment.create-payment": "Create Payment", "kgstudio.payment.create-product": "Create Product", "kgstudio.payment.create-product-title": "Create Product", "kgstudio.payment.create-payout": "Create Payout", "kgstudio.payment.create-payment-intent": "Create Payment Intent", "kgstudio.payment.create-intent-success": "Payment intent created successfully", "kgstudio.payment.create-intent-error": "Failed to create payment intent", "kgstudio.payment.payment-intent-details": "Payment Intent Details", "kgstudio.payment.configure-payment-intent-details": "Configure the payment intent details", "kgstudio.payment.pricing-mode": "Pricing Mode", "kgstudio.payment.pricing-mode-fiat": "Fiat", "kgstudio.payment.pricing-mode-crypto": "Crypto", "kgstudio.payment.pay-token": "Preferred Payment Token", "kgstudio.payment.required-fields": "Required <PERSON>", "kgstudio.payment.required-fields-description": "These fields are mandatory for creating the payment intent", "kgstudio.payment.optional-fields-description": "Additional fields that can be included", "kgstudio.payment.amount-fiat-placeholder": "100.00", "kgstudio.payment.amount-crypto-placeholder": "0.1", "kgstudio.payment.creator-creation-date": "Creator & Creation Date", "kgstudio.payment.crypto-amount": "Crypto Amount", "kgstudio.payment.crypto-price": "Crypto Price", "kgstudio.payment.currency": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.payment.custom-fields": "Custom Fields", "kgstudio.payment.date-range": "Date Range", "kgstudio.payment.deadline": "Deadline", "kgstudio.payment.delete-item-confirmation": "Are you sure you want to delete this payment item?", "kgstudio.payment.delete-item-title": "Delete Payment Item", "kgstudio.payment.description": "Description", "kgstudio.payment.due-date": "Due Date", "kgstudio.payment.duration": "Duration", "kgstudio.payment.edit-product-title": "Edit Product", "kgstudio.payment.error-loading-callbacks": "Failed to load callback logs", "kgstudio.payment.error-loading-oauth-clients": "Error loading OAuth clients", "kgstudio.payment.error-loading-payout-data": "Error loading payout data", "kgstudio.payment.error-url": "Error URL", "kgstudio.payment.error-url-placeholder": "Enter error redirect URL", "kgstudio.payment.event-details": "Event Details", "kgstudio.payment.event-type": "Event Type", "kgstudio.payment.export-csv": "Export CSV", "kgstudio.payment.failed": "Failed", "kgstudio.payment.fiat-amount": "Fiat Amount", "kgstudio.payment.fiat-currency": "Fiat Currency", "kgstudio.payment.handling-fee": "Handling Fee", "kgstudio.payment.field-key": "Key", "kgstudio.payment.field-label": "Field Label", "kgstudio.payment.field-label-required": "Field label is required", "kgstudio.payment.field-name": "Field Name", "kgstudio.payment.field-name-duplicate": "The field name already exists", "kgstudio.payment.field-name-required": "Field name is required", "kgstudio.payment.field-type": "Field Type", "kgstudio.payment.field-value": "Value", "kgstudio.payment.group-key": "Group Key", "kgstudio.payment.group-key-search-placeholder": "Search by Group Key", "kgstudio.payment.http-code": "HTTP Code", "kgstudio.payment.intent-id": "Intent ID", "kgstudio.payment.kg-deep-link": "KG Deep Link", "kgstudio.payment.merchant-email": "Merchant Email", "kgstudio.payment.merchant-settings": "Merchant Settings", "kgstudio.payment.merchant-settings-desc": "The merchant email and accent color appear on the payment page. Customers can contact the merchant via email for support, and the accent color highlights key elements.", "kgstudio.payment.new-field": "New Field", "kgstudio.payment.open-page": "Open Payment Page", "kgstudio.payment.optional-field": "Optional Field", "kgstudio.payment.optional-fields": "Optional Fields", "kgstudio.payment.order-data": "Order Data", "kgstudio.payment.order-data-expanded": "Expand Order Data", "kgstudio.payment.order-data-fields": "Order Data Fields", "kgstudio.payment.organization-icon": "Organization Icon", "kgstudio.payment.organization-icon-placeholder": "Enter organization icon URL", "kgstudio.payment.organization-id": "Organization ID", "kgstudio.payment.organization-id-placeholder": "Enter organization ID", "kgstudio.payment.organization-name": "Organization Name", "kgstudio.payment.organization-name-placeholder": "Enter organization name", "kgstudio.payment.organization-required": "Organization is required", "kgstudio.payment.payer-address": "Payer Address", "kgstudio.payment.payment-chain-id": "Payment Chain ID", "kgstudio.payment.payment-failed": "Payment Failed", "kgstudio.payment.payment-intent-id": "Payment Intent ID", "kgstudio.payment.payment-item-list": "Product", "kgstudio.payment.payment-link": "Payment Link", "kgstudio.payment.payment-list": "Order History", "kgstudio.payment.payout-list": "Payout History", "kgstudio.payment.payout-id": "Payout ID", "kgstudio.payment.payout-wallet": "Payout Wallet", "kgstudio.payment.connect-wallet": "Connect Wallet", "kgstudio.payment.wallet-connected": "Wallet Connected", "kgstudio.payment.wallet-disconnected": "Wallet Disconnected", "kgstudio.payment.wallet-connection-failed": "Wallet Connection Failed", "kgstudio.payment.disconnect-wallet": "Disconnect Wallet", "kgstudio.payment.copy-address": "Copy Address", "kgstudio.payment.address-copied": "Address Copied", "kgstudio.payment.select-wallet-description": "Choose a wallet to connect to the application", "kgstudio.payment.metamask-description": "Connect using MetaMask browser extension", "kgstudio.payment.browser-wallet-description": "Connect using browser wallet", "kgstudio.payment.wallet-description": "Connect using this wallet", "kgstudio.payment.connecting": "Connecting...", "kgstudio.payment.not-available": "Not Available", "kgstudio.payment.wallet-security-notice": "Only connect with sites you trust", "kgstudio.payment.payout_target_address": "Payout Address", "kgstudio.payment.recipient": "Recipient", "kgstudio.payment.request-rejected": "Request Rejected", "kgstudio.payment.reviewer-review-date": "Review Date", "kgstudio.payment.payment-status": "Payment Status", "kgstudio.payment.payment-success": "Payment Success", "kgstudio.payment.payment-text": "Customer", "kgstudio.payment.payment-tx-hash": "Payment Tx Hash", "kgstudio.payment.pending": "Pending", "kgstudio.payment.pricing-mode-dynamic": "Dynamic", "kgstudio.payment.pricing-mode-fixed": "Fixed", "kgstudio.payment.product-currency": "<PERSON><PERSON><PERSON><PERSON>", "kgstudio.payment.product-description": "Product Description", "kgstudio.payment.product-description-placeholder": "Please enter product description", "kgstudio.payment.product-image": "Product Image", "kgstudio.payment.product-name": "Product Name", "kgstudio.payment.product-name-placeholder": "Please enter product name", "kgstudio.payment.product-price": "Product Price", "kgstudio.payment.product-price-placeholder": "Please enter product price", "kgstudio.payment.received-amount": "Received Amount", "kgstudio.payment.refund": "Refund", "kgstudio.payment.refund.address.placeholder": "Enter address", "kgstudio.payment.refund.address.text": "Refund Address", "kgstudio.payment.refund-amount": "Refund Amount", "kgstudio.payment.refund.amount.placeholder": "Enter amount", "kgstudio.payment.refund.amount.text": "Amount", "kgstudio.payment.refund.button": "Refund", "kgstudio.payment.refund.cancel": "Cancel", "kgstudio.payment.refund.confirm": "Confirm Refund", "kgstudio.payment.refund.error": "Error initiating refund", "kgstudio.payment.refund.success": "Refund initiated successfully", "kgstudio.payment.refund.title": "Refund Payment", "kgstudio.payment.remove-field": "Remove", "kgstudio.payment.required-field": "Required Field", "kgstudio.payment.result-failed": "Failed", "kgstudio.payment.result-success": "Success", "kgstudio.payment.search-intent": "Search by Intent ID", "kgstudio.payment.select-client": "Select Client", "kgstudio.payment.select-client-first": "Please select a client first", "kgstudio.payment.sending": "Sending...", "kgstudio.payment.send-test": "Send Test", "kgstudio.payment.send-failed": "Send Failed", "kgstudio.payment.send-success": "Send Success", "kgstudio.payment.send-token": "Send Token", "kgstudio.payment.sent": "<PERSON><PERSON>", "kgstudio.payment.sign-payload": "Sign Payload", "kgstudio.payment.status": "Status", "kgstudio.payment.status-code": "Status Code", "kgstudio.payment.status-completed": "Completed", "kgstudio.payment.status-expired": "Expired", "kgstudio.payment.status-failed": "Failed", "kgstudio.payment.status-pending": "Pending", "kgstudio.payment.status-refunded": "Refunded", "kgstudio.payment.success": "Success", "kgstudio.payment.success-message": "Success Message (Optional)", "kgstudio.payment.success-message-desc": "This message will be included in the success notification email sent to customers after successful payment. You can consider providing a congratulatory message or a download link for the user's digital goods here.", "kgstudio.payment.success-message-placeholder": "Thank you for your purchase...", "kgstudio.payment.success-url": "Success URL", "kgstudio.payment.success-url-placeholder": "Enter success redirect URL", "kgstudio.payment.symbol": "Symbol", "kgstudio.payment.test": "Test", "kgstudio.payment.test-callback": "Test Callback", "kgstudio.payment.test-callback-error": "Failed to send test callback", "kgstudio.payment.test-callback-sent": "Test callback sent successfully", "kgstudio.payment.timestamp": "Timestamp", "kgstudio.payment.type": "Type", "kgstudio.payment.type-payment": "Payment", "kgstudio.payment.type-test": "Test", "kgstudio.payment.update-item-error": "Failed to update payment item", "kgstudio.payment.update-item-success": "Payment item updated successfully", "kgstudio.payment.upload-csv": "Upload CSV", "kgstudio.payment.upload-image-desc": "Upload image", "kgstudio.payment.url-copied": "Link copied", "kgstudio.payment.url-hint": "When redirecting, we will append \"?pid=payment_intent_id\" to this URL", "kgstudio.payment.view-payment-page": "Share Payment Page", "kgstudio.payment.webhook-url": "Webhook URL", "kgstudio.permissions.notification-description": "You can view the users who have permission to access the system. If you want to edit your users, please contact the system provider.", "kgstudio.permissions.title": "Users", "kgstudio.permissions.user-id": "User ID", "kgstudio.review.ai-summary": "AI summary", "kgstudio.review.ai-summary-fail": "No negative news related to this customer. No need to perform AI Summary.", "kgstudio.review.aml-risk": "AML Risk", "kgstudio.review.btn": "Review", "kgstudio.review.case-details": "Case Details", "kgstudio.review.check-id": "Check ID", "kgstudio.review.created": "Created", "kgstudio.review.details": "Details", "kgstudio.review.filter-placeholder": "Legal Name, Phone, Email, National ID, LINE ID", "kgstudio.review.high-risk": "High Risk", "kgstudio.review.idv": "IDV", "kgstudio.review.id-verification": "ID Verification", "kgstudio.review.idv-fail": "Failed", "kgstudio.review.idv-pass": "Pass", "kgstudio.review.internal-note": "Internal note", "kgstudio.review.internal-notes-length-limit": "Internal notes can have a maximum of 50 characters", "kgstudio.review.internal-notes-required": "Internal notes are required when status is 'rejected'", "kgstudio.review.kyc-status": "KYC Status", "kgstudio.review.latest-submit": "Latest Submit Time", "kgstudio.review.low-risk": "Low Risk", "kgstudio.review.mid-risk": "Mid Risk", "kgstudio.review.name": "Name", "kgstudio.review.name-screening": "Name Screening", "kgstudio.review.personal-information": "Personal Information", "kgstudio.review.process-ai": "AI summary processing…", "kgstudio.review.processing": "Processing", "kgstudio.review.review-detail": "Review Details", "kgstudio.review.reviewer": "Reviewer", "kgstudio.review.review-result": "Review result", "kgstudio.review.review-time": "Review Time", "kgstudio.review.risk": "Risk", "kgstudio.review.sanctioned": "Sanctioned", "kgstudio.review.sanctioned-false": "False", "kgstudio.review.sanctioned-true": "True", "kgstudio.review.start-ai": "Start AI summary", "kgstudio.review.submission": "Submission", "kgstudio.review.summary": "Summary", "kgstudio.review.updated": "Updated at", "kgstudio.search.email": "Search Email", "kgstudio.select.at-least-one": "Please select at least one item", "kgstudio.send.add-attachment": "Add Attachment", "kgstudio.send.attachment-required": "Attachment is required for approval", "kgstudio.send.by-email": "Email", "kgstudio.send.by-phone-number": "Phone Number", "kgstudio.send.do-not-leave-page": "Do not leave this page.", "kgstudio.send.gasless-alert-button": "Yes", "kgstudio.send.gasless-alert-desc": "Insufficient {mainTokenName}({chainName}) to submit the transaction. Use {sourceToken} to pay for the gas token automatically? (~ ${estimatedGasUsd})? If no, you'll need to manually deposit at least {estimatedGas} {mainTokenName}({chainName}).", "kgstudio.send.gasless-alert-title": "We can help you pay for the gas fee! (Arbitrum, Base, Optimism only)", "kgstudio.send.gasless-modal-deducted-desc-1": "You send:", "kgstudio.send.gasless-modal-deducted-desc-2": "Estimated amount to be received:", "kgstudio.send.gasless-modal-deducted-title": "Auto-deducted", "kgstudio.send.gasless-modal-desc": "Estimated gas fee: {gasFee}", "kgstudio.send.gasless-modal-full-title": "Full receiption", "kgstudio.send.gasless-modal-title": "How would you like to pay the gas?", "kgstudio.send.gas-token-balance": "Gas Token Balance : {balance} (Estimated gas fee: {gasFee})", "kgstudio.send.loading-hint": "Transaction might take longer than expected if the blockchain is very busy. To check the transaction status and details, please click on the Tx Hash.", "kgstudio.send.max": "Max", "kgstudio.send.note-placeholder": "Enter the transaction note and upload necessary attachments for approval.", "kgstudio.send.note-required": "Transaction note is required for approval", "kgstudio.send.over-limit": "Over the limit", "kgstudio.send.remaining-balance": "Remaining transferrable balance today: ", "kgstudio.send.remaining-balance-today": "Your remaining balance today is {formattedCurrentLimit} U (with a daily limit of {formattedDailyLimit} U).", "kgstudio.send.review-note": "Review Note", "kgstudio.send.send-confirm-alert": "Once this transaction is confirmed, it will be affective right away and cannot be reverted! Please make sure the blockchain, amount and recipient are correct.", "kgstudio.send.send-confirm-submit-desc-alert": "Ensure that your transaction details are correct, as they cannot be modified after submission.", "kgstudio.send.send-confirm-submit-title-alert": "This transaction will be sent only after approval.", "kgstudio.send.send-to": "Send to", "kgstudio.send.submit-request": "Submit Request", "kgstudio.send.title": "Send Fund", "kgstudio.send.to-user": "to user", "kgstudio.send.transaction-attachment": "Attachment", "kgstudio.send.transaction-error-desc": "Please retry after the administrator has deposited sufficient asset", "kgstudio.send.transaction-error-title": "Error: Insufficient inventory", "kgstudio.send.transaction-note": "Transaction Note", "kgstudio.send.transaction-submit-success-desc": "Your transaction has been submitted and will be executed after approval and release.", "kgstudio.send.transaction-submit-success-title": "Transaction Submitted", "kgstudio.send.transaction-success-desc": "Your transaction request has been submitted successfully and will soon be able to view the transaction results.", "kgstudio.send.transaction-success-title": "Transaction Sent Successfully", "kgstudio.send.tx-failed": "Transaction Failed", "kgstudio.send.tx-failed-description": "Failed to transfer funds, please retry or contact the system administrator.", "kgstudio.send.tx-in-progress": "Transaction In Progress.", "kgstudio.send.tx-success": "Transaction Success!", "kgstudio.send.view-profile": "View Profile", "kgstudio.setting.api-keys": "API Keys", "kgstudio.setting.create-api-key": "Create API Key", "kgstudio.setting.create-first-api-key": "Create your first API key to get started", "kgstudio.setting.create-first-oauth-client": "Create your first OAuth client to get started", "kgstudio.setting.create-oauth-client": "Create OAuth Client", "kgstudio.setting.no-api-keys": "No API keys", "kgstudio.setting.no-oauth-clients": "No OAuth clients", "kgstudio.setting.oauth-clients": "OAuth Clients", "kgstudio.setting.org-settings": "Organization Settings", "kgstudio.setting.user.account-settings": "Account <PERSON><PERSON>", "kgstudio.setting.user.add-address": "Add Address", "kgstudio.setting.user.add-new-address": "Add New Address", "kgstudio.setting.user.address-name": "Address Name", "kgstudio.setting.user.address-name-placeholder": "e.g. My Main Wallet", "kgstudio.setting.user.api-keys": "API Keys", "kgstudio.setting.user.api-keys-description": "API keys allow for programmatic access to your KryptoGO Studio account. They should be kept secure and never shared publicly.", "kgstudio.setting.user.cancel": "Cancel", "kgstudio.setting.user.check-documentation": "Check Documentation", "kgstudio.setting.user.client-domain": "Domain", "kgstudio.setting.user.client-id": "Client ID", "kgstudio.setting.user.client-id-desc": "If you don't have a Client ID, please add it in the [Account Settings -> OAuth Clients] page.", "kgstudio.setting.user.client-id-required": "Client ID is required", "kgstudio.setting.user.client-name": "Client Name", "kgstudio.setting.user.client-secret": "Client Secret", "kgstudio.setting.user.client-type": "Type", "kgstudio.setting.user.close": "Close", "kgstudio.setting.user.confirm-delete-api-key": "Are you sure you want to delete this API key? This action cannot be undone.", "kgstudio.setting.user.confirm-delete-oauth-client": "Are you sure you want to delete this OAuth client? This action cannot be undone.", "kgstudio.setting.user.confirm-deletion": "Confirm Deletion", "kgstudio.setting.user.copy-prompt": "Copy AI Prompt", "kgstudio.setting.user.create-api-key": "Create API Key", "kgstudio.setting.user.create-client-title": "Create OAuth Client", "kgstudio.setting.user.created": "Created", "kgstudio.setting.user.create-first-api-key": "Create your first API key to get started", "kgstudio.setting.user.create-first-oauth-client": "Create your first OAuth client to get started", "kgstudio.setting.user.create-key-title": "Create API Key", "kgstudio.setting.user.create-oauth-client": "Create OAuth Client", "kgstudio.setting.user.default": "<PERSON><PERSON><PERSON>", "kgstudio.setting.user.delete": "Delete", "kgstudio.setting.user.description": "Description", "kgstudio.setting.user.domain-format-note": "Domain must begin with http:// or https:// (e.g., https://app.kryptogo.com)", "kgstudio.setting.user.edit": "Edit", "kgstudio.setting.user.error.add-address": "Format error, cannot add EVM payment address", "kgstudio.setting.user.error.copy-clipboard": "Failed to copy to clipboard", "kgstudio.setting.user.error.create-api-key": "Failed to create API key", "kgstudio.setting.user.error.create-oauth-client": "Failed to create OAuth client", "kgstudio.setting.user.error.delete-api-key": "Failed to delete API key", "kgstudio.setting.user.error.domain-format": "Domain must begin with http:// or https://", "kgstudio.setting.user.error.domain-required": "Domain is required", "kgstudio.setting.user.error.fetch-api-keys": "Failed to fetch API keys", "kgstudio.setting.user.error.fetch-oauth-clients": "Failed to fetch OAuth clients", "kgstudio.setting.user.error.set-default-address": "Failed to set default address", "kgstudio.setting.user.error.update-oauth-client": "Failed to update OAuth client", "kgstudio.setting.user.joyride.back": "Back", "kgstudio.setting.user.joyride.close": "Close", "kgstudio.setting.user.joyride.finish": "Finish", "kgstudio.setting.user.joyride.next": "Next", "kgstudio.setting.user.joyride.skip": "<PERSON><PERSON>", "kgstudio.setting.user.joyride.step1": "OAuth Client ID (Client ID) allows you to integrate your application with KryptoGO Payment SDK service. You can also create your own Client ID to integrate into different applications.", "kgstudio.setting.user.joyride.step2": "One-click copy AI prompt and paste it into any AI model service (recommended: claude-3.7-sonnect, gpt-o3) to generate a crypto payment webpage that can be used immediately.", "kgstudio.setting.user.key-description": "Description (Optional)", "kgstudio.setting.user.key-name": "Name", "kgstudio.setting.user.key-prefix": "Key prefix", "kgstudio.setting.user.last-used": "Last used", "kgstudio.setting.user.loading": "Loading...", "kgstudio.setting.user.logo": "Logo", "kgstudio.setting.user.manage-payment-addresses": "Manage Payment Addresses", "kgstudio.setting.user.no-api-keys": "No API keys", "kgstudio.setting.user.no-oauth-clients": "No OAuth clients", "kgstudio.setting.user.oauth-clients": "OAuth Clients", "kgstudio.setting.user.oauth-clients-description": "OAuth clients allow you to integrate your applications with KryptoGO services. Each client has a unique ID and secret that should be kept secure.", "kgstudio.setting.user.org-wallet": "Organization Wallet", "kgstudio.setting.user.payment-address-description": "The payment address is used to receive payments from customers.", "kgstudio.setting.user.save-client-message": "This is the only time you will see the client secret. Please copy it and store it securely. Client secrets are used to authenticate your application when making API requests to KryptoGO services.", "kgstudio.setting.user.save-client-title": "Important: Save your client credentials", "kgstudio.setting.user.save-key-message": "This is the only time you will see this API key. Please copy it and store it securely.", "kgstudio.setting.user.save-key-title": "Important: Save your API key", "kgstudio.setting.user.set-as-default": "Set as <PERSON><PERSON><PERSON>", "kgstudio.setting.user.success.address-added": "EVM payment address added successfully!", "kgstudio.setting.user.success.api-key-created": "API key created successfully", "kgstudio.setting.user.success.api-key-deleted": "API key deleted successfully", "kgstudio.setting.user.success.copied": "{item} copied to clipboard", "kgstudio.setting.user.success.oauth-client-created": "OAuth client created successfully", "kgstudio.setting.user.success.oauth-client-updated": "OAuth client updated successfully", "kgstudio.setting.user.success.org-wallet-default": "Organization wallet set as default", "kgstudio.setting.user.update-oauth-client": "Update OAuth Client", "kgstudio.setting.user.wallet-address": "Wallet Address", "kgstudio.setting.user.your-addresses": "Your Addresses", "kgstudio.team.delete.description": "Are you sure you want to deactivate {currentMemberName}({currentMemberEmail})? They will be logged out immediately, and they will not be able to login again.", "kgstudio.team.delete.success": "\"{name} ({email})\" has been removed from your team.", "kgstudio.team.edit.title": "Edit Team Member", "kgstudio.team.invite.error.existed": "User already exists", "kgstudio.team.invite.error.rate-limit": "You have reached the invitation limit within a short period of time. Please try again later.", "kgstudio.team.invite.sent": "Invitation sent!", "kgstudio.team.invite.text": "Invite", "kgstudio.team.invite.title": "Invite Team Member", "kgstudio.team.member.member-id.title": "Member ID(Optional)", "kgstudio.team.member.name.placeholder": "e.g.: <PERSON>", "kgstudio.team.members": "Team", "kgstudio.team.name.validation.required": "Name can't be empty", "kgstudio.team.permission-settings": "Permission Settings", "kgstudio.team.rate-limit.hint": "Reinvite after 1 min", "kgstudio.team.remove-member": "Deactivate Member", "kgstudio.team.resend": "Resend Invitation", "kgstudio.team.role.approver": "Approver", "kgstudio.team.role.aseet-pro": "AssetPro Role", "kgstudio.team.role.asset-pro": "AssetPro Role", "kgstudio.team.role.compliance": "Compliance Role", "kgstudio.team.role-decription.aseet-pro.admin": "has full access to AssetPro, including viewing treasury, sending assets, approving, downloading all transaction history, and setting members", "kgstudio.team.role-decription.aseet-pro.approver": "can view all transactions in AssetPro, and approve or reject transactions awaiting approval.", "kgstudio.team.role-decription.aseet-pro.finance-manager": "can view all transactions in AssetPro, release or reject the transactions awaiting release, and view the Treasury.", "kgstudio.team.role-decription.aseet-pro.trader": "can send assets with a daily limit, or submit transfer request, and view their own transaction.", "kgstudio.team.role-decription.compliance.admin": "has full access to Compliance, including Review, global setup of verification process", "kgstudio.team.role-decription.compliance.reviewer": "can only review and edit compliance cases", "kgstudio.team.role.description": "Owner has full access to all the features of the modules your organization subscribes to.", "kgstudio.team.role.finance-manager": "Finance Manager", "kgstudio.team.role.nft-boost": "NFT Boost Role", "kgstudio.team.role.owner": "Owner", "kgstudio.team.role.reviewer": "Reviewer", "kgstudio.team.role.title": "Team Role", "kgstudio.team.role.trader": "Trader", "kgstudio.team.role.user360": "User 360 Role", "kgstudio.team.role.validation": "Please at least select one module role", "kgstudio.team.role.wallet": "Wallet Builder Role", "kgstudio.team.text": "Team", "kgstudio.time.end-after-start": "End time must be after start time", "kgstudio.time.input-time": "Please enter time", "kgstudio.transaction.export": "Export", "kgstudio.transaction.operator": "Operator", "kgstudio.transaction.placeholder": "Recipient’s E-mail, Phone, Address, Tx Hash", "kgstudio.transactions.recipient-placeholder": "E-mail, Phone, Address", "kgstudio.transaction.status-awaiting-approval": "Awaiting <PERSON><PERSON><PERSON><PERSON>", "kgstudio.transaction.status-awaiting-release": "Awaiting Release", "kgstudio.transaction.status-failed": "Send Failed", "kgstudio.transaction.status-pending": "Pending Approval ", "kgstudio.transaction.status-reject": "Rejected", "kgstudio.transaction.status-rejected": "Rejected", "kgstudio.transaction.status-sending": "Sending", "kgstudio.transaction.status-success": "Send Success", "kgstudio.transactions.title": "Transactions", "kgstudio.transaction.submit-time": "Submit Time", "kgstudio.transactions.user-placeholder": "ID, Address", "kgstudio.treasury.add-fund": "Add Funds", "kgstudio.treasury.add-fund-modal.desc": "Scan QRcode to send your asset to this wallet address", "kgstudio.treasury.agree-and-continue": "I Understand the Risks", "kgstudio.treasury.asset": "<PERSON><PERSON>", "kgstudio.treasury.asset-name": "Asset Name", "kgstudio.treasury.buy-crypto.desc": "Add enough crypto product here so your users can buy through credit/debit cards. You can make money through profit margin for their transaction.", "kgstudio.treasury.buy-crypto.margin": "Buy Crypto Profit Margin (%)", "kgstudio.treasury.buy-crypto-title": "Buy Crypto", "kgstudio.treasury.chart": "Chart", "kgstudio.treasury.click-to-refresh": "Click to refresh", "kgstudio.treasury.click-to-reveal": "Click the button below to reveal your private key", "kgstudio.treasury.contract-address": "Contract Address", "kgstudio.treasury.deposit-tooltip": "Balance lower than {alert_threshold} {symbol}! Add funds to ensure the success of transactions.", "kgstudio.treasury.gas-swap.desc": "Add native token for your users to get gas token. For example, deposit enough TRX here so your wallet user can use USDT(Tron) to swap to TRX. You can make money through profit margin for their transaction.", "kgstudio.treasury.gas-swap.margin": "Gas Swap Profit Margin (%)", "kgstudio.treasury.gas-swap-title": "Gas Swap", "kgstudio.treasury.liquidity-settings": "Liquidity Settings", "kgstudio.treasury.liquidity-settings-min-max": "Min: {min}%, Max: {max}%.", "kgstudio.treasury.liquidity-type": "Liquidity Type", "kgstudio.treasury.liquidity-update-confirm.btn": "Yes", "kgstudio.treasury.liquidity-update-confirm.message": "All your customers will see new quotes.", "kgstudio.treasury.liquidity-update-confirm.text": "Are you sure about updating the profit margin?", "kgstudio.treasury.liquidity-update-success": "Successfully Updated!", "kgstudio.treasury.network": "Network", "kgstudio.treasury.price": "Price", "kgstudio.treasury.profit-current-rate": "Current Rate", "kgstudio.treasury.profit-description": "Description", "kgstudio.treasury.profit-margin": "<PERSON><PERSON>", "kgstudio.treasury.profit-margin-desc": "Use dynamic pricing to calculate user payouts based on market prices, processing fees, and profit margins.", "kgstudio.treasury.profit-margin-rate": "Profit <PERSON>gin <PERSON>", "kgstudio.treasury.profit-margin-setting": "Profit <PERSON><PERSON>", "kgstudio.treasury.profit-rate-edit": "Edit Profit Rate", "kgstudio.treasury.profit-rate-edit-title": "Set Profit Rate (%)", "kgstudio.treasury.profit-rate-setting": "Profit Rate Settings", "kgstudio.treasury.profit-rate-update-success": "Successfully Updated!", "kgstudio.treasury.profit-rate-update-text": "Are you sure about updating the profit rate?", "kgstudio.treasury.profit-service": "Service", "kgstudio.treasury.profit-service-bridge": "Bridge (cross-chain swap on DeFi)", "kgstudio.treasury.profit-service-bridge-desc1": "Cross-chain swap between Tron, Ethereum, Arbitrum, Polygon, Base, Optimism, and Binance Smart Chain. e.g. swap USDT(Tron) to ETH(Arbitrum), you will earn the source token USDT(Tron) as revenue.", "kgstudio.treasury.profit-service-bridge-desc2": "e.g. A customer paid some USDT(Tron) that equivalent to $100 USD, to cross-chain swap for ETH(Arbitrum). If the Business set Profit Margin at 2%, => the customer will get ETH(Arbitrum) that’s equivalent to 100* (1 - 2％) = 98 USD.", "kgstudio.treasury.profit-service-bridge-hint1": "You earn $n for every $100 volume the customer swaps.  if profit rate = 2%, n = (100) - 100* (1 - 2％) = 2 USD", "kgstudio.treasury.profit-service-bridge-info1": "Set the profit rate (%) for decentralized token swaps. This rate will reduce the amount of tokens the customer receives.", "kgstudio.treasury.profit-service-bridge-info2": "*Liquidity provided by decentralized services.", "kgstudio.treasury.profit-service-buy": "Buy", "kgstudio.treasury.profit-service-buy-desc1": "If you set the Profit Margin at 1%, and a customer spends $100 USD on buying crypto\n=> They will get 100/(1+7％+1％) = 92.59 U. You will earn USD as revenue.", "kgstudio.treasury.profit-service-buy-desc2": "(the 1% is your profit margin, and the 7% is the market cost of Buy service)", "kgstudio.treasury.profit-service-buy-hint1": "Set the profit rate (%) for buying tokens. This rate will be added to the market price of the token.", "kgstudio.treasury.profit-service-buy-info1": "You earn $n for every $100 the customer spends.", "kgstudio.treasury.profit-service-buy-info2": "if profit rate = {profit_rate}%, n = 100/(1+7%)-100/(1+7%+{profit_rate}%) = {profit_usd} USD", "kgstudio.treasury.profit-service-send": "Send (with TRX)", "kgstudio.treasury.profit-service-send-batch": "Send (Batch)", "kgstudio.treasury.profit-service-send-batch-desc1": "(Batch (Collect) sending service only charges the initial network fee. No Profit Margin to set up now)", "kgstudio.treasury.profit-service-send-desc1": "When a customer sends any token on TRON to a friend, and they have sufficient TRX as gas token, you will earn part of the gas token (TRX) as income. (Profit margin for “Send” works on TRON only.)", "kgstudio.treasury.profit-service-send-desc2": "e.g. A customer wanted to send 12.5 USDT(TRC20) to a friend as their lunch payment, and the initial gas fee was 3 TRX. The Business set Profit Margin at 10%. \n=> The friend will receive 12.5 USDT, while the customer will send 12.5 USDT and 3.3 TRX as gas fee, since the gas fee will be 3 * (1 + 10％) = 3.3 TRX.", "kgstudio.treasury.profit-service-send-gasless": "Send (Gasless)", "kgstudio.treasury.profit-service-send-gasless-desc1": "When a customer sends any token on TRON to a friend while they don’t have sufficient TRX as gas token, they can simply send their TRC20 tokens, and you will earn part of the source token, say USDT, as income. (Profit margin for “Gasless Send” works on Tron only)", "kgstudio.treasury.profit-service-send-gasless-desc2": "e.g. A customer wanted to send 12.5 USDT(TRC20) to a friend as their lunch payment, and the initial gas fee was 3 TRX. The Business set Profit Margin at 10%. \n=> The friend will receive 12.5 USDT, while the customer will send 14.26 USDT in total. \n=> To cover the initial cost and the entire transaction, the smart contract needs 10 TRX as gas fee. \n=> Gas service fee = 10 * (1 + 10％) = 11 TRX ~ 1.76 USD \n=> The customer will send 1.76 + 12.5 = 14.26 USDT.", "kgstudio.treasury.profit-service-send-gasless-hint1": "Set the profit rate (%) for gasless sends. This rate will affect the actual amount of source token sent from the customer's wallet.", "kgstudio.treasury.profit-service-send-gasless-info1": "You might earn $n for every Tron transaction the customer sends, depending on how busy the network is.", "kgstudio.treasury.profit-service-send-gasless-info2": "if profit rate = {profit_rate}%, n = 10 * {profit_rate}% = {profit_trx} TRX ~ {profit_usd} USD", "kgstudio.treasury.profit-service-send-hint1": "Set the profit rate (%) for sending tokens. This rate will increase the gas fee paid by the customer.", "kgstudio.treasury.profit-service-send-hint2": "*TRC20-USDT transaction supported only.", "kgstudio.treasury.profit-service-send-info1": "You might earn $n for every Tron transaction customer sends, depending on how busy the network is.", "kgstudio.treasury.profit-service-send-info2": "if profit rate = {profit_rate}%, n = 3*{profit_rate}% = {profit_trx} TRX ~ {profit_usd} USD", "kgstudio.treasury.profit-service-swap-cefi": "Swap (CeFi(AssetPro))", "kgstudio.treasury.profit-service-swap-cefi-desc1": "For Tron (TRC20) token only. If a customer submits a Gas Swap request (USDT(Tron) -> TRX)), you will earn the target token (TRX) as income.", "kgstudio.treasury.profit-service-swap-cefi-desc2": "e.g. A customer paid some USDT that’s equivalent to $100 USD, to swap for TRX. And the blockchain energy rental cost was 5 USD. If the Business set Profit Margin at 10%, \n=> The customer will get TRX that’s equivalent to (100 - 5) * (1 - 10％) = 85.5 USD.", "kgstudio.treasury.profit-service-swap-cefi-hint1": "Set the profit rate (%) for centralized token swaps. This rate will reduce the amount of tokens the customer receives.", "kgstudio.treasury.profit-service-swap-cefi-hint2": "*Liquidity provided by your AssetPro Treasury.", "kgstudio.treasury.profit-service-swap-cefi-info1": "You might earn $n for every $100 volume the customer swaps, depending on how busy the network is.", "kgstudio.treasury.profit-service-swap-cefi-info2": "if profit rate = {profit_rate}%, n = (100 - 5) - [(100 - 5) * (1 - {profit_rate}%)]= {profit_usd} USD", "kgstudio.treasury.profit-service-swap-defi": "<PERSON><PERSON><PERSON> (DeFi)", "kgstudio.treasury.profit-service-swap-defi-desc1": "Liquidity is from 3rd party DeFi service, supports Ethereum, Arbitrum, Polygon & Binance Smart Chain. e.g. swap SUSHI to POL, you will earn the USD value of the source token (SUSHI) as income.", "kgstudio.treasury.profit-service-swap-defi-desc2": "e.g. A customer paid some SUSHI that equivalent to $100 USD, to swap for POL. If the Business set Profit Margin at 10%, \n=> the customer will get POL that’s equivalent to 100* (1 - 10％) = 90 USD.", "kgstudio.treasury.profit-service-swap-defi-hint1": "Set the profit rate (%) for decentralized token swaps. Raising this rate will decrease the amount of tokens the customer receives.", "kgstudio.treasury.profit-service-swap-defi-hint2": "*Liquidity provided by decentralized services.", "kgstudio.treasury.profit-service-swap-defi-info1": "You earn $n for every $100 volume the customer swaps.", "kgstudio.treasury.profit-service-swap-defi-info2": "if profit rate = {profit_rate}%, n = (100) - 100* (1 - {profit_rate}%) = {profit_usd} USD", "kgstudio.treasury.quantity": "Quantity", "kgstudio.treasury.retrieve-balance-error": "Failed to retrieve the organization's balance. Please try again later or contact our service team.", "kgstudio.treasury.reveal-seedphrase": "Reveal Private Key", "kgstudio.treasury.seedphrase-warning-1": "Your private key gives full access to your wallet and funds.", "kgstudio.treasury.seedphrase-warning-2": "Never share your private key with anyone or store it digitally.", "kgstudio.treasury.seedphrase-warning-3": "KryptoGO cannot recover your private key or funds if it is lost or stolen.", "kgstudio.treasury.seedphrase-warning-4": "KryptoGO will not be responsible for any loss resulting from private key exposure.", "kgstudio.treasury.seedphrase-warning-title": "Important Security Warning", "kgstudio.treasury.token": "Token", "kgstudio.treasury.token-price": "<PERSON><PERSON>", "kgstudio.treasury.value": "Value", "kgstudio.user360.wallet-usage-registered": "Activated", "kgstudio.user360.wallet-usage-unregistered": "Inactivated", "kgstudio.user-dna.7-day-active": "7-Day Active", "kgstudio.user-dna.applied_at": "Submitted on", "kgstudio.user-dna.app-open-times": "App Open Times", "kgstudio.user-dna.approved": "Approved", "kgstudio.user-dna.approved_at": "Verified on", "kgstudio.user-dna.dapp-favorites": "DApp Favorites", "kgstudio.user-dna.delay-hint": "Wallet address and balance information might sometimes be delayed if activity on the blockchain is high.", "kgstudio.user-dna.dob": "Date of Birth", "kgstudio.user-dna.email": "Email", "kgstudio.user-dna.first-apply": "First Apply", "kgstudio.user-dna.first-web3-activity": "First Web3 Activity", "kgstudio.user-dna.last-active": "Last Active", "kgstudio.user-dna.last-apply": "Last Apply", "kgstudio.user-dna.last-login": "Last Login", "kgstudio.user-dna.low": "Low", "kgstudio.user-dna.name": "Name", "kgstudio.user-dna.nation": "Nationality", "kgstudio.user-dna.national-id": "National ID", "kgstudio.user-dna.non-kyc-user": "Non-KYC User", "kgstudio.user-dna.no-wallet": "This customer does not have any wallet now.", "kgstudio.user-dna.of-10k-users": "of 10k users", "kgstudio.user-dna.phone": "Phone", "kgstudio.user-dna.real-name": "Legal Name", "kgstudio.user-dna.registered": "Downloaded & Logged in", "kgstudio.user-dna.risk-score": "Risk Score", "kgstudio.user-dna.sign-times": "Sign Times", "kgstudio.user-dna.status": "KYC Status", "kgstudio.user-dna.submission": "Submission", "kgstudio.user-dna.title": "User Profile", "kgstudio.user-dna.transactions-volume": "Transactions Volumn", "kgstudio.user-dna.tvl": "TVL", "kgstudio.user-dna.wallet-activity": "Wallet Activity", "kgstudio.user-dna.wallet-app-activities": "Wallet App Activities", "kgstudio.user-dna.wallets": "Wallets", "kgstudio.user-dna.wallet.tag": "Tag", "kgstudio.validation.correct-format": "Please enter the correct format.", "kgstudio.validation.number-greater-than-zero": "Please enter a value greater than 0", "kgstudio.validation.phone-or-email-required": "You can transfer to any blockchain address, or to any email address or phone number that has been registered to your wallet service.", "kgstudio.validation.required": "Value cannot be empty.", "kgstudio.validation.sorrect-format": "Please enter the correct format.", "kgstudio.validation.valid-address": "Please enter a valid wallet address.", "kgstudio.validation.valid-email": "Please enter a valid email.", "kgstudio.validation.valid-phone": "Please enter a valid phone number.", "kgstudio.wallet.active-features": "Active Features", "kgstudio.wallet.app-images.app-icon": "App Icon", "kgstudio.wallet.app-images.splash": "Splash", "kgstudio.wallet.app-images.title": "App Images", "kgstudio.wallet.app-settings": "App Settings", "kgstudio.wallet.app-under-review": "App is under review and cannot be modified.", "kgstudio.wallet.button.view-demo": "View Demo", "kgstudio.wallet.config.android": "Android (Google Play)", "kgstudio.wallet.config.app-image": "App Image", "kgstudio.wallet.config.app-startup": "App startup screen", "kgstudio.wallet.config.app-store-info": "App Store Information", "kgstudio.wallet.config.brand-logo-alt": "brand logo", "kgstudio.wallet.config.check": "Check", "kgstudio.wallet.config.completion.step1": "Please open KryptoGO Wallet, scan the QR code below to preview your Wallet App", "kgstudio.wallet.config.completion.step2": "If adjustments are needed, go back to the previous steps to modify your settings", "kgstudio.wallet.config.completion.step3": "After confirming the settings are correct, click the button below to submit", "kgstudio.wallet.config.completion.title": "App settings completed!", "kgstudio.wallet.config.configure-later": "Configure later", "kgstudio.wallet.config.configure-publish-data": "Setup publishing data", "kgstudio.wallet.config.confirm-before-submit": "Before submitting, ensure that all graphics and texts are correct", "kgstudio.wallet.config.confirm-submit": "Confirm and submit the Production version of your app", "kgstudio.wallet.config.data-verification": "Please verify the information you entered", "kgstudio.wallet.config.desc.all-chains-desc": "All chains supported by KryptoGO, including Ethereum, Polygon, BNB Chain, Arbitrum, KCC, Ronin, Bitcoin, Solana, Tron. When KryptoGO updates the supported chains, your wallet will also be updated automatically.", "kgstudio.wallet.config.desc.all-evm-chains-desc": "All EVM chains supported by KryptoGO, including Ethereum, Polygon, BNB Chain, Arbitrum, KCC, Ronin. When KryptoGO updates the supported EVM chains, your wallet will also be updated automatically.", "kgstudio.wallet.config.desc.currency-desc": "Home page displays the Swap function button, allowing users to exchange different trading pairs (supports ETH, Polygon, BNB single-chain Swap).", "kgstudio.wallet.config.desc.custom-desc": "Choose the chains you want to display from those supported by KryptoGO. Please note: When KryptoGO updates the supported chains, your wallet will not be updated automatically; you'll need to modify the settings yourself.", "kgstudio.wallet.config.desc.custom-token-description": "You can add custom cryptocurrencies through the token's contract address, and they will show up in your users' wallet token list. You can also set the peg price(optional). If you don't need custom tokens, simply skip this step.", "kgstudio.wallet.config.desc.dapp-list": "After the app goes live, you can update your DApp List at any time, simply publish in Studio and you're good. No need to submit App Publishing to App Stores. If you currently do not need to customize the DApp List, just skip this one.", "kgstudio.wallet.config.desc.defi-desc": "<PERSON><PERSON><PERSON>", "kgstudio.wallet.config.desc.displayed-asset-type-desc": "Asset type is what will show on the Home page of your wallet.", "kgstudio.wallet.config.desc.displayed-chains-desc": "Unselected chains will not be supported or displayed in your wallet.", "kgstudio.wallet.config.desc.english": "EN-US (Default)", "kgstudio.wallet.config.desc.explore-dapp-browser-desc": "Users can connect to DApp operations through the built-in DApp Browser in the wallet.", "kgstudio.wallet.config.desc.japanese": "日本語", "kgstudio.wallet.config.desc.kyc-user-verification-desc": "In combination with the Compliance Pro feature, users can submit KYC data, and you can perform identity verification and due diligence on them.", "kgstudio.wallet.config.desc.languages": "Wallet App Language", "kgstudio.wallet.config.desc.login-methods": "Login Method", "kgstudio.wallet.config.desc.nft-desc": "Supports NFTs on ETH and Polygon.", "kgstudio.wallet.config.desc.nft-rewards-desc": "Combine with NFT Boost feature, offer redeemable NFT empowerment to users.", "kgstudio.wallet.config.desc.nft-sell-desc": "NFT page displays the sell button, allowing users to list their NFTs on OpenSea with one click (DApp Browser feature needs to be enabled).", "kgstudio.wallet.config.desc.show-poap-desc": "Collectibles display the POAP NFTs owned by the user.", "kgstudio.wallet.config.desc.simplified-chinese": "中文（简体）", "kgstudio.wallet.config.desc.support-info": "Asset type is what will show on the Home page of your wallet. Unselected asset types will not be shown in your users' wallets.", "kgstudio.wallet.config.desc.swap-desc": "Home page displays the Swap function button, allowing users to exchange different blockchain tokens.(supports ETH, Polygon, BNB, Arbitrum single-chain Swap. Does not contain Fiat exchange.)", "kgstudio.wallet.config.desc.theme": "Apply your branding colors to the Wallet App", "kgstudio.wallet.config.desc.traditional-chinese": "中文（繁體）", "kgstudio.wallet.config.design-guideline": "App Icon Design Guideline", "kgstudio.wallet.config.extension": "Web Extension (Chrome Store)", "kgstudio.wallet.config.file-type-supported": "File types supported: JPG, PNG. Max size: 10 MB", "kgstudio.wallet.config.follow-guideline": "Please follow", "kgstudio.wallet.config.get-started-description": "The image displayed on the Get Started screen. It is recommended to use the brand logo", "kgstudio.wallet.config.get-started-title": "Get Started Image", "kgstudio.wallet.config.ios": "iOS (App Store)", "kgstudio.wallet.config.label.add": "ADD", "kgstudio.wallet.config.label.all-chains": "All Chains", "kgstudio.wallet.config.label.all-evm-chains": "All EVM Chains", "kgstudio.wallet.config.label.currency": "C<PERSON>rency (Mandatory)", "kgstudio.wallet.config.label.custom": "Custom", "kgstudio.wallet.config.label.custom-list": "Customize DApp List", "kgstudio.wallet.config.label.custom-token": "Custom Cryptocurrencies", "kgstudio.wallet.config.label.default-list": "KryptoGO Selection (UniSwap, OpenSea, SushiSwap, Rarible, Dune, and more.)", "kgstudio.wallet.config.label.defi": "<PERSON><PERSON><PERSON>", "kgstudio.wallet.config.label.email": "Email", "kgstudio.wallet.config.label.explore-dapp-browser": "Explore - <PERSON><PERSON><PERSON> Browser", "kgstudio.wallet.config.label.kyc-user-verification": "KYC: User Verification", "kgstudio.wallet.config.label.nft": "NFT", "kgstudio.wallet.config.label.nft-rewards": "NFT Rewards", "kgstudio.wallet.config.label.nft-sell": "NFT Sell（OpenSea）", "kgstudio.wallet.config.label.phone": "Phone", "kgstudio.wallet.config.label.preview": "Preview", "kgstudio.wallet.config.label.pro": "Pro", "kgstudio.wallet.config.label.show-poap": "Show POAP", "kgstudio.wallet.config.label.swap": "Cryptocurrency <PERSON>p", "kgstudio.wallet.config.processing-settings": "Please wait, we're processing your settings", "kgstudio.wallet.config.promote-banner.desc": "After the app goes live, you can update your Banner at any time. If you don't need to set a Banner now, please skip this question.", "kgstudio.wallet.config.promote-banner.title": "Promote Banner", "kgstudio.wallet.config.publish-settings-confirm-title": "Publish Settings data confirmed. Do you want to submit for production now?", "kgstudio.wallet.config.publish-settings-description": "The KryptoGO team will immediately create your wallet based on your app settings and submit it for app store review along with the publishing data. If everything goes smoothly without the need for revisions, it can be on the store in about 14 working days. (If not submitting for production now, please click 'Submit' on the Wallet Project Page later)", "kgstudio.wallet.config.recommended-size": "Recommended size : 1024 x 1024 px", "kgstudio.wallet.config.retry-or-contact": "Please try again later or contact customer service", "kgstudio.wallet.config.scanInstruction": "Open the scanner on your KryptoGO Wallet and scan the QR code for local preview", "kgstudio.wallet.config.scanToDemo.title1": "<PERSON>an to De<PERSON>", "kgstudio.wallet.config.scanToDemo.title2": "on your KryptoGO Wallet", "kgstudio.wallet.config.shelf-platform-title": "Platform", "kgstudio.wallet.config.splash-file-type-supported": "File types supported: JPG, PNG. Max size: 10 MB", "kgstudio.wallet.config.splash-recommended-size": "Recommended size : 1080 x 1920 px", "kgstudio.wallet.config.splash-screen-title": "Splash Screen", "kgstudio.wallet.config.steps.check": "Check", "kgstudio.wallet.config.steps.contact": "Contact", "kgstudio.wallet.config.steps.explorer": "Browser", "kgstudio.wallet.config.steps.explorer-hint": "Users can connect to various DApps through browser (Explorer). You can customize the list of DApps recommended to users and also add banners for advertising and guidance. Rest assured, after the app goes live, you can change these settings at any time.", "kgstudio.wallet.config.steps.feature": "Feature", "kgstudio.wallet.config.steps.theme": "Theme Colors", "kgstudio.wallet.config.store-display-info": "Set your app store display data", "kgstudio.wallet.config.submission-description": "The KryptoGO team will immediately create your wallet based on your app settings and submit it for app store review. If everything goes smoothly without the need for revisions, it can be on the store in about 14 working days.", "kgstudio.wallet.config.submission-failed": "Submission failed!", "kgstudio.wallet.config.submission-failed-description": "Submission failed, please try again later", "kgstudio.wallet.config.submission-in-progress": "Wallet app submission in progress...", "kgstudio.wallet.config.submission-in-progress-description": "Please wait, app submission in progress...", "kgstudio.wallet.config.submission-successful": "Submission successful!", "kgstudio.wallet.config.submit-failed": "App settings submission failed", "kgstudio.wallet.config.submit-later": "Submit later", "kgstudio.wallet.config.submit-success": "App settings have been submitted. Would you like to proceed with publishing settings?", "kgstudio.wallet.config.submitting-app-settings": "Submitting app settings...", "kgstudio.wallet.config.tabs.dex": "DEX", "kgstudio.wallet.config.tabs.hot": "Hot", "kgstudio.wallet.config.tabs.nft": "NFT", "kgstudio.wallet.config.title.dapp-list": "DApp List", "kgstudio.wallet.config.title.displayed-asset-type": "Displayed Asset Type", "kgstudio.wallet.config.title.displayed-chains": "Displayed Blockchains", "kgstudio.wallet.config.title.enable-features": "Select the features to enable", "kgstudio.wallet.config.title.help-center-url": "Help Center URL", "kgstudio.wallet.config.title.languages": "Language", "kgstudio.wallet.config.title.login-methods": "Login Methods", "kgstudio.wallet.config.title.primary-color": "Primary Color", "kgstudio.wallet.config.title.privacy-policy-url": "Privacy Policy URL", "kgstudio.wallet.config.title.secondary-color": "Secondary Color", "kgstudio.wallet.config.title.support-email": "Support Email", "kgstudio.wallet.config.title.support-info": "Support Information", "kgstudio.wallet.config.title.terms-condition-url": "Terms & Condition URL", "kgstudio.wallet.config.title.theme": "Theme", "kgstudio.wallet.customized-dapp-list": "Customized DApp List", "kgstudio.wallet.dex": "DEX", "kgstudio.wallet.explorer-banner": "Explorer Banner", "kgstudio.wallet.extension.subtitle": "Chrome Web Store", "kgstudio.wallet.extension.title": "Extension", "kgstudio.wallet.feature.explore-dapp": "Explore - <PERSON><PERSON><PERSON> Browser", "kgstudio.wallet.feature.nft-rewards": "NFT Rewards", "kgstudio.wallet.feature.nft-sell": "NFT Sell", "kgstudio.wallet.feature-settings": "Feature Settings", "kgstudio.wallet.feature.show-kyc": "Show KYC", "kgstudio.wallet.feature.show-poap": "Show POAP", "kgstudio.wallet.feature.swap": "<PERSON><PERSON><PERSON>", "kgstudio.wallet.google-play.subtitle": "Google Play", "kgstudio.wallet.google-play.title": "Android", "kgstudio.wallet.help-center": "Help Center", "kgstudio.wallet.hot": "Hot", "kgstudio.wallet.ios.subtitle": "App Store", "kgstudio.wallet.ios.title": "iOS", "kgstudio.wallet.language.vietnamese": "Tiếng <PERSON>", "kgstudio.wallet.mock.content": "Before submitting to the app platforms, please make sure to create your app store developer account using an organization's name. According to the app stores' policy, individual developer cannot publish cryptocurrency wallet app. For more details, please visit the publishing guideline of app stores.", "kgstudio.wallet.nft": "NFT", "kgstudio.wallet.privacy-policy": "Privacy Policy", "kgstudio.wallet.processing": "Please wait, we are processing your settings.", "kgstudio.wallet.project-image": "Project Image", "kgstudio.wallet.project-name": "Project Name", "kgstudio.wallet.publish-settings": "Publish Settings", "kgstudio.wallet.setProjectTitle": "Set Your Wallet Project", "kgstudio.wallet.status.draft": "Draft", "kgstudio.wallet.status.in-review": "In Review", "kgstudio.wallet.status.published": "Published", "kgstudio.wallet.supported-chains": "Supported Chains", "kgstudio.wallet.supported-links": "Supported Links", "kgstudio.wallet.support-email": "Support Email", "kgstudio.wallet.terms-condition": "Terms & Condition", "kgstudio.wallet.theme.primary-color": "Primary Color", "kgstudio.wallet.theme.secondary-color": "Secondary Color", "kgstudio.wallet.theme.title": "Theme", "kgstudio.wallet.use-recommended-option": "Use recommended options", "page.page-size-description": "Showing {pageSize} rows per page", "permissions.notification-description": "You can view the users who have permission to access the system. If you want to edit your users, please contact the system provider.", "permissions.title": "Users", "permissions.user-id": "User ID", "send.by-email": "Email", "send.by-phone-number": "Phone Number", "send.do-not-leave-page": "Do not leave this page.", "send.loading-hint": "Transaction might take longer than expected if the blockchain is very busy. To check the transaction status and details, please click on the Tx Hash.", "send.over-limit": "Over the limit", "send.remaining-balance": "Remaining transferrable balance today: ", "send.remaining-balance-today": "Your remaining balance today is {formattedCurrentLimit} U (with a daily limit of {formattedDailyLimit} U).", "send.send-confirm-alert": "Once this transaction is confirmed, it will be affective right away and cannot be reverted! Please make sure the blockchain, amount and recipient are correct.", "send.send-to": "Send to", "send.title": "Send Fund", "send.to-user": "to user", "send.tx-failed": "Transaction Failed", "send.tx-failed-description": "Failed to transfer funds, please retry or contact the system administrator.", "send.tx-in-progress": "Transaction In Progress.", "send.tx-success": "Transaction Success!", "transactions.recipient-placeholder": "E-mail, Phone, Address", "transactions.title": "Transaction History", "transactions.user-placeholder": "ID, Address", "validation.correct-format": "Please enter the correct format", "validation.number-greater-than-zero": "Please enter a value greater than 0", "validation.phone-or-email-required": "You can only transfer funds to users who have registered for this wallet using their \"Email\" or \"Phone number\".", "validation.required": "Please enter a value", "validation.sorrect-format": "Please enter a valid format.", "validation.valid-email": "Please enter a valid email", "validation.valid-phone": "Please enter a valid phone number"}