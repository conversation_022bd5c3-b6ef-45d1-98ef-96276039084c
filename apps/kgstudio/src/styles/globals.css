@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@400;500;700&family=DM+Sans:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

@import '@kryptogo/2b/styles.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Color styles */
  --brand-primary: rgba(255, 194, 17, 1);
  --brand-primary-lighter: rgba(255, 247, 220, 1);
  --brand-primary-light: rgba(255, 238, 140, 1);
  --brand-primary-dark: rgba(217, 157, 4, 1);
  --brand-primary-darker: rgba(140, 91, 0, 1);
  --surface-secondary: rgba(244, 247, 254, 1);
  --surface-primary: rgba(255, 255, 255, 1);

  --text-primary: rgba(27, 37, 89, 1);
  --text-secondary: rgba(104, 118, 159, 1);
  --text-placeholder: rgba(176, 187, 213, 1);
  --text-disabled: rgba(176, 187, 213, 1);
  --text-contrast: rgba(250, 252, 254, 1);
  --text-highlight: rgba(255, 194, 17, 1);
  --text-description: rgb(144, 154, 182);

  --alert-error: rgba(227, 26, 26, 1);
  --alert-error-light: rgba(255, 238, 243, 1);
  --alert-success: rgba(1, 181, 116, 1);
  --alert-success-light: rgba(237, 255, 248, 1);
  --alert-warning: rgba(255, 194, 17, 1);
  --alert-warning-light: rgba(255, 246, 219, 1);
  --alert-processing: rgba(0, 190, 255, 1);

  --border-primary: rgba(225, 233, 248, 1);
  --border-focused: rgba(255, 194, 17, 1);
}

/* Due to this issue, https://github.com/radix-ui/primitives/issues/1925 */
body[style] {
  margin-right: 0px !important;
}
