import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { ExternalToast, Toaster, toast } from 'sonner';

type ToastType = 'success' | 'error' | 'info';

const showToast = (message: string | React.ReactNode, variant: ToastType = 'info') => {
  let toastOptions: ExternalToast = {
    style: {
      color: '#fff',
    },
    duration: 3000,
  };

  switch (variant) {
    case 'success':
      toastOptions = {
        ...toastOptions,
        style: {
          ...toastOptions.style,
          backgroundColor: '#48BB78',
        },
      };
      break;
    case 'error':
      toastOptions = {
        ...toastOptions,
        style: { ...toastOptions.style, backgroundColor: '#F56565' },
      };
      break;
    case 'info':
    default:
      toastOptions = {
        ...toastOptions,
        style: { ...toastOptions.style, backgroundColor: '#4299E1' },
      };
      break;
  }

  toast(message, toastOptions);
};

/**
 * Show toast when error happens, and call onError callback
 */
// FIXME: decouple this hook from i18n hooks
const useErrorResponseToast = (error: { code?: number; status?: number } | null, onError?: () => void) => {
  const t = useTranslations();
  useEffect(() => {
    if (error?.code === 1005) {
      showToast(t('kgstudio.error.no-access'), 'error');
      onError?.();
      return;
    }
    if (error) {
      showToast(`${t('kgstudio.error.general-error')} ${error.code}`, 'error');
      onError?.();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [error]);
};

export { Toaster as ToastContainer, showToast, useErrorResponseToast };
