import { NextSeo } from 'next-seo';

export interface ISEOProps {
  title: string;
  description: string;
  url: string;
  image: string;
}

const SEO = ({ title, description, url, image }: ISEOProps) => {
  return (
    <NextSeo
      title={title}
      description={description}
      canonical={url}
      openGraph={{
        type: 'website',
        url,
        title,
        description,
        images: [
          {
            url: image,
            width: 800,
            height: 600,
            alt: 'Og Image Alt',
          },
        ],
        site_name: title,
      }}
      twitter={{
        handle: '@kryptogo_',
        site: title,
        cardType: image,
      }}
    />
  );
};

export default SEO;
