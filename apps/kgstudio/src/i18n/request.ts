import { getRequestConfig } from 'next-intl/server';

import { getMessages, getMessagesFromLocalizely } from '@/app/_common/lib/i18n';
import { env } from '@/env.mjs';

import { routing } from './routing';

// Helper function to check if locale is valid
function isValidLocale(locale: string, locales: readonly string[]): boolean {
  return locales.includes(locale);
}

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  let locale = await requestLocale;

  // Ensure that a valid locale is used
  if (!locale || !isValidLocale(locale, routing.locales)) {
    locale = routing.defaultLocale;
  }

  // Get messages based on environment
  const messages =
    env.NODE_ENV === 'development' || env.NEXT_PUBLIC_CI === 'true'
      ? await getMessages(locale)
      : await getMessagesFromLocalizely(locale);

  return {
    locale: locale as (typeof routing.locales)[number],
    messages,
  };
});
