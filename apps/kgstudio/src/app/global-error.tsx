'use client';

import { useEffect } from 'react';
import { match } from 'ts-pattern';

import { Footer } from '@/app/_common/components';
import * as Sentry from '@sentry/nextjs';

import { StatusFeedback } from './_common/components';

export default function Error({ error }: { error: Error; reset: () => void }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  const content = match(error.message).otherwise(() => ({
    message: {
      title: 'Sorry, something went wrong',
      description: 'Some error occurred, please try again later.',
    },
    link: {
      href: '/',
      text: 'Go Back',
    },
  }));

  return (
    <html>
      <body>
        <div className="relative">
          <div className="flex h-screen items-center justify-center">
            <StatusFeedback>
              <StatusFeedback.Logo />
              <StatusFeedback.Content message={content.message} />
              <StatusFeedback.LinkButton href={content.link.href}>{content.link.text}</StatusFeedback.LinkButton>
            </StatusFeedback>
          </div>
          <Footer />
        </div>
      </body>
    </html>
  );
}
