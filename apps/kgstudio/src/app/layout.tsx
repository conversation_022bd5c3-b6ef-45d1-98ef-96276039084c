import type { Metadata } from 'next';
import { Outfit } from 'next/font/google';

import { cn } from '@/app/_common/lib/utils';
// These styles apply to every route in the application
import '@/styles/globals.css';

import { GoogleAdsTags, MetaPixel } from './_common/components/AdsTags';
import Providers from './provider';

const outfit = Outfit({
  weight: ['300', '400', '700'],
  variable: '--font-outfit',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'KryptoGO Studio - Web3 Infrastructure for Your DApps',
  description:
    'Explore KryptoGO Studio, the comprehensive Wallet as a Service platform. Drive customer acquisition, engagement, and loyalty in the Web3 ecosystem. Manage Forms, KYC Profiles, Wallet Explorer, User Management, NFT utility, community engagement, verification, and more.',
  //TODO: Add more metadata here
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html className={cn(outfit.variable)}>
      <body className="bg-[var(--text-contrast)]">
        <GoogleAdsTags />
        <MetaPixel />
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
