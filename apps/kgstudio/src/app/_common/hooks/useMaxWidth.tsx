import { useId, useLayoutEffect } from 'react';

const useMaxWidth = () => {
  const id = useId();
  const sanitizedId = id.replace(/[^a-zA-Z0-9-]/g, '');
  const containerId = `mw-container-${sanitizedId}`;
  const itemId = `mw-item-${sanitizedId}`;

  useLayoutEffect(() => {
    const maxWidthContainer = document.querySelector<HTMLElement>(`#${containerId}`);
    if (!maxWidthContainer) return;

    const maxWidthElements = maxWidthContainer.querySelectorAll<HTMLElement>(`#${itemId}`);
    if (!maxWidthElements) return;

    let maxWidth = 0;
    maxWidthElements.forEach((element) => {
      const width = element.clientWidth;
      if (width > maxWidth) maxWidth = width;

      element.style.width = `var(--max-width-${sanitizedId})`;
      element.style.flexBasis = `var(--max-width-${sanitizedId})`;
      element.style.flexShrink = `0`;
      element.style.whiteSpace = `nowrap`;
    });

    maxWidthContainer.style.setProperty(`--max-width-${sanitizedId}`, `${maxWidth}px`);
  }, [sanitizedId, containerId, itemId]);

  return {
    _id: sanitizedId,
    containerId,
    itemId,
  };
};

export { useMaxWidth };
