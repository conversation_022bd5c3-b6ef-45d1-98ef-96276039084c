import WAValidator from 'multicoin-address-validator';
import { useTranslations } from 'next-intl';
import { useCallback, useMemo } from 'react';
import { match } from 'ts-pattern';

import { ChainId } from '@kryptogo/utils';

import { ValidationResult } from '../types';

const validateAddressLogic = (address: string, symbol: string, t: (key: string) => string): ValidationResult => {
  if (!address) {
    return { isValid: false, errorMessage: t('kgstudio.validation.required') };
  }

  if (symbol === 'base') {
    return { isValid: true };
  }

  const valid = WAValidator.validate(address, symbol);
  if (!valid) {
    return { isValid: false, errorMessage: t('kgstudio.validation.valid-address') };
  }
  return { isValid: true };
};

const useAddressValidation = (blockchain: ChainId) => {
  const t = useTranslations();
  const symbol = useMemo(() => {
    return match(blockchain)
      .with('sepolia', () => 'eth')
      .with('tron', 'shasta', () => 'trx')
      .with('bsc', () => 'bnb')
      .otherwise(() => blockchain);
  }, [blockchain]);

  const validateAddress = useCallback((address: string) => validateAddressLogic(address, symbol, t), [symbol, t]);
  return validateAddress;
};

export { useAddressValidation };
