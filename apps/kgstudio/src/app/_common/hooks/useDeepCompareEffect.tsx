import { isEqual } from 'lodash-es';
import { useEffect, useRef } from 'react';

const useDeepCompareEffect = <T,>(callback: () => void, dependencies: T) => {
  const currentDependenciesRef = useRef<T>();

  if (!isEqual(currentDependenciesRef.current, dependencies)) {
    currentDependenciesRef.current = dependencies;
  }

  //   eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(callback, [currentDependenciesRef.current]);
};

export { useDeepCompareEffect };
