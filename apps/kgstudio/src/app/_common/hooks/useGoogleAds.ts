'use client';

type ConversionParams = {
  url?: string;
  sendTo?: string;
  value?: number;
  currency?: string;
};

export function useGoogleAds() {
  const trackConversion = (params: ConversionParams = {}) => {
    if (typeof window !== 'undefined' && window.gtag_report_conversion) {
      return window.gtag_report_conversion(params.url, params.sendTo, params.value, params.currency);
    }
    return false;
  };

  return {
    trackConversion,
  };
}

// Add type definition for the global window object
declare global {
  interface Window {
    gtag_report_conversion: (url?: string, sendTo?: string, value?: number, currency?: string) => boolean;
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}
