import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Table as TableComponent, TableProps } from '@kryptogo/2b';

export const useTable = () => {
  const t = useTranslations();
  const [currentPage, setCurrentPage] = useState(1);

  const Table = <T,>({ data, columnDefs, pageSize, totalCount, pageSort, setPageSort, tooltip }: TableProps<T>) => {
    return (
      <TableComponent
        data={data}
        columnDefs={columnDefs}
        pageSize={pageSize}
        totalCount={totalCount}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSort={pageSort}
        setPageSort={setPageSort}
        tooltip={tooltip}
        // FIXME: since intlProvider is not working in package kryptogo-2b, we need to pass the messages manually
        noDataMessage={t('common.no-data-available')}
        pageSizeDescription={t('kgstudio.page.page-size-description', { pageSize })}
      />
    );
  };

  return {
    Table,
    currentPage,
    setCurrentPage,
  };
};
