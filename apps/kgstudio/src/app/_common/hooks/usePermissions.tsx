import { useState } from 'react';

import { useDeepCompareEffect } from '@/app/_common/hooks';
import { Module } from '@/app/_common/services/organization/model';
import { useAuthStore } from '@/app/_common/store';

type Permission = {
  asset_pool: ['read'];
  transaction: ['apply', 'read', 'read_all', 'approve', 'release'];
  asset_pro_operator: ['edit', 'read'];
  transaction_quota_admin: ['edit'];
  transaction_quota_approver: ['edit'];
  transaction_quota_trader: ['edit'];
  case_submission: ['edit', 'read'];
  project: ['edit'];
  user_360_statistics_compliance: ['read'];
  user_360_statistics_asset_pro: ['read'];
  asset_pro_product: ['read', 'edit'];
  asset_pro_order: ['read'];
  asset_pro_market_info: ['read'];
  member: ['read', 'edit'];
  organization_info: ['edit'];
  asset_pro_liquidity: ['read', 'edit'];
};

const PERMISSION_MODULE_MAP: Record<string, [keyof Module, string]> = {
  'read:asset_pool': ['asset_pro', 'treasury'],
  'apply:transaction': ['asset_pro', 'send_token'],
  'read:transaction': ['asset_pro', 'transaction_history'],
  'read_all:transaction': ['asset_pro', 'transaction_history'],
  'approve:transaction': ['asset_pro', 'transaction_history'],
  'release:transaction': ['asset_pro', 'transaction_history'],
  'edit:asset_pro_operator': ['asset_pro', 'operators'],
  'read:asset_pro_operator': ['asset_pro', 'operators'],
  'edit:transaction_quota_admin': ['asset_pro', 'operators'],
  'edit:transaction_quota_approver': ['asset_pro', 'operators'],
  'edit:transaction_quota_trader': ['asset_pro', 'operators'],
  'edit:case_submission': ['compliance', 'case_management'],
  'read:case_submission': ['compliance', 'case_management'],
  'edit:project': ['nft_boost', 'campaign'],
  'read:user_360_statistics_compliance': ['user_360', 'data'],
  'read:user_360_statistics_asset_pro': ['user_360', 'data'],
  'read:asset_pro_product': ['asset_pro', 'market'],
  'edit:asset_pro_product': ['asset_pro', 'market'],
  'read:asset_pro_order': ['asset_pro', 'market'],
  'read:asset_pro_market_info': ['asset_pro', 'market'],
  'read:asset_pro_revenue': ['asset_pro', 'revenue'],
};

type PermissionTuple = {
  [K in keyof Permission]: Permission[K] extends Array<infer A> ? [K, A] : never;
}[keyof Permission];

const checkPermissionModuleMap = (permission: string, modules: Module) => {
  const moduleKeys = PERMISSION_MODULE_MAP[permission];
  if (!moduleKeys) {
    return true;
  }

  const rootKey = moduleKeys[0];
  if (!modules[rootKey]) {
    return false;
  }

  const subKey = moduleKeys[1];
  return (modules[rootKey] as string[]).includes(subKey);
};

// NOTE: Please note that the `usePermissions` hook returns an initial value of "[]" if the comparison has not been done yet,
// which causes the values destructed from usePermission to be "undefined"
export function usePermissions(...permissionsToCheck: PermissionTuple[]) {
  const userInfo = useAuthStore((state) => state.userInfo);
  const [permissionResults, setPermissionResults] = useState<boolean[]>([]);

  useDeepCompareEffect(() => {
    if (!userInfo?.permissions) {
      setPermissionResults(permissionsToCheck.map(() => false));
      return;
    }
    const results = permissionsToCheck.map(([resource, action]) => {
      const hasPermission =
        userInfo.permissions.find((permission) => permission.resource === resource && permission.action === action) &&
        checkPermissionModuleMap(`${action}:${resource}`, userInfo.modules);

      return !!hasPermission;
    });

    setPermissionResults(results);
  }, [permissionsToCheck, userInfo]);

  return permissionResults;
}
