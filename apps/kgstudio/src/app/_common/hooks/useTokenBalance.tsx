import BigNumber from 'bignumber.js';
import * as O from 'fp-ts/Option';
import * as A from 'fp-ts/ReadonlyArray';
import { pipe } from 'fp-ts/lib/function';
import { useMemo } from 'react';
import { formatUnits } from 'viem';

import { TRONGRID_BASE_URL, TRONGRID_BASE_URL_SHASTA } from '@/app/_common/constant';
import { getClient } from '@/app/_common/lib/clients';
import { readOnlyArrayIncludes } from '@/app/_common/lib/utils';
import { apiOrganizationHooks, proxyHooks } from '@/app/_common/services';
import { AssetProChainId } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import { ASSETPRO_SUPPORTED_TRON_CHAINS, ERC20ABI, formatCurrency, getChainInfo } from '@kryptogo/utils';
import { useQuery } from '@tanstack/react-query';

type Token = {
  chainId: AssetProChainId;
  contractAddress: string;
  symbol: string;
  decimals: number;
};

interface UseTokenBalanceProps {
  token: Token;
  txTokenAmount?: string;
}

const useTokenBalance = ({ token, txTokenAmount }: UseTokenBalanceProps) => {
  const orgId = useOrganizationStore((state) => state.orgId);
  const isTronLikeChain = readOnlyArrayIncludes(ASSETPRO_SUPPORTED_TRON_CHAINS, token.chainId);
  const txTokenAmountBN = txTokenAmount ? BigNumber(txTokenAmount) : undefined;

  const { data: orgAccountsData } = apiOrganizationHooks.useGetOrganizationAccounts(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
    },
  );
  const { mutateAsync: getTronBalanceAsync, error: tronBalanceError } = proxyHooks.useProxy();

  const address = useMemo(() => {
    return pipe(
      O.fromNullable(orgAccountsData?.data),
      O.chain(A.findFirst((account) => account.chain_id === token.chainId)),
      O.map((account) => account.address),
      O.getOrElseW(() => undefined),
    );
  }, [orgAccountsData, token.chainId]);

  const { data: tronTokenBalance, isLoading: tronTokenBalanceLoading } = useQuery({
    queryKey: ['tron', token.chainId, token.contractAddress, address],
    queryFn: async () => {
      const baseUrl = token.chainId === 'tron' ? TRONGRID_BASE_URL : TRONGRID_BASE_URL_SHASTA;

      const { data } = await getTronBalanceAsync({
        path: `${baseUrl}/v1/accounts/${address}`,
      });
      return data;
    },
    enabled: isTronLikeChain && !!address,
    select: (data) => {
      if (Array.isArray(data) && data.length > 0) {
        const balance = data[0].trc20.find((item) => item[token.contractAddress]);
        return balance ? BigNumber(balance[token.contractAddress]).dividedBy(1e6) : BigNumber(0);
      }
    },
  });

  const {
    data: erc20TokenBalance,
    error: erc20TokenBalanceError,
    isLoading: erc20TokenBalanceLoading,
  } = useQuery({
    queryKey: ['evm', token.chainId, token.contractAddress, address],
    queryFn: () => {
      const client = getClient(token.chainId);

      return client?.readContract({
        address: token.contractAddress as `0x${string}`,
        abi: ERC20ABI,
        functionName: 'balanceOf',
        args: [address as `0x${string}`],
      });
    },
    enabled: !isTronLikeChain && !!address && token.contractAddress.includes('0x') && !!token.contractAddress,
    select: (data) => {
      if (data === undefined) return;
      return BigNumber(formatUnits(data, token.decimals));
    },
  });

  const error = isTronLikeChain ? tronBalanceError : erc20TokenBalanceError;
  const isLoading = isTronLikeChain ? tronTokenBalanceLoading : erc20TokenBalanceLoading;

  const tokenBalance = isTronLikeChain ? tronTokenBalance : erc20TokenBalance;
  const formattedTokenBalance = tokenBalance ? formatCurrency({ amount: tokenBalance }) : undefined;

  const shortfallAmount = tokenBalance ? txTokenAmountBN?.minus(tokenBalance) : undefined;
  const formattedShortfallAmount = shortfallAmount ? formatCurrency({ amount: shortfallAmount }) : undefined;
  const isBalanceEnough = shortfallAmount ? shortfallAmount.isLessThanOrEqualTo(0) : false;

  return {
    isLoading,
    error,
    tokenBalance,
    formattedTokenBalance,
    shortfallAmount,
    formattedShortfallAmount,
    isBalanceEnough,
  };
};

export { useTokenBalance };
