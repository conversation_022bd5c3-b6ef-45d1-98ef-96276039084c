import Image from 'next/image';
import { ReactNode, useEffect, useState } from 'react';

import { TokenListResponse } from '@/app/[locale]/(protected)/asset/_types';
import { ChainId, getChainIcon } from '@kryptogo/utils';

export const useTokenSelection = (
  tokenListData: TokenListResponse | undefined,
  blockchain: ChainId | null,
  token: string,
  setBlockchain: (chain: ChainId | null) => void,
  setToken: (token: string) => void,
) => {
  const [blockchainOptions, setBlockchainOptions] = useState<{ value: ChainId; label: ReactNode; symbol: string }[]>(
    [],
  );
  const [tokenOptions, setTokenOptions] = useState<
    { value: string; label: ReactNode; symbol: string; logoUrl: string }[]
  >([]);

  useEffect(() => {
    const blockchainOptionsSet = [
      ...new Set(
        tokenListData?.data?.map((token) =>
          JSON.stringify({
            value: token.chain_id,
            label: token.chain_name,
          }),
        ),
      ),
    ];
    const opts: { value: ChainId; label: string }[] = [...blockchainOptionsSet.map((t) => JSON.parse(t))];
    const optsWithSymbol = opts.map((o) => ({
      ...o,
      symbol: o.label,
      label: (
        <div className="flex items-center gap-2">
          <Image className="rounded-full" src={getChainIcon(o.value) ?? ''} alt={o.value} width={24} height={24} />
          {o.label}
        </div>
      ),
    }));

    setBlockchainOptions(optsWithSymbol);
    setBlockchain(optsWithSymbol.length > 0 ? optsWithSymbol[0]?.value : null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tokenListData]);

  useEffect(() => {
    const opts =
      tokenListData?.data
        ?.filter((t) => t.chain_id == blockchain)
        .map((t) => ({
          value: t.contract_address,
          label: (
            <div className="flex items-center gap-2">
              <Image src={t.logo_url} alt={t.contract_address} width={24} height={24} />
              <p>{t.symbol}</p>
            </div>
          ),
          symbol: t.symbol,
          logoUrl: t.logo_url,
        })) ?? [];
    setTokenOptions(opts);
    setToken(opts.length > 0 ? opts[0]?.value : '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blockchain]);

  const selectedBlockchain = () => {
    const selected = blockchainOptions.find((t) => t.value == blockchain);
    if (!selected) {
      return { value: '', label: '', symbol: '' };
    }

    return selected;
  };
  const selectedToken = () => {
    const selected = tokenOptions.filter((t) => t.value == token);

    if (selected.length == 0) {
      return { value: '', label: '', logoUrl: '', symbol: '' };
    }

    return selected[0];
  };

  return {
    blockchainOptions,
    tokenOptions,
    selectedBlockchain: selectedBlockchain(),
    selectedToken: selectedToken(),
  };
};
