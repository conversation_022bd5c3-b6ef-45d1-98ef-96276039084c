import { useTranslations } from 'next-intl';
import { phone } from 'phone';
import { useCallback } from 'react';

import { ValidationResult } from '../types';

const validatePhoneNumberLogic = (value: string, countryCode: string, t: (key: string) => string): ValidationResult => {
  if (!value) {
    return { isValid: false, errorMessage: t('kgstudio.validation.required') };
  }

  const fullPhoneNumber = `+${countryCode}${value}`;

  if (!phone(fullPhoneNumber).isValid) {
    return { isValid: false, errorMessage: t('kgstudio.validation.valid-phone') };
  }
  return { isValid: true };
};

const usePhoneNumberValidation = (countryCode: string) => {
  const t = useTranslations();

  const validatePhoneNumber = useCallback(
    (value: string) => validatePhoneNumberLogic(value, countryCode, t),
    [countryCode, t],
  );
  return validatePhoneNumber;
};

export { usePhoneNumberValidation };
