import { useState, useEffect, useRef, useCallback } from 'react';

const useCountdown = (initialCount: number, intervalMs = 1000) => {
  const [count, setCount] = useState(initialCount);
  const intervalRef = useRef<number | null>(null);

  const startCountdown = useCallback(() => {
    if (intervalRef.current !== null) return;

    intervalRef.current = window.setInterval(() => {
      setCount((prevCount) => {
        if (prevCount <= 1) {
          window.clearInterval(intervalRef.current as number);
          intervalRef.current = null;
          return 0;
        }
        return prevCount - 1;
      });
    }, intervalMs);
  }, [intervalMs]);

  const resetCountdown = useCallback(() => {
    if (!!intervalRef.current) {
      window.clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setCount(initialCount);
  }, [initialCount]);

  const restartCountdown = useCallback(() => {
    resetCountdown();
    startCountdown();
  }, [resetCountdown, startCountdown]);

  useEffect(() => {
    return () => {
      if (!intervalRef.current) return;
      window.clearInterval(intervalRef.current);
    };
  }, []);

  return { count, startCountdown, resetCountdown, restartCountdown };
};

export { useCountdown };
