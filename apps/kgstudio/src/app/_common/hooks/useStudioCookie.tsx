import { useState } from 'react';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { Studio<PERSON>ookieKey } from '@/app/_common/constant';
import { <PERSON>ieOperator, createCookieOperator } from '@/app/_common/lib/cookie';

function useStudioCookie(key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>): CookieOperator<string>;
function useStudioCookie<T extends z.ZodType<string | Record<string, unknown>>>(
  key: StudioCook<PERSON>K<PERSON>,
  schema?: T,
): CookieOperator<z.infer<T>>;
function useStudioCookie<T extends z.ZodType<string | Record<string, unknown>>>(key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, schema?: T) {
  const [operator] = useState(() =>
    match(schema)
      .with(undefined, () => createCookieOperator(key))
      .otherwise((s) => createCookieOperator(key, s)),
  );

  return operator;
}

export { useStudioCookie };
