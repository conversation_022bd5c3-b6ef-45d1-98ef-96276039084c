import { useState, useEffect } from 'react';

type DeviceSize = 'sm' | 'md' | 'lg';

export function useDeviceSize() {
  const [deviceSize, setDeviceSize] = useState<DeviceSize>('lg');

  useEffect(() => {
    const getDeviceSize = () => {
      if (typeof window === 'undefined') return;

      const width = window.innerWidth;
      let size: DeviceSize = 'lg';

      if (width < 768) {
        size = 'sm';
      } else if (width >= 768 && width < 1280) {
        size = 'md';
      } else {
        size = 'lg';
      }
      setDeviceSize(size);
    };

    getDeviceSize();
    window.addEventListener('resize', getDeviceSize);

    return () => {
      window.removeEventListener('resize', getDeviceSize);
    };
  }, []);

  return { deviceSize };
}
