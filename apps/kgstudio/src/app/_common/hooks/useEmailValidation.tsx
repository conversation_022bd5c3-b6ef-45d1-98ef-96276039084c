import { useTranslations } from 'next-intl';
import { useCallback } from 'react';

import { ValidationResult } from '../types';

const validateEmailLogic = (value: string, t: (key: string) => string): ValidationResult => {
  if (!value) {
    return { isValid: false, errorMessage: t('kgstudio.validation.required') };
  }

  // Regular expression for basic email validation
  const emailRegEx = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegEx.test(value)) {
    return { isValid: false, errorMessage: t('kgstudio.validation.valid-email') };
  }
  return { isValid: true };
};

const useEmailValidation = () => {
  const t = useTranslations();

  const validateEmail = useCallback((value: string) => validateEmailLogic(value, t), [t]);
  return validateEmail;
};

export { useEmailValidation };
