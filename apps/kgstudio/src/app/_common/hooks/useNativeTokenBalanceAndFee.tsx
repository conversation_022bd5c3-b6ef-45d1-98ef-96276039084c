import BigNumber from 'bignumber.js';
import * as O from 'fp-ts/Option';
import * as A from 'fp-ts/ReadonlyArray';
import { pipe } from 'fp-ts/lib/function';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { encodeFunctionData, formatEther } from 'viem';

import { TRONGRID_BASE_URL, TRONGRID_BASE_URL_SHASTA } from '@/app/_common/constant';
import axios from '@/app/_common/lib/axios/instances/external';
import { getClient } from '@/app/_common/lib/clients';
import { readOnlyArrayIncludes } from '@/app/_common/lib/utils';
import { apiOrganizationHooks, proxyHooks } from '@/app/_common/services';
import { AssetProChainId } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import { ASSETPRO_SUPPORTED_TRON_CHAINS, ERC20ABI, formatCurrency, getChainInfo } from '@kryptogo/utils';
import { useQuery } from '@tanstack/react-query';

interface TronTriggerContractResp {
  result: {
    result: boolean;
  };
  energy_used: number;
  constant_result: string[];
}

interface UseNativeTokenBalanceAndFeeProps {
  chainId: AssetProChainId;
  contractAddress: string;
  tokenBalance?: BigNumber;
  isGasless?: boolean;
}

const TR0N_ENERGY_PRICE = 420;
const TRON_MAIN_TOKEN_GAS_FEE = 1.3; // The gas fee for transfer tron main token is a fixed value
const RECEIVER_ADDRESS_EVM = '0x0000000000000000000000000000000000000001';
const RECEIVER_ADDRESS_TRON = '0x4100000000000000000000000000000000000000';

const useNativeTokenBalanceAndFee = ({
  chainId,
  contractAddress,
  tokenBalance,
  isGasless,
}: UseNativeTokenBalanceAndFeeProps) => {
  const orgId = useOrganizationStore((state) => state.orgId);
  const isTronLikeChain = readOnlyArrayIncludes(ASSETPRO_SUPPORTED_TRON_CHAINS, chainId);
  const nativeTokenInfo = getChainInfo(chainId);
  const client = getClient(chainId);

  const [gasFee, setGasFee] = useState<string | undefined>(undefined);

  const { data: orgAccountsData } = apiOrganizationHooks.useGetOrganizationAccounts(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
    },
  );
  const { mutateAsync: getTronBalanceAsync, error: tronBalanceError } = proxyHooks.useProxy();

  const address = useMemo(() => {
    return pipe(
      O.fromNullable(orgAccountsData?.data),
      O.chain(A.findFirst((account) => account.chain_id === chainId)),
      O.map((account) => account.address),
      O.getOrElseW(() => undefined),
    );
  }, [orgAccountsData, chainId]);

  const { data: tronNativeTokenBalance, isLoading: tronNativeTokenBalanceLoading } = useQuery({
    queryKey: ['tron', chainId, contractAddress, nativeTokenInfo.symbol],
    queryFn: async () => {
      const baseUrl = chainId === 'tron' ? TRONGRID_BASE_URL : TRONGRID_BASE_URL_SHASTA;

      const { data } = await getTronBalanceAsync({
        path: `${baseUrl}/v1/accounts/${address}`,
      });
      return data;
    },
    enabled: isTronLikeChain && !!address,
    select: (data) => {
      if (Array.isArray(data) && data.length > 0) {
        return BigNumber(data[0].balance).dividedBy(1e6);
      }
    },
  });

  const {
    data: evmNativeTokenBalance,
    isLoading: evmNativeTokenBalanceLoading,
    error: evmNativeTokenBalanceError,
  } = useQuery({
    queryKey: ['evm', chainId, contractAddress, nativeTokenInfo.symbol],
    queryFn: () => {
      return client?.getBalance({
        address: address as `0x${string}`,
      });
    },
    enabled: !isTronLikeChain && !!address,
    select: (data) => {
      if (data === undefined) return;
      return BigNumber(formatEther(data));
    },
  });

  const retrieveTronGasFee = useCallback(async (address: string, contractAddress: string, chainId: AssetProChainId) => {
    // If the contract address is empty, it means the user is transferring trx token
    if (contractAddress === '') {
      setGasFee(TRON_MAIN_TOKEN_GAS_FEE.toString());
      return;
    }

    const data = encodeFunctionData({
      abi: [
        {
          name: 'transfer',
          type: 'function',
          inputs: [
            { name: 'to', type: 'address' },
            { name: 'value', type: 'uint256' },
          ],
        },
      ],
      functionName: 'transfer',
      args: [RECEIVER_ADDRESS_TRON, BigInt(1)],
    });

    const baseUrl = chainId === 'tron' ? TRONGRID_BASE_URL : TRONGRID_BASE_URL_SHASTA;
    const res = await axios.post<TronTriggerContractResp>(`${baseUrl}/wallet/triggerconstantcontract`, {
      owner_address: address,
      contract_address: contractAddress,
      function_selector: 'transfer(address,uint256)',
      parameter: data.slice(2), // remove 0x prefix
      visible: true,
    });

    const gasFee = ((res.data.energy_used * TR0N_ENERGY_PRICE) / 1_000_000).toString();
    setGasFee(gasFee);
  }, []);

  const retrieveEvmGasFee = useCallback(async (address: string, contractAddress: string) => {
    const tokenBalanceIsZero = !!tokenBalance ? tokenBalance.isZero() : true;

    const data = encodeFunctionData({
      abi: ERC20ABI,
      functionName: 'transfer',
      args: [RECEIVER_ADDRESS_EVM, tokenBalanceIsZero ? BigInt(0) : BigInt(1)],
    });
    const gasLimit = await client?.estimateGas({
      data,
      account: address as `0x${string}`,
      to: !!contractAddress ? (contractAddress as `0x${string}`) : RECEIVER_ADDRESS_EVM,
    });
    const gasPriceInWei = await client?.getGasPrice();

    if (gasLimit && gasPriceInWei) {
      const gasFeeInWei = BigNumber(gasLimit.toString()).multipliedBy(BigNumber(gasPriceInWei.toString()));
      const gasFee = formatEther(BigInt(gasFeeInWei.toNumber()));
      setGasFee(gasFee);
    }
  }, []);

  useEffect(() => {
    const shouldRetrieveGasFee =
      address &&
      ((isTronLikeChain && !contractAddress.includes('0x')) || (!isTronLikeChain && contractAddress.includes('0x')));

    if (shouldRetrieveGasFee || contractAddress === '') {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      isTronLikeChain
        ? retrieveTronGasFee(address as string, contractAddress, chainId)
        : retrieveEvmGasFee(address as string, contractAddress);
    }
  }, [address, contractAddress, chainId, isTronLikeChain, retrieveEvmGasFee, retrieveTronGasFee]);

  const isLoading = isTronLikeChain ? tronNativeTokenBalanceLoading : evmNativeTokenBalanceLoading;
  const error = isTronLikeChain ? tronBalanceError : evmNativeTokenBalanceError;

  let nativeTokenBalance = isTronLikeChain ? tronNativeTokenBalance : evmNativeTokenBalance;
  // FIXME workaround for cypress intercept (eth_estimateGas, eth_call, eth_gasPrice with different payload but same URL)
  // https://github.com/cypress-io/cypress/issues/8560
  if (window.Cypress) {
    nativeTokenBalance = BigNumber(1);
  }
  const formattedNativeTokenBalance = nativeTokenBalance ? formatCurrency({ amount: nativeTokenBalance }) : undefined;

  const gasFeeBN = BigNumber(gasFee ?? 0);
  const nativeTokenBalanceBN = BigNumber(nativeTokenBalance ?? 0);

  const nativeShortfallAmount = gasFeeBN.isGreaterThan(nativeTokenBalanceBN)
    ? gasFeeBN.minus(nativeTokenBalanceBN)
    : BigNumber(0);
  const formattedNativeTokenShortfallAmount = nativeShortfallAmount.isZero()
    ? undefined
    : formatCurrency({ amount: nativeShortfallAmount });

  const formattedFee = gasFee ? formatCurrency({ amount: gasFee }) : undefined;

  const isFeeEnough = isGasless ? true : nativeShortfallAmount.isLessThanOrEqualTo(0);

  return {
    isLoading,
    error,
    gasFee,
    formattedFee,
    nativeTokenBalance,
    formattedNativeTokenBalance,
    nativeShortfallAmount,
    formattedNativeTokenShortfallAmount,
    isFeeEnough,
    nativeTokenSymbol: nativeTokenInfo.symbol,
  };
};

export { useNativeTokenBalanceAndFee };
