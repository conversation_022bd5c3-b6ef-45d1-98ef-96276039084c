import { useCallback, useEffect, useRef } from 'react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

const useReCaptcha = () => {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const recaptchaToken = useRef<string | null>(null);

  const verifyReCaptcha = useCallback(async () => {
    if (!executeRecaptcha) {
      console.log('Execute recaptcha not yet available');
      return;
    }

    const token = await executeRecaptcha('login');

    recaptchaToken.current = token;
  }, [executeRecaptcha]);

  useEffect(() => {
    verifyReCaptcha();
  }, [verifyReCaptcha]);

  return recaptchaToken;
};
export { useReCaptcha };
