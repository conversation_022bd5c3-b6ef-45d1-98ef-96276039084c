import { flatten } from 'lodash-es';
import { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { useCreateSignedUrls } from '@/app/[locale]/(protected)/wallet/projects/_services/command/createSignedUrls';
import { createError } from '@/app/_common/lib/error';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';

export type Media = {
  dataURL: string;
  file: File;
};

type UploadResult = {
  data: Record<string, string[]> | null; // File URLs array of the input files
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isIdle: boolean;
  progress: number | null;
  uploadAllFiles: (mediasWithKey: Record<string, Media[]>, module: string) => Promise<Record<string, string[]> | null>;
  reset: () => void;
};

type UseGcpFileUploadHook = () => UploadResult;

const uploadFilesError = createError('uploadFileError', 'Error uploading files');

const useGcpFileUpload: UseGcpFileUploadHook = () => {
  const orgId = useOrganizationStore((state) => state.orgId);
  const [data, setData] = useState<Record<string, string[]> | null>(null);
  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isIdle, setIsIdle] = useState(true);
  const [filesProgress, setFilesProgress] = useState<Map<Media, number> | null>(null);

  const progress =
    filesProgress &&
    Array.from(filesProgress.values()).reduce((acc, current) => acc + current, 0) / (filesProgress.size * 100);

  const isSuccess = data !== null && !isError && !isLoading;

  const { mutateAsync: createSignedUrls } = useCreateSignedUrls();

  const uploadFileToGcp = async (media: Media, signedUrl: string): Promise<string> => {
    const fileToUpload = getFileFromBase64(media);
    return new Promise<string>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('PUT', signedUrl, true);
      xhr.setRequestHeader('x-goog-acl', 'public-read');
      xhr.setRequestHeader('Content-Type', 'application/octet-stream');

      xhr.upload.onprogress = (event) => {
        const uploadedBytes = event.loaded;
        const totalBytes = event.total;
        const currentProgress = (uploadedBytes / totalBytes) * 100;
        setFilesProgress((prevFilesProgress) => {
          const newFilesProgress = new Map(prevFilesProgress);
          newFilesProgress.set(media, currentProgress);
          return newFilesProgress;
        });
      };

      xhr.onreadystatechange = () => {
        if (xhr.readyState !== 4) {
          return;
        }
        if (xhr.status === 200) {
          resolve(xhr.response);
        } else {
          const gcpApiError = Object.assign(new Error(`Upload To GCP failed`), {
            name: 'GcpApiError',
            status: xhr.status,
            path: signedUrl,
          });

          reject(uploadFilesError(gcpApiError));
        }
      };

      xhr.onerror = () => {
        const networkError = createError(
          'NetworkError',
          `
          Some network error occurred while uploading media ${media.file.name} to ${signedUrl}
        `,
        );

        reject(uploadFilesError(networkError));
      };

      xhr.send(fileToUpload);
    });
  };

  const uploadAllFiles = async (mediasWithKey: Record<string, Media[]>, module: string) => {
    if (isIdle) {
      setIsIdle(false);
    }
    setIsLoading(true);

    try {
      const {
        data: { signed_urls: signedUrls },
      } = await createSignedUrls({
        files: Object.keys(mediasWithKey).reduce((acc, key) => {
          const files = mediasWithKey[key];
          const randomedFileNames = files.map((media) => `${uuidv4()}-${media.file.name}`);
          return {
            ...acc,
            [key]: randomedFileNames,
          };
        }, {}),
        module,
        org_id: orgId ?? -1,
      });
      const defaultFilesProgressArray = flatten(Object.values(mediasWithKey)).map((media) => [media, 0]) as [
        Media,
        number,
      ][];
      setFilesProgress(new Map(defaultFilesProgressArray));
      const promises: Promise<string>[] = [];

      Object.keys(mediasWithKey).forEach((key) => {
        const medias = mediasWithKey[key];
        const signedUrlsForMedias = signedUrls[key];

        if (!signedUrlsForMedias || medias.length !== signedUrlsForMedias.length) {
          throw uploadFilesError('Key of mediasWithKey and signedUrls must be the same');
        }

        medias.forEach((media, index) => {
          promises.push(uploadFileToGcp(media, signedUrlsForMedias[index]));
        });
      });

      await Promise.all(promises);

      const processedSignedUrls = Object.keys(signedUrls).reduce((acc, key) => {
        const signedUrlsForMedias = signedUrls[key];
        const transformedSignedUrls = signedUrlsForMedias.map((signedUrl) => signedUrl.split('?')[0]);

        return {
          ...acc,
          [key]: transformedSignedUrls,
        };
      }, {});
      setData(processedSignedUrls);

      return processedSignedUrls;
    } catch (error) {
      setIsError(true);
      console.error('Error uploading files:', error);

      setData(null);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const reset = () => {
    setData(null);
    setIsError(false);
    setIsLoading(false);
    setIsIdle(true);
    setFilesProgress(null);
  };

  return {
    data,
    isLoading,
    isError,
    isSuccess,
    isIdle,
    progress,
    uploadAllFiles,
    reset,
  };
};

export { useGcpFileUpload };

export function getFileFromBase64(media: Media) {
  const base64 = media.dataURL.split(',')[1];
  const imageContent = Buffer.from(base64, 'base64').toString('binary');
  const buffer = new ArrayBuffer(imageContent.length);
  const view = new Uint8Array(buffer);

  for (let n = 0; n < imageContent.length; n++) {
    view[n] = imageContent.charCodeAt(n);
  }
  const type = media.file.type;
  const blob = new Blob([buffer], { type });
  return new File([blob], media.file.name, { type });
}
