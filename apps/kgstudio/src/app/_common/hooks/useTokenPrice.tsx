import BigNumber from 'bignumber.js';
import qs from 'qs';
import { useEffect, useState } from 'react';

import { apiAssetProHooks } from '@/app/_common/services';

const useTokenPrice = (contractAddress: string, chainId: string) => {
  const [coingeckoIds, setCoingeckoIds] = useState<string[]>([]);
  const [currCoingeckoId, setCurrCoingeckoId] = useState<string | undefined>(undefined);

  const { data: tokenListData } = apiAssetProHooks.useGetTokenList();
  const { data: tokenPricesData } = apiAssetProHooks.useGetAssetPrices(
    {
      queries: { assets_cid: coingeckoIds },
      paramsSerializer: (params) => qs.stringify(params, { indices: false }),
    },
    {
      enabled: coingeckoIds.length > 0,
    },
  );

  useEffect(() => {
    if (!!tokenListData) {
      setCoingeckoIds(tokenListData.data.map((token) => token.coingecko_id));
    }
  }, [tokenListData]);

  useEffect(() => {
    if (!!tokenListData && !!tokenPricesData) {
      setCurrCoingeckoId(
        tokenListData.data.find((t) => t.contract_address === contractAddress && t.chain_id === chainId)?.coingecko_id,
      );
    }
  }, [chainId, contractAddress, tokenListData, tokenPricesData]);

  const hasUsdPrice = !!tokenPricesData && !!currCoingeckoId;
  const tokenPriceUSD = hasUsdPrice ? BigNumber(tokenPricesData.data[currCoingeckoId].price) : undefined;

  return {
    tokenPriceUSD,
  };
};

export { useTokenPrice };
