import { MutableRefObject, useLayoutEffect, useRef, useState } from 'react';

export function useMeasure(): [MutableRefObject<null>, { width: number; height: number } | null] {
  const ref = useRef(null);
  const [rect, setRect] = useState<{
    width: number;
    height: number;
  } | null>(null);

  useLayoutEffect(() => {
    if (!ref.current) return;

    const observer = new ResizeObserver(([entry]) => {
      if (entry && entry.contentRect) {
        setRect({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        });
      }
    });

    observer.observe(ref.current);
    return () => {
      observer.disconnect();
    };
  }, []);

  return [ref, rect];
}
