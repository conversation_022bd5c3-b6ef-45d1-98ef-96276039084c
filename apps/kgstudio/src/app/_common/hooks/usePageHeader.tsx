import { useEffect } from 'react';

import { PageHeader, useLayoutStore } from '@/app/_common/store';

const usePageHeader = (header?: PageHeader) => {
  const stringifyHeader = !!header ? JSON.stringify(header) : null;

  const [pageHeader, setPageHeader, resetPageHeader] = useLayoutStore((state) => [
    state.pageHeader,
    state.setPageHeader,
    state.resetPageHeader,
  ]);

  useEffect(() => {
    if (!stringifyHeader) return;

    setPageHeader(JSON.parse(stringifyHeader) as PageHeader);

    return () => {
      resetPageHeader();
    };
  }, [resetPageHeader, setPageHeader, stringifyHeader]);

  return pageHeader;
};

export { usePageHeader };
