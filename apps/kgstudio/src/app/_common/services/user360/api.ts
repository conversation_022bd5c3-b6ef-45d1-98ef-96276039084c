import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, makeParameters, mergeApis } from '@zodios/core';

import { paginationQuery, qQuery, sortingQuery } from '../common/model';
import { AudienceListSchema, ComplianceStatSchema, KycStatusSchema, UserDnaSchema } from './model';

const user360Api = makeApi([
  {
    method: 'get',
    path: '/compliance',
    alias: 'getComplianceStat',
    description: 'Get compliance list',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().int(),
      },
    ],
    response: ApiResponseSchema(ComplianceStatSchema),
  },
  {
    method: 'get',
    path: '/audience/:uid',
    alias: 'getUserDna',
    description: 'Get compliance detail',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().int(),
      },
      {
        name: 'uid',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: ApiResponseSchema(UserDnaSchema),
  },
  {
    method: 'get',
    path: '/audience',
    alias: 'getAudiences',
    description: 'Get audiences',
    parameters: makeParameters([
      ...paginationQuery,
      ...sortingQuery,
      ...qQuery,
      {
        name: 'kyc_status',
        type: 'Query',
        schema: KycStatusSchema.optional(),
      },
    ]),
    response: AudienceListSchema,
  },
]);

const mergedApi = mergeApis({
  '/studio/organization/:org_id/user_360': user360Api,
});
export const user360Client = new Zodios(API_BASE_URL, mergedApi, { axiosInstance });
