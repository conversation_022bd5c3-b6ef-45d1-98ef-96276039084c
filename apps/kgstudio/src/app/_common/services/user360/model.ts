import { z } from 'zod';

import { ApiResponseWithPagingSchema } from '@/app/_common/lib/api';

import { TimestampSchema } from '../common/model';

export const StatSchema = z.object({
  value: z.number(),
  percentage: z.number().optional(),
});
const ComplyFlowSchema = z.object({
  form_submission: StatSchema,
  idv_tasks: StatSchema,
  cdd_tasks: StatSchema,
});

export const KycStatusSchema = z.union([
  z.literal('verified'),
  // for those who haven't submitted kyc form
  z.literal('unverified'),
  z.literal('pending'),
  z.literal('rejected'),
]);

const WalletSchema = z.object({
  registered: StatSchema,
  unregistered: StatSchema,
});

const KycSchema = z.object({
  pending_tasks: StatSchema,
  verified_customers: StatSchema,
  kyc_status: z.object({
    verified: StatSchema,
    pending: StatSchema,
    rejected: StatSchema,
  }),
  wallet: WalletSchema,
});

export const ComplianceStatSchema = z.object({
  comply_flow: ComplyFlowSchema,
  kyc: KycSchema,
});
export type ComplianceStat = z.infer<typeof ComplianceStatSchema>;

const UserComplianceSchema = z.object({
  kyc_status: KycStatusSchema,
  risk_level: z.union([z.literal('low'), z.literal('mid'), z.literal('high')]).nullable(),
  potential_risk: z.number().nullable(),
  applied_at: TimestampSchema.nullable(),
  approved_at: TimestampSchema.nullable(),
  form_id: z.number().int().nullable(),
  email: z.string().email().nullable(),
  real_name: z.string(),
  nation_name: z.string(),
  national_id: z.string(),
  dob: TimestampSchema.nullable(),
  phone: z.string().nullable(),
});

const UserWalletSchema = z.array(
  z.object({
    chain_id: z.string(),
    address: z.string(),
    balance: z.number(),
    tag: z.literal('default_receiving').nullable(),
  }),
);

export const UserDnaSchema = z.object({
  uid: z.string(),
  created_at: TimestampSchema,
  name: z.string().nullable(),
  compliance: UserComplianceSchema,
  wallets: UserWalletSchema,
});
export type UserDna = z.infer<typeof UserDnaSchema>;

export const AudienceSchema = z.object({
  uid: z.string(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  name: z.string().nullable(),
  phone: z.string().nullable(),
  email: z.string().email().nullable(),
  comply_flow: z.object({
    kyc_status: KycStatusSchema,
    form_id: z.number().int().nullable(),
  }),
});
export type Audience = z.infer<typeof AudienceSchema>;

export const AudienceListSchema = ApiResponseWithPagingSchema(z.array(AudienceSchema));
export type AudienceList = z.infer<typeof AudienceListSchema>;

export const GrafanaConfigSchema = z.object({
  kiosk: z.enum(['_', 'tv', 'full']).nullable(), // null means without grafana sidebar and nav
  from: z.number().nullable(),
});
export type GrafanaConfig = z.infer<typeof GrafanaConfigSchema>;
export const GrafanaLoginSchema = z.object({
  auth_token: z.string().nonempty(),
  configs: GrafanaConfigSchema,
});
export type GrafanaLogin = z.infer<typeof GrafanaLoginSchema>;
