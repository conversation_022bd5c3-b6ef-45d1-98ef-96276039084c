import { z } from 'zod';

// Retrieve Mnemonic request
export const RetrieveMnemonicRequestSchema = z.object({
  wallet_type: z.string(),
  public_key: z.string().describe('X25519 public key in base64url format for secure key exchange'),
});
export type RetrieveMnemonicRequest = z.infer<typeof RetrieveMnemonicRequestSchema>;

// Retrieve Mnemonic response
export const RetrieveMnemonicResponseSchema = z.object({
  mnemonic: z.string().describe('Encrypted mnemonic in base64url format, encrypted using X25519 key exchange'),
});
export type RetrieveMnemonicResponse = z.infer<typeof RetrieveMnemonicResponseSchema>;
