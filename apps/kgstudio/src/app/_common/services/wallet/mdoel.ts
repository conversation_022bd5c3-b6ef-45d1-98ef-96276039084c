import { z } from 'zod';

// Imported Address schema
export const ImportedAddressSchema = z.object({
  id: z.number(),
  organization_id: z.number(),
  chain: z.string(),
  address: z.string(),
  added_by_user_id: z.string(),
  added_at: z.string(), // date-time string
  default_receive_address: z.boolean(),
});

export type ImportedAddress = z.infer<typeof ImportedAddressSchema>;

// Import Address request
export const ImportAddressRequestSchema = z.object({
  chain: z.string(),
  address: z.string(),
});
export type ImportAddressRequest = z.infer<typeof ImportAddressRequestSchema>;

// Import Address response (success message)
export const ImportAddressResponseSchema = z.object({
  message: z.string(),
});
export type ImportAddressResponse = z.infer<typeof ImportAddressResponseSchema>;

// Delete Address response (success message)
export const DeleteAddressResponseSchema = z.object({
  message: z.string(),
});
export type DeleteAddressResponse = z.infer<typeof DeleteAddressResponseSchema>;

// Retrieve Mnemonic request
export const RetrieveMnemonicRequestSchema = z.object({
  wallet_type: z.string(),
  public_key: z.string(),
});
export type RetrieveMnemonicRequest = z.infer<typeof RetrieveMnemonicRequestSchema>;

// Retrieve Mnemonic response
export const RetrieveMnemonicResponseSchema = z.object({
  encrypted_mnemonic: z.string(),
  public_key: z.string(),
});
export type RetrieveMnemonicResponse = z.infer<typeof RetrieveMnemonicResponseSchema>;

// Set Default Address request
export const SetDefaultAddressRequestSchema = z.object({
  default: z.boolean().nullable(),
});
export type SetDefaultAddressRequest = z.infer<typeof SetDefaultAddressRequestSchema>;

// Set Default Address response (success message)
export const SetDefaultAddressResponseSchema = z.object({
  message: z.string(),
});
export type SetDefaultAddressResponse = z.infer<typeof SetDefaultAddressResponseSchema>;
