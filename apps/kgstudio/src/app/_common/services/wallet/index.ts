import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi } from '@zodios/core';

import {
  DeleteAddressResponseSchema,
  ImportAddressRequestSchema,
  ImportAddressResponseSchema,
  ImportedAddressSchema,
  RetrieveMnemonicRequestSchema,
  RetrieveMnemonicResponseSchema,
  SetDefaultAddressRequestSchema,
  SetDefaultAddressResponseSchema,
} from './mdoel';

const walletApi = makeApi([
  {
    method: 'get',
    path: '/studio/organization/:org_id/wallet/addresses',
    alias: 'getImportedAddresses',
    description: 'Retrieve the list of all addresses that have been imported for an organization.',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
    ],
    response: ApiResponseSchema(z.array(ImportedAddressSchema)),
  },
  {
    method: 'post',
    path: '/studio/organization/:org_id/wallet/addresses',
    alias: 'importAddress',
    description: 'Add a new address to track for an organization.',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: ImportAddressRequestSchema,
      },
    ],
    response: ApiResponseSchema(ImportAddressResponseSchema),
  },
  {
    method: 'delete',
    path: '/studio/organization/:org_id/wallet/addresses/:addressID',
    alias: 'deleteImportedAddress',
    description: 'Remove an imported address from an organization.',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'addressID',
        type: 'Path',
        schema: z.number(),
      },
    ],
    response: ApiResponseSchema(DeleteAddressResponseSchema),
  },
  {
    method: 'put',
    path: '/studio/organization/:org_id/wallet/addresses/:addressID/default',
    alias: 'setDefaultImportedAddress',
    description:
      "Marks or unmarks an organization's imported address as the default receiving address for its specific blockchain.",
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'addressID',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: SetDefaultAddressRequestSchema,
      },
    ],
    response: ApiResponseSchema(SetDefaultAddressResponseSchema),
  },
  {
    method: 'post',
    path: '/studio/organization/:org_id/wallet/retrieve-mnemonic',
    alias: 'retrieveOrganizationWalletMnemonic',
    description: 'Securely retrieve the mnemonic for a specific organization wallet.',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: RetrieveMnemonicRequestSchema,
      },
    ],
    response: ApiResponseSchema(RetrieveMnemonicResponseSchema),
  },
]);

export const walletClient = new Zodios(API_BASE_URL, walletApi, { axiosInstance });
