import { z } from 'zod';

import { ApiResponseSchema, ApiResponseWithPagingSchema } from '@/app/_common/lib/api';

// Chain ID schema for payment service
export const PaymentChainIdSchema = z.enum(['arb', 'base', 'optimism']);
export type PaymentChainId = z.infer<typeof PaymentChainIdSchema>;

// Payment status schema
export const PaymentStatusSchema = z.enum([
  'pending',
  'success',
  'expired',
  'insufficient_not_refunded',
  'insufficient_refunded',
]);
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;

// Individual payment intent schema
export const PaymentIntentSchema = z.object({
  payment_intent_id: z.string(),
  client_id: z.string(),
  payment_chain_id: PaymentChainIdSchema,
  payment_address: z.string(),
  token_address: z.string(),
  symbol: z.string(),
  decimals: z.number().positive(),
  crypto_amount: z.string(),
  fiat_amount: z.string().optional(),
  fiat_currency: z.string().optional(),
  payment_deadline: z.number().positive(),
  status: PaymentStatusSchema,
  group_key: z.string().nullable().optional(),
  payment_tx_hash: z.string().nullable().optional(),
  order_data: z.unknown().nullable().optional(),
  callback_url: z.string().url().nullable().optional(),
  received_crypto_amount: z.string().nullable().optional(),
  aggregated_crypto_amount: z.string().nullable().optional(),
  refund_crypto_amount: z.string().nullable().optional(),
  refund_tx_hash: z.string().nullable().optional(),
});
export type PaymentIntent = z.infer<typeof PaymentIntentSchema>;

// List of payment intents response schema
export const PaymentIntentsListSchema = ApiResponseWithPagingSchema(z.array(PaymentIntentSchema));
export type PaymentIntentsList = z.infer<typeof PaymentIntentsListSchema>;

// Create payment intent request schema
export const CreatePaymentIntentRequestSchema = z
  .object({
    chain_id: PaymentChainIdSchema,
    token_address: z.string().optional(),
    amount: z.string(),
    currency: z.enum(['TWD', 'USD']).optional(),
    pricing_mode: z.enum(['fiat', 'crypto']),
    callback_url: z.string().url().optional(),
    order_data: z.unknown().optional(),
    pay_token: z.enum(['USDC', 'USDT']),
    group_key: z.string().optional(),
    payout_target_address: z.string().optional(),
  })
  .refine(
    (data) => {
      // If pricing mode is fiat, currency is required
      if (data.pricing_mode === 'fiat' && !data.currency) {
        return false;
      }
      return true;
    },
    {
      message: 'Currency is required when pricing mode is fiat',
      path: ['currency'],
    },
  );
export type CreatePaymentIntentRequest = z.infer<typeof CreatePaymentIntentRequestSchema>;

// Create payment intent response schema
export const CreatePaymentIntentResponseSchema = ApiResponseSchema(PaymentIntentSchema);
export type CreatePaymentIntentResponse = z.infer<typeof CreatePaymentIntentResponseSchema>;

// Get payment intent by ID response schema
export const GetPaymentIntentResponseSchema = ApiResponseSchema(PaymentIntentSchema);
export type GetPaymentIntentResponse = z.infer<typeof GetPaymentIntentResponseSchema>;
