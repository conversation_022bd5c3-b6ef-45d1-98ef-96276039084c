import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi } from '@zodios/core';

import {
  CreatePaymentIntentRequestSchema,
  PaymentChainIdSchema,
  PaymentIntentSchema,
  PaymentIntentsListSchema,
} from './model';

export const paymentApi = makeApi([
  {
    method: 'get',
    path: '/studio/organization/:org_id/payment/intents',
    alias: 'getPaymentIntents',
    description: 'Get a list of payment intents',

    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      { name: 'page_size', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_number', type: 'Query', schema: z.number().positive().optional() },
      { name: 'client_id', type: 'Query', schema: z.string().optional() },
      { name: 'status', type: 'Query', schema: z.array(z.string()).optional() },
    ],
    response: PaymentIntentsListSchema,
  },
  {
    method: 'post',
    path: '/payment/intent',
    alias: 'createPaymentIntent',
    description: 'Create a new payment intent',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: CreatePaymentIntentRequestSchema,
      },
    ],
    response: ApiResponseSchema(PaymentIntentSchema),
  },
  {
    method: 'get',
    path: '/organization/:org_id/payment/intents/:payment_intent_id',
    alias: 'getPaymentIntent',
    description: 'Get a specific payment intent by ID',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'payment_intent_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
    ],
    response: ApiResponseSchema(PaymentIntentSchema),
  },
  {
    method: 'post',
    path: '/studio/organization/:org_id/payment/intents/:payment_intent_id/cancel',
    alias: 'cancelPaymentIntent',
    description: 'Cancel a payment intent',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'payment_intent_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
    ],
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'get',
    path: '/studio/organization/:org_id/payment/supported-tokens',
    alias: 'getSupportedTokens',
    description: 'Get list of supported tokens for payment',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'chain_id',
        type: 'Query',
        schema: PaymentChainIdSchema.optional(),
      },
    ],
    response: ApiResponseSchema(
      z.array(
        z.object({
          token_address: z.string(),
          symbol: z.string(),
          decimals: z.number().positive(),
          chain_id: PaymentChainIdSchema,
          logo_url: z.string().url().optional(),
        }),
      ),
    ),
  },
  {
    method: 'get',
    path: '/studio/organization/:org_id/payment/dashboard',
    alias: 'getPaymentDashboard',
    description: 'Get payment dashboard',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'days',
        type: 'Query',
        schema: z.number().positive().optional(),
      },
    ],
    response: ApiResponseSchema(
      z.object({
        total_revenue: z.record(
          z.string(),
          z.record(
            z.string(),
            z.object({
              amount: z.string(),
              rate: z.string(),
              total_usd: z.string(),
            }),
          ),
        ),
        valid_order_count: z.number(),
        unique_customer_count: z.number(),
      }),
    ),
  },
]);

export const paymentClient = new Zodios(API_BASE_URL, paymentApi, { axiosInstance });
