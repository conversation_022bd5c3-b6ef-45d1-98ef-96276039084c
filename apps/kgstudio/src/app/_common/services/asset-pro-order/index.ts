import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import { OrderDetailSchema, OrdersListSchema } from './model';

export const assetProOrderApi = makeApi([
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/orders',
    alias: 'getOrdersList',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      { name: 'q', type: 'Query', schema: z.string().optional() },
      { name: 'page_size', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_number', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_sort', type: 'Query', schema: z.string().optional() },
    ],
    description: 'Get AssetPro orders list.',
    response: OrdersListSchema,
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/orders/:order_id',
    alias: 'getOrderDetails',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'order_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
    ],
    description: 'Get the order details',
    response: ApiResponseSchema(OrderDetailSchema),
  },
  {
    method: 'post',
    path: '/organization/:org_id/asset_pro/orders/:order_id/confirm_payment',
    alias: 'confirmPayment',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'order_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          customer_transfer_time: z.number().positive(),
          transfer_amount: z.string(),
          last_five_digits: z.string().nonempty(),
          note: z.string().nullable(),
          attachments: z.array(z.string()).nullable(),
        }),
      },
    ],
    description: 'Confirm payment',
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'post',
    path: '/organization/:org_id/asset_pro/orders/:order_id/transfer',
    alias: 'transferOrderAssets',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'order_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.literal(null),
      },
    ],
    description: 'Transfer assets specified in the order',
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'patch',
    path: '/organization/:org_id/asset_pro/orders/:order_id',
    alias: 'editOrder',
    description: 'Edit the order(internal notes or payment details)',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'order_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          internal_note: z.string().nonempty().optional(),
          payment_details: z
            .object({
              customer_transfer_time: z.number().nullable().optional(),
              transfer_amount: z.string().optional(),
              last_five_digits: z.string().optional(),
              note: z.string().optional(),
              attachments: z.array(z.string()).nullable().optional(),
            })
            .optional(),
        }),
      },
    ],
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'put',
    path: '/organization/:org_id/asset_pro/orders/:order_id/cancel',
    alias: 'cancelOrder',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'order_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          internal_note: z.string().nonempty(),
        }),
      },
    ],
    description: 'Cancel the order',
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/pending_order_count',
    alias: 'getPendingOrderCount',
    response: ApiResponseSchema(z.object({ count: z.number().nonnegative() })),
  },
]);

const assetProOrderApis = mergeApis({
  '/studio': assetProOrderApi,
});
export const assetProOrderClient = new Zodios(API_BASE_URL, assetProOrderApis, { axiosInstance });
