import { z } from 'zod';

import { ApiResponseWithPagingSchema } from '@/app/_common/lib/api';
import {
  AllTxStatusSchema,
  AssetProChainIdSchema,
  BaseCurrencySchema,
  KycStatusSchema,
  OperatorSchema,
} from '@/app/_common/services/asset-pro/model';

import { TimestampSchema } from '../common/model';

// Order list schema
export const PaymentStatusSchema = z.union([
  z.literal('unpaid'),
  z.literal('paid'),
  z.literal('awaiting_refund'),
  z.literal('refunded'),
]);
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;

export const OrderStatusSchema = z.union([
  z.literal('unpaid'),
  z.literal('awaiting_confirmation'),
  z.literal('awaiting_shipment'),
  z.literal('shipping'),
  z.literal('delivered'),
  z.literal('cancelled'),
]);
export type OrderStatus = z.infer<typeof OrderStatusSchema>;

const ShippingStatusSchema = z.union([
  z.literal('not_shipped'),
  z.literal('send_success'),
  z.literal('send_failed'),
  z.literal('pending'),
]);
export type ShippingStatus = z.infer<typeof ShippingStatusSchema>;

export const OrderSchema = z.object({
  create_time: z.number(),
  order_id: z.string(),
  customer: z.object({
    name: z.string(),
    phone: z.string().nullable(),
    email: z.string().nullable(),
    kyc_status: KycStatusSchema,
  }),
  purchase: z.object({
    amount: z.string(),
    logo_url: z.string(),
    name: z.string(),
    chain_id: AssetProChainIdSchema,
    base_currency: BaseCurrencySchema,
    usd_price: z.string(),
  }),
  total_price: z.object({
    amount: z.string(),
    logo_url: z.string(),
    quote_currency: z.literal('TWD'),
    usd_amount: z.string(),
  }),
  payment_status: PaymentStatusSchema,
  shipment_details: z
    .object({
      tx_hash: z.string(),
      shipped_at: z.number(),
    })
    .nullable(),
  order_status: OrderStatusSchema,
  shipping_status: ShippingStatusSchema,
});
export type Order = z.infer<typeof OrderSchema>;

export const OrdersListSchema = ApiResponseWithPagingSchema(z.array(OrderSchema));

// Order detials schema

const customerSchema = z.object({
  name: z.string().nonempty(),
  phone: z.string().nullable(),
  email: z.string().nullable(),
  kyc_status: KycStatusSchema,
  wallet_address: z.string(),
  bank_account: z
    .object({
      bank_name: z.string(),
      branch_name: z.string(),
      account_number: z.string(),
      account_holder_name: z.string(),
    })
    .nullable(),
});

const purchaseSchema = z.object({
  amount: z.string(),
  logo_url: z.string(),
  name: z.string(),
  chain_id: AssetProChainIdSchema,
  base_currency: z.string(),
  usd_amount: z.string(),
  price: z.string(),
});

const totalPriceSchema = z.object({
  amount: z.string(),
  logo_url: z.string(),
  quote_currency: z.literal('TWD'),
  usd_amount: z.string(),
  handling_fee: z.string(),
  handling_fee_percentage: z.string(),
});

const shipmentDetailsSchema = z
  .object({
    tx_hash: z.string(),
    shipped_at: TimestampSchema.nullable(),
    tx_id: z.number(),
    send_to: z.string(),
    tx_status: AllTxStatusSchema,
    processed_by: OperatorSchema,
  })
  .nullable();

const paymentDetailsSchema = z.object({
  deadline: TimestampSchema,
  transfer_to: z.object({
    bank_name: z.string(),
    branch_name: z.string(),
    account_number: z.string(),
    account_holder_name: z.string(),
  }),
  payment_method: z.literal('bank_transfer'),
  note: z.string().nullable(),
  attachments: z.array(z.string()).nullable(),
  customer_transfer_time: TimestampSchema.nullable(),
  last_five_digits: z.string().nullable(),
  customer: z
    .object({
      transferred: z.object({
        quote_currency: z.literal('TWD'),
        updated_at: TimestampSchema,
        attachments: z.array(z.string()),
      }),
    })
    .nullable(),
  last_edited_at: TimestampSchema.nullable(),
  editor: OperatorSchema.omit({ profile_img: true }).nullable(),
  transfer_amount: z.string().nullable(),
});

export const OrderDetailSchema = z.object({
  create_time: TimestampSchema,
  order_id: z.string().min(1),
  customer: customerSchema,
  purchase: purchaseSchema,
  total_price: totalPriceSchema,
  payment_status: PaymentStatusSchema,
  shipment_details: shipmentDetailsSchema,
  order_status: OrderStatusSchema,
  payment_details: paymentDetailsSchema,
  shipping_status: ShippingStatusSchema,
  internal_note: z.string().nullable(),
});
export type OrderDetails = z.infer<typeof OrderDetailSchema>;
