import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import {
  ComplianceReviewFilterSchema,
  ComplianceReviewSchema,
  ReviewFormSchema,
  ReviewIDVSchema,
  ReviewRiskSchema,
  ReviewStatusSchema,
} from './model';

export const complianceApi = makeApi([
  {
    method: 'get',
    path: '/:org_id/comply_flow/cases',
    alias: 'getComplianceCases',
    parameters: [
      { name: 'q', type: 'Query', schema: z.string().optional() },
      { name: 'page_size', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_number', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_sort', type: 'Query', schema: z.string().optional() },
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      { name: 'kyc_status', type: 'Query', schema: z.union([ReviewStatusSchema, z.literal('')]).optional() },
      { name: 'submitted_at_from', type: 'Query', schema: z.number().optional() },
      { name: 'submitted_at_to', type: 'Query', schema: z.number().optional() },
      { name: 'risk_label', type: 'Query', schema: z.union([ReviewRiskSchema, z.literal('')]).optional() },
      { name: 'sanctioned', type: 'Query', schema: z.union([z.boolean(), z.literal('')]).optional() },
      { name: 'idv_status', type: 'Query', schema: z.union([ReviewIDVSchema, z.literal('')]).optional() },
    ],
    description: 'Get compliance case list',
    response: ComplianceReviewSchema,
  },
  {
    method: 'get',
    path: '/:org_id/comply_flow/cases/filter_options',
    alias: 'getComplianceCasesFilterOptions',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
    ],
    description: 'Get compliance case list filter options',
    response: ComplianceReviewFilterSchema,
  },
  {
    method: 'post',
    path: '/:org_id/comply_flow/:uid/audit',
    alias: 'reviewComplianceCase',
    description: 'Accept or reject compliance case with internal notes and reject reasons',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'uid',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: ReviewFormSchema,
      },
    ],
    response: z.object({ code: z.literal(0) }),
  },
]);

const complianceApis = mergeApis({
  '/studio/organization': complianceApi,
});
export const complianceClient = new Zodios(API_BASE_URL, complianceApis, { axiosInstance });
