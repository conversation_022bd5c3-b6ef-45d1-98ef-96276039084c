import { z } from 'zod';

import { ApiResponseSchema, ApiResponseWithPagingSchema } from '@/app/_common/lib/api';

export const ReviewStatusSchema = z.union([
  z.literal('processing'),
  z.literal('pending'),
  z.literal('verified'),
  z.literal('rejected'),
]);
export type ReviewStatus = z.infer<typeof ReviewStatusSchema>;

export const ReviewRiskSchema = z.union([z.literal('low'), z.literal('mid'), z.literal('high')]);
export type ReviewRisk = z.infer<typeof ReviewRiskSchema>;

export const ReviewIDVSchema = z.union([z.literal('pass'), z.literal('failed')]);
export type ReviewIDV = z.infer<typeof ReviewIDVSchema>;

export const ReviewRejectReasonSchema = z.union([
  z.literal('sanctioned'),
  z.literal('high_risk_money_laundering'),
  z.literal('faked_document'),
  z.literal('not_the_document_holder'),
  z.literal('incorrect_document_information'),
]);
export type ReviewRejectReason = z.infer<typeof ReviewRejectReasonSchema>;

export const ReviewCaseSchema = z.object({
  uid: z.string(),
  case_id: z.number(),
  name: z.string().nullable(),
  phone: z.string().nullable(),
  email: z.string().email().nullable(),
  country: z.string().nullable(),
  national_id: z.string().nullable(),
  birthday: z.string().nullable(),
  line_id: z.string().nullable(),
  submitted_at: z.number(),
  updated_at: z.number(),
  idv_status: ReviewIDVSchema.nullable(),
  risk_score: z.number().nullable(),
  risk_label: ReviewRiskSchema.nullable(),
  kyc_status: ReviewStatusSchema,
  form_id: z.number().nullable(),
  cdd_id: z.number().nullable(),
  idv_id: z.number().nullable(),
  sanction_matched: z.boolean().nullable(),
  reviewer_name: z.string().nullable(),
  reviewed_at: z.number().nullable(),
  reject_reasons: z.array(ReviewRejectReasonSchema).nullable(),
  internal_notes: z.string().nullable(),
});
export type ReviewCase = z.infer<typeof ReviewCaseSchema>;

export const ComplianceReviewSchema = ApiResponseWithPagingSchema(z.array(ReviewCaseSchema));
export type ComplianceReview = z.infer<typeof ComplianceReviewSchema>;

export const ComplianceReviewFilterSchema = ApiResponseSchema(
  z.object({
    submitted_at_from: z.number(),
    submitted_at_to: z.number(),
  }),
);
export type ComplianceFilterReview = z.infer<typeof ComplianceReviewFilterSchema>;

export const ReviewFormSchema = z.object({
  kyc_status: z.enum(['verified', 'rejected']),
  reject_reasons: z.string().array().optional(),
  internal_notes: z.string().max(50),
});
export type ReviewFormSchema = z.infer<typeof ReviewFormSchema>;
