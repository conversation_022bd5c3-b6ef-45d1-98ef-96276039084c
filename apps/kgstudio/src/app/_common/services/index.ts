import { ZodiosHooks } from '@zodios/react';

import { assetProClient } from './asset-pro';
import { assetProExtendClient } from './asset-pro-extend';
import { assetProOrderClient } from './asset-pro-order';
import { complianceClient } from './compliance';
import { organizationClient } from './organization';
import { organizationExtendClient } from './organization-extend';
import { paymentClient } from './payment';
import { paymentItemClient } from './payment-item';
import { proxyClient } from './proxy';
import { userClient } from './user';
import { user360Client } from './user360';
import { walletClient } from './wallet';

export const apiOrganizationHooks = new ZodiosHooks('organization', organizationClient);
export const apiOrganizationExtendHooks = new ZodiosHooks('organization', organizationExtendClient);
export const apiAssetProHooks = new ZodiosHooks('asset_pro', assetProClient);
export const apiAssetProOrderHooks = new ZodiosHooks('asset_pro', assetProOrderClient);
export const apiAssetProExtendHooks = new ZodiosHooks('asset_pro', assetProExtendClient);
export const proxyHooks = new ZodiosHooks('asset_pro', proxyClient);
export const apiUser360Hooks = new ZodiosHooks('user360', user360Client);
export const apiComplianceHooks = new ZodiosHooks('studio/organization', complianceClient);
export const apiUserHooks = new ZodiosHooks('user', userClient);
export const apiPaymentHooks = new ZodiosHooks('payment', paymentClient);
export const apiPaymentItemHooks = new ZodiosHooks('payment-item', paymentItemClient);
export const apiWalletHooks = new ZodiosHooks('wallet', walletClient);
