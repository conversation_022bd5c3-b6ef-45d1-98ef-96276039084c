import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi } from '@zodios/core';

import {
  CreatePaymentItemRequestSchema,
  PaymentItemSchema,
  PaymentItemsListSchema,
  UpdatePaymentItemRequestSchema,
} from './model';

export const paymentItemApi = makeApi([
  {
    method: 'get',
    path: '/studio/organization/:org_id/payment/items',
    alias: 'getPaymentItems',
    description: 'Get a list of payment items',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      { name: 'client_id', type: 'Query', schema: z.string() },
      { name: 'page', type: 'Query', schema: z.number().positive().optional().default(1) },
      { name: 'page_size', type: 'Query', schema: z.number().positive().optional().default(10) },
    ],
    response: PaymentItemsListSchema,
  },
  {
    method: 'post',
    path: '/studio/organization/:org_id/payment/items',
    alias: 'createPaymentItem',
    description: 'Create a new payment item',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: CreatePaymentItemRequestSchema,
      },
    ],
    response: ApiResponseSchema(PaymentItemSchema),
  },
  {
    method: 'put',
    path: '/studio/organization/:org_id/payment/items/:id',
    alias: 'updatePaymentItem',
    description: 'Update an existing payment item',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'id',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: UpdatePaymentItemRequestSchema,
      },
    ],
    response: ApiResponseSchema(PaymentItemSchema),
  },
  {
    method: 'delete',
    path: '/studio/organization/:org_id/payment/items/:item_id',
    alias: 'deletePaymentItem',
    description: 'Delete a payment item',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'item_id',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: z.object({ code: z.number() }),
  },
]);

export const paymentItemClient = new Zodios(API_BASE_URL, paymentItemApi, { axiosInstance });
