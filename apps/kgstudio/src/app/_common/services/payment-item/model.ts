import { z } from 'zod';

import { ApiResponseSchema, ApiResponseWithPagingSchema } from '@/app/_common/lib/api';

// Order data field schema
export const OrderDataFieldSchema = z.object({
  field_name: z.string(),
  field_label: z.string(),
  required: z.boolean(),
  field_type: z.string(),
  default_value: z.any().optional(),
});
export type OrderDataField = z.infer<typeof OrderDataFieldSchema>;

// Payment item schema
export const PaymentItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable().optional(),
  price: z.string(), // using string for decimal values
  currency: z.string(),
  image: z.string().nullable().optional(),
  success_url: z.string().nullable().optional(),
  error_url: z.string().nullable().optional(),
  callback_url: z.string().nullable().optional(),
  success_message: z.string().nullable().optional(),
  order_data_fields: z.array(OrderDataFieldSchema),
  config: z.record(z.any()).optional(), // Flexible config field
  client_id: z.string().optional(),
  pay_token: z.string().nullable().optional(),
  organization_id: z.number(),
  organization_name: z.string().optional(),
  organization_icon: z.string().optional(),
  created_at: z.string().or(z.date()), // accepting both string and Date
  updated_at: z.string().or(z.date()),
  chain_id: z.string().nullable().optional(),
});
export type PaymentItem = z.infer<typeof PaymentItemSchema>;

// List of payment items response schema
export const PaymentItemsListSchema = ApiResponseWithPagingSchema(z.array(PaymentItemSchema));
export type PaymentItemsList = z.infer<typeof PaymentItemsListSchema>;

// Create payment item request schema
export const CreatePaymentItemRequestSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  price: z.string(),
  currency: z.string(),
  image: z.string().optional(),
  callback_url: z.string().optional(),
  success_url: z.string().optional(),
  error_url: z.string().optional(),
  success_message: z.string().optional(),
  order_data_fields: z.array(OrderDataFieldSchema),
  config: z.record(z.any()).optional(), // Flexible config field
  client_id: z.string().optional(),
  pay_token: z.string().nullable().optional(),
  chain_id: z.string().nullable().optional(),
});
export type CreatePaymentItemRequest = z.infer<typeof CreatePaymentItemRequestSchema>;

// Update payment item request schema
export const UpdatePaymentItemRequestSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  price: z.string().optional(),
  currency: z.string().optional(),
  image: z.string().optional(),
  success_url: z.string().optional(),
  error_url: z.string().optional(),
  callback_url: z.string().optional(),
  success_message: z.string().optional(),
  order_data_fields: z.array(OrderDataFieldSchema).optional(),
  config: z.record(z.any()).optional(), // Flexible config field
  client_id: z.string().optional(),
  pay_token: z.string().nullable().optional(),
  chain_id: z.string().nullable().optional(),
});
export type UpdatePaymentItemRequest = z.infer<typeof UpdatePaymentItemRequestSchema>;

// Get payment item by ID response schema
export const GetPaymentItemResponseSchema = ApiResponseSchema(PaymentItemSchema);
export type GetPaymentItemResponse = z.infer<typeof GetPaymentItemResponseSchema>;

// Create payment item response schema
export const CreatePaymentItemResponseSchema = ApiResponseSchema(PaymentItemSchema);
export type CreatePaymentItemResponse = z.infer<typeof CreatePaymentItemResponseSchema>;

// Update payment item response schema
export const UpdatePaymentItemResponseSchema = ApiResponseSchema(PaymentItemSchema);
export type UpdatePaymentItemResponse = z.infer<typeof UpdatePaymentItemResponseSchema>;
