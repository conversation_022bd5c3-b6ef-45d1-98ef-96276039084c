import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import {
  AllTxStatusSchema,
  AssetPriceSchema,
  AssetProChainIdSchema,
  AssetProTransactionSchema,
  EditProductRequestSchema,
  KGAccountSchema,
  KycStatusSchema,
  MarketSettingsSchema,
  ProductsListSchema,
  TokenSchema,
  TransactionFilterOptionsSchema,
  TransferTokenSchema,
  TxDetailsSchema,
} from './model';

export const assetProApi = makeApi([
  {
    method: 'get',
    path: '/asset_pro/transfer/tokens',
    alias: 'getTokenList',
    description: 'Get AssetPro teansfer token list.',
    response: ApiResponseSchema(z.array(TokenSchema)),
  },
  {
    method: 'get',
    path: '/organization/:org_id/customer',
    alias: 'getOrganizationCustomer',
    parameters: [
      {
        name: z.union([z.literal('email'), z.literal('phone')]).toString(),
        type: 'Query',
        schema: z.union([z.string().email().optional(), z.string().optional()]),
      },
    ],
    description: 'Get customer info.',
    response: ApiResponseSchema(
      z.object({
        display_name: z.string(),
        avatar_url: z.string(),
        kyc_status: KycStatusSchema,
        uid: z.string().optional(),
        wallets: KGAccountSchema.array(),
      }),
    ),
  },
  {
    method: 'post',
    path: '/organization/:org_id/asset_pro/transfer',
    alias: 'transferToken',
    description: 'Transfer token to another user in AssetPro.',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          chain_id: AssetProChainIdSchema,
          contract_address: z.string(),
          amount: z.string().nonempty(),
          wallet_address: z.string().nonempty(),
          email: z.string().email().optional(),
          phone: z.string().optional(),
          display_name: z.string().optional(),
          kyc_status: KycStatusSchema.optional(),
          note: z.string().optional(),
          attachments: z.array(z.string()).optional(),
        }),
      },
    ],
    response: ApiResponseSchema(TransferTokenSchema),
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/transfer/histories',
    alias: 'getTransactionHistory',
    parameters: [
      { name: 'q', type: 'Query', schema: z.string().optional() },
      { name: 'page_size', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_number', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_sort', type: 'Query', schema: z.string().optional() },
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      { name: 'submitter', type: 'Query', schema: z.string().optional() },
      { name: 'approver', type: 'Query', schema: z.string().optional() },
      { name: 'finance_manager', type: 'Query', schema: z.string().optional() },
      { name: 'rejecter', type: 'Query', schema: z.string().optional() },
      { name: 'tx_hash', type: 'Query', schema: z.string().optional() },
      { name: 'status', type: 'Query', schema: z.array(AllTxStatusSchema).optional() },
      { name: 'amount_from', type: 'Query', schema: z.number().min(0).optional() },
      { name: 'amount_to', type: 'Query', schema: z.number().min(0).optional() },
      { name: 'submit_time_from', type: 'Query', schema: z.number().optional() },
      { name: 'submit_time_to', type: 'Query', schema: z.number().optional() },
      { name: 'chain_id', type: 'Query', schema: AssetProChainIdSchema.optional() },
      { name: 'token', type: 'Query', schema: z.string().optional() },
    ],
    description: 'Get the transaction history',
    response: AssetProTransactionSchema,
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/transfer/filter_options',
    alias: 'getTransactionHistoryFilterOptions',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
    ],
    description: "Get the transaction history's filter options",
    response: TransactionFilterOptionsSchema,
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/products',
    alias: 'getProductsList',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      { name: 'is_published', type: 'Query', schema: z.boolean().optional() },
      { name: 'page_size', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_number', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_sort', type: 'Query', schema: z.string().optional() },
      { name: 'q', type: 'Query', schema: z.string().optional() },
    ],
    description: 'Get AssetPro product list.',
    response: ProductsListSchema,
  },
  {
    method: 'put',
    path: '/organization/:org_id/asset_pro/products/:product_id',
    alias: 'editProduct',
    description: 'Edit AssetPro product detail.',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'product_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: EditProductRequestSchema,
      },
      { name: 'is_published', type: 'Query', schema: z.boolean().optional() },
    ],
    response: z.object({ code: z.literal(0) }),
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/market',
    alias: 'getMarketSettings',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
    ],
    description: 'Get the market settings',
    response: ApiResponseSchema(MarketSettingsSchema),
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/transfer/histories/:tx_id',
    alias: 'getTransactionDetails',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'tx_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
    ],
    response: ApiResponseSchema(TxDetailsSchema),
  },
  // Backend API is not ready yet
  {
    method: 'patch',
    path: '/organization/:org_id/asset_pro/transfer/histories/:tx_id',
    alias: 'updateTxNoteOrAttachments',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'tx_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          note: z.string().nullish(),
          attachments: z.array(z.string().url()).nullish(),
        }),
      },
    ],
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'post',
    path: '/organization/:org_id/asset_pro/transfer/histories/:tx_id/approve',
    alias: 'txApproval',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'tx_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          note: z.string().optional(),
          operation: z.enum(['approve', 'reject']),
        }),
      },
    ],
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'post',
    path: '/organization/:org_id/asset_pro/transfer/histories/:tx_id/release',
    alias: 'txRelease',
    description: 'Release the transfer request only when tx status is awaiting_release',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'tx_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          note: z.string().optional(),
          operation: z.enum(['release', 'reject']),
        }),
      },
    ],
    response: ApiResponseSchema(z.undefined()),
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/transfer/pending_history_count',
    alias: 'getPendingTxHistoryCount',
    response: ApiResponseSchema(
      z.object({
        count: z.number().nonnegative(),
        count_awaiting_approval_self: z.number().nonnegative(),
        count_awaiting_approval: z.number().nonnegative(),
        count_awaiting_release: z.number().nonnegative(),
      }),
    ),
  },
  {
    method: 'post',
    path: '/organization/:org_id/asset_pro/transfer/histories/:tx_id/resend',
    alias: 'resendFailedTx',
    description: 'Release the transfer request only when tx status is awaiting_release',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'tx_id',
        type: 'Path',
        schema: z.string().nonempty(),
      },
    ],
    response: z.object({ code: z.literal(0) }),
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/free_send_count',
    alias: 'getFreeSendCount',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
    ],
    response: ApiResponseSchema(z.any()),
  },
]);

export const assetProPriceApi = makeApi([
  {
    method: 'get',
    path: '/asset_prices',
    alias: 'getAssetPrices',
    description: 'Get specific token prices.',
    parameters: [
      {
        name: 'assets_cid',
        type: 'Query',
        schema: z.array(z.string()),
      },
    ],
    response: ApiResponseSchema(AssetPriceSchema),
  },
]);

const assetProApis = mergeApis({
  '/studio': assetProApi,
  '': assetProPriceApi,
});
export const assetProClient = new Zodios(API_BASE_URL, assetProApis, { axiosInstance });
