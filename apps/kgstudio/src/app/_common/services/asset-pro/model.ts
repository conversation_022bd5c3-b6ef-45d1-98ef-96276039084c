import { z } from 'zod';

import { ApiResponseSchema, ApiResponseWithPagingSchema } from '@/app/_common/lib/api';
import { ASSETPRO_SUPPORTED_EVM_CHAINS, ASSETPRO_SUPPORTED_TRON_CHAINS } from '@kryptogo/utils';

import { TimestampSchema } from '../common/model';

export const EvmChainIdSchema = z.enum(ASSETPRO_SUPPORTED_EVM_CHAINS);
export const NonEvmChainIdSchema = z.enum(ASSETPRO_SUPPORTED_TRON_CHAINS);
export const AssetProChainIdSchema = z.union([EvmChainIdSchema, NonEvmChainIdSchema]);

export type AssetProEvmChainId = z.infer<typeof EvmChainIdSchema>;
export type AssetProNonEvmChainId = z.infer<typeof NonEvmChainIdSchema>;
export type AssetProChainId = z.infer<typeof AssetProChainIdSchema>;

export const TokenSchema = z.object({
  name: z.string(),
  symbol: z.string(),
  chain_id: AssetProChainIdSchema,
  chain_name: z.string(),
  contract_address: z.string(),
  logo_url: z.string(),
  coingecko_id: z.string(),
  decimals: z.number().positive(),
});
export type Token = z.infer<typeof TokenSchema>;

export const KycStatusSchema = z.union([
  z.literal('verified'),
  z.literal('pending'),
  z.literal('rejected'),
  z.literal('unverified'),
]);
export type KycStatus = z.infer<typeof KycStatusSchema>;

export const KGAccountChainIdSchame = z.enum([
  'eth',
  'matic',
  'bsc',
  'tron',
  'sepolia',
  'shasta',
  'kcc',
  'ronin',
  'oasys',
  'btc',
  'sol',
  'arb',
  'localhost',
  'holesky',
]);

export const KGAccountSchema = z.object({
  chain_id: z.union([KGAccountChainIdSchame, z.string()]),
  address: z.string(),
});
export type KGAccount = z.infer<typeof KGAccountSchema>;

export const TxStatusSchema = z.enum(['sending', 'send_success', 'send_failed']);
export type TxStatus = z.infer<typeof TxStatusSchema>;

// Transaction details schema
// NOTE: TxStatusWithApprovalStatus: if tx send failed, will automatically fall back to 'awaiting_release' status
export const TxStatusWithApprovalSchema = TxStatusSchema.exclude(['send_failed']).or(
  z.enum(['awaiting_approval', 'rejected', 'awaiting_release']),
);
export type TxStatusWithApproval = z.infer<typeof TxStatusWithApprovalSchema>;

// NOTE: This for utils, component, etc. that need to handle both TxStatus and TxStatusWithApproval
export const AllTxStatusSchema = z.union([TxStatusSchema, TxStatusWithApprovalSchema]);
export type AllTxStatus = z.infer<typeof AllTxStatusSchema>;

export const AssetProTransactionSchema = ApiResponseWithPagingSchema(
  z.array(
    z.object({
      id: z.string(),
      token_coingecko_id: z.string(),
      chain_id: AssetProChainIdSchema,
      token_name: z.string(),
      token_logo_url: z.string(),
      amount: z.string(),
      transfer_time: z.number(),
      submit_time: z.number(),
      recipient: z.object({
        uid: z.string().nullable(),
        name: z.string().nullable(),
        kyc_status: KycStatusSchema.nullable(),
        phone: z.string().nullable(),
        email: z.string().email().nullable(),
        wallet_address: z.string(),
      }),
      status: AllTxStatusSchema,
      tx_hash: z.string(),
      updated_by: z.string().nullable(),
      update_time: z.number(),
    }),
  ),
);
export type AssetProTransaction = z.infer<typeof AssetProTransactionSchema>;

export const TransactionFilterOptionsSchema = ApiResponseSchema(
  z.object({
    status: z.array(AllTxStatusSchema),
    submitter: z.array(z.object({ name: z.string(), uid: z.string() })),
    token: z.array(z.string()),
    chain_id: z.array(AssetProChainIdSchema),
    approver: z.array(z.object({ name: z.string(), uid: z.string() })),
    rejecter: z.array(z.object({ name: z.string(), uid: z.string() })),
    finance_manager: z.array(z.object({ name: z.string(), uid: z.string() })),
    submit_time_from: z.number().nullable(),
    submit_time_to: z.number().nullable(),
    amount_from: z.number().nullable(),
    amount_to: z.number().nullable(),
  }),
);
export type TransactionFilterOptions = z.infer<typeof TransactionFilterOptionsSchema>;

export const MarketSettingsSchema = z.object({
  market_url: z.string().url(),
  title: z.string(),
  logo: z.string().url(),
  introduction: z.string().nullable(),
  email: z.string().email().nullable(),
  phone: z.string().nullable(),
  line_id: z.string().nullable(),
  payment_method: z.literal('bank_transfer'),
  payment_currency: z.string(),
  bank_name: z.string().nullable(),
  branch_name: z.string().nullable(),
  bank_account: z.string().nullable(),
  bank_account_holder_name: z.string().nullable(),
  payment_expiration_sec: z.number().positive(),
});
export type MarketSettings = z.infer<typeof MarketSettingsSchema>;
// Product schema
export const BaseCurrencySchema = z.union([z.literal('USDC'), z.literal('USDT')]);

export const ProductSchema = z.object({
  product_id: z.number().positive(),
  organization_id: z.number().positive(),
  is_published: z.boolean(),
  type: z.literal('buy_crypto'),
  image: z.string(),
  logo_url: z.string(),
  name: z.string(),
  chain_id: AssetProChainIdSchema,
  base_currency: BaseCurrencySchema,
  quote_currency: z.literal('TWD'),
  price: z.string().nullable(),
  order_limits_from: z.string().nullable(),
  order_limits_to: z.string().nullable(),
  stock: z.string().nullable(),
  fee_type: z.union([z.literal('fee_included'), z.literal('no_fee')]),
  proportional_minimum_fee: z.string().nullable(),
  proportional_fee_percentage: z.string().nullable(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  operator: z.object({
    name: z.string().nullable(),
  }),
});
export type Product = z.infer<typeof ProductSchema>;
export const ProductsListSchema = ApiResponseWithPagingSchema(z.array(ProductSchema));
export type ProductsList = z.infer<typeof ProductsListSchema>;

export const EditProductRequestSchema = z.object({
  product_id: z.number().positive(),
  organization_id: z.number().positive(),
  is_published: z.boolean(),
  type: z.literal('buy_crypto'),
  image: z.string(),
  logo_url: z.string(),
  name: z.string(),
  chain_id: AssetProChainIdSchema,
  base_currency: z.union([z.literal('USDC'), z.literal('USDT')]),
  quote_currency: z.literal('TWD'),
  price: z.string(),
  order_limits_from: z.string(),
  order_limits_to: z.string(),
  fee_type: z.union([z.literal('fee_included'), z.literal('no_fee')]),
  proportional_minimum_fee: z.string().nullable(),
  proportional_fee_percentage: z.string().nullable(),
  // NOTE: BE API schema can be null, but we do the non-null check in the UI
  stock: z.string().nullable(),
});

export type EditProductRequest = z.infer<typeof EditProductRequestSchema>;

export const TransferTokenSchema = z.object({ id: z.string().nonempty(), transfer_time: z.number().positive() });
export type TransferToken = z.infer<typeof TransferTokenSchema>;

export const TxCustomerSchema = z.object({
  uid: z.string(),
  profile_img: z.string().nullable(),
  name: z.string(),
  kyc_status: KycStatusSchema,
  phone: z.string().nullable(),
  email: z.string().email().nullable(),
});
export type TxCustomer = z.infer<typeof TxCustomerSchema>;

export const TxTokenSchema = z.object({
  name: z.string(),
  symbol: z.string(),
  chain_id: AssetProChainIdSchema,
  logo_url: z.string(),
  coingecko_id: z.string(),
  decimals: z.number().positive(),
  contract_address: z.string(),
});
export type TxToken = z.infer<typeof TxTokenSchema>;

export const TxInfoSchema = z.object({
  id: z.string(),
  status: AllTxStatusSchema,
  amount: z.string(),
  token: TxTokenSchema,
  recipient: z.object({
    customer: TxCustomerSchema.nullable(),
    wallet_address: z.string(),
  }),
  notes: z.object({
    submission_note: z.string().nullable(),
    rejection_note: z.string().nullable(),
  }),
  attachments: z.array(z.string().url()).nullable(),
  tx_hashes: z.array(z.string()).nullable(),
});
export type TxInfo = z.infer<typeof TxInfoSchema>;

export const OperatorSchema = z.object({
  name: z.string(),
  profile_img: z.string().nullable(),
  email: z.string(),
});

export const TxOperatorsSchema = z.object({
  trader: z.object({
    operator: OperatorSchema,
    time: TimestampSchema,
  }),
  approver: z
    .object({
      operator: OperatorSchema,
      time: TimestampSchema,
    })
    .nullable(),
  finance_manager: z
    .object({
      operator: OperatorSchema,
      time: TimestampSchema,
    })
    .nullable(),
});
export type TxOperators = z.infer<typeof TxOperatorsSchema>;

export const TxDetailsSchema = z.object({
  tx_info: TxInfoSchema,
  operators: TxOperatorsSchema,
});
export type TxDetails = z.infer<typeof TxDetailsSchema>;

export const AssetPriceSchema = z.record(
  z.object({
    price: z.number(),
  }),
);
export type AssetPrice = z.infer<typeof AssetPriceSchema>;
