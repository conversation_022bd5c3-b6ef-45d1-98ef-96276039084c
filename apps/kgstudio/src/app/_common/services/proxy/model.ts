import { z } from 'zod';

export const ProxyGetSchema = z.object({
  method: z.undefined().transform(() => 'GET'),
  path: z.string().nonempty(),
});

export const ProxyKyaBodySchema = z.object({
  method: z.undefined().transform(() => 'POST'),
  path: z.undefined().transform(() => 'https://apiexpert.crystalblockchain.com/monitor/tx/add'),
  body: z.object({
    address: z.string().nonempty(),
    name: z.undefined().transform(() => 'name'),
    direction: z.undefined().transform(() => 'withdrawal'),
    currency: z.union([
      z.literal('btc'),
      z.literal('eth'),
      z.literal('trx'), // NOTE: use 'trx' instead of 'tron'
      z.literal('sol'),
      z.literal('matic'),
      z.literal('arb'),
      z.literal('base'),
      z.literal('optimism'),
      z.literal('op'),
    ]),
  }),
});

export const ProxyKyaResponseSchema = z.object({
  data: z.object({
    riskscore: z.number().nullable(),
    riskscore_profile: z
      .object({
        signals: z.record(z.number()),
      })
      .nullable(),
    signals: z.record(z.number()).nullable(),
  }),
});
export type ProxyKyaResponse = z.infer<typeof ProxyKyaResponseSchema>;

export const ProxySimplePriceResponseSchema = z.record(
  z.object({
    usd: z.number(),
  }),
);
export type ProxySimplePriceResponse = z.infer<typeof ProxySimplePriceResponseSchema>;

export const ProxyBlockchairResponseSchema = z.object({
  context: z.object({}),
  data: z.record(
    z.string(),
    z.object({
      address: z.object({
        balance: z.union([z.number(), z.string()]),
        balance_usd: z.number(),
        received: z.number().optional(),
        received_approximate: z.string().optional(),
        received_usd: z.number(),
        spent: z.number().optional(),
        spent_approximate: z.string().optional(),
        spent_usd: z.number(),
        transaction_count: z.number(),
      }),
    }),
  ),
});
export type ProxyBlockchairResponse = z.infer<typeof ProxyBlockchairResponseSchema>;

export const ProxyTronGridBalanceSchema = z.object({
  data: z.array(
    z.object({
      account_resource: z.record(z.any()).optional(),
      active_permission: z.array(z.record(z.any())).optional(),
      address: z.string(),
      assetV2: z.array(z.record(z.any())).optional(),
      balance: z.number(),
      create_time: z.number().optional(),
      free_asset_net_usageV2: z.array(z.record(z.any())).optional(),
      free_net_usage: z.number().optional(),
      frozenV2: z.array(z.record(z.any())).optional(),
      latest_consume_free_time: z.number().optional(),
      latest_opration_time: z.number().optional(),
      net_window_optimized: z.boolean().optional(),
      net_window_size: z.number().optional(),
      owner_permission: z.record(z.any()).optional(),
      trc20: z.array(z.record(z.string(), z.string())),
    }),
  ),
  success: z.boolean(),
  meta: z.object({}),
});
export type ProxyTronGridBalance = z.infer<typeof ProxyTronGridBalanceSchema>;
