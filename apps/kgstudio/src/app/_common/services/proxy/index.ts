import { match, P } from 'ts-pattern';
import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import {
  ProxyBlockchairResponseSchema,
  ProxyGetSchema,
  ProxyKyaBodySchema,
  ProxyKyaResponseSchema,
  ProxySimplePriceResponseSchema,
  ProxyTronGridBalanceSchema,
} from './model';

export const proxyApi = makeApi([
  {
    method: 'post',
    path: '/proxy_3rd_party',
    alias: 'proxy',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.union([ProxyGetSchema, ProxyKyaBodySchema]),
      },
    ],
    // zod.union() will merge keys with the same field name across different schemas, it will cause zod to be unable to correctly parse the corresponding schema
    response: z.preprocess(
      (data) => {
        return match(data)
          .with({ context: P._ }, (d) => ProxyBlockchairResponseSchema.parse(d))
          .with({ meta: P._, success: P.boolean }, (d) => ProxyTronGridBalanceSchema.parse(d))
          .otherwise((d) => z.union([ProxyKyaResponseSchema, ProxySimplePriceResponseSchema]).parse(d));
      },
      z.union([
        ProxyBlockchairResponseSchema,
        ProxyKyaResponseSchema,
        ProxySimplePriceResponseSchema,
        ProxyTronGridBalanceSchema,
      ]),
    ),
  },
]);

const proxyApis = mergeApis({
  '': proxyApi,
});
export const proxyClient = new Zodios(API_BASE_URL, proxyApis, { axiosInstance });
