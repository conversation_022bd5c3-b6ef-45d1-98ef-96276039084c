import { z } from 'zod';

// API Key schemas
export const APIKeySchema = z.object({
  id: z.number(),
  name: z.string(),
  key_prefix: z.string(),
  description: z.string().nullable(),
  last_used_at: z.number().nullable(),
  created_at: z.number(),
});

export const CreateAPIKeyRequestSchema = z.object({
  name: z.string(),
  description: z.string().nullable(),
});

export const CreateAPIKeyResponseSchema = z.object({
  api_key: z.string(),
});

export type APIKey = z.infer<typeof APIKeySchema>;
export type CreateAPIKeyRequest = z.infer<typeof CreateAPIKeyRequestSchema>;
export type CreateAPIKeyResponse = z.infer<typeof CreateAPIKeyResponseSchema>;

// OAuth Client schemas
export const OAuthClientSchema = z.object({
  client_id: z.string(),
  client_name: z.string(),
  client_domain: z.string(),
  client_type: z.string(),
  main_logo: z.string(),
  square_logo: z.string(),
  created_at: z.number().optional(),
  app_store_link: z.string().optional(),
  google_play_link: z.string().optional(),
});

export const CreateOAuthClientRequestSchema = z.object({
  client_name: z.string(),
  client_domain: z.string(),
  client_type: z.string(), // "mobile_wallet", "complyflow", "market"
});

export const CreateOAuthClientResponseSchema = z.object({
  client_id: z.string(),
  client_secret: z.string(),
  client_name: z.string(),
  client_domain: z.string(),
});

export const UpdateOAuthClientRequestSchema = z.object({
  client_name: z.string().optional(),
  client_domain: z.string().optional(),
  client_type: z.string().optional(),
  main_logo: z.string().optional(),
  square_logo: z.string().optional(),
  app_store_link: z.string().optional(),
  google_play_link: z.string().optional(),
});

export type OAuthClient = z.infer<typeof OAuthClientSchema>;
export type CreateOAuthClientRequest = z.infer<typeof CreateOAuthClientRequestSchema>;
export type CreateOAuthClientResponse = z.infer<typeof CreateOAuthClientResponseSchema>;
export type UpdateOAuthClientRequest = z.infer<typeof UpdateOAuthClientRequestSchema>;
