import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import {
  APIKeySchema,
  CreateAPIKeyRequestSchema,
  CreateAPIKeyResponseSchema,
  CreateOAuthClientRequestSchema,
  CreateOAuthClientResponseSchema,
  OAuthClientSchema,
  UpdateOAuthClientRequestSchema,
} from './model';

// API Key endpoints
const apiKeyApi = makeApi([
  {
    method: 'get',
    path: '/:org_id/api-keys',
    alias: 'listAPIKeys',
    description: 'List all API keys for the authenticated user',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
    ],
    response: ApiResponseSchema(z.array(APIKeySchema)),
  },
  {
    method: 'post',
    path: '/:org_id/api-keys',
    alias: 'createAPIKey',
    description: 'Create a new API key',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: CreateAPIKeyRequestSchema,
      },
    ],
    response: ApiResponseSchema(CreateAPIKeyResponseSchema),
  },
  {
    method: 'delete',
    path: '/:org_id/api-keys/:id',
    alias: 'deleteAPIKey',
    description: 'Delete an API key',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'id',
        type: 'Path',
        schema: z.number(),
      },
    ],
    response: ApiResponseSchema(z.unknown()),
  },
]);

// OAuth Client endpoints
const oauthClientApi = makeApi([
  {
    method: 'get',
    path: '/:org_id/oauth-clients',
    alias: 'listOAuthClients',
    description: 'List all OAuth clients for the organization',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
    ],
    response: ApiResponseSchema(z.array(OAuthClientSchema)),
  },
  {
    method: 'get',
    path: '/:org_id/oauth-clients/:client_id',
    alias: 'getOAuthClient',
    description: 'Get a specific OAuth client',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'client_id',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: ApiResponseSchema(OAuthClientSchema),
  },
  {
    method: 'post',
    path: '/:org_id/oauth-clients',
    alias: 'createOAuthClient',
    description: 'Create a new OAuth client',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: CreateOAuthClientRequestSchema,
      },
    ],
    response: ApiResponseSchema(CreateOAuthClientResponseSchema),
  },
  {
    method: 'put',
    path: '/:org_id/oauth-clients/:client_id',
    alias: 'updateOAuthClient',
    description: 'Update an OAuth client',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number(),
      },
      {
        name: 'client_id',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: UpdateOAuthClientRequestSchema,
      },
    ],
    response: ApiResponseSchema(OAuthClientSchema),
  },
]);

const userApis = mergeApis({
  '/studio/organization': [...apiKeyApi, ...oauthClientApi],
});

export const userClient = new Zodios(API_BASE_URL, userApis, { axiosInstance });
