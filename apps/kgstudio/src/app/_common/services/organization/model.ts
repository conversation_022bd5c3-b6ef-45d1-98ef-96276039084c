import { z } from 'zod';

import {
  AssetProLimitSchema,
  InvitationStatusSchema,
  MemberSchema,
  ModuleRoleSchema,
} from '@/app/[locale]/(protected)/setting/team/_types';
import { ApiResponseWithPagingSchema } from '@/app/_common/lib/api';

export const ModuleSchema = z.object({
  user_360: z.array(z.union([z.literal('data'), z.literal('engage'), z.literal('audience')])).optional(),
  wallet_builder: z
    .array(
      z.union([
        z.literal('project'),
        z.literal('configuration'),
        z.literal('app_publish'),
        z.literal('marketing_tools'),
      ]),
    )
    .optional(),
  asset_pro: z
    .array(
      z.union([
        z.literal('treasury'),
        z.literal('send_token'),
        z.literal('transaction_history'),
        z.literal('operators'),
        z.literal('market'),
        z.literal('revenue'),
      ]),
    )
    .optional(),
  nft_boost: z.array(z.union([z.literal('campaign'), z.literal('reward')])).optional(),
  compliance: z
    .array(z.union([z.literal('create_a_task'), z.literal('case_management'), z.literal('all_tasks')]))
    .optional(),
  admin: z.array(z.union([z.literal('members'), z.literal('settings'), z.literal('billing')])).optional(),
});
export type Module = z.infer<typeof ModuleSchema>;

export const PermissionSchema = z.object({ resource: z.string(), action: z.string() });
export type Permission = z.infer<typeof PermissionSchema>;
export const UserInfoSchema = z.object({
  uid: z.string(),
  name: z.string(),
  member_id: z.string(),
  phone: z.string().optional(),
  email: z.string(),
  roles: ModuleRoleSchema,
  status: InvitationStatusSchema,
  modules: ModuleSchema,
  asset_pro: AssetProLimitSchema,
  permissions: z.array(PermissionSchema),
});
export type UserInfo = z.infer<typeof UserInfoSchema>;

export const OrganizationSchema = z.object({
  id: z.number().int(),
  name: z.string().nonempty(),
  icon_url: z.string().nullable(),
});

export const MemberListSchema = ApiResponseWithPagingSchema(z.array(MemberSchema));
export type MemberList = z.infer<typeof MemberListSchema>;

export const OrgInfoSchema = OrganizationSchema.extend({
  modules: ModuleSchema,
  owners: z.array(z.string()),
  created_at: z.number(),
});
export type OrgInfo = z.infer<typeof OrgInfoSchema>;

export const AssetProOperatorsSchema = ApiResponseWithPagingSchema(
  z.array(
    z.object({
      uid: z.string().nonempty(),
      name: z.string().nonempty(),
      member_id: z.string(),
      email: z.string().email(),
      daily_transfer_limit: z.number().nullable(),
      transfer_approval_threshold: z.number(),
      roles: z.array(
        z.union([
          z.literal('admin'),
          z.literal('approver'),
          z.literal('trader'),
          z.literal('owner'),
          z.literal('finance_manager'),
        ]),
      ),
    }),
  ),
);
export type AssetProOperators = z.infer<typeof AssetProOperatorsSchema>;
