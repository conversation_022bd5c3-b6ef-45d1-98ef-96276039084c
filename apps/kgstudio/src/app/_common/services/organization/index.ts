import { z } from 'zod';

import { ModuleRoleSchema, RoleIdentifierSchema } from '@/app/[locale]/(protected)/setting/team/_types';
import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import { KGAccountSchema } from '../asset-pro/model';
import { PageWithFilterParams } from '../common/model';
import { AssetProOperatorsSchema, MemberListSchema, OrgInfoSchema, OrganizationSchema, UserInfoSchema } from './model';

export const organizationsApi = makeApi([
  {
    method: 'get',
    path: '/',
    alias: 'getOrganizations',
    description: 'Get all the organizations of the user.',
    response: ApiResponseSchema(z.array(OrganizationSchema).nonempty()),
  },
]);

export const organizationApi = makeApi([
  {
    method: 'get',
    path: '/:org_id/accounts',
    alias: 'getOrganizationAccounts',
    description: 'Get Organization accounts.',
    response: ApiResponseSchema(z.array(KGAccountSchema)),
  },
  {
    method: 'get',
    path: '/:org_id/me',
    alias: 'getCurrentUserInfo',
    description: 'Get current studio user info',
    response: ApiResponseSchema(UserInfoSchema),
  },
  {
    method: 'get',
    path: '/:org_id/users',
    alias: 'getOrganizationMembers',
    parameters: [
      { name: 'page_number', type: 'Query', schema: z.number().positive().optional() },
      { name: 'email', type: 'Query', schema: z.string().optional() },
      { name: 'page_size', type: 'Query', schema: z.number().positive().optional() },
      { name: 'page_sort', type: 'Query', schema: z.string().optional() },
    ],
    description: 'Get all members of the organization.',
    response: MemberListSchema,
  },
  {
    method: 'get',
    path: '/:org_id/roles',
    alias: 'getOrganizationRoles',
    description: 'Get all the roles according to the organization.',
    response: ApiResponseSchema(ModuleRoleSchema),
  },
  {
    method: 'get',
    path: '/:org_id/user_exist',
    alias: 'checkUserExists',
    parameters: [{ name: 'email', type: 'Query', schema: z.string().optional() }],
    description: 'Check if user exists in the organization.',
    response: z.object({ code: z.union([z.literal(0), z.literal(2100)]), data: z.object({ exist: z.boolean() }) }),
  },
  {
    method: 'post',
    path: '/:org_id/users',
    alias: 'inviteOrganizationMember',
    description: 'Invite a member to the organization.',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          name: z.string(),
          member_id: z.string(),
          email: z.string().email(),
          roles: RoleIdentifierSchema.array(),
        }),
      },
    ],
    response: z.object({ code: z.literal(0) }),
  },
  {
    method: 'post',
    path: '/:org_id/reinvite',
    alias: 'reinviteOrganizationMember',
    description: 'Reinvite a member to the organization.',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ uid: z.string() }),
      },
    ],
    response: z.object({ code: z.literal(0) }),
  },
  {
    method: 'put',
    path: '/:org_id/users/:user_id',
    alias: 'editOrganizationMember',
    description: 'Edit a member of the organization.',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          name: z.string(),
          member_id: z.string(),
          roles: RoleIdentifierSchema.array(),
        }),
      },
    ],
    response: z.object({ code: z.literal(0) }),
  },
  {
    method: 'delete',
    path: '/:org_id/users/:uid',
    alias: 'deleteOrganizationMember',
    description: 'Delete a member from the organization.',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'uid',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: ApiResponseSchema(
      z.object({
        name: z.string(),
        email: z.string().email(),
      }),
    ),
  },
  {
    method: 'get',
    path: '/:org_id/info',
    alias: 'getOrganizationInfo',
    description: 'Get the organization info.',
    response: ApiResponseSchema(OrgInfoSchema),
  },
  {
    method: 'get',
    path: '/:org_id/asset_pro/operators',
    alias: 'getAssetProOperators',
    parameters: [
      ...PageWithFilterParams,
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
    ],
    description: "Get studio organization's asset pro operators",
    response: AssetProOperatorsSchema,
  },
  {
    method: 'put',
    path: '/:org_id/asset_pro/operators/:uid',
    alias: 'editOperatorTransfer',
    description: "Modify studio organization's assetpro operator transfer",
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          daily_transfer_limit: z.number().positive(),
          transfer_approval_threshold: z.number(),
        }),
      },
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'uid',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: z.object({ code: z.literal(0) }),
  },
  {
    method: 'post',
    path: '/:org_id/compliance_token',
    alias: 'getComplianceToken',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.undefined(),
      },
    ],
    response: ApiResponseSchema(z.object({ studio_compliance_token: z.string() })),
  },
  {
    method: 'put',
    path: '/:org_id/info',
    alias: 'editOrganizationInfo',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          name: z.string().nonempty(),
          icon_url: z.string().nullable(),
        }),
      },
    ],
    response: z.object({ code: z.literal(0) }),
  },
]);

const organizationApis = mergeApis({
  '/studio/organization': organizationApi,
  '/studio/organizations': organizationsApi,
});
export const organizationClient = new Zodios(API_BASE_URL, organizationApis, { axiosInstance });
