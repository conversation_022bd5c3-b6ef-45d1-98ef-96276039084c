import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import { LiquiditySchema, ProfitRateSchema } from './model';

export const assetProExtendApi = makeApi([
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/liquidity',
    alias: 'getLiquidity',
    response: ApiResponseSchema(z.array(LiquiditySchema)),
  },
  {
    method: 'put',
    path: '/organization/:org_id/asset_pro/liquidity',
    alias: 'editProfitMargin',
    description: "Edit organization's buy/swap profit margin",
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          profit_margin: z.object({
            gas_swap: z.number(),
            buy_crypto: z.number(),
          }),
        }),
      },
    ],
    response: ApiResponseSchema(z.literal(undefined)),
  },
  {
    method: 'get',
    path: '/organization/:org_id/asset_pro/profit_rates',
    alias: 'getProfitRates',
    description: "Get organization's profit rates",
    response: ApiResponseSchema(z.array(ProfitRateSchema)),
  },
  {
    method: 'put',
    path: '/organization/:org_id/asset_pro/profit_rates',
    alias: 'editProfitRate',
    description: "Edit organization's profit rate",
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().positive(),
      },
      {
        name: 'body',
        type: 'Body',
        schema: z.object({
          service: z.enum(['buy', 'swap_gas', 'swap_defi', 'send_with_fee', 'send_gasless', 'send_batch', 'bridge']),
          profit_rate: z.number(),
        }),
      },
    ],
    response: ApiResponseSchema(z.literal(undefined)),
  },
]);

const assetProExtendApis = mergeApis({
  '/studio': assetProExtendApi,
});
export const assetProExtendClient = new Zodios(API_BASE_URL, assetProExtendApis, { axiosInstance });
