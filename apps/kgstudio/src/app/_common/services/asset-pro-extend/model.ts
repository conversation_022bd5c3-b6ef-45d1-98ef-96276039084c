import { z } from 'zod';

import { AssetProChainIdSchema } from '@/app/_common/services/asset-pro/model';

export const LiquiditySchema = z.object({
  liquidity_type: z.enum(['buy_crypto', 'gas_swap']),
  symbol: z.string(),
  chain_id: AssetProChainIdSchema,
  token_url: z.string(),
  contract_address: z.string().nullable(),
  price: z.string(),
  unit: z.string(),
  profit_margin: z.number(),
  alert_threshold: z.string().nullable(),
});
export type Liquidity = z.infer<typeof LiquiditySchema>;

export const ProfitRateSchema = z.object({
  service: z.enum(['buy', 'swap_gas', 'swap_defi', 'send_with_fee', 'send_gasless', 'send_batch', 'bridge']),
  profit_rate: z.number(),
});
export type ProfitRate = z.infer<typeof ProfitRateSchema>;
