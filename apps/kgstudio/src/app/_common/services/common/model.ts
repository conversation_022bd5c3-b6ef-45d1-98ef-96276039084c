import { z } from 'zod';

import { makeParameters } from '@zodios/core';

export const paginationQuery = makeParameters([
  {
    name: 'page_number',
    type: 'Query',
    schema: z.number().int().positive().default(1),
  },
  {
    name: 'page_size',
    type: 'Query',
    schema: z.number().int().positive().default(10),
  },
]);

export const qQuery = makeParameters([
  {
    name: 'q',
    type: 'Query',
    schema: z.string().optional(),
  },
]);

export const sortingQuery = makeParameters([
  {
    name: 'page_sort',
    type: 'Query',
    schema: z.string().optional(),
  },
]);

export const TimestampSchema = z
  .number()
  .int()
  .transform((v) => new Date(v * 1000));

export const PageWithFilterParams = makeParameters([...paginationQuery, ...qQuery, ...sortingQuery]);
