import { z } from 'zod';

export const GrafanaConfigSchema = z.object({
  kiosk: z.enum(['_', 'tv', 'full']).nullable(), // null means without grafana sidebar and nav
  from: z.number().nullable(),
});
export type GrafanaConfig = z.infer<typeof GrafanaConfigSchema>;
export const GrafanaLoginSchema = z.object({
  auth_token: z.string().nonempty(),
  configs: GrafanaConfigSchema,
});
export type GrafanaLogin = z.infer<typeof GrafanaLoginSchema>;
