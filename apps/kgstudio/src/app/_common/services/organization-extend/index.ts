import { z } from 'zod';

import { API_BASE_URL } from '@/app/_common/constant';
import { ApiResponseSchema } from '@/app/_common/lib/api';
import axiosInstance from '@/app/_common/lib/axios/instances/internal';
import { Zodios, makeApi, mergeApis } from '@zodios/core';

import { GrafanaLoginSchema } from './model';

export const organizationExtendApi = makeApi([
  {
    method: 'post',
    path: '/:org_id/grafana_login',
    alias: 'loginToGrafana',
    description: 'Login to grafana',
    parameters: [
      {
        name: 'org_id',
        type: 'Path',
        schema: z.number().int(),
      },
    ],
    response: ApiResponseSchema(GrafanaLoginSchema),
  },
]);

const organizationExtendApis = mergeApis({
  '/studio/organization': organizationExtendApi,
});
export const organizationExtendClient = new Zodios(API_BASE_URL, organizationExtendApis, { axiosInstance });
