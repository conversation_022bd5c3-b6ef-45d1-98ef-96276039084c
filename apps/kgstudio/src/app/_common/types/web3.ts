import { z } from 'zod';

export const EvmSchema = z.enum(['ethereum', 'polygon', 'arb', 'bsc', 'kcc', 'ronin']);
export const NonEvmSchema = z.enum(['solana', 'btc', 'tron']);

export type EvmRpc = z.infer<typeof EvmSchema>;
export type NonEvmRpc = z.infer<typeof NonEvmSchema>;
export type AssetType = 'token' | 'nft' | 'defi';

export type ChainId = 'eth' | 'matic' | 'bsc' | 'tron' | 'sepolia' | 'shasta' | 'arb' | 'base' | 'optimism';
export const ChainIdList: ChainId[] = ['eth', 'matic', 'bsc', 'tron', 'sepolia', 'shasta', 'base', 'optimism'];
export type EvmChainId = Exclude<ChainId, 'tron' | 'shasta'>;
export const EvmChainIdList: ChainId[] = ['eth', 'matic', 'bsc', 'sepolia', 'base', 'optimism'];
export type NonEvmChainId = 'tron' | 'shasta';
export const isChainId = (chain: string | string[] | undefined): chain is ChainId => {
  return ChainIdList.indexOf(chain as ChainId) > -1;
};
