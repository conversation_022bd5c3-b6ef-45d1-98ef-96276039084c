import { ChainId } from './web3';

export type TxStatus = 'success' | 'failed' | 'pending';
export type MemberActiveStatus = 'active' | 'inactive';
export type NFTCollectionStatus = 'draft' | 'pending' | 'failed' | 'published' | 'expired';

declare global {
  interface Window {
    Cypress: unknown;
  }
}
export type KGAccount = {
  chain_id: ChainId;
  address: string;
};
export type KGAccounts = KGAccount[];

export type ValidationResult = {
  isValid: boolean;
  errorMessage?: string;
};
