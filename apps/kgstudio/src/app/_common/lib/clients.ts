import { match, P } from 'ts-pattern';
import { createPublicClient, http } from 'viem';

import { env } from '@/env.mjs';
import { getChain } from '@kryptogo/utils';

export const getClient = (chainId: string) => {
  const alchemyApiKey = env.NEXT_PUBLIC_ALCHEMY_API_KEY;
  const chain = getChain(chainId);
  const rpcUrl = match(chainId)
    .with('bsc', () => `https://bnb-mainnet.g.alchemy.com/v2/${alchemyApiKey}`)
    .with(P.union('polygon', 'matic'), () => `https://polygon-mainnet.g.alchemy.com/v2/${alchemyApiKey}`)
    .with(P.union('ethereum', 'eth'), () => `https://eth-mainnet.g.alchemy.com/v2/${alchemyApiKey}`)
    .with('sepolia', () => `https://eth-sepolia.g.alchemy.com/v2/${alchemyApiKey}`)
    .with('arb', () => `https://arb-mainnet.g.alchemy.com/v2/${alchemyApiKey}`)
    .with('solana', () => 'https://solana-rpc.publicnode.com')
    .otherwise(() => undefined);

  return chain
    ? createPublicClient({
        chain,
        transport: http(rpcUrl),
      })
    : undefined;
};
