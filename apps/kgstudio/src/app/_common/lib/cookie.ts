import Cookies from 'js-cookie';
import { z } from 'zod';

export interface CookieOperator<T> {
  get: () => T | undefined;
  set: (value: T, options?: Cookies.CookieAttributes) => void;
  remove: (options?: Cookies.CookieAttributes) => void;
}

function createCookieOperator(key: string): CookieOperator<string>;
function createCookieOperator<T extends z.ZodType<string | Record<string, unknown>>>(
  key: string,
  schema: T,
): CookieOperator<z.infer<T>>;
function createCookieOperator<T extends z.ZodType<string | Record<string, unknown>>>(key: string, schema?: T) {
  return {
    get() {
      try {
        const rawValue = Cookies.get(key);
        if (!schema || !rawValue) return rawValue;

        const result = schema.safeParse(rawValue);
        if (result.success) return result.data as z.infer<T>;

        console.error(`<PERSON><PERSON> parse error for '${key}':`, result.error);
        return undefined;
      } catch (error) {
        console.error(`Error getting cookie '${key}':`, error);
        return undefined;
      }
    },
    set(value: unknown, options?: Cookies.CookieAttributes) {
      try {
        if (!schema) {
          const valueToStore = JSON.stringify(value).slice(1, -1);
          Cookies.set(key, valueToStore, {
            path: '/',
            ...options,
          });
          return;
        }

        const result = schema.safeParse(value);
        if (!result.success) {
          console.error(`Invalid cookie value for '${key}':`, result.error);
          throw new Error('Invalid cookie value');
        }

        const valueToStore = JSON.stringify(result.data);
        Cookies.set(key, valueToStore, {
          path: '/',
          ...options,
        });
      } catch (error) {
        console.error(`Error setting cookie '${key}':`, error);
      }
    },
    remove(options?: Cookies.CookieAttributes) {
      try {
        Cookies.remove(key, {
          path: '/',
          ...options,
        });
      } catch (error) {
        console.error(`Error removing cookie '${key}':`, error);
      }
    },
  };
}

export { createCookieOperator };
