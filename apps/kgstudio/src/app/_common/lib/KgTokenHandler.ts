import { createCookieOperator } from './cookie';

// Constant for KG token key - renamed from temp to a permanent name
const KG_TOKEN_KEY = 'kg-token';

// Cookie options with longer expiration and proper settings
const COOKIE_OPTIONS = {
  path: '/',
  expires: 365, // 1 year expiration for better persistence
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const, // Changed to 'lax' for better cross-site handling
};

/**
 * Handler for KG token used during organization operations
 *
 * This token is persistent and should remain even when other tokens like
 * Kg-Studio-Token-V2 are set.
 */
class KgTokenHandler {
  // Event handlers for token changes (similar to OAuthTokenHandler)
  private static tokenChangeHandlers: Array<() => void> = [];

  // Create cookie operator with our key
  private static kgTokenOperator = createCookieOperator(KG_TOKEN_KEY);

  /**
   * Get the KG token - attempts to get from cookie first, then from localStorage if cookie fails
   */
  static get(): string | undefined {
    try {
      // First, try to get from cookie
      const token = this.kgTokenOperator.get();

      // If found in cookie, return it
      if (token) {
        return token;
      }

      // If not in cookie, try backup
      console.log('KgTokenHandler: Token not found in cookie, checking backup');
      return this.getBackupToken();
    } catch (error) {
      console.error('KgTokenHandler: Error retrieving token:', error);
      // Try backup if cookie retrieval fails
      return this.getBackupToken();
    }
  }

  /**
   * Set the KG token
   * This ensures the token is stored with proper options and won't be easily overwritten
   */
  static set(token: string): void {
    if (!token) {
      console.warn('KgTokenHandler: Attempted to set empty token, ignoring');
      return;
    }

    try {
      console.log(`KgTokenHandler: Setting token (prefix: ${token.substring(0, 5)}...)`);

      // Always use explicit cookie options to ensure it's not overwritten
      this.kgTokenOperator.set(token, COOKIE_OPTIONS);

      // Store in localStorage as a backup
      this.setBackupToken(token);

      // Verify it was set correctly
      setTimeout(() => {
        const storedToken = this.kgTokenOperator.get();
        if (!storedToken) {
          console.warn('KgTokenHandler: Token was not properly stored in cookie, using backup');
        } else {
          console.log('KgTokenHandler: Token successfully stored in cookie');
        }
      }, 50);

      // Notify token change handlers
      this.notifyTokenChanged();
    } catch (error) {
      console.error('KgTokenHandler: Error setting token:', error);
      // Still set backup even if cookie fails
      this.setBackupToken(token);
    }
  }

  /**
   * Clear the KG token
   */
  static clear(): void {
    try {
      console.log('KgTokenHandler: Clearing token');
      this.kgTokenOperator.remove(COOKIE_OPTIONS);
      this.clearBackupToken();
      this.notifyTokenChanged();
    } catch (error) {
      console.error('KgTokenHandler: Error clearing token:', error);
    }
  }

  /**
   * Register a handler function to be called when the token changes
   */
  static onTokenChange(handler: () => void): void {
    this.tokenChangeHandlers.push(handler);
  }

  /**
   * Get the backup token from localStorage
   */
  private static getBackupToken(): string | undefined {
    if (typeof window !== 'undefined') {
      const token = window.localStorage.getItem('kg_token_backup');
      if (token) {
        console.log('KgTokenHandler: Retrieved token from localStorage backup');

        // Re-set in cookie if found in localStorage
        this.kgTokenOperator.set(token, COOKIE_OPTIONS);
        console.log('KgTokenHandler: Re-set token from backup to cookie');

        return token;
      }

      console.log('KgTokenHandler: No token found in localStorage backup');
      return undefined;
    }
    return undefined;
  }

  /**
   * Set the backup token in localStorage
   */
  private static setBackupToken(token: string): void {
    if (typeof window !== 'undefined') {
      window.localStorage.setItem('kg_token_backup', token);
      console.log('KgTokenHandler: Token backup stored in localStorage');

      // Also set in sessionStorage as an extra backup
      window.sessionStorage.setItem('kg_token_backup', token);
    }
  }

  /**
   * Clear the backup token from localStorage
   */
  private static clearBackupToken(): void {
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem('kg_token_backup');
      window.sessionStorage.removeItem('kg_token_backup');
    }
  }

  /**
   * Notify all registered handlers that the token has changed
   */
  private static notifyTokenChanged(): void {
    this.tokenChangeHandlers.forEach((handler) => {
      try {
        handler();
      } catch (error) {
        console.error('KgTokenHandler: Error in token change handler:', error);
      }
    });
  }
}

export { KgTokenHandler };
