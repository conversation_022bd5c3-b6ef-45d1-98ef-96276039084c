import { ZodTypeAny, z } from 'zod';

export const ApiResponseSchema = <TData extends ZodTypeAny>(dataSchema: TData) =>
  z.object({
    code: z.literal(0),
    data: dataSchema,
  });
export const ApiResponseWithPagingSchema = <TData extends ZodTypeAny>(dataSchema: TData) =>
  ApiResponseSchema(dataSchema).extend({
    paging: z.object({
      page_number: z.number().int(),
      page_size: z.number().int(),
      total_count: z.number().int(),
      page_sort: z.string(),
    }),
  });

export type ApiResponse<T = unknown> = Omit<z.infer<ReturnType<typeof ApiResponseSchema>>, 'data'> & {
  data: T;
};
export type ApiResponseWithPaging<T = unknown> = Omit<
  z.infer<ReturnType<typeof ApiResponseWithPagingSchema>>,
  'data'
> & {
  data: T;
};

export const ApiErrorSchema = z.object({
  code: z.number().refine((code) => code !== 0),
  message: z.string(),
  status: z.number(),
  timestamp: z.number().int(),
});
export type ApiError = z.infer<typeof ApiErrorSchema>;
export const isApiError = (data: unknown): data is ApiError => {
  return ApiErrorSchema.safeParse(data).success;
};

export const getAPISortingString = (
  sortingArray: {
    key: string;
    value: null | boolean;
  }[],
) => {
  const queryString = getApiSortingArray(sortingArray).join(',').trim();

  return queryString === '' ? undefined : queryString;
};

export const getApiSortingArray = (
  sortingArray: {
    key: string;
    value: null | boolean;
  }[],
) => {
  return sortingArray
    .filter((sorting) => sorting.value !== null)
    .map((sorting) => `${sorting.key}:${sorting.value === true ? 'a' : 'd'}`);
};

export const serializeQueryParams = (params: Record<string, string | number | boolean>) => {
  return Object.keys(params)
    .map((key) => {
      const encodedValue = encodeURIComponent(params[key]);
      return `${key}=${encodedValue}`;
    })
    .join('&');
};
