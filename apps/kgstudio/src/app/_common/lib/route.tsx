// We need to embed svg into html here because we need the stroke color to be dynamic
import { find } from 'lodash-es';
import { GiftIcon, Settings } from 'lucide-react';

import { FormattedMessage, RoutesType } from '@/app/_common/components';
import { Module, Permission } from '@/app/_common/services/organization/model';
import AppPublishIcon from '@/assets/sidebar/app-publish';
import AssetFinanceIcon from '@/assets/sidebar/asset-finance';
import MarketIcon from '@/assets/sidebar/asset-market-settings';
import AssetOperatorsIcon from '@/assets/sidebar/asset-operators';
import AssetOrdersIcon from '@/assets/sidebar/asset-orders';
import AssetProductIcon from '@/assets/sidebar/asset-products';
import AssetTransactionIcon from '@/assets/sidebar/asset-transaction';
import AssetTransferIcon from '@/assets/sidebar/asset-transfer';
import AssetTreasuryIcon from '@/assets/sidebar/asset-treasury';
import CallbackIcon from '@/assets/sidebar/callback-icon';
import ComplianceCaseManagementIcon from '@/assets/sidebar/compliance-case-management';
import ComplianceReviewIcon from '@/assets/sidebar/compliance-review';
import MarketingToolsIcon from '@/assets/sidebar/marketing-tools';
import NftCampaignIcon from '@/assets/sidebar/nft-campaign';
import NftRewardIcon from '@/assets/sidebar/nft-reward';
import OverviewIcon from '@/assets/sidebar/overview';
import PaymentListIcon from '@/assets/sidebar/payment-icon';
import PayoutIcon from '@/assets/sidebar/payout-icon';
import User360DataIcon from '@/assets/sidebar/user360-data';
import User360ManagementIcon from '@/assets/sidebar/user360-management';
import WalletConfigIcon from '@/assets/sidebar/wallet-config';
import WalletProjectIcon from '@/assets/sidebar/wallet-project';
import { env } from '@/env.mjs';

export const allRoutes: RoutesType[] = [
  {
    name: '',
    moduleKey: 'overview',
    public: true,
    items: [
      {
        name: <FormattedMessage id="kgstudio.common.overview" />,
        moduleItemKey: 'overview',
        path: '/home/<USER>',
        public: true,
        icon: <OverviewIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.revenue" />,
        moduleKey: 'asset_pro', // NOTE: This is in asset_pro module but need to display in overview layer
        moduleItemKey: 'revenue',
        path: '/asset/finance',
        regex: /\/asset\/finance(\/[A-Za-z\d-]+)?$/,
        icon: <AssetFinanceIcon />,
        permissions: [{ resource: 'asset_pro_revenue', action: 'read' }],
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.common.user360" />,
    moduleKey: 'user_360',
    items: [
      {
        name: <FormattedMessage id="kgstudio.common.data.analysis" />,
        moduleItemKey: 'data',
        path: '/user360/data',
        icon: <User360DataIcon />,
        items: [
          {
            name: <FormattedMessage id="kgstudio.data.wallet" />,
            moduleItemKey: 'wallet_builder',
            path: '/user360/data/wallet',
          },
          {
            name: <FormattedMessage id="kgstudio.common.data.compliance" />,
            path: '/user360/data/compliance',
          },
          {
            name: <FormattedMessage id="kgstudio.common.data.asset-pro" />,
            path: '/user360/data/asset-pro',
          },
        ],
      },
      // hide engage temporarily because it's not ready
      // {
      //   name: <FormattedMessage id="kgstudio.common.engage" />,
      //   moduleItemKey: 'engage',
      //   path: '/user360/engage/overview',
      //   icon: <User360EngageIcon />,
      // },
      {
        name: <FormattedMessage id="kgstudio.common.user-management" />,
        moduleItemKey: 'audience',
        path: '/user360/audience/overview',
        regex: /\/user360\/audience\/(([0-9a-zA-Z]+)|overview)$/,
        icon: <User360ManagementIcon />,
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.payment.payment-text" />,
    moduleKey: 'payment_intent',
    public: true,
    items: [
      {
        name: <FormattedMessage id="kgstudio.payment.payment-list" />,
        moduleItemKey: 'payment_order_list',
        path: '/payment/payment-list',
        icon: <PaymentListIcon />,
        public: true,
      },
      {
        name: <FormattedMessage id="kgstudio.payment.callback-dashboard" />,
        moduleItemKey: 'callback_dashboard',
        path: '/payment/callback-dashboard',
        icon: <CallbackIcon />,
        public: true,
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.common.invoice-pro" />,
    moduleKey: 'asset_pro',
    items: [
      {
        name: <FormattedMessage id="kgstudio.payment.payout-list" />,
        moduleItemKey: 'payout_list',
        path: '/payment/payout-list',
        icon: <PayoutIcon />,
        public: true,
      },
      {
        name: <FormattedMessage id="kgstudio.payment.create-payout" />,
        moduleItemKey: 'create_payout',
        path: '/payment/create-payout',
        icon: <PayoutIcon />,
        public: true,
        secondary: true,
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.common.my-shop" />,
    moduleKey: 'asset_pro',
    items: [
      {
        name: <FormattedMessage id="kgstudio.payment.payment-item-list" />,
        moduleItemKey: 'payment_product',
        path: '/payment/payment-item-list',
        icon: <GiftIcon />,
        public: true,
      },
      {
        name: <FormattedMessage id="kgstudio.common.treasury" />,
        moduleItemKey: 'treasury',
        path: '/asset/treasury',
        icon: <AssetTreasuryIcon />,
        permissions: [{ resource: 'asset_pool', action: 'read' }],
      },
      {
        name: <FormattedMessage id="kgstudio.common.transfer" />,
        moduleItemKey: 'send_token',
        path: '/asset/transfer',
        regex: /\/asset\/(send-status(?:\?.*)?|transfer)$/,
        icon: <AssetTransferIcon />,
        permissions: [{ resource: 'transaction', action: 'apply' }],
      },
      {
        name: <FormattedMessage id="kgstudio.transactions.title" />,
        moduleItemKey: 'transaction_history',
        path: '/asset/transactions',
        regex: /\/asset\/transactions(\/[A-Za-z\d-]+)?$/,
        icon: <AssetTransactionIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.products" />,
        moduleItemKey: 'market',
        path: '/asset/products',
        icon: <AssetProductIcon />,
        permissions: [
          { resource: 'asset_pro_product', action: 'read' },
          { resource: 'asset_pro_product', action: 'edit' },
        ],
      },
      {
        name: <FormattedMessage id="kgstudio.common.orders" />,
        moduleItemKey: 'market',
        path: '/asset/orders',
        regex: /\/asset\/orders(\/[A-Z\d-]+)?(\?status=pending)?$/,
        icon: <AssetOrdersIcon />,
        permissions: [{ resource: 'asset_pro_order', action: 'read' }],
      },
      {
        name: <FormattedMessage id="kgstudio.common.operators" />,
        moduleItemKey: 'operators',
        path: '/asset/operators',
        icon: <AssetOperatorsIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.market-settings" />,
        moduleItemKey: 'market',
        path: '/asset/market-settings',
        icon: <MarketIcon />,
        permissions: [{ resource: 'asset_pro_market_info', action: 'read' }],
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.common.compliance" />,
    moduleKey: 'compliance',
    items: [
      {
        name: <FormattedMessage id="kgstudio.common.review" />,
        moduleItemKey: 'case_management',
        path: '/compliance/review',
        icon: <ComplianceReviewIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.all-tasks" />,
        moduleItemKey: 'all_tasks',
        path: '/compliance/all-tasks',
        regex: /\/compliance\/(all-tasks|create-a-task|case-management)$/,
        icon: <ComplianceCaseManagementIcon />,
        collapse: true,
        items: [
          {
            name: <FormattedMessage id="kgstudio.common.kyc-form" />,
            path: '/compliance/all-tasks/kyc-form',
          },
          {
            name: <FormattedMessage id="kgstudio.common.idv-tasks" />,
            path: '/compliance/all-tasks/idv-tasks',
          },
          {
            name: <FormattedMessage id="kgstudio.common.cdd-tasks" />,
            path: '/compliance/all-tasks/cdd-tasks',
          },
          {
            name: <FormattedMessage id="kgstudio.common.kyt-tasks" />,
            path: '/compliance/all-tasks/kyt-tasks',
          },
        ],
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.common.wallet" />,
    moduleKey: 'wallet_builder',
    items: [
      {
        name: <FormattedMessage id="kgstudio.common.project" />,
        moduleItemKey: 'project',
        path: '/wallet/projects/overview',
        regex: /\/wallet\/projects\/(\d+|overview)$/,
        icon: <WalletProjectIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.configuration" />,
        moduleItemKey: 'configuration',
        path: '/wallet/projects/1/app-settings/edit',
        regex: /\/wallet\/projects\/\d+\/app-settings\/edit/,
        icon: <WalletConfigIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.app-publish" />,
        moduleItemKey: 'app_publish',
        path: '/wallet/projects/1/publish-settings/edit',
        regex: /\/wallet\/projects\/\d+\/publish-settings\/edit/,
        icon: <AppPublishIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.marketing-tools" />,
        moduleItemKey: 'marketing_tools',
        path: '',
        icon: <MarketingToolsIcon />,
        collapse: true,
        items: [
          // FIXME: ensure the path is correct and navigation is working
          {
            name: <FormattedMessage id="kgstudio.common.explorer-banner" />,
            path: '/wallet/projects/1/app-settings/edit?step=explorer#banner',
            regex: /\/wallet\/projects\/\d+\/app-settings\/edit?step=explorer#banner/,
          },
          {
            name: <FormattedMessage id="kgstudio.common.dapp-list" />,
            path: '/wallet/projects/1/app-settings/edit?step=explorer#dapp',
            regex: /\/wallet\/projects\/\d+\/app-settings\/edit?step=explorer#dapp/,
          },
          {
            name: <FormattedMessage id="kgstudio.common.push-notification" />,
            path: '/wallet/marketing/push-notification',
            disabled: true,
          },
          {
            name: <FormattedMessage id="kgstudio.common.in-app-message" />,
            path: '/wallet/marketing/in-app-message',
            disabled: true,
          },
        ],
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.common.nft-boost" />,
    moduleKey: 'nft_boost',
    items: [
      {
        name: <FormattedMessage id="kgstudio.nft.campaign" />,
        moduleItemKey: 'campaign',
        path: '/nft/campaign/overview',
        regex: /\/nft\/campaign\/(\d+(\/edit)?|overview|create)$/,
        icon: <NftCampaignIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.nft.reward" />,
        moduleItemKey: 'reward',
        path: env.NEXT_PUBLIC_KG_DASHBOARD,
        icon: <NftRewardIcon />,
        external: true,
      },
    ],
  },
  {
    name: <FormattedMessage id="kgstudio.common.settings" />,
    moduleKey: 'admin',
    public: true,
    items: [
      {
        name: <FormattedMessage id="kgstudio.common.members" />,
        moduleItemKey: 'members',
        path: '/setting/team',
        public: true,
        permissions: [{ resource: 'member', action: 'read' }],
        icon: <User360ManagementIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.account_setting" />,
        moduleItemKey: 'user',
        path: '/setting/user',
        public: true,
        icon: <User360ManagementIcon />,
      },
      {
        name: <FormattedMessage id="kgstudio.common.general" />,
        moduleItemKey: 'billing',
        path: '/setting/system',
        icon: <Settings />,
        permissions: [{ resource: 'organization_info', action: 'edit' }],
      },
    ],
  },
];

const checkPermission = (userPermissions: Permission[], routePermissions: Permission[] | undefined) => {
  let result = true;
  if (!routePermissions) return result;

  routePermissions?.forEach((p) => {
    if (find(userPermissions, p) === undefined) {
      result = false;
    }
  });
  return result;
};

// Helper function to filter user authorized modules
export const filterModulesRoutes = (
  routes: RoutesType[],
  modules: Module | undefined,
  notificationCounts: Record<string, number>,
  permissions: Permission[] | undefined,
): RoutesType[] => {
  if (!modules) return [];

  return routes
    .filter((route) => Object.keys(modules).find((r) => r === route.moduleKey || route?.public))
    .map((route) => {
      if (route.items) {
        return {
          ...route,
          items: route.items
            .filter((l2Item) => {
              if (l2Item?.public) return true;

              let hasPermissions = true;

              const parentModule = (l2Item.moduleKey ?? route.moduleKey) as keyof Module;
              const hasModule = modules[parentModule]?.includes(l2Item.moduleItemKey as never);

              if (l2Item.hasOwnProperty('permissions')) {
                hasPermissions = !!permissions && checkPermission(permissions, l2Item?.permissions);
              }

              return hasModule && hasPermissions;
            })
            .map((l2Item) => {
              if (!l2Item?.items) return l2Item;
              const permissionSubItems = l2Item.items.filter(
                (subItem) => !subItem?.moduleItemKey || Object.keys(modules).includes(subItem.moduleItemKey as never),
              );
              return { ...l2Item, items: permissionSubItems };
            })
            .map((d) => {
              return notificationCounts?.[d.path] ? { ...d, notificationCount: notificationCounts?.[d.path] } : d;
            }),
        };
      }

      return route;
    });
};
