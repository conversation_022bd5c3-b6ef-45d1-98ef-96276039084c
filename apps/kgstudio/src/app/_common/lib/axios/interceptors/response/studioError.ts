// TODO: Move sentry capture to another file
import { AxiosInstance, isAxiosError } from 'axios';
import { match, P } from 'ts-pattern';

import { isApiError } from '@/app/_common/lib/api';
import { ApiInternalError, ApiNetworkError, ApiResponseError } from '@/app/_common/lib/errors/axios';
import { BaseError } from '@/app/_common/lib/errors/base';
import { StudioApiError } from '@/app/_common/lib/errors/studioApi';
import { captureException } from '@sentry/nextjs';

/**
 * This interceptor will handle all errors that are thrown by the axios instance.
 * It will handle the following errors:
 * - ApiNetworkError
 * - ApiInternalError
 * - ApiResponseError
 * - StudioApiError: This error is thrown when the Studio API returns an error.
 *
 * @remarks
 * This interceptor will also capture the error using Sentry.(Temporarily)
 */
export const setupStudioErrorInterceptor = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.response.use(undefined, (unknownError: any) => {
    const e = match(unknownError)
      .with(P.instanceOf(BaseError), (error) => {
        return error;
      })
      .with(P.when(isAxiosError), (error) => {
        const { response, config } = error;

        if (!!response) {
          const { status, data } = response;

          return match(data)
            .with(P.when(isApiError), (resp) => {
              return new StudioApiError({
                url: config?.url ?? '',
                status,
                code: resp.code,
                message: resp.message,
                timestamp: resp.timestamp,
                payload: config?.data,
                data: resp,
                params: config?.params,
                headers: config?.headers,
                retries: config?.__retryCount,
                severity: status >= 500 ? 'fatal' : 'error',
              });
            })
            .otherwise((resp) => {
              return new ApiResponseError({
                url: config?.url ?? '',
                status,
                payload: config?.data,
                data: resp,
                params: config?.params,
                headers: config?.headers,
                retries: config?.__retryCount,
              });
            });
        } else {
          return new ApiNetworkError({
            url: config?.url ?? '',
            retries: error.config?.__retryCount,
            error: JSON.stringify(error),
          });
        }
      })
      .otherwise((error) => {
        const originalRequest = error.config;

        return new ApiInternalError(error, { url: originalRequest?.url });
      });

    const unauthorizedError = e.walk((err) => {
      return err instanceof StudioApiError && err.status === 401 && err.retries === 0;
    });

    if (!unauthorizedError) {
      captureException(e, {
        level: e.severity,
        tags: {
          name: e.name,
        },
        contexts: e.contexts,
      });
    }

    throw unknownError;
  });

  return axiosInstance;
};
