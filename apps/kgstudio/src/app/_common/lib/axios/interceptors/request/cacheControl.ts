import { AxiosInstance } from 'axios';

/**
 * Setup cache control interceptor
 * This interceptor is used to prevent google CDN from caching the response
 */
export const setupCacheControlInterceptor = (axiosInstance: AxiosInstance): AxiosInstance => {
  axiosInstance.interceptors.request.use((config) => {
    // This is to prevent google CDN from caching the response
    if (config.method === 'get') {
      config.headers['Cache-Control'] = 'no-cache, max-age=0';
    }

    return config;
  });

  return axiosInstance;
};
