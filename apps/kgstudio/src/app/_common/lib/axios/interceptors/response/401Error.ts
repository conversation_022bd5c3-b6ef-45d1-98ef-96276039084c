import { AxiosInstance, InternalAxiosRequestConfig, isAxiosError } from 'axios';
import { identity } from 'fp-ts/lib/function';
import { match, P } from 'ts-pattern';

let refreshTokenPromise: Promise<unknown> | null = null;

type RefreshTokenOptions<T> = {
  onSuccess?: (config: InternalAxiosRequestConfig<unknown>, refreshTokenResponse: T) => unknown | Promise<unknown>;
  onError?: (error: unknown) => unknown | Promise<unknown>;
  onSettled?: () => unknown | Promise<unknown>;
};

/**
 * This interceptor is used to
 * 1. refresh the token if it is a 401 error
 * 2. retry the request if it is a 401 error
 *
 * @remarks
 * If the response status is 401 and has not been retried after token refreshed, retry the request
 */
export const setup401ErrorInterceptor =
  <T>(refreshTokenHandler: () => Promise<T>, options: RefreshTokenOptions<T> = {}) =>
  (axiosInstance: AxiosInstance) => {
    axiosInstance.interceptors.response.use(identity, async (unknownError: unknown) => {
      const refreshToken = async () => {
        if (!refreshTokenPromise) {
          refreshTokenPromise = refreshTokenHandler().finally(() => {
            refreshTokenPromise = null;
          });
        }
        return refreshTokenPromise as Promise<T>;
      };

      return match(unknownError)
        .with(P.when(isAxiosError), async (error) => {
          const { config, response } = error;

          // Check if it is a 401 error and has not been retried after token refreshed
          if (!!config && response?.status === 401 && !config.__retriedAfterRefresh) {
            config.__retriedAfterRefresh = true;

            try {
              const resp = await refreshToken();

              await options.onSuccess?.(config, resp);

              return axiosInstance(config);
            } catch (error) {
              await options.onError?.(error);

              return Promise.reject(error);
            }
          }

          return Promise.reject(error);
        })
        .otherwise(identity);
    });

    return axiosInstance;
  };
