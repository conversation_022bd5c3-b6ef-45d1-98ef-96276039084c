import { AxiosInstance, isAxiosError } from 'axios';
import { identity } from 'fp-ts/lib/function';
import { match, P } from 'ts-pattern';

import { jitterBackoff } from '@kryptogo/utils';

const MAX_RETRIES = 3;

/**
 * This interceptor will retry the request
 * 1. If the response status is 5xx server error
 * 2. If the there is no response => network error
 */
export const setupJitterBackoffRetryInterceptor = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.response.use(identity, async (unknownError: unknown) => {
    return match(unknownError)
      .with(P.when(isAxiosError), async (error) => {
        const { config, response } = error;

        if (!config) return Promise.reject(error);

        config.__retryCount = config.__retryCount ?? 0;

        const shouldRetryOnConfig =
          config.retry == null
            ? true
            : typeof config.retry === 'boolean'
              ? config.retry
              : config.retry(config.__retryCount, error);
        if (!shouldRetryOnConfig) return Promise.reject(error);

        if (config.__retryCount >= MAX_RETRIES) {
          return Promise.reject(error);
        }
        config.__retryCount += 1;

        const shouldRetryOnResponse =
          !response || (response.status >= 500 && response.status < 600) || response.status === 429;

        if (!shouldRetryOnResponse) {
          return Promise.reject(error);
        }

        const backoffDelay = jitterBackoff(config.__retryCount);
        await new Promise((resolve) => setTimeout(resolve, backoffDelay));

        return axiosInstance.request(config);
      })
      .otherwise(identity);
  });

  return axiosInstance;
};
