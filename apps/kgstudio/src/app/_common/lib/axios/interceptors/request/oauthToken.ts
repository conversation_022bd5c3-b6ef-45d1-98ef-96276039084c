import { AxiosInstance } from 'axios';

import { StudioHeaders } from '@/app/_common/constant';
import { OAuthTokenHandler } from '@/app/_common/lib/OAuthTokenHandler';

/**
 * Setup oauth token interceptor
 * This interceptor is used to add the oauth token to the request header if it is not already present
 */
export const setupOauthTokenInterceptor = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.request.use((config) => {
    const overriddenToken = config.headers[StudioHeaders.STUDIO_OAUTH_TOKEN];
    const token = OAuthTokenHandler.get();

    if (!overriddenToken && token) {
      config.headers[StudioHeaders.STUDIO_OAUTH_TOKEN] = token;
    }

    return config;
  });

  return axiosInstance;
};
