import axios from 'axios';
import { pipe } from 'fp-ts/lib/function';

import { env } from '@/env.mjs';

import { setupCacheControlInterceptor } from '../interceptors/request';
import {
  setupJitterBackoffRetryInterceptor,
  setupStudioErrorInterceptor,
  setupUnwrapAxiosErrorInterceptor,
} from '../interceptors/response';

const baseAuthAxiosInstance = axios.create({
  baseURL: env.NEXT_PUBLIC_API_ENDPOINT,
  headers: { accept: 'application/json', 'Content-Type': 'application/json' },
});

/**
 * Auth axios instance
 *
 * @remarks
 * This axios instance is used to make requests to the auth api endpoints
 * 1. POST /email
 * 2. POST /login
 * 3. POST /login/google
 * 4. POST /studio/login_v2
 * 5. POST /studio/refresh
 *
 */
const authAxiosInstance = pipe(
  baseAuthAxiosInstance,
  // Request interceptors
  // Order: bottom to top
  setupCacheControlInterceptor,

  // Response interceptors
  // Order: top to bottom
  setupJitterBackoffRetryInterceptor,

  setupStudioErrorInterceptor,

  // TODO: Uncomment this when sentry interceptor is abstracted from studioErrorInterceptor
  // setupSentryInterceptor,

  // TODO: Remove this after the application is migrated to handle custom BaseError type instead of AxiosError
  setupUnwrapAxiosErrorInterceptor,
);

export default authAxiosInstance;
