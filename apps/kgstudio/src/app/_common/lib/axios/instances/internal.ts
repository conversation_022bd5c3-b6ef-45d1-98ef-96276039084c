import axios from 'axios';
import { pipe } from 'fp-ts/lib/function';

import { StudioHeaders } from '@/app/_common/constant';
import { OAuthTokenHandler } from '@/app/_common/lib/OAuthTokenHandler';
import { useAuthStore } from '@/app/_common/store';
import { env } from '@/env.mjs';

import { setupCacheControlInterceptor, setupOauthTokenInterceptor } from '../interceptors/request';
import {
  setup401ErrorInterceptor,
  setupJitterBackoffRetryInterceptor,
  setupStudioErrorInterceptor,
  setupUnwrapAxiosErrorInterceptor,
} from '../interceptors/response';

const baseInternalAxiosInstance = axios.create({
  baseURL: env.NEXT_PUBLIC_API_ENDPOINT,
  headers: { accept: 'application/json', 'Content-Type': 'application/json' },
  validateStatus: function (status) {
    // throw exception when not 2xx or 3xx
    return status >= 200 && status < 400;
  },
});

const internalAxiosInstance = pipe(
  baseInternalAxiosInstance,
  // Request interceptors
  // Order: bottom to top
  setupCacheControlInterceptor,
  setupOauthTokenInterceptor,

  // Response interceptors
  // Order: top to bottom
  setupJitterBackoffRetryInterceptor,

  // use arrow function to make sure `this` is bound to the correct instance
  setup401ErrorInterceptor(() => OAuthTokenHandler.refresh(), {
    // remove the expired token from the request header
    onSuccess: (config) => delete config.headers[StudioHeaders.STUDIO_OAUTH_TOKEN],
    onError() {
      useAuthStore.getState().logout();
    },
  }),

  setupStudioErrorInterceptor,

  // TODO: Uncomment this when sentry interceptor is abstracted from studioErrorInterceptor
  // setupSentryInterceptor,

  // TODO: Remove this after the application is migrated to handle custom BaseError type instead of AxiosError
  setupUnwrapAxiosErrorInterceptor,
);

export default internalAxiosInstance;
