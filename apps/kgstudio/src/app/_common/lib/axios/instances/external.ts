import axios from 'axios';
import { pipe } from 'fp-ts/lib/function';

import { setupJitterBackoffRetryInterceptor, setupUnwrapAxiosErrorInterceptor } from '../interceptors/response';
import { setupStudioErrorInterceptor } from '../interceptors/response/studioError';

const baseExternalAxiosInstance = axios.create();

const externalAxiosInstance = pipe(
  baseExternalAxiosInstance,
  setupJitterBackoffRetryInterceptor,
  setupStudioErrorInterceptor,

  // TODO: Uncomment this when sentry interceptor is abstracted from studioErrorInterceptor
  // setupSentryInterceptor,

  // TODO: Remove this after the application is migrated to handle custom BaseError type instead of AxiosError
  setupUnwrapAxiosErrorInterceptor,
);

export default externalAxiosInstance;
