import axios from '@/app/_common/lib/axios/instances/external';
import { NonEvmChainId } from '@/app/_common/types/web3';

const api: Record<NonEvmChainId, string> = {
  tron: 'https://api.trongrid.io/jsonrpc',
  shasta: 'https://api.shasta.trongrid.io/jsonrpc',
};

export type TxReceiptResponseStatus = '0x0' | '0x1' | '0x';

export type TxStatus = 'success' | 'failed' | 'pending';

export type TxReceiptResponse = {
  jsonrpc: string;
  result?: {
    status?: TxReceiptResponseStatus;
  };
};

export const getTxStatus = async (chainId: NonEvmChainId, txHash: string): Promise<TxStatus> => {
  const request = {
    method: 'POST',
    url: api[chainId],
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      id: 1,
      jsonrpc: '2.0',
      method: 'eth_getTransactionReceipt',
      params: [txHash],
    }),
  };

  const res = await axios(request);
  const data: TxReceiptResponse = res.data;

  switch (data?.result?.status) {
    case '0x0':
    case '0x':
      return 'failed';
    case '0x1':
      return 'success';
    default:
      return 'pending';
  }
};
