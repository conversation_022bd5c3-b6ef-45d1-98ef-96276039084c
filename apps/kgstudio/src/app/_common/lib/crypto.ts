import { x25519 } from '@noble/curves/ed25519';
import * as ed from '@noble/ed25519';
import { bytesToHex } from '@noble/hashes/utils';

/**
 * Utility functions for X25519 key exchange to securely retrieve mnemonic phrases
 */

// Define the exported key pair type
interface X25519KeyPair {
  privateKey: string;
  publicKey: string;
}

/**
 * Convert Uint8Array to hexadecimal string
 */
export function byteArrayToHexString(byteArray: Uint8Array): string {
  return Array.from(byteArray, function (byte) {
    return ('0' + (byte & 0xff).toString(16)).slice(-2);
  }).join('');
}

/**
 * Convert base64 string to Uint8Array
 */
export function Base64StringToUint8Array(base64String: string): Uint8Array {
  const binaryString = atob(base64String);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * Convert Uint8Array to base64 string
 */
export function Uint8ToBase64String(u8a: Uint8Array): string {
  return btoa(String.fromCharCode.apply(null, Array.from(u8a)));
}

/**
 * Convert hex string to Uint8Array
 */
export function hexStringToUint8Array(hexString: string): Uint8Array {
  return new Uint8Array(hexString.match(/.{1,2}/g)?.map((byte) => parseInt(byte, 16)) || []);
}

const StringToUint8Array = (str: string) => {
  console.log('str', str);
  return new Uint8Array(str.split('').map((c) => c.charCodeAt(0)));
};

/**
 * Generates an X25519 key pair for secure key exchange
 * @param exportType - The format to export keys ('base64' or 'hex')
 * @returns Generated key pair with public and private keys
 */
export async function generateX25519KeyPair(exportType: 'base64' | 'hex' = 'base64'): Promise<X25519KeyPair> {
  // Generate a new X25519 private key
  const privateObj = ed.utils.randomPrivateKey();

  // Apply the same bit manipulations as Go backend for compatibility
  privateObj[0] &= 248;
  privateObj[31] &= 127;
  privateObj[31] |= 64;

  // Derive the public key from the private key
  const pubkeyObj = x25519.getPublicKey(privateObj);

  let privateKeyString: string;
  let publicKeyString: string;

  if (exportType === 'base64') {
    privateKeyString = Uint8ToBase64String(privateObj);
    publicKeyString = Uint8ToBase64String(pubkeyObj);
  } else {
    privateKeyString = bytesToHex(privateObj);
    publicKeyString = bytesToHex(pubkeyObj);
  }
  return {
    privateKey: privateKeyString,
    publicKey: publicKeyString,
  };
}

/**
 * 使用 AES-CTR 解密數據，匹配 Go 後端實現
 */
async function decryptDataAes(
  privateKeyBytes: Uint8Array,
  data: string,
  isBase64Encode: boolean = true,
): Promise<Uint8Array> {
  try {
    // 將編碼的數據轉換為字節
    const concatenatedBytes = isBase64Encode ? Base64StringToUint8Array(data) : hexStringToUint8Array(data);

    // 根據 Go 後端的格式設置
    const ivLength = 16;
    const hmacLength = 32;

    // 檢查數據長度
    if (concatenatedBytes.length < ivLength + hmacLength) {
      throw new Error('數據長度不足以包含 IV 和 HMAC');
    }

    // 提取 IV (Go 後端中的 iv)
    const iv = concatenatedBytes.slice(0, ivLength);

    // 提取密文 (不包括 IV 和 HMAC)
    const ciphertext = concatenatedBytes.slice(ivLength, concatenatedBytes.length - hmacLength);

    // 提取 HMAC (在 Web Crypto API 中不驗證，但記錄下來)
    // const hmac = concatenatedBytes.slice(concatenatedBytes.length - hmacLength);
    // console.log('HMAC (最後 32 字節):', byteArrayToHexString(hmac));

    // 使用 AES-CTR 模式進行解密
    const key = await window.crypto.subtle.importKey(
      'raw',
      new Uint8Array(privateKeyBytes),
      { name: 'AES-CTR', length: 256 },
      false,
      ['decrypt'],
    );

    // Web Crypto API 中的 AES-CTR 解密
    const decrypted = await window.crypto.subtle.decrypt(
      {
        name: 'AES-CTR',
        counter: iv,
        length: 128, // 標準計數器長度
      },
      key,
      ciphertext,
    );

    return new Uint8Array(decrypted);
  } catch (error) {
    throw new Error('Failed to decrypt data with AES: ' + (error instanceof Error ? error.message : String(error)));
  }
}

/**
 * 從遠端解密加密數據
 * 對應 Go 後端的實現
 */
export async function decryptRemoteEncryptedData(
  accountPrivateKey: string,
  remotePublicKey: string,
  encryptedData: string,
  keyFormat: 'base64' | 'hex' = 'hex',
): Promise<string> {
  try {
    // 轉換 privateKey 從 string 到 Uint8Array
    let privateKeyBytes: Uint8Array;
    if (keyFormat === 'base64') {
      privateKeyBytes = Base64StringToUint8Array(accountPrivateKey);
    } else {
      // 將 hex 字串轉為 Uint8Array
      privateKeyBytes = hexStringToUint8Array(accountPrivateKey);
    }

    // 轉換 remotePublicKey
    let remotePublicKeyBytes: Uint8Array;
    if (remotePublicKey.includes('+') || remotePublicKey.includes('/') || remotePublicKey.includes('=')) {
      // Likely base64
      remotePublicKeyBytes = Base64StringToUint8Array(remotePublicKey);
    } else {
      // Likely hex
      remotePublicKeyBytes = hexStringToUint8Array(remotePublicKey);
    }

    // 計算 X25519 共享秘密 - 使用與 Go 後端兼容的方法
    const sharedSecret = x25519.getSharedSecret(privateKeyBytes, remotePublicKeyBytes);

    // 使用 AES 解密
    const decryptedBytes = await decryptDataAes(sharedSecret, encryptedData, true);

    // 返回解碼結果 - 直接返回十六進制字符串，因為這是私鑰
    const hexResult = byteArrayToHexString(decryptedBytes);
    return hexResult;
  } catch (error) {
    console.error('Failed to decrypt remote encrypted data:', error);
    throw new Error('[decryptRemoteEncryptedData] error');
  }
}

/**
 * 此方法替換原來的 decryptMnemonic，使用 AES 而非 XOR
 */
export async function decryptMnemonic(
  privateKeyString: string,
  ciphertext: string,
  publicKey: string,
  keyFormat: 'base64' | 'hex' = 'base64',
): Promise<string> {
  return decryptRemoteEncryptedData(privateKeyString, publicKey, ciphertext, keyFormat);
}
