import { decodeJwt, JWTPayload } from 'jose';

import { prettyPrint } from '@/app/_common/lib/utils';

import { BaseError } from './base';

export type RefreshTokenErrorType = RefreshTokenError & { name: 'RefreshTokenError' };
export class RefreshTokenError extends BaseError {
  override name = 'RefreshTokenError';

  constructor(
    cause: Error,
    {
      token,
    }: {
      token: string;
    },
  ) {
    let decodedJwt: JWTPayload | undefined;

    try {
      decodedJwt = decodeJwt(token);
    } catch (e) {
      decodedJwt = undefined;
    }

    super(cause instanceof BaseError ? cause.shortMessage : 'Refresh token error', {
      severity: 'fatal',
      cause,
      metaMessages: [...(!!decodedJwt ? ['Decoded JWT:', `${prettyPrint(decodedJwt)}`] : 'Invalid JWT token')],
      context: {
        token,
      },
    });
  }
}
