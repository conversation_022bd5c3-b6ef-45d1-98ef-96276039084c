import { prettyPrint } from '../utils';
import { BaseApiResponseErrorParams } from './axios';
import { BaseError } from './base';
import { StudioErrorCode } from './errorCodes';

type StudioApiErrorParams = BaseApiResponseErrorParams & {
  code: number;
  message: string;
  timestamp: number;
};

export type StudioApiErrorType = StudioApiError & { name: 'StudioApiError' };
export class StudioApiError extends BaseError {
  override name = 'StudioApiError';

  url: string;
  code: number;
  status: number;
  retries: number;

  constructor({
    url,
    status,
    payload,
    data,
    params,
    headers,
    code,
    message,
    timestamp,
    retries,
    severity = 'error',
  }: StudioApiErrorParams) {
    super(`[${status}]${StudioErrorCode[code] ?? code}: ${message}`, {
      severity,
      metaMessages: [
        `Request URL: ${url}`,
        ...(!!params ? ['Request Params:', `${prettyPrint(params)}`] : []),
        ...(!!payload ? ['Request payload:', `${prettyPrint(payload)}`] : []),
        ...(!!data ? ['Response data:', `${prettyPrint(data)}`] : []),
        `Retries: ${retries ?? 0}`,
        `timestamp: ${timestamp}`,
      ].filter(Boolean),
      context: {
        ...(headers ?? {}),
        url,
        code,
        status,
        retries: retries ?? 0,
      },
    });

    this.url = url;
    this.code = code;
    this.status = status;
    this.retries = retries ?? 0;
  }
}
