import { prettyPrint } from '@/app/_common/lib/utils';

import { BaseError, Severity } from './base';

export type ApiNetworkErrorType = ApiNetworkError & { name: 'ApiNetworkError' };
export class ApiNetworkError extends BaseError {
  override name = 'ApiNetworkError';

  constructor({ url, retries, error }: { url: string; retries?: number; error?: string }) {
    super(`Request failed due to network error`, {
      severity: 'info',
      metaMessages: [`Request URL: ${url}`, `Retries: ${retries ?? 0}`, `Error: ${error ?? ''}`],
    });
  }
}

export type BaseApiResponseErrorParams = {
  url: string;
  status: number;
  payload?: unknown | undefined;
  data?: unknown | undefined;
  params?: Record<string, unknown> | undefined;
  headers?: Record<string, unknown> | undefined;
  retries?: number;
  severity?: keyof typeof Severity;
};
export type ApiResponseErrorType = ApiResponseError & { name: 'ApiResponseError' };
export class ApiResponseError extends BaseError {
  override name = 'ApiResponseError';

  url: string;
  status: number;
  data?: unknown;
  retries: number;

  constructor({
    url,
    status,
    payload,
    data,
    params,
    headers,
    retries,
    severity = 'error',
  }: BaseApiResponseErrorParams) {
    super(`[${status}]: Third party API request failed`, {
      severity,
      metaMessages: [
        `Request URL: ${url}`,
        !!params ? `Request Params:\n${prettyPrint(params)}` : '',
        !!payload ? `Request payload:\n${prettyPrint(payload)}` : '',
        !!data ? `Response data:\n${prettyPrint(data)}` : '',
        `Retries: ${retries ?? 0}`,
      ].filter(Boolean),
      context: {
        headers,
      },
    });

    this.url = url;
    this.status = status;
    this.data = data;
    this.retries = retries ?? 0;
  }
}

export type ApiInternalErrorType = ApiInternalError & { name: 'ApiInternalError' };
export class ApiInternalError extends BaseError {
  override name = 'ApiInternalError';

  constructor(cause: Error, { url }: { url: string }) {
    super(`Axios internal error`, {
      severity: 'fatal',
      cause,
      metaMessages: [`Request URL: ${url}`],
    });
  }
}
