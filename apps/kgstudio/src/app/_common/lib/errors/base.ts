type BaseErrorParameters = {
  severity?: keyof typeof Severity;
  cause?: BaseError | Error | undefined;
  details?: string | undefined;
  metaMessages?: string[] | undefined;
  context?: Record<string, unknown> | undefined;
};

// Severity levels: default is `error`
export enum Severity {
  fatal = 0,
  error,
  warning,
  info,
  debug,
}
export type Context = Record<string, unknown>;
export type Contexts = Record<string, Context | undefined>;

export type BaseErrorType = BaseError & { name: 'StudioError' };
export class BaseError extends Error {
  // These are for Sentry error information
  s: Severity;
  contexts?: Contexts;

  details?: string;
  metaMessages?: string[] | undefined;
  shortMessage: string;

  override name = 'StudioError';

  constructor(shortMessage: string, args: BaseErrorParameters = {}) {
    super();

    const details = args.cause instanceof BaseError ? args.cause.details : args.cause?.message || args.details;

    this.message = [
      shortMessage || 'An error occurred.',
      '',
      ...(args.metaMessages ? [...args.metaMessages, ''] : []),
      ...(details ? [`Details: ${details}`] : []),
    ].join('\n');

    this.s = Severity[args.severity ?? 'error'];
    this.contexts = {
      Error: {
        error: this,
        ...args.context,
      },
    };
    if (args.cause) this.cause = args.cause;
    this.details = details;
    this.metaMessages = args.metaMessages;
    this.shortMessage = shortMessage;
  }

  get severity(): keyof typeof Severity {
    // Get the minimum severity level of the error and the causes chain
    const level = this.cause instanceof BaseError ? Math.min(Severity[this.cause.severity], this.s) : this.s;

    return Severity[level] as keyof typeof Severity;
  }

  walk(): Error;
  walk(fn: (err: unknown) => boolean): Error | null;
  walk(fn?: any): any {
    return walk(this, fn);
  }
}

function walk(err: unknown, fn?: ((err: unknown) => boolean) | undefined): unknown {
  if (fn?.(err)) return err;
  if (err && typeof err === 'object' && 'cause' in err) return walk(err.cause, fn);
  return fn ? null : err;
}
