import { refreshToken } from '@/app/[locale]/auth/login/_services/command/refreshToken';
import { StudioCookieKey } from '@/app/_common/constant';

import { createCookieOperator } from './cookie';

class OAuthTokenHandler {
  private static tokenRefreshHandlers: Array<() => void> = [];
  private static oauthTokenOperator = createCookieOperator(StudioCookieKey.STUDIO_OAUTH_TOKEN);

  static get(): string | undefined {
    return this.oauthTokenOperator.get();
  }

  static set(token: string): void {
    this.oauthTokenOperator.set(token);
  }

  // will be called when token is refreshed
  static onTokenRefresh(handler: () => void): void {
    this.tokenRefreshHandlers.push(handler);
  }

  static clear(): void {
    this.oauthTokenOperator.remove();
  }

  static async refresh(): Promise<void> {
    if (!this.get()) return;
    const response = await refreshToken({
      refresh_token: this.get() ?? '',
    });
    if (response.data.access_token) {
      this.set(response.data.access_token);
    }
    this.tokenRefreshHandlers.forEach((handler) => handler());
  }
}

export { OAuthTokenHandler };
