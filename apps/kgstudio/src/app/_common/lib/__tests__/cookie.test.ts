import Cookies from 'js-cookie';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { z } from 'zod';

import { createCookieOperator } from '../cookie';

vi.mock('js-cookie');

describe('createCookieOperator', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('string cookie', () => {
    const cookieKey = 'testCookie';
    const operator = createCookieOperator(cookieKey);

    it('should set string cookie', () => {
      operator.set('test-value');
      expect(Cookies.set).toHaveBeenCalledWith(cookieKey, 'test-value', { path: '/' });
    });

    it('should get string cookie', () => {
      (Cookies.get as unknown as ReturnType<typeof vi.fn>).mockReturnValue('test-value');
      expect(operator.get()).toBe('test-value');
    });

    it('should remove cookie', () => {
      operator.remove();
      expect(Cookies.remove).toHaveBeenCalledWith(cookieKey, { path: '/' });
    });
  });

  describe('schema validated cookie', () => {
    const cookieKey = 'testJsonCookie';
    const schema = z.string().email();
    const operator = createCookieOperator(cookieKey, schema);

    it('should set string with schema validation', () => {
      operator.set('<EMAIL>');
      expect(Cookies.set).toHaveBeenCalledWith(cookieKey, '"<EMAIL>"', { path: '/' });
    });

    it('should get string with schema validation', () => {
      (Cookies.get as unknown as ReturnType<typeof vi.fn>).mockReturnValue('<EMAIL>');
      expect(operator.get()).toBe('<EMAIL>');
    });

    it('should return undefined for invalid data format', () => {
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

      (Cookies.get as unknown as ReturnType<typeof vi.fn>).mockReturnValue('not-an-email');
      const result = operator.get();

      expect(consoleError).toHaveBeenCalled();
      expect(result).toBeUndefined();
      consoleError.mockRestore();
    });

    it('should handle invalid data when setting', () => {
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

      const invalidData = 'not-an-email';
      operator.set(invalidData);

      expect(consoleError).toHaveBeenCalled();
      expect(Cookies.set).not.toHaveBeenCalled();
      consoleError.mockRestore();
    });
  });
});
