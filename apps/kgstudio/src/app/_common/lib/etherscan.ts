import axios from '@/app/_common/lib/axios/instances/external';
import { EvmChainId } from '@/app/_common/types/web3';
import { env } from '@/env.mjs';

const api: Record<EvmChainId, string> = {
  eth: 'https://api.etherscan.io/v2/api',
  matic: 'https://api.polygonscan.com/api',
  bsc: 'https://api.bscscan.com/api',
  sepolia: 'https://api-sepolia.etherscan.io/api',
  arb: 'https://api.arbiscan.io/api',
  base: 'https://api.basescan.org/api',
  optimism: 'https://api-optimistic.etherscan.io/api',
};

const apiKey: Record<EvmChainId, string> = {
  eth: env.NEXT_PUBLIC_ETHERSCAN_API_KEY,
  matic: env.NEXT_PUBLIC_POLYGONSCAN_API_KEY,
  bsc: env.NEXT_PUBLIC_BSCSCAN_API_KEY,
  sepolia: env.NEXT_PUBLIC_ETHERSCAN_API_KEY,
  arb: env.NEXT_PUBLIC_ARBISCAN_API_KEY,
  base: env.NEXT_PUBLIC_ETHERSCAN_API_KEY,
  optimism: env.NEXT_PUBLIC_ETHERSCAN_API_KEY,
};

// 0: failed. 1: successful. "": pending
export type TxReceiptResponseStatus = '0' | '1' | '';

export type TxStatus = 'success' | 'failed' | 'pending';

export type TxReceiptResponse = {
  status: string;
  message: string;
  result: {
    status: TxReceiptResponseStatus;
  };
};

export const getTxStatus = async (chainId: EvmChainId, txHash: string): Promise<TxStatus> => {
  const baseUrl = api[chainId];
  const res = await axios.get<TxReceiptResponse>(`${baseUrl}`, {
    params: {
      module: 'transaction',
      action: 'gettxreceiptstatus',
      chainid: '1',
      txHash,
      apikey: apiKey[chainId],
    },
  });
  switch (res.data.result.status) {
    case '0':
      return 'failed';
    case '1':
      return 'success';
    default:
      return 'pending';
  }
};
