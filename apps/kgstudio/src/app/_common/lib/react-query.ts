import {
  matchQuery,
  MutationCache,
  QueryClient,
  QueryKey,
  UseMutationOptions,
  UseQueryOptions,
} from '@tanstack/react-query';

export type UseQueryOptionType<TData, TError = unknown> =
  | Omit<UseQueryOptions<TData, TError, TData, QueryKey>, 'queryKey' | 'queryFn'>
  | undefined;

export type UseMutationOptionType<TCommand = void, TData = unknown, TError = unknown> = UseMutationOptions<
  TData,
  TError,
  TCommand,
  unknown
>;

export const generateStudioQueryClient = () => {
  const queryClient = new QueryClient({
    mutationCache: new MutationCache({
      onSuccess: async (_data, _variables, _context, mutation) => {
        // invalidates is used to invalidate the query, fire and forget
        queryClient.invalidateQueries({
          predicate: (query) => {
            // invalidate everything if meta.invalidates is 'all'
            if (mutation.meta?.invalidates === 'all') {
              return true;
            }

            // invalidate all matching tags at once
            return mutation.meta?.invalidates?.some((queryKey) => matchQuery({ queryKey }, query)) ?? false;
          },
        });

        // awaitInvalidates is used to wait for the invalidation to complete
        await queryClient.invalidateQueries(
          {
            predicate: (query) =>
              // invalidate all matching tags at once
              // or everything if no meta is provided
              mutation.meta?.awaitInvalidates?.some((queryKey) => matchQuery({ queryKey }, query)) ?? false,
          },
          {
            cancelRefetch: false,
          },
        );
      },
    }),
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000,
        cacheTime: Infinity,
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return queryClient;
};
