import clsx, { ClassValue } from 'clsx';
import { formatInTimeZone, zonedTimeToUtc } from 'date-fns-tz';
import { set } from 'lodash-es';
import { toast } from 'sonner';
import { P, match } from 'ts-pattern';

import { twMerge } from '@kryptogo/2b/lib';

export const identity = <T>(val: T) => val;

// eslint-disable-next-line @typescript-eslint/no-empty-function
export const noop = () => {};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const nestedObject = (input: Record<string, string>) =>
  Object.entries(input).reduce((acc, [key, value]) => set(acc, key, value), {});

export const removeNullish = <T extends Record<string, unknown>>(obj: T): Partial<T> => {
  Object.entries(obj).forEach(([key, val]) =>
    match(val)
      //
      .with(<PERSON>.nullish, () => delete obj[key])
      .otherwise(identity),
  );

  return obj;
};

export const removeEmptyString = <T extends Record<string, unknown>>(obj: T): Partial<T> => {
  Object.entries(obj).forEach(([key, val]) =>
    match(val)
      //
      .with(
        P.string,
        (str) => str.trim().length === 0,
        () => delete obj[key],
      )
      .otherwise(identity),
  );

  return obj;
};

export const removeEmptyArray = <T extends Record<string, unknown>>(obj: T): Partial<T> => {
  Object.entries(obj).forEach(([key, val]) =>
    match(val)
      //
      .with(
        P.array(P._),
        (arr) => arr.length === 0,
        () => delete obj[key],
      )
      .otherwise(identity),
  );

  return obj;
};

export function formatDate(date: Date, timeZone?: string, formatStr?: string): string;
export function formatDate(unixTimeStamp: number, timeZone?: string, formatStr?: string): string;
export function formatDate(
  dateOrUnixTimeStamp: Date | number,
  timeZone?: string,
  formatStr = 'yyyy/MM/dd HH:mm (zzz)',
): string {
  const defaultTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const unixTimestampInMs =
    dateOrUnixTimeStamp instanceof Date ? dateOrUnixTimeStamp.getTime() : dateOrUnixTimeStamp * 1000;
  const date = new Date(unixTimestampInMs);

  const timeZoneStr = formatInTimeZone(date, timeZone ?? defaultTimeZone, formatStr);

  return timeZoneStr;
}

export const getUnixTimeStampFromZoned = (date: Date, timeZone?: string) => {
  const defaultTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const utcDate = zonedTimeToUtc(date, timeZone ?? defaultTimeZone);

  return Math.floor(utcDate.getTime() / 1000);
};

export const formatFileSize = (bytes: number) => {
  const suffixes: string[] = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
  let i = 0;

  while (bytes >= 1024 && i < suffixes.length - 1) {
    bytes /= 1024;
    i++;
  }

  return `${bytes.toFixed(2)} ${suffixes[i]}`;
};

export const maskSecretString = (str: string, start: number, end: number, maskChar = '*') => {
  // end could possibly be negative
  const endIdx = end < 0 ? str.length + end : end;

  return str.substring(0, start) + maskChar.repeat(endIdx - start) + str.substring(endIdx);
};

export const handleCopyAddress = (address: string, t: any) => {
  navigator.clipboard.writeText(address);
  toast.success(t('kgstudio.common.address-copied'));
};

export const readOnlyArrayIncludes = <T extends U, U>(readOnlyArray: ReadonlyArray<T>, value: U): value is T => {
  return readOnlyArray.includes(value as T);
};

export const prettyPrint = (args: unknown) => {
  if (typeof args === 'object' && args !== null && !Array.isArray(args)) {
    const entries = Object.entries(args)
      .map(([key, value]) => {
        if (value === undefined || value === false) return null;
        return [key, value];
      })
      .filter(Boolean) as [string, string][];
    const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0);
    return entries.map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`).join('\n');
  }

  return JSON.stringify(args);
};
