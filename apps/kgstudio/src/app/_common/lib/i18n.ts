import { pick } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { unstable_cache } from 'next/cache';
import { notFound } from 'next/navigation';
import 'server-only';

import { nestedObject } from '@/app/_common/lib/utils';
import { env } from '@/env.mjs';

export const LOCALIZELY_API_BASE_URL = 'https://api.localizely.com/v1';
export const LOCALIZELY_PROJECT_ID = '7bbf0447-393e-4381-b95c-596afeda0b7b';
export const TRANSLATION_NAMESPACE = ['common', 'kgstudio'];

export type TranslationFunc = ReturnType<typeof useTranslations>;

export const getMessagesFromLocalizely = unstable_cache(
  async (locale: string) => {
    const params = new URLSearchParams({
      type: 'json',
      lang_codes: locale,
      export_empty_as: 'main',
    });
    try {
      const response = await fetch(
        `${LOCALIZELY_API_BASE_URL}/projects/${LOCALIZELY_PROJECT_ID}/files/download?${params.toString()}`,
        {
          headers: {
            'X-Api-Token': env.LOCALIZELY_API_KEY,
            Accept: 'application/octet-stream',
          },
          cache: 'no-store',
        },
      );

      if (!response.ok) {
        throw {
          name: 'getMessagesFromLocalizelyError',
          message: 'Failed to fetch messages from localizely',
          cause: await response.json(),
        };
      }
      const arrayBuffer = await response.arrayBuffer();
      const text = new TextDecoder().decode(arrayBuffer);
      const flattenMessages = JSON.parse(text);
      return formatMessage(flattenMessages);
    } catch (error) {
      console.error(error);
      return getMessages(locale);
    }
  },
  ['getMessages'],
  {
    revalidate: 5 * 60, // 5 minutes
  },
);

export const getMessages = async (locale: string) => {
  try {
    const flattenMessages = (await import(`@/locales/${locale}.json`)).default;

    return formatMessage(flattenMessages);
  } catch (error) {
    notFound();
  }
};

const formatMessage = (flattenMessages: Record<string, string>) => {
  const messages = nestedObject(flattenMessages);

  return pick(messages, TRANSLATION_NAMESPACE);
};
