import * as A from 'fp-ts/Array';
import * as O from 'fp-ts/Option';
import * as R from 'fp-ts/Record';
import { pipe } from 'fp-ts/lib/function';
import { z } from 'zod';

/**
 * This function filters out the keys that caused the error in the zod schema,
 * and returns the input object with only the keys that did not cause the error.
 *
 * @param schema
 * @returns Return the processed schema used for filtering out the keys that caused the error,
 * Mainly used for search params filtering for now.
 */
export const zodFilterSchema = <T extends z.ZodTypeAny>(schema: T) => {
  return schema.catch(({ error, input }: { error: z.ZodError<z.infer<T>>; input: unknown }) => {
    const errorKeys = Object.keys(error.format());

    const filteredInput = pipe(
      input,
      O.fromPredicate((input): input is Record<string, unknown> => typeof input === 'object' && input !== null),
      O.map(R.toEntries),
      O.map(A.filter(([key]) => !errorKeys.includes(key))),
      O.map(R.fromEntries),
      O.getOrElse(() => ({})),
    );

    return filteredInput as Partial<z.infer<T>>;
  });
};
