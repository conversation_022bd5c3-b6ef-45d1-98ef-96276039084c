'use client';

import { Check, ChevronLeft, Globe } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { startTransition } from 'react';

import { useIsClient, usePageHeader } from '@/app/_common/hooks';
import { cn } from '@/app/_common/lib/utils';
import { useAuthStore } from '@/app/_common/store';
import { usePathname, useRouter, Link } from '@/i18n/navigation';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@kryptogo/2b';

const Navbar = () => {
  const t = useTranslations();
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  const email = useAuthStore((state) => state.userInfo?.email);
  const { title: pageTitle, backLink } = usePageHeader();
  const { isClient } = useIsClient();

  const languages = [
    { locale: 'en-US', label: t('kgstudio.common.language.english') },
    { locale: 'zh-TW', label: t('kgstudio.common.language.traditional-chinese') },
    { locale: 'zh-CN', label: t('kgstudio.common.language.simplified-chinese') },
    { locale: 'vi-VN', label: t('kgstudio.common.language.vietnamese') },
    { locale: 'ja', label: t('kgstudio.common.language.japanese') },
    { locale: 'es', label: t('kgstudio.common.language.spanish') },
  ];

  const handleLocaleChange = (nextLocale: string) => {
    startTransition(() => {
      router.replace(pathname, { locale: nextLocale as 'en-US' | 'es' | 'ja' | 'vi-VN' | 'zh-CN' | 'zh-TW' });
    });
  };

  const logOut = () => {
    useAuthStore.getState().logout();
  };

  return (
    <header className="flex w-full flex-col-reverse items-end justify-between gap-6 lg:flex-row lg:items-start">
      <div className="text-primary flex items-center gap-[10px] self-start lg:self-auto">
        {backLink && (
          <Link href={backLink}>
            <ChevronLeft size={24} className="cursor-pointer stroke-current" />
          </Link>
        )}
        {pageTitle && <h1 className="text-h1 text-primary font-bold">{pageTitle}</h1>}
      </div>

      <div className="z-header flex items-center gap-4">
        {isClient ? (
          <NavigationMenu>
            <NavigationMenuList>
              <NavigationMenuItem>
                <NavigationMenuTrigger
                  className="ml-2 max-w-[170px] rounded-xl px-3 shadow-[0px_4px_12px_rgba(0,0,0,0.1)] md:max-w-none"
                  disableHoverOnOpen
                >
                  <p className="text-primary text-body truncate" data-cy="user-email">
                    {email}
                  </p>
                </NavigationMenuTrigger>
                <NavigationMenuContent className="border-primary min-w-[150px] rounded-xl bg-white">
                  <div className={cn('text-body hover:bg-surface-secondary cursor-pointer px-4 py-3')} onClick={logOut}>
                    <p className="text-small md:text-body text-center" data-cy="user-logout">
                      {t('kgstudio.common.logout')}
                    </p>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        ) : null}
        <NavigationMenu viewportClassName="right-6 md:right-10">
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger
                className="ml-2 rounded-xl px-3 shadow-[0px_4px_12px_rgba(0,0,0,0.1)]"
                disableHoverOnOpen
              >
                <Globe size="20px" />
              </NavigationMenuTrigger>
              <NavigationMenuContent className="border-primary w-[110px] bg-white md:w-auto md:min-w-[150px]">
                {languages.map((language) => (
                  <div
                    className={cn(
                      'text-body hover:bg-surface-secondary flex cursor-pointer select-none justify-between px-2 py-[6px] md:px-4 md:py-3',
                      {
                        'bg-brand-primary-lighter': locale === language.locale,
                      },
                    )}
                    key={language.locale}
                    onClick={() => handleLocaleChange(language.locale)}
                  >
                    <p className="text-small md:text-body">{language.label}</p>
                    {locale === language.locale && <Check className="text-brand-primary h-5 w-5" />}
                  </div>
                ))}
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>
    </header>
  );
};

export { Navbar };
