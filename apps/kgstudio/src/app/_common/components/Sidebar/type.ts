import { Permission } from '@/app/_common/services/organization/model';

interface RoutesType {
  name: React.ReactNode;
  moduleKey: string;
  adminOnly?: boolean;
  public?: boolean;
  items: MenuItemType[];
}

/**
 * @param {string} moduleKey - When a module item needs to be displayed in the overview layer, use moduleKey to specify the item's actual module key.
 */
interface MenuItemType {
  name: React.ReactNode;
  moduleKey?: string;
  moduleItemKey: string;
  path: string;
  external?: boolean;
  icon?: JSX.Element;
  secondary?: boolean;
  collapse?: boolean;
  adminOnly?: boolean;
  public?: boolean;
  regex?: RegExp;
  disabled?: boolean;
  items?: {
    name: React.ReactNode;
    path: string;
    moduleItemKey?: string;
    external?: boolean;
    icon?: JSX.Element;
    secondary?: boolean;
    adminOnly?: boolean;
    public?: boolean;
    regex?: RegExp;
    disabled?: boolean;
  }[];
  permissions?: Permission[];
}

export type { MenuItemType, RoutesType };
