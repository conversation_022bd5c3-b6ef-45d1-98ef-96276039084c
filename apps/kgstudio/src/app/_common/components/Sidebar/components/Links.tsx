import { ReactNode } from 'react';

import { cn } from '@/app/_common/lib/utils';
import { usePathname, Link } from '@/i18n/navigation';
import { Accordion, Separator } from '@kryptogo/2b';

import { RoutesType } from '../type';

export function Links(props: { routes: RoutesType[] }) {
  const pathname = usePathname();
  const { routes } = props;

  const isBoundaryOfMenu = (routeIndex: number, moduleKey: RoutesType['moduleKey'], routes: RoutesType[]) => {
    return routeIndex === routes.length - 1 || moduleKey === 'overview';
  };

  const isActiveRoute = (routePath: string) => {
    const currentRoute = pathname;

    // FIXME: This is the temp fix for the overview route that point to a specific id route
    const pathIsOverviewRoute = routePath.split('/').at(-1) === 'overview';
    const baseRoutePath = routePath.split('/').slice(0, -1).join('/');
    const regex = new RegExp(`${baseRoutePath}/\\d+$`);

    return currentRoute == routePath || (pathIsOverviewRoute && regex.test(currentRoute));
  };

  const createMenuItem = (
    menu: {
      icon?: ReactNode;
      name: ReactNode;
      disabled?: boolean;
      notificationCount?: number;
      moduleItemKey?: string;
    },
    isActive: boolean,
    isSubMenu?: boolean,
  ) => {
    return (
      <div
        className={cn(
          'relative flex w-full flex-row items-center justify-start space-x-3 rounded-lg transition-all duration-500',
          {
            'bg-brand-primary-lighter text-brand-primary-dark': isActive,
            'hover:bg-surface-secondary text-primary': !isActive && !menu.disabled,
            'text-disabled': menu.disabled,
            'px-4 py-3': isSubMenu !== true,
          },
          `${menu.moduleItemKey}`,
        )}
      >
        <div className="stroke-current">{menu.icon ?? <div className="h-4 w-4" />}</div>
        <div className="font-dm text-body-2-bold">{menu.name}</div>
        {!!menu?.notificationCount && menu.notificationCount > 0 && (
          <span
            className="bg-error text-button-sm absolute right-3 z-10 flex aspect-square w-[22px] shrink-0 items-center justify-center rounded-full text-white"
            data-cy={`${menu.moduleItemKey}-notification`}
          >
            {menu.notificationCount}
          </span>
        )}
      </div>
    );
  };

  return (
    <Accordion
      type="multiple"
      className="mt-4 space-y-4"
      defaultValue={Array.from({ length: routes.length }).map((_, i) => `${i}`)}
    >
      {routes.map((route, key) => {
        return (
          <Accordion.Item value={`${key}`} key={key} className="border-none">
            {route.name && (
              <Accordion.Trigger className="text-placeholder [&[data-state=closed]>*]:text-secondary [&[data-state=closed]>*]:hover:text-primary [&[data-state=open]>*]:hover:text-secondary px-4 py-2 [&>*]:transition-all [&>*]:duration-500 [&[data-state=open]>svg]:text-[transparent]">
                <p className="font-dm text-base font-bold">{route.name}</p>
              </Accordion.Trigger>
            )}
            <Accordion.Content className="p-0">
              {(route.items || []).map((item, k) => {
                if (item.items && item.items.length > 0) {
                  return (
                    <Accordion key={k} type="multiple" className="space-y-[16px]" defaultValue={['first']}>
                      <Accordion.Item value="first" key={k} className="border-none">
                        <Accordion.Trigger className="text-placeholder hover:bg-surface-secondary rounded-lg px-4 py-3 transition-all duration-500">
                          {createMenuItem(item, false, true)}
                        </Accordion.Trigger>
                        <Accordion.Content className="px-2 py-0">
                          {item.items?.map((subItem, subKey) => {
                            return subItem.disabled === true ? (
                              <ul
                                key={`${k}-${subKey}`}
                                className="pointer-events-none"
                                data-cy={`sidebar-item-${subItem.path}`}
                              >
                                {createMenuItem(subItem, isActiveRoute(subItem.path.toLowerCase()))}
                              </ul>
                            ) : (
                              <Link
                                key={`${k}-${subKey}`}
                                href={subItem.path}
                                target={subItem.external === true ? '_blank' : undefined}
                                className="hover:cursor-pointer"
                                data-cy={`sidebar-item-${subItem.path}`}
                              >
                                <ul key={`${k}-${subKey}`}>
                                  {createMenuItem(subItem, isActiveRoute(subItem.path.toLowerCase()))}
                                </ul>
                              </Link>
                            );
                          })}
                        </Accordion.Content>
                      </Accordion.Item>
                    </Accordion>
                  );
                } else {
                  return item.disabled === true ? (
                    <ul key={k} className="pointer-events-none" data-cy={`sidebar-item-${item.path}`}>
                      {createMenuItem(item, isActiveRoute(item.path.toLowerCase()))}
                    </ul>
                  ) : (
                    <Link
                      key={k}
                      href={item.path}
                      target={item.external === true ? '_blank' : undefined}
                      className="hover:cursor-pointer"
                      data-cy={`sidebar-item-${item.path}`}
                    >
                      <ul key={k}>{createMenuItem(item, isActiveRoute(item.path.toLowerCase()))}</ul>
                    </Link>
                  );
                }
              })}
              {isBoundaryOfMenu(key, route.moduleKey, routes) || (
                <Separator key={`${key}-separator`} className="border-primary mt-4" />
              )}
            </Accordion.Content>
          </Accordion.Item>
        );
      })}
    </Accordion>
  );
}
