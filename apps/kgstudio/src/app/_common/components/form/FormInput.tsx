'use client';

import Image from 'next/image';
import { InputHTMLAttributes } from 'react';
import { Control, FieldValues, Path } from 'react-hook-form';

import { cn } from '@/app/_common/lib/utils';
import {
  FormControl,
  FormDescription,
  FormField,
  FormHint,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@kryptogo/2b';

import alertCircle from './assets/AlertCircle.svg';

interface FormInputProps<T extends FieldValues> extends InputHTMLAttributes<HTMLInputElement> {
  title?: string;
  desc?: string;
  info?: React.ReactNode;
  hint?: React.ReactNode;
  name: Path<T>;
  control: Control<T>;
  required?: boolean;
  withoutMessage?: boolean;
  suffix?: React.ReactNode;
  innerClassName?: string;
  wrapperClassName?: string;
}

const FormInput = <TFieldValues extends FieldValues>({
  name,
  title,
  desc,
  control,
  required,
  info,
  hint,
  withoutMessage,
  innerClassName,
  wrapperClassName,
  ...props
}: FormInputProps<TFieldValues>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn('flex flex-col gap-2', wrapperClassName)}>
          {(!!title || !!info) && (
            <div className="flex items-end justify-between">
              {title && (
                <div className="flex flex-col gap-2">
                  <FormLabel required={required}>{title}</FormLabel>
                  {desc && <FormDescription>{desc}</FormDescription>}
                </div>
              )}
              {info && <FormHint>{info}</FormHint>}
            </div>
          )}
          <FormControl>
            <Input
              {...field}
              {...props}
              innerClassName={innerClassName}
              onChange={(e: any) => {
                field.onChange(e);
                props.onChange?.(e);
              }}
            />
          </FormControl>
          {hint && <FormHint>{hint}</FormHint>}
          {!withoutMessage && <FormMessage />}
        </FormItem>
      )}
    />
  );
};

function FormInputError({ error, className }: { error: string | undefined; className?: string }) {
  return (
    <div className={cn('flex items-center', className)} data-cy="input-error-hint">
      <Image src={alertCircle} alt="alert" className="mr-1 h-4 w-4" />
      <div className="text-error text-sm">{error}</div>
    </div>
  );
}

export { FormInput, FormInputError };
