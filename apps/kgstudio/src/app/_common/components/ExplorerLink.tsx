import { Copy } from 'lucide-react';
import { toast } from 'sonner';

import { ChainId } from '@/app/_common/types/web3';
import { Link } from '@/i18n/navigation';
import { getExplorerUrl, truncateTxhashOrAddress } from '@kryptogo/utils';

type Type = 'address' | 'txHash';

interface ExplorerLinkProps {
  hex: string;
  chain: ChainId;
  type: Type;
  copyable?: boolean;
  truncate?: boolean;
  t?: any;
}

const ExplorerLink = ({ hex, type, chain, t, copyable = false, truncate = false }: ExplorerLinkProps) => {
  const href = type === 'address' ? getExplorerUrl('address', chain, hex) : getExplorerUrl('tx', chain, hex);
  const text = truncate ? truncateTxhashOrAddress(hex) : hex;

  const handleCopy = () => {
    navigator.clipboard.writeText(hex);
    const toastContent = !!t ? t('kgstudio.common.address-copied') : 'Address copied!';

    toast.success(toastContent);
  };

  return (
    <div className="text-primary item-gap-small flex items-center">
      <Link href={href ?? '#'} target="_blank" rel="noreferrer noopener">
        <span className="underline decoration-1">{text}</span>
      </Link>
      {copyable && (
        <button onClick={handleCopy}>
          <Copy className="h-3 w-3 stroke-current" />
        </button>
      )}
    </div>
  );
};

export { ExplorerLink };
