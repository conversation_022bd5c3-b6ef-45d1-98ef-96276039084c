import { match } from 'ts-pattern';

import { env } from '@/env.mjs';
import { Link } from '@/i18n/navigation';
import { Badge } from '@kryptogo/2b';

const CaseIDBadge = ({ type, id }: { type: 'FORM' | 'CDD' | 'IDV'; id: number }) => {
  return match(type)
    .with('FORM', () => (
      <Badge variant="grey">
        <Link
          href={`${env.NEXT_PUBLIC_COMPLIANCE_URL}/submission/${id}`}
          target="_blank"
          rel="noopener noreferrer"
        >{`FORM-${id}`}</Link>
      </Badge>
    ))
    .with('CDD', () => (
      <Badge variant="grey">
        <Link
          href={`${env.NEXT_PUBLIC_COMPLIANCE_URL}/audit/${id}`}
          target="_blank"
          rel="noopener noreferrer"
        >{`CDD-${id}`}</Link>
      </Badge>
    ))
    .with('IDV', () => (
      <Badge variant="grey">
        <Link
          href={`${env.NEXT_PUBLIC_COMPLIANCE_URL}/idv/${id}`}
          target="_blank"
          rel="noopener noreferrer"
        >{`IDV-${id}`}</Link>
      </Badge>
    ))
    .exhaustive();
};

export { CaseIDBadge };
