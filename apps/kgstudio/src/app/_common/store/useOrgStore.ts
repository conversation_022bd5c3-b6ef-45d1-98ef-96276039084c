import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { OrgInfo } from '@/app/_common/services/organization/model';

export interface OrganizationState {
  orgId: number | null;
  orgInfo: OrgInfo | null;
  setOrgId: (id: number) => void;
  setOrgInfo: (orgInfo: OrgInfo) => void;
  clearOrgId: () => void;
}

const useOrganizationStore = create<OrganizationState>()(
  persist(
    immer((set) => ({
      orgId: null,
      orgInfo: null,
      setOrgId: (id: number) => {
        set((state) => {
          state.orgId = id;
        });
      },
      setOrgInfo: (orgInfo) => {
        set((state) => {
          state.orgInfo = orgInfo;
        });
      },
      clearOrgId: () => {
        set((state) => {
          state.orgId = null;
        });
      },
    })),
    { name: 'org-storage', partialize: (state) => ({ orgId: state.orgId }) },
  ),
);

export { useOrganizationStore };
