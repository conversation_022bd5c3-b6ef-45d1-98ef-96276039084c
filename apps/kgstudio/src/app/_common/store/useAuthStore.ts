import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

import { UserInfo } from '@/app/_common/services/organization/model';

import { KgTokenHandler } from '../lib/KgTokenHandler';
import { OAuthTokenHandler } from '../lib/OAuthTokenHandler';
import { useOrganizationStore } from './useOrgStore';

export interface AuthState {
  isLoggedIn: boolean;
  userInfo: UserInfo | null;
  login: (token: string) => void;
  logout: () => void;
  setUserInfo: (userInfo: UserInfo) => void;
}

const useAuthStore = create<AuthState>()(
  immer((set) => ({
    userInfo: null,
    isLoggedIn: !!OAuthTokenHandler?.get(),
    login: (token) => {
      OAuthTokenHandler.set(token);
      set({ isLoggedIn: true });
    },
    logout: () => {
      useOrganizationStore.getState().clearOrgId();
      OAuthTokenHandler.clear();
      KgTokenHandler.clear();
      set({ isLoggedIn: false });
    },
    setUserInfo: (userInfo) => {
      set({ userInfo });
    },
  })),
);

export { useAuthStore };
