import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

export type PageHeader = {
  title?: string;
  backLink?: string;
};

const initialPageHeader: PageHeader = {
  title: undefined,
  backLink: undefined,
};

export interface LayoutState {
  pageHeader: PageHeader;
  setPageHeader: (header: PageHeader) => void;
  resetPageHeader: () => void;
}

const useLayoutStore = create<LayoutState>()(
  immer((set) => ({
    pageHeader: {
      ...initialPageHeader,
    },
    setPageHeader: (header) => {
      set((state) => {
        state.pageHeader.title = header.title;
        state.pageHeader.backLink = header.backLink;
      });
    },
    resetPageHeader: () => {
      set({
        pageHeader: {
          ...initialPageHeader,
        },
      });
    },
  })),
);

export { useLayoutStore };
