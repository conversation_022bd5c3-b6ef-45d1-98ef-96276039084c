'use client';

import { useState } from 'react';
import { WagmiConfig, configureChains, createConfig } from 'wagmi';
import { mainnet, polygon, arbitrum } from 'wagmi/chains';
import { InjectedConnector } from 'wagmi/connectors/injected';
import { MetaMaskConnector } from 'wagmi/connectors/metaMask';
import { publicProvider } from 'wagmi/providers/public';

import { ToastContainer } from '@/2b/toast';
import { generateStudioQueryClient } from '@/app/_common/lib/react-query';
import { TooltipProvider } from '@kryptogo/2b';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

const { publicClient, webSocketPublicClient, chains } = configureChains(
  [mainnet, polygon, arbitrum],
  [publicProvider()],
);
const wagmiConfig = createConfig({
  autoConnect: true,
  publicClient,
  webSocketPublicClient,
  connectors: [
    // @ts-expect-error fix when upgrade to V2
    new MetaMaskConnector({ chains }),
    // @ts-expect-error fix when upgrade to V2
    new InjectedConnector({
      chains,
      options: {
        name: 'Injected',
        shimDisconnect: true,
      },
    }),
  ],
});

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(generateStudioQueryClient);

  return (
    <QueryClientProvider client={queryClient}>
      <WagmiConfig config={wagmiConfig}>
        <TooltipProvider>
          {children}
          <ReactQueryDevtools initialIsOpen={false} />
          <ToastContainer className="z-50 mb-[14px]" position="bottom-center" closeButton richColors />
        </TooltipProvider>
      </WagmiConfig>
    </QueryClientProvider>
  );
}
