'use client';

import { decodeJwt } from 'jose';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import { useTokenExchange, isNoOrganizationError } from '@/app/[locale]/auth/login/_services/command/tokenExchange';
import { Loading } from '@/app/_common/components';
import { HOME_PAGE_URL } from '@/app/_common/constant';
import { KgTokenHandler } from '@/app/_common/lib/KgTokenHandler';
import { useAuthStore } from '@/app/_common/store/useAuthStore';
import { useRouter } from '@/i18n/navigation';

import { oauthAuthorizeError } from './_lib/error';
import { isErrorSearchParams, isSuccessSearchParams } from './_lib/model';

export default function Page() {
  const { login } = useAuthStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = {
    kg_token: searchParams?.get('kg_token'),
    error: searchParams?.get('error'),
    error_code: searchParams?.get('error_code'),
  };

  if (!isSuccessSearchParams(params)) {
    if (isErrorSearchParams(params)) {
      throw oauthAuthorizeError({
        error_code: params.error_code,
        error: params.error.split('+').join(' '),
      });
    }
    throw oauthAuthorizeError(params);
  }

  const { mutate: tokenExchange, error: tokenExchangeError } = useTokenExchange({
    onSuccess: (data) => {
      KgTokenHandler.set(params.kg_token);
      login(data.studio_token);
      router.push(HOME_PAGE_URL);
    },
    onError: (error) => {
      // Check if it's a NoOrganizationError
      if (isNoOrganizationError(error)) {
        console.log('NoOrganizationError detected in OAuth flow, preparing to redirect...');

        // If there's a kg_token in the params, store it and redirect
        if (params.kg_token) {
          KgTokenHandler.set(params.kg_token);

          const decodedJwt = decodeJwt(params.kg_token ?? '');
          const email = decodedJwt.email;

          const targetPath = `/auth/login/create-organization?email=${email}`;

          console.log(`Redirecting to: ${targetPath}`);

          // Using setTimeout to handle redirect after state update
          setTimeout(() => {
            // First verify token is properly stored
            const verifyToken = KgTokenHandler.get();
            if (verifyToken) {
              console.log('Token verified before redirect, proceeding to organization creation');
              router.push(targetPath);
            } else {
              console.error('Token not stored properly, attempting alternative approach');
              // Try storing in sessionStorage as last resort
              sessionStorage.setItem('kg_temp_token_emergency', params.kg_token);
              router.push(targetPath);
            }
          }, 100); // Allow a bit more time for cookie operations
        } else {
          console.warn('No kg_token found in params');
          router.push('/auth/login');
        }
        return;
      }

      // Handle other errors
      throw error;
    },
  });

  useEffect(() => {
    if (!params.kg_token) return;

    tokenExchange({
      kg_token: params.kg_token,
    });
  }, [params.kg_token, tokenExchange]);

  if (tokenExchangeError && !isNoOrganizationError(tokenExchangeError)) {
    throw tokenExchangeError;
  }

  return <Loading />;
}
