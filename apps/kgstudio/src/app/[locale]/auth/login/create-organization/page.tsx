'use client';

import { decodeJwt } from 'jose';
import { ArrowLeft } from 'lucide-react';
import { nanoid } from 'nanoid';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { FormInput } from '@/app/_common/components/form';
import { HOME_PAGE_URL } from '@/app/_common/constant';
import { KgTokenHandler } from '@/app/_common/lib/KgTokenHandler';
import { OAuthTokenHandler } from '@/app/_common/lib/OAuthTokenHandler';
import { useAuthStore } from '@/app/_common/store/useAuthStore';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON>, Form } from '@kryptogo/2b';

import googleIcon from '../_assets/icon-google.png';
import { FormFooter, FormHeader } from '../_components';
import { useCreateOrganization } from '../_services/command/createOrganization';
import { State, useGoogleLogin } from '../_services/command/oauthLogin';
import { useTokenExchange } from '../_services/command/tokenExchange';

const FormSchema = z.object({
  orgName: z.string().min(1, 'Organization name is required'),
});

type FormValues = z.infer<typeof FormSchema>;

export default function CreateOrganizationPage() {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { login, isLoggedIn } = useAuthStore((state) => ({
    login: state.login,
    isLoggedIn: state.isLoggedIn,
  }));
  const [kgToken, setKgToken] = useState<string | undefined>(undefined);
  const token = KgTokenHandler.get();
  const email = searchParams.get('email') ?? (token ? decodeJwt(token).email : undefined);

  useEffect(() => {
    setKgToken(token);
  }, [token]);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      orgName: '',
    },
  });

  const { mutate: createOrganization, isLoading: isCreatingOrganization } = useCreateOrganization({
    onSuccess: async (data) => {
      toast.success(t('kgstudio.organization.create.success'));

      useOrganizationStore.getState().setOrgId(data.organization_id);

      if (isLoggedIn) {
        // For existing users, redirect to the overview page of the new organization
        localStorage.setItem('from-create-org', 'true');
        router.replace(`${HOME_PAGE_URL}?firstVisit=true`);
      } else {
        // For new users, complete the login flow
        if (kgToken) {
          tokenExchange({
            kg_token: kgToken,
          });
        } else {
          toast.error(t('kgstudio.organization.create.error.missing-token'));
        }
      }
    },
    onError: () => {
      toast.error(t('kgstudio.organization.create.error.failed'));
    },
  });

  const { mutate: tokenExchange, isLoading: isTokenExchangeLoading } = useTokenExchange({
    onSuccess: (data) => {
      login(data.studio_token);
      localStorage.setItem('from-create-org', 'true');
      router.replace(`${HOME_PAGE_URL}?firstVisit=true`);
      toast.success(t('kgstudio.organization.create.login-success'));
    },
    onError: () => {
      toast.error(t('kgstudio.organization.create.error.login-failed'));
    },
  });

  const { mutate: getGoogleLoginUrl } = useGoogleLogin({
    onSuccess: (data) => {
      if (data?.login_url) {
        window.location.href = data.login_url;
      }
    },
    onError: (error) => {
      toast.error('Something went wrong. Please try again later.');
      console.error(error);
    },
  });

  const handleSubmit: SubmitHandler<FormValues> = (data) => {
    if (!kgToken && !isLoggedIn) {
      toast.error(t('kgstudio.organization.create.error.missing-token'));
      router.push('/auth/login');
      return;
    }

    if (OAuthTokenHandler.get()) {
      createOrganization({
        org_name: data.orgName,
        email: email as string,
      });
    } else {
      createOrganization({
        org_name: data.orgName,
        email: email as string,
        kg_token: kgToken!,
      });
    }
  };

  const handleGoogleLogin = (): void => {
    const state: State = {
      nonce: nanoid(),
      service: 'studio',
    };
    const base64State = Buffer.from(JSON.stringify(state)).toString('base64');
    getGoogleLoginUrl({
      state: base64State,
    });
  };

  const isLoading = isCreatingOrganization || isTokenExchangeLoading;

  return (
    <div className="grid h-full w-full grid-rows-[auto_1fr_auto] items-center gap-8 p-6">
      <FormHeader
        subtitle={
          isLoggedIn
            ? t('kgstudio.organization.create.subtitle.existing-user')
            : t('kgstudio.organization.create.subtitle.new-user')
        }
      />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          <div className="-mt-8 space-y-4">
            {!token && (
              <button
                className="border-primary text-primary flex w-full items-center justify-center gap-3 rounded-xl border p-3"
                onClick={handleGoogleLogin}
                type="button"
                data-cy="google-login-button"
              >
                <Image src={googleIcon} alt="google-icon" width={20} height={20} />
                <p className="text-md">{t('kgstudio.login.with-google')}</p>
              </button>
            )}
            {token && (
              <>
                <FormInput
                  name="orgName"
                  control={form.control}
                  placeholder={t('kgstudio.organization.create.org-name-placeholder')}
                  disabled={isLoading}
                  data-cy="org-name-input"
                  autoFocus
                />
                <Button
                  className="w-full"
                  loading={isLoading}
                  disabled={!form.formState.isValid || isLoading}
                  data-cy="create-organization-button"
                >
                  {t('kgstudio.organization.create.button')}
                </Button>
              </>
            )}
          </div>
          {isLoggedIn && (
            <Button
              variant="text"
              onClick={() => router.push('/home/<USER>')}
              icon={<ArrowLeft size={16} />}
              className="mt-6 w-full"
              type="button"
            >
              {t('kgstudio.organization.create.back')}
            </Button>
          )}
        </form>
      </Form>
      <FormFooter />
    </div>
  );
}
