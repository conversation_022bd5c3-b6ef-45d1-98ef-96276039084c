'use client';

import { nanoid } from 'nanoid';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { FormInput } from '@/app/_common/components/form';
import { useReCaptcha } from '@/app/_common/hooks';
import { useGoogleAds } from '@/app/_common/hooks/useGoogleAds';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Separator } from '@kryptogo/2b';

import googleIcon from './_assets/icon-google.png';
import { FormFooter, FormHeader } from './_components';
import { EmailSchema } from './_lib/model';
import { useGetOtpCode } from './_services/command/getOtpCode';
import { State, useGoogleLogin } from './_services/command/oauthLogin';

const Footer = dynamic(() => import('../_components/Footer'), { ssr: false });

const FormSchema = z.object({
  email: EmailSchema,
});

type FormValues = z.infer<typeof FormSchema>;

export default function LoginPage() {
  const t = useTranslations();
  const router = useRouter();
  const recaptchaToken = useReCaptcha();
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
    },
  });
  const { mutate: getGoogleLoginUrl, isLoading: isGoogleLoginLoading } = useGoogleLogin({
    onSuccess: (data) => {
      if (data?.login_url) {
        window.location.href = data.login_url;
      }
    },
    onError: (error) => {
      toast.error('Something went wrong. Please try again later.');
      console.error(error);
    },
  });

  // const handleSwitchToPhoneLogin = () => {
  //   console.log('Not available');
  // };
  const { mutate: getOtpCode, isLoading: isGetOtpCodeLoading } = useGetOtpCode({
    onSuccess: () => {
      const params = new URLSearchParams({
        email: form.getValues('email'),
      });
      router.push(`/auth/login/verify-account?${params.toString()}`);
    },
    onError: (error) => {
      toast.error('Something went wrong. Please try again later.');
      console.error(error);
    },
  });
  const formDisabled = !form.formState.isValid || isGoogleLoginLoading || isGetOtpCodeLoading;

  const { trackConversion } = useGoogleAds();
  const handleNext: SubmitHandler<FormValues> = (data) => {
    trackConversion({
      sendTo: 'AW-***********/0nYCCMGQgcoaEPOi0cU_',
    });
    if (!recaptchaToken.current) {
      toast.error(t('kgstudio.common.recaptcha-error'));
      return;
    }
    getOtpCode({
      email: data.email,
      category: 'login',
      recaptcha_token: recaptchaToken.current,
    });
  };
  const handleGoogleLogin = (): void => {
    trackConversion({
      sendTo: 'AW-***********/B8TxCKCNj8oaEPOi0cU_',
    });

    const state: State = {
      nonce: nanoid(),
      service: 'studio',
    };
    const base64State = Buffer.from(JSON.stringify(state)).toString('base64');
    getGoogleLoginUrl({
      state: base64State,
    });
  };
  return (
    <>
      <div className="grid h-full w-full grid-rows-[auto_1fr_auto] items-center gap-8 p-6">
        <FormHeader />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleNext)} className="space-y-8">
            <div className="space-y-4">
              <FormInput
                name="email"
                control={form.control}
                placeholder="Enter your email "
                disabled={isGoogleLoginLoading || isGetOtpCodeLoading}
                data-cy="email-input"
                autoFocus
              />
              <Button className="w-full" disabled={formDisabled} data-cy="email-login">
                {t('kgstudio.auth.login.continue-with-email')}
              </Button>
            </div>

            <div className="grid grid-cols-[1fr_auto_1fr] items-center gap-6">
              <Separator />
              <span className="text-small text-disabled">OR</span>
              <Separator />
            </div>
            <div className="flex w-full items-center justify-center gap-4">
              {/* <button
              className="border-primary text-primary flex items-center justify-center rounded-xl border p-3"
              onClick={handleSwitchToPhoneLogin}
              type="button"
            >
              <Smartphone size={20} />
            </button> */}
              <button
                className="border-primary text-primary flex items-center justify-center rounded-xl border p-3"
                onClick={handleGoogleLogin}
                type="button"
                data-cy="google-login-button"
              >
                <Image src={googleIcon} alt="google-icon" width={20} height={20} />
              </button>
              {/* <button
              className="border-primary text-primary flex items-center justify-center rounded-xl border p-3"
              onClick={handleAppleLogin}
              type="button"
            >
              <Image src={appleIcon} alt="apple-icon" width={20} height={20} />
            </button> */}
            </div>
          </form>
        </Form>
        <FormFooter />
      </div>
      <Footer />
    </>
  );
}
