'use client';

import { ArrowLeft } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { OtpSchema } from '@/app/[locale]/auth/login/_lib/model';
import { useGetOtpCode } from '@/app/[locale]/auth/login/_services/command/getOtpCode';
import { useLoginWithEmail } from '@/app/[locale]/auth/login/_services/command/otpLogin';
import { useTokenExchange, isNoOrganizationError } from '@/app/[locale]/auth/login/_services/command/tokenExchange';
import { FormOtpInput } from '@/app/_common/components/form';
import { HOME_PAGE_URL } from '@/app/_common/constant';
import { useReCaptcha } from '@/app/_common/hooks';
import { KgTokenHandler } from '@/app/_common/lib/KgTokenHandler';
import { isApiError } from '@/app/_common/lib/api';
import { MissingParamsError } from '@/app/_common/lib/error';
import { cn } from '@/app/_common/lib/utils';
import { useAuthStore } from '@/app/_common/store/useAuthStore';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form } from '@kryptogo/2b';

import { FormFooter, FormHeader } from '../_components';
import { Countdown } from './_components';
import { isValidSearchParams } from './_lib/model';

const FormSchema = z.object({
  otp: OtpSchema,
});
type FormValues = z.infer<typeof FormSchema>;

export default function Page() {
  const t = useTranslations();
  const recaptchaToken = useReCaptcha();
  const { login } = useAuthStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = {
    email: searchParams?.get('email'),
  };
  const isParamsValid = isValidSearchParams(params);
  if (!isParamsValid) {
    throw MissingParamsError('/auth/login/verify-account', params);
  }

  const [resendAvailable, setResendAvailable] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number | undefined>(new Date().getTime() + 60 * 1000);
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: { otp: '' },
    mode: 'onBlur',
  });
  const otp = form.watch('otp');

  const { mutate: getOtpCode, isLoading: isGetOtpCodeLoading } = useGetOtpCode({
    onSuccess: () => {
      restrictOtpResendFor(60);
    },
    onError: (error) => {
      restrictOtpResendFor(100);
      toast.error('Something went wrong. Please try again later.');
      console.error(error);
    },
  });
  const { mutate: loginWithEmail, isLoading: isLoginWithEmailLoading } = useLoginWithEmail({
    onSuccess: (data) => {
      KgTokenHandler.set(data.kg_token);
      if (data?.kg_token) {
        tokenExchange({
          kg_token: data.kg_token,
        });
      }
    },
    onError: (error) => {
      if (isApiError(error.cause) && error.cause.status == 400) {
        toast.error('Invalid OTP code');
      } else {
        toast.error('Something went wrong. Please try again later.');
        console.error(error);
      }
    },
  });
  const {
    mutate: tokenExchange,
    error: tokenExchangeError,
    isLoading: isTokenExchangeLoading,
    isSuccess: isTokenExchangeSuccess,
  } = useTokenExchange({
    onSuccess: (data) => {
      login(data.studio_token);
      router.replace(HOME_PAGE_URL);
      toast.success(`Successfully logged in!`);
    },
    onError: (error) => {
      // Check if it's a NoOrganizationError
      if (isNoOrganizationError(error)) {
        console.log('NoOrganizationError detected, preparing to redirect...');

        // Store the kg_token in KgTokenHandler before redirecting
        const loginResponse = error.cause as { kg_token?: string };
        if (loginResponse?.kg_token) {
          KgTokenHandler.set(loginResponse.kg_token);

          const targetPath = `/auth/login/create-organization?email=${params.email}`;

          console.log(`Redirecting to: ${targetPath}`);

          // Using setTimeout to handle redirect after state update
          setTimeout(() => {
            // First verify token is properly stored
            const verifyToken = KgTokenHandler.get();
            if (verifyToken) {
              console.log('Token verified before redirect, proceeding to organization creation');
              router.push(targetPath);
            } else {
              console.error('Token not stored properly, attempting alternative approach');
              // Try storing in sessionStorage as last resort
              sessionStorage.setItem('kg_temp_token_emergency', loginResponse.kg_token!);
              router.push(targetPath);
            }
          }, 100); // Allow a bit more time for cookie operations
        } else {
          console.warn('No kg_token found in error.cause');
          router.push('/auth/login');
        }
        return;
      }

      // Handle other errors
      throw error;
    },
  });

  const restrictOtpResendFor = (seconds: number) => {
    setCountdown(new Date().getTime() + seconds * 1000);
    setResendAvailable(false);
  };

  const onSubmit: SubmitHandler<FormValues> = useCallback(
    (data) => {
      loginWithEmail({
        email: params.email,
        verification_code: data.otp,
      });
    },
    [loginWithEmail, params.email],
  );

  useEffect(() => {
    if (FormSchema.safeParse({ otp }).success) {
      form.handleSubmit(onSubmit)();
    }
  }, [otp, form, onSubmit]);

  if (tokenExchangeError && !isNoOrganizationError(tokenExchangeError)) {
    throw tokenExchangeError;
  }

  return (
    <div className="grid h-full w-full grid-rows-[auto_1fr_auto] items-center gap-8 p-6">
      <FormHeader subtitle="Sign in / Register with KryptoGO to continue" />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-4">
            <FormOtpInput
              name="otp"
              control={form.control}
              valueLength={6}
              className={cn(
                'text-body text-primary grid w-full grid-cols-6 gap-2',
                '[&>input]:h-[50px] [&>input]:w-full [&>input]:rounded-xl [&>input]:p-3',
              )}
              data-cy="otp-input"
              disabled={isLoginWithEmailLoading || isTokenExchangeLoading || isTokenExchangeSuccess}
            />
            <Button
              className="w-full"
              loading={isLoginWithEmailLoading || isTokenExchangeLoading}
              disabled={isGetOtpCodeLoading || !form.formState.isValid || isTokenExchangeSuccess}
            >
              Confirm
            </Button>
            {!resendAvailable && !!countdown && (
              <div className="text-disabled text-button-md flex w-full justify-center font-bold">
                <span>Resend（</span>
                <Countdown countUntil={countdown} setExpired={setResendAvailable} className="text-disabled" />
                <span>）</span>
              </div>
            )}
            {resendAvailable && (
              <Button
                variant="outline"
                onClick={() => {
                  if (!recaptchaToken.current) {
                    toast.error(t('kgstudio.common.recaptcha-error'));
                    return;
                  }
                  getOtpCode({
                    recaptcha_token: recaptchaToken.current,
                    email: params.email,
                    category: 'login',
                  });
                }}
                type="button"
                className="text-secondary border-primary w-full"
                loading={isGetOtpCodeLoading}
              >
                Resend
              </Button>
            )}
          </div>
          <Button
            variant="text"
            onClick={() => {
              router.push('/auth/login');
            }}
            icon={<ArrowLeft size={16} />}
            className="w-full"
            type="button"
          >
            Back
          </Button>
        </form>
      </Form>
      <FormFooter />
    </div>
  );
}
