'use client';

import { getYear } from 'date-fns';
import { Check, ChevronDown, Globe } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { startTransition } from 'react';

import { useRouter } from '@/i18n/navigation';
import { Separator, DropdownMenu } from '@kryptogo/2b';

const Footer = () => {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const currentYear = getYear(new Date());

  const languages = [
    { locale: 'en-US', label: t('kgstudio.common.language.english') },
    { locale: 'zh-TW', label: t('kgstudio.common.language.traditional-chinese') },
    { locale: 'zh-CN', label: t('kgstudio.common.language.simplified-chinese') },
    { locale: 'vi-VN', label: t('kgstudio.common.language.vietnamese') },
    { locale: 'ja', label: t('kgstudio.common.language.japanese') },
    { locale: 'es', label: t('kgstudio.common.language.spanish') },
  ];

  const handleLocaleChange = (nextLocale: string) => {
    startTransition(() => {
      router.replace(pathname, { locale: nextLocale as 'en-US' | 'es' | 'ja' | 'vi-VN' | 'zh-CN' | 'zh-TW' });
    });
  };

  return (
    <footer className="text-secondary text-small z-10 grid grid-cols-[repeat(3,auto)] items-center justify-center gap-x-5 px-5 py-0 md:!flex md:!flex-wrap md:!justify-start md:!px-10 md:!py-6">
      <DropdownMenu>
        <DropdownMenu.Trigger>
          <div className="flex items-center gap-[7px]">
            <Globe size="20px" />
            <p>{languages.find((language) => language.locale === locale)?.label}</p>
            <ChevronDown size={12} />
          </div>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content>
          {languages.map((language) => (
            <DropdownMenu.Item key={language.locale} onClick={() => handleLocaleChange(language.locale)}>
              <p className="text-small md:text-body">{language.label}</p>
              {locale === language.locale && <Check className="text-brand-primary h-5 w-5" />}
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu>
      <Separator orientation="vertical" className="text-secondary hidden w-[2px] md:block" />
      <Link href="https://www.kryptogo.com/terms-of-use" target="_blank">
        <p>{t('common.terms')}</p>
      </Link>
      <Separator orientation="vertical" className="text-secondary hidden w-[2px] md:block" />
      <Link href="https://www.kryptogo.com/privacy" target="_blank">
        <p>{t('common.privacy-policy')}</p>
      </Link>
      <Separator orientation="vertical" className="text-secondary hidden w-[2px] md:block" />
      <p className="col-span-3 text-center">© {currentYear} KryptoGO, Co. Ltd.</p>
    </footer>
  );
};

export default Footer;
