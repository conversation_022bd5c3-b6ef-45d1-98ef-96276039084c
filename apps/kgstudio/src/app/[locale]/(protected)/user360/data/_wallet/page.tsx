'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';

import { useIsClient } from '@/app/_common/hooks';
import { Separator } from '@kryptogo/2b';
import { truncateTxhashOrAddress } from '@kryptogo/utils';

import {
  DataCard,
  HeatmapCard,
  LineChartCard,
  PieChartCard,
  StatisticCard,
  getRandomTimestamps,
} from '../../_components';

const WalletData = () => {
  const { isClient } = useIsClient();
  const t = useTranslations();

  if (!isClient) return null;
  return (
    <div className="space-y-6">
      <h1 className="text-h1 text-primary font-bold">{t('kgstudio.data.header')}</h1>
      <section className="grid grid-cols-3 gap-6">
        <StatisticCard
          title={t('kgstudio.data.registered-users')}
          value={9969}
          formatter="number"
          percentage={{
            value: 0.7,
          }}
          caption={t('kgstudio.data.since-last-month')}
          icon="users"
        />
        <StatisticCard
          title={t('kgstudio.data.wallets')}
          value={89947}
          formatter="number"
          percentage={{
            value: 0.7,
          }}
          caption={t('kgstudio.data.since-last-month')}
          icon="wallet"
        />
        <StatisticCard
          title={t('kgstudio.data.balance')}
          value={3278054}
          formatter="currency"
          percentage={{
            value: 0.7,
          }}
          caption={t('kgstudio.data.since-last-month')}
          icon="coins"
        />
        <StatisticCard
          title={t('kgstudio.data.active-users')}
          value={6688}
          formatter="number"
          percentage={{
            value: 0.7,
          }}
          caption={t('kgstudio.data.since-last-month')}
          icon="chart"
        />
        <StatisticCard
          title={t('kgstudio.data.users-balance-greater')}
          value={11208}
          formatter="number"
          percentage={{
            value: 0.7,
          }}
          caption={t('kgstudio.data.since-last-month')}
        />
        <StatisticCard
          title={t('kgstudio.data.wallet-balance-greater')}
          value={34705}
          formatter="number"
          percentage={{
            value: 0.7,
          }}
          caption={t('kgstudio.data.since-last-month')}
        />
      </section>
      <section className="grid grid-cols-2 items-stretch gap-6">
        <LineChartCard
          title={t('kgstudio.data.registered-users')}
          data={[
            { date: new Date('2023-08-01'), phone: 5063, email: 2309 },
            { date: new Date('2023-08-02'), phone: 15000, email: 10000 },
            { date: new Date('2023-08-03'), phone: 20320, email: 15000 },
            { date: new Date('2023-08-04'), phone: 8304, email: 5234 },
            { date: new Date('2023-08-05'), phone: 12432, email: 10587 },
            { date: new Date('2023-08-06'), phone: 18543, email: 20895 },
            { date: new Date('2023-08-07'), phone: 22123, email: 23000 },
            { date: new Date('2023-08-08'), phone: 24312, email: 28098 },
          ]}
          groupKeys={['phone', 'email']}
          className="h-[300px]"
        />
        <LineChartCard
          title={t('kgstudio.data.wallets')}
          data={[
            { date: new Date('2023-08-01'), 'balance=0': 15063, 'balance>0': 12309 },
            { date: new Date('2023-08-02'), 'balance=0': 25000, 'balance>0': 20000 },
            { date: new Date('2023-08-03'), 'balance=0': 30320, 'balance>0': 25000 },
            { date: new Date('2023-08-04'), 'balance=0': 48304, 'balance>0': 35234 },
            { date: new Date('2023-08-05'), 'balance=0': 52432, 'balance>0': 40587 },
            { date: new Date('2023-08-06'), 'balance=0': 58543, 'balance>0': 50895 },
            { date: new Date('2023-08-07'), 'balance=0': 62123, 'balance>0': 53000 },
            { date: new Date('2023-08-08'), 'balance=0': 64312, 'balance>0': 58098 },
          ]}
          groupKeys={['balance=0', 'balance>0']}
          className="h-[300px]"
        />
        <div className="bg-surface-primary grid h-[300px] grid-rows-[auto_auto_1fr] gap-2 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.data.top-users')}</h3>
          <div className="text-body-2 text-primary grid h-full grid-cols-[200px_repeat(3,1fr)] gap-2">
            {/* Thead */}
            <p className="text-secondary">{t('kgstudio.data.wallet-address')}</p>
            <p className="text-secondary">{t('kgstudio.data.login')}</p>
            <p className="text-secondary">{t('kgstudio.data.events')}</p>
            <p className="text-secondary">{t('kgstudio.data.balance')}</p>
            <Separator className="col-span-3" />
          </div>

          {/* Tbody */}
          <div className="text-body-2 text-primary grid auto-rows-[40px] grid-cols-[200px_repeat(3,1fr)] content-start items-center gap-2 overflow-y-auto">
            {[
              {
                address: '******************************************',
                image_url:
                  'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/77.jpg',
                login: 35,
                events: 3807,
                balance: 413308,
              },
              {
                address: '******************************************',
                image_url:
                  'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1201.jpg',
                login: 24,
                events: 5602,
                balance: 198520,
              },
              {
                address: '0x2D3Aa15D15d19E27e76A7dF71a808C4f61d4954C',
                image_url:
                  'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/130.jpg',
                login: 66,
                events: 2095,
                balance: 294059,
              },
              {
                address: '0x3c7D46F71CcEb88f59E1480E2fA80a1442Bd3B51',
                image_url:
                  'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1035.jpg',
                login: 51,
                events: 5626,
                balance: 208601,
              },
              {
                address: '0x4A61F6dE180Eb156FCA672cdd6b284c99e6A899d',
                image_url:
                  'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/910.jpg',
                login: 49,
                events: 5417,
                balance: 294071,
              },
            ].map((datum) => (
              <>
                <div className="flex items-center gap-2">
                  <div className="relative h-6 w-6 overflow-hidden rounded-full">
                    <Image src={datum.image_url} alt={datum.address} fill />
                  </div>
                  <p>{truncateTxhashOrAddress(datum.address)}</p>
                </div>
                <p>{datum.login}</p>
                <p>{datum.events}</p>
                <p>
                  {datum.balance.toLocaleString('en-US', {
                    maximumFractionDigits: 2,
                  })}
                </p>
              </>
            ))}
          </div>

          {/* <div className="space-y-2">{Array.from({ length: 5 }).map((_, i) => ({}))}</div> */}
        </div>
        <div className="bg-surface-primary grid h-[300px] grid-rows-[auto_1fr] gap-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.data.retention')}</h3>
          <div className="grid grid-cols-2 grid-rows-2 gap-2">
            <DataCard title={t('kgstudio.data.new-users')} icon="new-users" value={758} />
            <DataCard title={t('kgstudio.data.churn-users')} icon="users-down" value={406} />
            <DataCard
              title={t('kgstudio.data.retention-rate')}
              icon="line-up"
              value={29.76}
              format={(value) => `${value}%`}
            />
            <DataCard
              title={t('kgstudio.data.churn-rate')}
              icon="line-down"
              value={70.24}
              format={(value) => `${value}%`}
            />
          </div>
        </div>
        <PieChartCard
          title={t('kgstudio.data.tx-events')}
          data={[
            {
              name: 'Ethereum',
              value: 331806.475,
            },
            { name: 'BNB Chain', value: 293010.641 },
            { name: 'Polygon', value: 208272.372 },
            { name: 'Tron', value: 134764.476 },
            { name: 'Others', value: 53089.036 },
          ]}
          overlayInfo={{
            title: t('kgstudio.data.all'),
            percentage: {
              value: 2.3,
              prefix: true,
              icon: true,
            },
          }}
          className="h-[320px]"
        />
        <PieChartCard
          title={t('kgstudio.data.balance')}
          data={[
            {
              name: 'Ethereum',
              value: 1196489.71,
            },
            { name: 'BNB Chain', value: 1357114.36 },
            { name: 'Polygon', value: 601195.104 },
            { name: 'Tron', value: 127844.106 },
            { name: 'Others', value: 29502.486 },
          ]}
          overlayInfo={{
            title: t('kgstudio.data.all'),
            percentage: {
              value: 0.8,
              prefix: true,
              icon: true,
            },
          }}
          className="h-[320px]"
        />

        <div className="bg-surface-primary grid h-[300px] grid-rows-[auto_1fr] gap-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.data.balance-greater-than-zero')}</h3>
          <div className="grid grid-cols-2 gap-2">
            <DataCard title={t('kgstudio.data.user')} icon="users" value={758} />
            <DataCard title={t('kgstudio.data.wallet')} icon="wallet" value={29.76} format={(value) => `${value}%`} />
          </div>
        </div>
        <HeatmapCard
          title={t('kgstudio.data.user-activities')}
          data={getRandomTimestamps(new Date('2023-08-01'), new Date('2023-08-30'), 1000)}
          className="h-[300px]"
        />
      </section>
    </div>
  );
};

export default WalletData;
