'use client';

import * as O from 'fp-ts/Option';
import * as R from 'fp-ts/Record';
import { pipe } from 'fp-ts/function';
import { Edit, FileClock, FileSearch, UserCheck } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { usePageHeader } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiUser360Hooks } from '@/app/_common/services';
import { StatSchema } from '@/app/_common/services/user360';
import { useOrganizationStore } from '@/app/_common/store';
import { Skeleton } from '@kryptogo/2b';

import { PieChartCard, StatisticCard } from '../../_components';

const ComplianceData = () => {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.compliance.title') });
  const orgId = useOrganizationStore((state) => state.orgId);
  const CHART_LABEL_MAP = {
    verified: t('kgstudio.kyc-status.verified'),
    pending: t('kgstudio.kyc-status.pending'),
    rejected: t('kgstudio.kyc-status.rejected'),
    registered: t('kgstudio.user360.wallet-usage-registered'),
    unregistered: t('kgstudio.user360.wallet-usage-unregistered'),
  };

  const { data: complianceStat, isLoading: isComplianceStatLoading } = apiUser360Hooks.useGetComplianceStat(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
      staleTime: 60_000,
      onError: (error) => {
        console.error(error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  type Stat = z.infer<typeof StatSchema>;
  const transformToData = (data: O.Option<Record<string, Stat>>) =>
    pipe(
      data,
      O.map(R.mapWithIndex((key, { value }) => ({ name: key, value }))),
      O.map(R.toArray),
      O.map((array) =>
        array.map(([_, value]) => ({
          name: CHART_LABEL_MAP[value.name as keyof typeof CHART_LABEL_MAP],
          value: value.value,
        })),
      ),
      O.getOrElseW(() => []),
    );

  const kycStatusData = pipe(
    complianceStat,
    O.fromNullable,
    O.map((stat) => stat.data.kyc.kyc_status),
    transformToData,
  );

  const walletUsageData = pipe(
    complianceStat,
    O.fromNullable,
    O.map((stat) => stat.data.kyc.wallet),
    transformToData,
  );

  if (isComplianceStatLoading)
    return (
      <div className="space-y-6">
        <Skeleton className="h-10 w-[150px]" />

        <Skeleton className="h-9 w-[100px]" />

        <div className="grid grid-cols-1 gap-3 lg:grid-cols-3 lg:gap-6">
          <Skeleton className="h-[112px] rounded-3xl" />
          <Skeleton className="h-[112px] rounded-3xl" />
          <Skeleton className="h-[112px] rounded-3xl" />
        </div>

        <Skeleton className="h-9 w-[100px]" />

        <div className="grid grid-cols-1 gap-3 lg:grid-cols-2 lg:gap-6">
          <Skeleton className="h-[320px] rounded-3xl" />
          <Skeleton className="h-[320px] rounded-3xl" />
        </div>
      </div>
    );
  return (
    <div className="space-y-8">
      <div className="space-y-6">
        <h2 className="text-h2 text-primary">ComplyFlow</h2>
        <section className="grid grid-cols-1 gap-3 lg:grid-cols-3 lg:gap-6">
          <StatisticCard
            title={t('kgstudio.data.compliance.form-submission')}
            value={complianceStat?.data.comply_flow.form_submission.value ?? 0}
            {...(complianceStat?.data.comply_flow.form_submission.percentage && {
              percentage: {
                icon: true,
                value: complianceStat.data.comply_flow.form_submission.percentage,
              },
              caption: t('kgstudio.data.since-last-month'),
            })}
            icon={<Edit />}
          />
          <StatisticCard
            title={t('kgstudio.data.compliance.idv-tasks')}
            {...(complianceStat?.data.comply_flow.idv_tasks.percentage && {
              percentage: {
                icon: true,
                value: complianceStat.data.comply_flow.idv_tasks.percentage,
              },
              caption: t('kgstudio.data.since-last-month'),
            })}
            value={complianceStat?.data.comply_flow.idv_tasks.value ?? 0}
            icon={<Edit />}
          />
          <StatisticCard
            title={t('kgstudio.data.compliance.cdd-tasks')}
            value={complianceStat?.data.comply_flow.cdd_tasks.value ?? 0}
            {...(complianceStat?.data.comply_flow.cdd_tasks.percentage && {
              percentage: {
                icon: true,
                value: complianceStat.data.comply_flow.cdd_tasks.percentage,
              },
              caption: t('kgstudio.data.since-last-month'),
            })}
            icon={<FileSearch />}
          />
        </section>

        <section className="space-y-6">
          <h2 className="text-h2 text-primary">KYC</h2>
          <div className="grid grid-cols-1 gap-3 lg:grid-cols-2 lg:!gap-6">
            <StatisticCard
              title={t('kgstudio.data.compliance.pending-review')}
              value={complianceStat?.data.kyc.pending_tasks.value ?? 0}
              {...(complianceStat?.data.kyc.pending_tasks.percentage && {
                percentage: {
                  icon: true,
                  value: complianceStat.data.kyc.pending_tasks.percentage,
                },
                caption: t('kgstudio.data.since-last-month'),
              })}
              icon={<FileClock />}
            />
            <StatisticCard
              title={t('kgstudio.data.compliance.verified-customers')}
              value={complianceStat?.data.kyc.verified_customers.value ?? 0}
              {...(complianceStat?.data.kyc.verified_customers.percentage && {
                percentage: {
                  icon: true,
                  value: complianceStat.data.kyc.verified_customers.percentage,
                },
                caption: t('kgstudio.data.since-last-month'),
              })}
              icon={<UserCheck />}
            />
            <PieChartCard
              title={t('kgstudio.data.compliance.kyc-status.title')}
              data={kycStatusData}
              className="h-[320px]"
            />
            <PieChartCard
              title={t('kgstudio.data.compliance.v-wallet-usage.title')}
              data={walletUsageData}
              tooltip={t('kgstudio.data.compliance.v-wallet-usage.tooltip')}
              className="h-[320px]"
            />
          </div>
        </section>
      </div>
    </div>
  );
};

export default ComplianceData;
