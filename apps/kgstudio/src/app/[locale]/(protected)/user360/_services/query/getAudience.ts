import { chunk } from 'lodash-es';

import { ApiResponseWithPaging } from '@/app/_common/lib/api';
import { UseQueryOptionType } from '@/app/_common/lib/react-query';
import { useQuery } from '@tanstack/react-query';

import { MOCK_AUDIENCE_DATA } from '../../_constant/mock';

type Product = 'wallet' | 'form' | 'compliance' | 'nft';
export type AudienceQuery = {
  products?: Product[];
  nft_projects?: string[];
  countries?: string[];
  created_at?: Date;
  page_number?: number;
  page_size?: number;
  page_sort?: string;
};

export type Audience = {
  name: string;
  avatar_url?: string;
  email: string;
  phone: string;
  country: string;
  wallet_id: string;
  kyc_status: string;
  compliance: string[];
  nft_projects: string[];
  created_at: Date;
};
export type AudienceQueryResponse = ApiResponseWithPaging<Audience[]>;

// TODO: remove this mock data, add query to fetch data from backend
const getAudience = (params?: Partial<AudienceQuery>) => {
  const pageSize = params?.page_size || 10;
  const pageNumber = params?.page_number || 1;

  const chunkedData = chunk(MOCK_AUDIENCE_DATA, pageSize);
  const data = chunkedData[pageNumber - 1] || [];

  return new Promise<AudienceQueryResponse>((resolve) => {
    setTimeout(() => {
      resolve({
        code: 0,
        data: data,
        paging: {
          page_number: pageNumber,
          page_size: pageSize,
          total_count: MOCK_AUDIENCE_DATA.length,
          page_sort: 'created_at',
        },
      });
    }, 1500);
  });
};

export const useAudience = (params: Partial<AudienceQuery>, options?: UseQueryOptionType<AudienceQueryResponse>) =>
  useQuery(['audience', params], () => getAudience(params), options);
