import axios from '@/app/_common/lib/axios/instances/external';
import { createError } from '@/app/_common/lib/error';
import { UseQueryOptionType } from '@/app/_common/lib/react-query';
import { useQuery } from '@tanstack/react-query';

const API_PATH = `https://api-stag.genomix.one/v1/wallet/tags`;

export type getUserTagsQueryParams = {
  address: string;
};
export type GetUserTagsResponse = {
  code: 200;
  message: 'ok';
  data: {
    name: string;
    description: string;
  }[];
};

const getUserTagsError = createError('GetUserTagsError', 'Failed to get user tags.');
export type GetUserTagsError = ReturnType<typeof getUserTagsError>;

const getUserTags = async (params: getUserTagsQueryParams) =>
  axios
    .get<GetUserTagsResponse>(`${API_PATH}`, { params })
    .then((res) => res.data)
    .catch((err: unknown) => {
      throw getUserTagsError(err);
    });

export const useUserTags = (
  params: getUserTagsQueryParams,
  options?: UseQueryOptionType<GetUserTagsResponse, GetUserTagsError>,
) => useQuery(['tags', params], () => getUserTags(params), options);
