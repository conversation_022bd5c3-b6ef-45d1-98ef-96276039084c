import { createError } from '@/app/_common/lib/error';
import { UseMutationOptions, useMutation } from '@tanstack/react-query';

import { MOCK_AI_RECOMMENDATIONS } from '../../_constant/mock';

// FIXME: temporary solution
const API_PATH = 'https://api.openai.com/v1/engines/davinci/completions';

export type getAiRecommendationsCommand = {
  prompt: string;
};
export type AiRecommendation = {
  target: {
    behavior: string;
    asset_balance: string;
    activity: string;
  };
  estimation: {
    reach: {
      users: number;
      wallets: number;
    };
    gas: number;
  };
};

export const aiRecommendError = createError('AiRecommendError', 'Failed to get AI recommendations.');
export type AiRecommendError = ReturnType<typeof aiRecommendError>;

export const aiRecommend = async (command: getAiRecommendationsCommand): Promise<AiRecommendation[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getRandomItems(MOCK_AI_RECOMMENDATIONS, 3));
    }, 1000);
  });
};

export const useAiRecommendations = (
  options?: UseMutationOptions<AiRecommendation[], AiRecommendError, getAiRecommendationsCommand>,
) => useMutation(aiRecommend, options);

const getRandomItems = <T>(items: T[], count: number): T[] => {
  const shuffled = items.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};
