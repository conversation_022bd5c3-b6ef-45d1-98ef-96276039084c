import { z } from 'zod';

import { UseMutationOptions, useMutation } from '@tanstack/react-query';

import { db } from '../../_lib/db';

const UpsertEngagementCommandSchema = z.object({
  engage_name: z.string().optional(),
  session_id: z.string().min(1),
  status: z.string().optional(),
  target: z.string().nullish(),
  nft: z.string().nullish(),
  token: z.string().nullish(),
  notification: z.string().nullish(),
  time: z.string().nullish(),
});
export type UpsertEngagementCommand = z.infer<typeof UpsertEngagementCommandSchema>;
export const isValidUpsertEngagementCommand = (input: unknown): input is UpsertEngagementCommand =>
  UpsertEngagementCommandSchema.safeParse(input).success;

export type UpsertEngagementResponse = {
  code: 0;
};

const upsertEngagement = async (data: UpsertEngagementCommand): Promise<UpsertEngagementResponse> => {
  const originalData = await db.engagement.get({ session_id: data.session_id });
  await db.engagement.put({
    status: 'draft',
    ...originalData,
    ...data,
  });

  return { code: 0 };
};

export const useUpsertEngagement = (
  options?: UseMutationOptions<UpsertEngagementResponse, unknown, UpsertEngagementCommand>,
) => useMutation(upsertEngagement, options);
