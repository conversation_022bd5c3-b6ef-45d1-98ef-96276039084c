import { format } from 'date-fns';
import { chunk } from 'lodash-es';

import { ApiResponseWithPaging } from '@/app/_common/lib/api';
import { UseQueryOptionType } from '@/app/_common/lib/react-query';
import { useQuery } from '@tanstack/react-query';

import { EngagementDto, db } from '../../_lib/db';

export type EngagementsQuery = {
  query?: string;
  page_number?: number;
  page_size?: number;
  page_sort?: string;
};

type Method = 'NFT' | 'Token' | 'Notification';
type Status = 'published' | 'pending' | 'draft';

export type Engagement = {
  name: string;
  methods: Method[];
  time: string;
  date: string;
  status: Status;
  session_id: string;
};
export type EngagementsQueryResponse = ApiResponseWithPaging<Engagement[]>;

type SingleEngagementResponse = EngagementDto | null;

const getEngagementsFromIndexDB = async (params?: Partial<EngagementsQuery>): Promise<EngagementsQueryResponse> => {
  const engagements = await db.engagement.toArray().then((data) => {
    return data.map((item) => {
      const name = item.engage_name || 'Untitled';
      const methods = ['NFT', 'Token', 'Notification'].filter(
        (method) => !!item[method.toLowerCase() as keyof EngagementDto],
      ) as Method[];
      const status = (item.status || 'draft') as Status;
      const timeObj = item.time ? JSON.parse(item.time) : null;
      const date = timeObj ? format(new Date(timeObj.engage_date), 'yyyy/MM/dd') : format(new Date(), 'yyyy/MM/dd');
      const time = timeObj ? timeObj.engage_time : '00:00';
      return {
        name,
        methods,
        time,
        date,
        status,
        session_id: item.session_id,
      };
    });
  });
  const pageSize = params?.page_size || 10;
  const pageNumber = params?.page_number || 1;
  const chunkedData = chunk(engagements, pageSize);
  const data = chunkedData[pageNumber - 1] || [];

  return {
    code: 0,
    data: data,
    paging: {
      page_number: pageNumber,
      page_size: pageSize,
      total_count: engagements.length,
      page_sort: 'created_at',
    },
  };
};

const getSingleEngagementFromIndexDB = async (sessionId: string): Promise<SingleEngagementResponse> => {
  const engagement = await db.engagement.where('session_id').equals(sessionId).first();

  if (!engagement) return null;

  return engagement;
};

export const useEngagements = (
  params: Partial<EngagementsQuery>,
  options?: UseQueryOptionType<EngagementsQueryResponse>,
) => useQuery(['engagements', params], () => getEngagementsFromIndexDB(params), options);

export const useSingleEngagement = (sessionId: string, options?: UseQueryOptionType<SingleEngagementResponse>) =>
  useQuery(['engagements', sessionId], () => getSingleEngagementFromIndexDB(sessionId), options);
