import { AiRecommendation } from '../_services/command/getAiRecommendations';
import { Audience } from '../_services/query/getAudience';

export const MOCK_AI_RECOMMENDATIONS: AiRecommendation[] = [
  {
    target: {
      behavior: 'Registered in 7D',
      asset_balance: '100 >= Balance > 0',
      activity: 'Claimed Sushiswap rewards',
    },
    estimation: {
      reach: {
        users: 16300,
        wallets: 18600,
      },
      gas: 488,
    },
  },
  {
    target: {
      behavior: 'Registered in 10D',
      asset_balance: '100 >= Balance > 0',
      activity: 'All',
    },
    estimation: {
      reach: {
        users: 18200,
        wallets: 20300,
      },
      gas: 506,
    },
  },
  {
    target: {
      behavior: 'Registered in 90D',
      asset_balance: 'All',
      activity: 'Minted NFT - KGYC',
    },
    estimation: {
      reach: {
        users: 16700,
        wallets: 18800,
      },
      gas: 491,
    },
  },
  {
    target: {
      behavior: 'Registered in 28D',
      asset_balance: 'Balance = 0',
      activity: 'Minted NFT - Gacha',
    },
    estimation: {
      reach: {
        users: 17100,
        wallets: 19200,
      },
      gas: 499,
    },
  },
  {
    target: {
      behavior: 'All',
      asset_balance: 'Balance ≥ 1258',
      activity: 'Staked on LIDO',
    },
    estimation: {
      reach: {
        users: 14300,
        wallets: 16100,
      },
      gas: 462,
    },
  },
  {
    target: {
      behavior: 'All',
      asset_balance: 'Balance ≥ 1493',
      activity: 'Staked on Convex',
    },
    estimation: {
      reach: {
        users: 13600,
        wallets: 15400,
      },
      gas: 442,
    },
  },
];

export const MOCK_AUDIENCE_DATA: Audience[] = [
  {
    name: 'Estelle.H',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/295.jpg',
    email: '<EMAIL>',
    phone: '+886912888888',
    country: 'Taiwan',
    wallet_id: '0x830232859691613',
    kyc_status: 'approved',
    compliance: ['CASE-8886', 'IDV-5153', 'CDD-9788'],
    nft_projects: ['AAMA2023'],
    created_at: new Date('2023-03-28T16:39:38.416Z'),
  },
  {
    name: 'Janet Moen',
    email: '<EMAIL>',
    phone: '62-858157-356415-1',
    country: 'Wallis and Futuna',
    wallet_id: '0x207168707525289',
    kyc_status: 'rejected',
    compliance: ['IDV-4187', 'CDD-8144', 'CASE-7747'],
    nft_projects: ['BEAR2023', 'SHOPING1111', 'KGYC', 'AAMA2023'],
    created_at: new Date('2023-03-08T18:48:55.598Z'),
  },
  {
    name: 'Patrick Reilly',
    email: '<EMAIL>',
    phone: '29-137869-225579-5',
    country: 'Bhutan',
    wallet_id: '0x636867849410116',
    kyc_status: 'pending',
    compliance: ['IDV-9981', 'CDD-5980', 'CASE-5749'],
    nft_projects: ['BEAR2023', 'AAMA2023'],
    created_at: new Date('2023-07-06T18:29:01.507Z'),
  },
  {
    name: 'Julius Pacocha',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/857.jpg',
    email: '<EMAIL>',
    phone: '32-920060-212278-0',
    country: 'Iceland',
    wallet_id: '0x547863998948054',
    kyc_status: 'pending',
    compliance: ['CASE-3678', 'IDV-4332', 'CDD-1800'],
    nft_projects: ['KGYC', 'BEAR2023', 'AAMA2023'],
    created_at: new Date('2022-12-25T15:19:10.174Z'),
  },
  {
    name: 'Andrea Boyle',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/454.jpg',
    email: '<EMAIL>',
    phone: '39-080819-840108-9',
    country: 'Montenegro',
    wallet_id: '0x974709909590399',
    kyc_status: 'rejected',
    compliance: ['CASE-6133', 'IDV-4468', 'CDD-6920'],
    nft_projects: ['KGYC', 'AAMA2023', 'BEAR2023', 'SHOPING1111'],
    created_at: new Date('2023-03-29T05:33:16.903Z'),
  },
  {
    name: 'Aubrey Glover Jr.',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1129.jpg',
    email: '<EMAIL>',
    phone: '72-849410-952445-0',
    country: 'Ireland',
    wallet_id: '0x585745154039412',
    kyc_status: 'pending',
    compliance: ['CASE-9631', 'IDV-4566', 'CDD-8938'],
    nft_projects: ['AAMA2023', 'KGYC', 'SHOPING1111'],
    created_at: new Date('2022-09-23T02:40:38.004Z'),
  },
  {
    name: 'Connie Hintz',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/668.jpg',
    email: '<EMAIL>',
    phone: '44-196391-815452-3',
    country: 'United States of America',
    wallet_id: '0x104645512707568',
    kyc_status: 'approved',
    compliance: ['CASE-2902', 'CDD-5769', 'IDV-5963'],
    nft_projects: ['SHOPING1111'],
    created_at: new Date('2023-05-21T01:14:00.497Z'),
  },
  {
    name: 'Kristy Trantow',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/400.jpg',
    email: '<EMAIL>',
    phone: '47-627775-507303-7',
    country: 'Benin',
    wallet_id: '0x470216948065113',
    kyc_status: 'pending',
    compliance: ['IDV-1025', 'CASE-9688', 'CDD-7492'],
    nft_projects: ['SHOPING1111', 'BEAR2023', 'AAMA2023', 'KGYC'],
    created_at: new Date('2023-08-03T20:11:58.324Z'),
  },
  {
    name: 'Lola Ondricka',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/137.jpg',
    email: '<EMAIL>',
    phone: '75-824527-570326-7',
    country: 'Vanuatu',
    wallet_id: '0x404734066845681',
    kyc_status: 'rejected',
    compliance: ['CDD-9244', 'IDV-9358', 'CASE-1986'],
    nft_projects: ['KGYC'],
    created_at: new Date('2023-04-29T12:33:37.569Z'),
  },
  {
    name: 'Joseph Langosh',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1126.jpg',
    email: '<EMAIL>',
    phone: '43-168425-223016-4',
    country: 'Bahamas',
    wallet_id: '0x756131184534499',
    kyc_status: 'rejected',
    compliance: ['IDV-1537', 'CDD-9287', 'CASE-6300'],
    nft_projects: ['BEAR2023', 'AAMA2023'],
    created_at: new Date('2023-04-02T03:29:25.304Z'),
  },
  {
    name: 'Ronnie Johnston',
    email: '<EMAIL>',
    phone: '31-978198-276615-7',
    country: 'Uganda',
    wallet_id: '0x472100374398079',
    kyc_status: 'rejected',
    compliance: ['CDD-9109', 'CASE-8297', 'IDV-4121'],
    nft_projects: ['AAMA2023', 'KGYC', 'SHOPING1111'],
    created_at: new Date('2023-05-22T08:33:16.540Z'),
  },
  {
    name: 'Alexander Konopelski-Shields',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/188.jpg',
    email: '<EMAIL>',
    phone: '16-185542-827592-1',
    country: 'Cambodia',
    wallet_id: '0x257925217429282',
    kyc_status: 'pending',
    compliance: ['IDV-2050', 'CDD-9683', 'CASE-7661'],
    nft_projects: ['SHOPING1111'],
    created_at: new Date('2023-01-19T04:58:33.075Z'),
  },
  {
    name: 'Wade Zulauf',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1205.jpg',
    email: '<EMAIL>',
    phone: '46-199152-779842-7',
    country: 'Hungary',
    wallet_id: '0x893751618039454',
    kyc_status: 'rejected',
    compliance: ['IDV-4044', 'CDD-1788', 'CASE-1342'],
    nft_projects: ['AAMA2023'],
    created_at: new Date('2022-12-11T22:40:39.704Z'),
  },
  {
    name: 'Sonja Braun-Ebert',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/773.jpg',
    email: '<EMAIL>',
    phone: '68-417820-931754-1',
    country: 'Rwanda',
    wallet_id: '0x147016861111475',
    kyc_status: 'pending',
    compliance: ['CASE-5491', 'CDD-3060', 'IDV-9099'],
    nft_projects: ['BEAR2023', 'AAMA2023', 'SHOPING1111'],
    created_at: new Date('2023-05-29T12:46:38.862Z'),
  },
  {
    name: 'Mr. Angelo Smitham',
    email: '<EMAIL>',
    phone: '36-847736-797435-5',
    country: 'Armenia',
    wallet_id: '0x420052920927787',
    kyc_status: 'rejected',
    compliance: ['CASE-8488', 'IDV-7983', 'CDD-7470'],
    nft_projects: ['KGYC', 'SHOPING1111', 'BEAR2023', 'AAMA2023'],
    created_at: new Date('2023-05-26T20:43:45.647Z'),
  },
  {
    name: 'Ana Connelly',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/526.jpg',
    email: '<EMAIL>',
    phone: '39-216451-620761-0',
    country: 'Faroe Islands',
    wallet_id: '0x743602740325050',
    kyc_status: 'pending',
    compliance: ['CASE-6393', 'IDV-6585', 'CDD-1350'],
    nft_projects: ['KGYC'],
    created_at: new Date('2023-06-10T14:16:39.887Z'),
  },
  {
    name: 'Justin Cormier',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/20.jpg',
    email: '<EMAIL>',
    phone: '24-345002-344033-6',
    country: 'Canada',
    wallet_id: '0x63827553154858',
    kyc_status: 'approved',
    compliance: ['CDD-9753', 'CASE-2788', 'IDV-6234'],
    nft_projects: ['AAMA2023', 'BEAR2023', 'SHOPING1111'],
    created_at: new Date('2023-07-21T23:38:54.819Z'),
  },
  {
    name: 'Mr. Hector Muller',
    avatar_url: 'https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1215.jpg',
    email: '<EMAIL>',
    phone: '60-535695-706304-8',
    country: 'Eswatini',
    wallet_id: '0x877543987077529',
    kyc_status: 'approved',
    compliance: ['CDD-2046', 'CASE-1165', 'IDV-5859'],
    nft_projects: ['SHOPING1111'],
    created_at: new Date('2022-11-21T23:00:31.077Z'),
  },
  {
    name: 'Dallas Hermiston',
    email: '<EMAIL>',
    phone: '08-924860-791500-5',
    country: 'Montserrat',
    wallet_id: '0x57545066584195',
    kyc_status: 'pending',
    compliance: ['CDD-3057', 'IDV-4714', 'CASE-5242'],
    nft_projects: ['BEAR2023', 'AAMA2023', 'SHOPING1111'],
    created_at: new Date('2023-03-11T15:45:42.423Z'),
  },
  {
    name: 'Mr. Jimmy McKenzie',
    email: '<EMAIL>',
    phone: '21-320539-563230-8',
    country: 'Azerbaijan',
    wallet_id: '0x822959543047278',
    kyc_status: 'approved',
    compliance: ['CASE-9425', 'IDV-9347', 'CDD-3917'],
    nft_projects: ['BEAR2023'],
    created_at: new Date('2023-05-27T14:40:47.381Z'),
  },
];
