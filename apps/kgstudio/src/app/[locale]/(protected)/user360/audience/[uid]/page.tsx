'use client';

import { format } from 'date-fns';
import { Copy, ExternalLink, Info } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useEffect, useMemo, useState } from 'react';

import { showToast } from '@/2b/toast';
import { KycStatusBadge } from '@/app/_common/components/badge';
import { useIsClient, usePageHeader } from '@/app/_common/hooks';
import { apiUser360Hooks } from '@/app/_common/services';
import { UserDna } from '@/app/_common/services/user360/model';
import { useOrganizationStore } from '@/app/_common/store';
import { env } from '@/env.mjs';
import { Avatar, Badge, Card, DataTable, PageSortType, Skeleton, Tooltip } from '@kryptogo/2b';
import { formatCurrency, getChainFullName, getChainIcon, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';
import { ColumnDef, TableOptions, getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';

import { TwoColCard } from '../../_components';

export interface WalletRow {
  blockchain: React.ReactNode;
  address: React.ReactNode;
  balance: React.ReactNode;
  tag: React.ReactNode;
}

const DNA = ({ params }: { params: { uid: string } }) => {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.user-dna.title'), backLink: '/user360/audience/overview' });
  const { isClient } = useIsClient();
  const orgId = useOrganizationStore((state) => state.orgId);

  const { data: userDna, isLoading: isUserDnaLoading } = apiUser360Hooks.useGetUserDna(
    {
      params: { org_id: orgId ?? -1, uid: params.uid },
    },
    {
      enabled: !!orgId,
      staleTime: 60_000,
    },
  );

  const [tableOption, setTableOption] = useState<TableOptions<UserDna['wallets'][number]>>({
    data: [],
    columns: [],
    manualSorting: true,
    getPaginationRowModel: getPaginationRowModel(),
    getCoreRowModel: getCoreRowModel(),
  });

  const handleCopyAddress = (address: string, t: any) => {
    navigator.clipboard.writeText(address);
    showToast(t('kgstudio.common.successfully-copied'), 'success');
  };

  const table = useReactTable(tableOption);
  const columns: ColumnDef<UserDna['wallets'][number]>[] = useMemo(
    () => [
      {
        accessorKey: 'blockchain',
        header: t('common.blockchain'),
        cell: ({ row: { original: wallet } }) => {
          return (
            <div className="flex flex-row items-center justify-start gap-2">
              <div className="relative h-6 w-6 shrink-0 overflow-hidden rounded-full">
                {
                  <Image
                    src={getChainIcon(wallet.chain_id) ?? ''}
                    alt={wallet.chain_id}
                    fill
                    className="object-cover"
                  />
                }
              </div>
              <span>{getChainFullName(wallet.chain_id)}</span>
            </div>
          );
        },
      },
      {
        accessorKey: 'address',
        header: t('kgstudio.common.address'),
        minSize: 460,
        cell: ({ row: { original: wallet } }) => {
          return (
            <div className="flex items-center gap-2">
              <p className="text-body-2 text-primary truncate">{wallet.address}</p>
              <Copy
                onClick={() => handleCopyAddress(wallet.address, t)}
                className="cursor-pointer stroke-[var(--text-secondary)]"
                size={16}
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'balance',
        header: t('kgstudio.data.balance'),
        cell: ({ row: { original: wallet } }) => {
          return (
            <p className="text-body-2 text-primary">
              {formatCurrency({ amount: wallet.balance, decimals: DECIMAL_DISPLAY_MODE.FIAT, fmt: { prefix: '$' } })}
            </p>
          );
        },
      },
      {
        accessorKey: 'tag',
        header: t('kgstudio.user-dna.wallet.tag'),
        cell: ({ row: { original: wallet } }) => {
          return wallet.tag ? <Badge variant="grey">{wallet.tag}</Badge> : null;
        },
      },
    ],
    [t],
  );

  const [pageSort, setPageSort] = useState<PageSortType<keyof WalletRow>[]>([{ key: 'balance', value: null }]);
  const sortedWallets = userDna?.data.wallets.sort((a, b) => {
    const sort = pageSort[0];
    if (sort.key !== 'balance') return 0;

    if (sort.value) {
      return a[sort.key] - b[sort.key];
    }

    if (!sort.value) {
      return b[sort.key] - a[sort.key];
    }

    return 0;
  });

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: sortedWallets ?? [],
      columns,
    }));
  }, [columns, sortedWallets]);
  if (!isClient) return null;

  if (isUserDnaLoading)
    return (
      <div className="space-y-6">
        <Skeleton className="h-[36px] w-[160px]" />
        <Skeleton className="h-[104px] w-full" />
        <Skeleton className="h-[28.8px] w-[160px]" />
        <div className="grid grid-cols-2 gap-6">
          <Skeleton className="h-[290px] w-full" />
          <Skeleton className="h-[290px] w-full" />
        </div>
        <Skeleton className="h-[28.8px] w-[160px]" />
        <Skeleton className="h-[500px] w-full" />
      </div>
    );

  return (
    <div className="space-y-6">
      <Card className="flex items-center gap-4 p-6">
        <Avatar
          imageSrc={undefined}
          imageAlt={userDna?.data.name ?? t('kgstudio.user-dna.non-kyc-user')}
          displayName={userDna?.data.name}
          size="36"
        />

        <div className="flex flex-col gap-2">
          <p className="text-h2 text-primary">{userDna?.data.name ?? t('kgstudio.user-dna.non-kyc-user')}</p>
          <div className="gap-1">
            <p className="text-caption text-primary">
              {!!userDna?.data.created_at &&
                `${t('kgstudio.dna.create-at')} ${format(new Date(userDna.data.created_at), 'yyyy/MM/dd')}`}
            </p>
            <div className="flex">
              <p className="text-small text-disabled mr-4 mt-1">
                {t('kgstudio.audience.wallet_id')} {userDna?.data.uid}
              </p>
              {userDna?.data.uid && (
                <button onClick={() => handleCopyAddress(userDna?.data.uid, t)}>
                  <Copy className="mr-2 h-3 w-3 stroke-[var(--text-placeholder)]" />
                </button>
              )}
            </div>
          </div>
        </div>
      </Card>

      <section className="space-y-6">
        <h2 className="text-h2 text-primary">{t('kgstudio.common.compliance')}</h2>
        <div className="grid grid-cols-2 gap-6">
          <TwoColCard
            title={t('kgstudio.data.compliance.kyc-status.title')}
            schema={{
              [t('kgstudio.user-dna.status')]: !!userDna?.data.compliance.kyc_status && (
                <KycStatusBadge status={userDna.data.compliance.kyc_status} />
              ),
              [t('kgstudio.user-dna.risk-score')]: typeof userDna?.data.compliance.potential_risk === 'number' && (
                <p className="text-primary">{userDna?.data.compliance.potential_risk}</p>
              ),
              [t('kgstudio.user-dna.applied_at')]: !!userDna?.data.compliance.applied_at && (
                <p className="text-primary">{format(new Date(userDna.data.compliance.applied_at), 'yyyy/MM/dd')}</p>
              ),
              [t('kgstudio.user-dna.approved_at')]: !!userDna?.data.compliance.approved_at && (
                <p className="text-primary">{format(new Date(userDna?.data.compliance.approved_at), 'yyyy/MM/dd')}</p>
              ),

              [t('kgstudio.user-dna.submission')]: !!userDna?.data.compliance.form_id && (
                <a
                  href={`${env.NEXT_PUBLIC_COMPLIANCE_URL}/submission/${userDna?.data.compliance.form_id}`}
                  target="KryptoGO_Compliance"
                  className="flex items-center gap-3"
                >
                  <p className="text-primary underline">{`FORM-${userDna?.data.compliance.form_id}`}</p>
                  <ExternalLink size={16} className="text-primary" />
                </a>
              ),
            }}
          />
          <TwoColCard
            title={t('kgstudio.data.compliance.personal-info.title')}
            schema={{
              [t('kgstudio.user-dna.real-name')]: <p className="text-primary">{userDna?.data.compliance.real_name}</p>,
              [t('kgstudio.user-dna.nation')]: <p className="text-primary">{userDna?.data.compliance.nation_name}</p>,
              [t('kgstudio.user-dna.dob')]: (
                <p className="text-primary">
                  {!!userDna?.data.compliance.dob &&
                    `${format(new Date(userDna?.data.compliance.dob), 'yyyy/MM/dd')} (${
                      new Date().getFullYear() - new Date(userDna?.data.compliance.dob).getFullYear()
                    })`}
                </p>
              ),
              [t('kgstudio.user-dna.national-id')]: (
                <p className="text-primary">{userDna?.data.compliance.national_id}</p>
              ),
              [t('kgstudio.user-dna.email')]: (
                <a className="text-primary underline" href={`mailto:${userDna?.data.compliance.email}`}>
                  {userDna?.data.compliance.email}
                </a>
              ),
              [t('kgstudio.user-dna.phone')]: <p className="text-primary">{userDna?.data.compliance.phone}</p>,
            }}
          />
        </div>
      </section>

      <section className="space-y-6">
        <h2 className="text-h2 text-primary">{t('kgstudio.common.data.wallet')}</h2>
        <Card className="!p-0">
          <div className="flex items-center gap-4 p-4 md:p-6">
            <h3 className="text-h3 text-primary font-bold">{t('common.wallet-address')}</h3>
            <Tooltip>
              <Tooltip.Trigger>
                <Info className="stroke-[var(--text-primary)]" size={12} />
              </Tooltip.Trigger>
              <Tooltip.Content
                side="top"
                className="text-caption bg-surface-primary text-primary overflow-visible border-none px-3 py-2 shadow"
              >
                <p>{t('kgstudio.user-dna.delay-hint')}</p>
                <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
              </Tooltip.Content>
            </Tooltip>
          </div>
          <DataTable
            table={table}
            isLoading={isUserDnaLoading}
            dataLength={userDna?.data?.wallets?.length || 0}
            noDataMessage={t('kgstudio.user-dna.no-wallet')}
          />
        </Card>
      </section>
    </div>
  );
};

export default DNA;
