'use client';

import { format } from 'date-fns';
import { ChevronRight, MoreVertical, Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { KycStatusBadge } from '@/app/_common/components/badge';
import { FilterItem, FormFilterGroup } from '@/app/_common/components/form';
import { useDebounce, usePageHeader, useUpdateEffect } from '@/app/_common/hooks';
import { apiUser360Hooks } from '@/app/_common/services';
import { KycStatus } from '@/app/_common/services/asset-pro/model';
import { AudienceList, KycStatusSchema } from '@/app/_common/services/user360/model';
import { useOrganizationStore } from '@/app/_common/store';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Avatar, Badge, Card, DataTable, Form, Input, useDataTable } from '@kryptogo/2b';
import { ColumnDef, TableOptions, getCoreRowModel, useReactTable } from '@tanstack/react-table';

export interface AudienceRow {
  name: React.ReactNode;
  phone: React.ReactNode;
  email: React.ReactNode;
  kyc_status: React.ReactNode;
  compliance: React.ReactNode;
  created_at: React.ReactNode;
  action: React.ReactNode;
}

const AudienceFilterSchema = z.object({
  kyc_status: z.union([KycStatusSchema, z.literal('')]).optional(),
});
type AudienceFilter = z.infer<typeof AudienceFilterSchema>;

const AudienceOverview = () => {
  const router = useRouter();
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.common.user-management') });

  const searchParams = useSearchParams();
  const kyc_status = (searchParams.get('kyc_status') as KycStatus) || undefined;

  const orgId = useOrganizationStore((state) => state.orgId);
  const [tableOption, setTableOption] = useState<TableOptions<AudienceList['data'][number]>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    initialState: {
      sorting: [
        {
          id: 'updated_at',
          desc: true,
        },
      ],
    },
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size, page_sort } = useDataTable(table);
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);
  const form = useForm<AudienceFilter>({
    defaultValues: {
      kyc_status,
    },
    mode: 'onChange',
    resolver: zodResolver(AudienceFilterSchema),
  });

  const { data: audiences, isLoading: isAudiencesLoading } = apiUser360Hooks.useGetAudiences(
    {
      params: { org_id: orgId ?? -1 },
      queries: {
        page_size,
        page_number,
        page_sort: page_sort === '' ? undefined : page_sort,
        ...(!!debouncedQuery && { q: debouncedQuery }),
        ...(!!form.watch().kyc_status && { kyc_status: form.watch().kyc_status }),
      },
    },
    {
      enabled: !!orgId,
      staleTime: 60_000,
      onError: (error) => {
        console.error('error', error);
      },
    },
  );

  const columns: ColumnDef<AudienceList['data'][number]>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: t('kgstudio.audience.name'),
        size: 200,
        cell: ({ row: { original: user } }) => {
          const { name } = user;

          return (
            <div className="flex items-center gap-3">
              <Avatar
                imageSrc={undefined}
                imageAlt={name ?? t('kgstudio.user-dna.non-kyc-user')}
                displayName={name}
                size="36"
                className="row-span-2"
              />

              <p className="text-body-2-bold text-primary whitespace-nowrap">
                {name ?? t('kgstudio.user-dna.non-kyc-user')}
              </p>
            </div>
          );
        },
      },
      {
        accessorKey: 'phone',
        size: 144,
        header: t('kgstudio.audience.phone'),
        cell: ({ row: { original: user } }) => {
          return <p className="text-body-2-bold text-primary">{user.phone}</p>;
        },
      },
      {
        accessorKey: 'email',
        size: 180,
        header: t('kgstudio.audience.email'),
        cell: ({ row: { original: user } }) => {
          return <p className="text-body-2-bold text-primary truncate">{user.email}</p>;
        },
      },
      {
        accessorKey: 'kyc_status',
        size: 100,
        header: t('kgstudio.audience.kyc_status'),
        cell: ({ row: { original: user } }) => {
          return <KycStatusBadge status={user.comply_flow.kyc_status} />;
        },
      },
      {
        accessorKey: 'compliance',
        size: 100,
        header: t('kgstudio.audience.compliance'),
        cell: ({ row: { original: user } }) => {
          return (
            <>{!!user.comply_flow.form_id && <Badge variant="grey">{`FORM-${user.comply_flow.form_id}`}</Badge>}</>
          );
        },
      },
      {
        accessorKey: 'updated_at',
        size: 120,
        header: t('kgstudio.common.updated_at'),
        meta: {
          sortable: true,
        },
        cell: ({ row: { original: user } }) => {
          return (
            <p className="text-body-2 text-primary font-bold">{format(new Date(user.updated_at), 'MMM dd, yyyy')}</p>
          );
        },
      },
      {
        id: 'actions',
        header: () => <></>,
        size: 50,
        cell: () => {
          return (
            <div className="flex items-center justify-center">
              <ChevronRight className="text-placeholder" />
            </div>
          );
        },
      },
    ],
    [t],
  );

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: audiences?.data || [],
      columns,
    }));
  }, [columns, audiences]);
  const filterItems: FilterItem<AudienceFilter>[] = [
    {
      name: 'kyc_status',
      subject: t('kgstudio.audience.kyc_status'),
      type: 'select',
      options: [
        {
          label: t('kgstudio.kyc-status.pending'),
          value: 'pending',
        },
        {
          label: t('kgstudio.kyc-status.verified'),
          value: 'verified',
        },
        {
          label: t('kgstudio.kyc-status.unverified'),
          value: 'unverified',
        },
        {
          label: t('kgstudio.kyc-status.rejected'),
          value: 'rejected',
        },
      ],
    },
  ];

  useUpdateEffect(() => {
    table.resetPageIndex();
  }, [form.watch().kyc_status, debouncedQuery]);

  return (
    <div className="space-y-6">
      <Form {...form}>
        <FormFilterGroup control={form.control} items={filterItems} data-cy="audience-filter-group" />
      </Form>

      <Card className="!p-0">
        <div className="flex items-center justify-between p-6">
          <Input
            className="w-full md:!w-[312px]"
            placeholder={t('kgstudio.audience.query-placeholder')}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
            value={query}
            suffix={<Search />}
          />
        </div>
        <DataTable
          table={table}
          isLoading={isAudiencesLoading}
          dataLength={audiences?.paging.total_count || 0}
          onRowClick={(rowData) => {
            router.push(`/user360/audience/${rowData.uid}`);
          }}
        />
      </Card>
      <br />
    </div>
  );
};
export default AudienceOverview;
