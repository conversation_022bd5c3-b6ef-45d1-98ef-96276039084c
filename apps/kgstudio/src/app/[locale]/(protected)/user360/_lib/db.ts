import Dexie, { Table } from 'dexie';

export interface EngagementDto {
  session_id: string;
  engage_name?: string;
  status?: string;
  target?: string | null;
  nft?: string | null;
  token?: string | null;
  notification?: string | null;
  time?: string | null;
}

export class EngagementDatabase extends <PERSON>ie {
  engagement!: Table<EngagementDto>;

  constructor() {
    super('engagementDatabase');
    this.version(2).stores({
      engagement: 'session_id', // Primary key and indexed props
    });
  }
}

export const db = new EngagementDatabase();
