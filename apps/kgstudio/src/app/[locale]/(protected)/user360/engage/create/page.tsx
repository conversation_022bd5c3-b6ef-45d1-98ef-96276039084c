'use client';

import { addYears, format } from 'date-fns';
import { Pencil, Plus, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { LoadingModal } from '@/app/_common/components';
import { FormDayPicker, FormRadioGroup, FormTimePicker } from '@/app/_common/components/form';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Form, Separator } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import tokenIcon from '../../_assets/token-icon.png';
import { AiRecommend, CurrencyModal, NftModal, NotificationModal } from '../../_components';
import { useUpsertEngagement } from '../../_services/command/upsertEngagement';
import { useSingleEngagement } from '../../_services/query/getEngagements';

const CreateEngagementSchema = (t: any) =>
  z.object({
    type: z.enum(['ai', 'csv', 'all']),
    time_option: z.enum(['immediately', 'custom']),
    engage_date: z.date(),
    engage_time: z.string().min(1, t('kgstudio.time.input-time')),
  });
export type CreateEngageFormValues = z.infer<ReturnType<typeof CreateEngagementSchema>>;

const CreateEngage = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [nftModalOpen, setNftModalOpen] = useState(false);
  const [currencyModalOpen, setCurrencyModalOpen] = useState(false);
  const [notificationModalOpen, setNotificationModalOpen] = useState(false);
  const form = useForm<CreateEngageFormValues>({
    defaultValues: {
      type: 'ai',
      time_option: undefined,
      engage_date: new Date(),
    },
    resolver: zodResolver(CreateEngagementSchema(t)),
    mode: 'all',
  });
  const type = form.watch('type');
  const timeOption = form.watch('time_option');

  const params = useSearchParams();
  const sessionId = params?.get('session');
  const {
    data: engagement,
    isLoading: engagementLoading,
    error: engagementError,
  } = useSingleEngagement(sessionId as string, {
    enabled: !!sessionId,
  });

  const {
    mutate: upsertEngagement,
    isLoading: upsertEngagementLoading,
    isError: upsertEngagementError,
  } = useUpsertEngagement({
    onSuccess: async () => {
      await queryClient.invalidateQueries(['engagements', sessionId]);
      router.push(`/user360/engage/${sessionId}`);
    },
  });

  const nftCollection = engagement?.nft && JSON.parse(engagement.nft);
  const currency = engagement?.token && JSON.parse(engagement.token);
  const notification = engagement?.notification && JSON.parse(engagement.notification);

  const onSubmit = async (values: CreateEngageFormValues) => {
    upsertEngagement(
      {
        session_id: sessionId as string,
        status: 'pending',
        time: JSON.stringify({
          engage_date: values.engage_date,
          engage_time: values.engage_time,
        }),
      },
      {
        onSuccess: async () => {
          await queryClient.refetchQueries(['engagements', sessionId]);
          router.push(`/user360/engage/${sessionId}`);
        },
      },
    );
  };

  const buttonDisabled = upsertEngagementLoading || engagementLoading || !form.formState.isValid;

  useEffect(() => {
    if (timeOption === 'immediately') {
      const now = new Date();

      form.setValue('engage_date', now, {
        shouldValidate: true,
      });
      form.setValue('engage_time', format(now, 'HH:mm'), {
        shouldValidate: true,
      });
    }
  }, [timeOption, form]);

  return (
    <section>
      <Form {...form}>
        <form className="space-y-10" onSubmit={form.handleSubmit(onSubmit)}>
          <Card className="space-y-6 p-10">
            <h2 className="text-section-title font-roboto">{t('kgstudio.data.target-section')}</h2>
            <Separator />
            <FormRadioGroup
              name="type"
              control={form.control}
              items={[
                { label: t('kgstudio.data.ai-recommend'), value: 'ai' },
                { label: t('kgstudio.data.upload-csv-list'), value: 'csv' },
                { label: t('kgstudio.data.all'), value: 'all' },
              ]}
              variant="horizontal"
            />
            {type === 'ai' && <AiRecommend sessionId={sessionId as string} />}
          </Card>
          <Card className="space-y-6 p-10">
            <div className="flex flex-col gap-2">
              <h2 className="text-section-title font-roboto">2. {t('kgstudio.data.actions-section')}</h2>
              <p className="text-body-2">{t('kgstudio.data.select-multiple-actions')}</p>
            </div>
            <Separator />

            <div className="flex flex-col gap-2">
              <h3 className="text-body-bold text-primary">{t('kgstudio.data.send-nft')}</h3>
              <p className="text-body-2 text-secondary">{t('kgstudio.data.use-nft-boost')}</p>
              <div
                className="border-primary flex cursor-pointer items-center gap-3 rounded-xl border p-3"
                {...{
                  onClick: !nftCollection
                    ? () => {
                        setNftModalOpen(true);
                      }
                    : undefined,
                }}
              >
                {!nftCollection ? (
                  <>
                    <div className="bg-surface-secondary flex h-10 w-10 items-center justify-center rounded-lg">
                      <Plus className="stroke-primary h-4 w-4" />
                    </div>
                    <p className="text-body-2-bold">{t('kgstudio.wallet.config.label.add')}</p>
                  </>
                ) : (
                  <div className="grid w-full grid-cols-[auto_1fr_auto] items-center gap-3">
                    <div className="relative h-10 w-10 overflow-hidden rounded-lg">
                      <Image src={nftCollection.nft_image} alt="banner" fill className="object-cover" />
                    </div>

                    <p className="text-body-2 text-primary font-bold">{nftCollection.collection_name}</p>
                    <div className="flex items-center gap-3">
                      <Pencil
                        className="text-placeholder h-4 w-4 cursor-pointer"
                        onClick={() => {
                          setNftModalOpen(true);
                        }}
                      />
                      <X className="text-placeholder h-4 w-4" />
                    </div>
                  </div>
                )}
              </div>
            </div>

            <NftModal open={nftModalOpen} onOpenChange={setNftModalOpen} sessionId={sessionId as string} />
            <div className="flex flex-col gap-2">
              <h3 className="text-body-bold text-primary">{t('kgstudio.data.send-currency')}</h3>
              <p className="text-body-2 text-secondary">{t('kgstudio.data.use-assetpro')}</p>
              <div
                className="border-primary flex cursor-pointer items-center gap-3 rounded-xl border p-3"
                {...{
                  onClick: !currency
                    ? () => {
                        setCurrencyModalOpen(true);
                      }
                    : undefined,
                }}
              >
                {!currency ? (
                  <>
                    <div className="bg-surface-secondary flex h-10 w-10 items-center justify-center rounded-full">
                      <Plus className="stroke-primary h-4 w-4" />
                    </div>
                    <p className="text-body-2-bold">{t('kgstudio.wallet.config.label.add')}</p>
                  </>
                ) : (
                  <div className="grid w-full grid-cols-[auto_1fr_auto] items-center gap-3">
                    <div className="relative h-10 w-10 overflow-hidden rounded-lg">
                      <Image src={tokenIcon} alt="token" fill className="object-cover" />
                    </div>
                    <div className="flex flex-col items-start justify-center">
                      <p className="text-body-2 text-primary font-bold">{`${currency.amount} USDT`}</p>
                      <p className="text-small text-secondary">{`≈ ${currency.amount} USD`}</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <Pencil
                        className="text-placeholder h-4 w-4 cursor-pointer"
                        onClick={() => {
                          setCurrencyModalOpen(true);
                        }}
                      />
                      <X className="text-placeholder h-4 w-4" />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <CurrencyModal
              open={currencyModalOpen}
              onOpenChange={setCurrencyModalOpen}
              sessionId={sessionId as string}
            />
            <div className="flex flex-col gap-2">
              <h3 className="text-body-bold text-primary">{t('kgstudio.data.send-notification')}</h3>
              <p className="text-body-2 text-secondary">{t('kgstudio.data.use-wallet-notification')}</p>
              <div
                className="border-primary flex cursor-pointer items-center gap-3 rounded-xl border p-3"
                {...{
                  onClick: !notification
                    ? () => {
                        setNotificationModalOpen(true);
                      }
                    : undefined,
                }}
              >
                {!notification ? (
                  <>
                    <div className="bg-surface-secondary flex h-[80px] w-[248px] items-center justify-center rounded-lg">
                      <Plus className="stroke-primary h-4 w-4" />
                    </div>
                    <p className="text-body-2-bold">{t('kgstudio.wallet.config.label.add')}</p>
                  </>
                ) : (
                  <div className="grid w-full grid-cols-[auto_1fr_auto] gap-3">
                    <div className="relative h-[80px] w-[240px] overflow-hidden rounded-lg">
                      <Image
                        src={notification.in_app_notification.image}
                        alt="in app notification banner"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="flex flex-col items-start justify-center">
                      <p className="text-body-2 text-primary font-bold">{notification.in_app_notification.title}</p>
                      <p className="text-small text-secondary w-[200px] truncate">
                        {notification.in_app_notification.description}
                      </p>
                    </div>
                    <div className="flex items-center gap-3">
                      <Pencil
                        className="text-placeholder h-4 w-4"
                        onClick={() => {
                          setNotificationModalOpen(true);
                        }}
                      />
                      <X className="text-placeholder h-4 w-4" />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <NotificationModal
              open={notificationModalOpen}
              onOpenChange={setNotificationModalOpen}
              sessionId={sessionId as string}
            />
          </Card>
          <Card className="space-y-6 p-10">
            <div className="flex flex-col gap-2">
              <h2 className="text-section-title font-roboto">3. {t('kgstudio.data.time-section')}</h2>
              <p className="text-body-2">{t('kgstudio.data.specify-engage-time')}</p>
            </div>
            <Separator />
            <FormRadioGroup
              name="time_option"
              control={form.control}
              items={[
                { label: t('kgstudio.data.time-option-immediately'), value: 'immediately' },
                { label: t('kgstudio.data.time-option-custom'), value: 'custom' },
              ]}
              variant="horizontal"
            />
            {timeOption === 'custom' && (
              <div className="grid grid-cols-2 justify-start gap-2">
                <FormDayPicker
                  name="engage_date"
                  control={form.control}
                  fromDate={new Date()}
                  toDate={addYears(new Date(), 1)}
                />
                <FormTimePicker name="engage_time" control={form.control} />
              </div>
            )}
          </Card>
          <div className="flex items-center justify-between gap-[10px]">
            <Button variant="secondary"> {t('kgstudio.common.save-draft')}</Button>
            <Button className="w-[152px]" disabled={buttonDisabled}>
              {t('kgstudio.nft.next-step')}
            </Button>
          </div>
          <LoadingModal open={upsertEngagementLoading} />
        </form>
      </Form>
    </section>
  );
};

export default CreateEngage;
