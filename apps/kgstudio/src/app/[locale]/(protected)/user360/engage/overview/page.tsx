'use client';

import { MoreVertical, Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useState } from 'react';

import { useTable } from '@/app/_common/hooks';
import { Badge, <PERSON><PERSON>, Card, ColumnDefType, Input, Skeleton } from '@kryptogo/2b';

import { CreateEngageModal } from '../../_components';
import { useEngagements } from '../../_services/query/getEngagements';

export interface EngagementRow {
  name: React.ReactNode;
  methods: React.ReactNode;
  time: React.ReactNode;
  status: React.ReactNode;
  action: React.ReactNode;
}

const EngagementOverview = () => {
  const [query, setQuery] = useState('');
  const [createEngagementModalOpen, setCreateEngagementModalOpen] = useState(false);
  const t = useTranslations();
  const { currentPage, setCurrentPage, Table } = useTable();
  const columnDefs: ColumnDefType<EngagementRow>[] = [
    { title: t('kgstudio.data.engage.name'), dataKey: 'name' },
    { title: t('kgstudio.engagement.methods'), dataKey: 'methods' },
    { title: t('common.time'), dataKey: 'time' },
    { title: t('common.status.text'), dataKey: 'status' },
    { title: '', dataKey: 'action', width: 60 },
  ];

  const { data: engagements, isLoading: engagementLoading } = useEngagements(
    {
      query,
      page_number: currentPage,
    },
    {
      keepPreviousData: true,
    },
  );

  const parsedData = engagementLoading
    ? Array(5).fill({
        name: <Skeleton className="h-[21px]" />,
        time: <Skeleton className="h-[21px]" />,
        methods: <Skeleton className="h-[21px]" />,
        status: <Skeleton className="h-[21px]" />,
      })
    : engagements?.data.map((engagement) => {
        return {
          name: engagement.name,
          time: (
            <div className="flex flex-col">
              <p className="font-bold">{engagement.date}</p>
              <p className="text-secondary">{engagement.time}</p>
            </div>
          ),
          methods: (
            <div className="flex items-center gap-2">
              {engagement.methods.map((method) => (
                <Badge key={method} variant="grey">
                  {method}
                </Badge>
              ))}
            </div>
          ),
          status: (
            <Badge
              variant={engagement.status === 'published' ? 'green' : engagement.status === 'pending' ? 'yellow' : 'red'}
            >
              <p className="capitalize">{engagement.status}</p>
            </Badge>
          ),
          action: (
            <span className="flex w-[50px] items-center justify-center gap-2">
              <Link href={`/user360/engage/${engagement.session_id}`} className="cursor-pointer">
                <MoreVertical className="text-placeholder w-5 cursor-pointer" />
              </Link>
            </span>
          ),
        };
      });

  return (
    <>
      <h1 className="text-center text-[28px] font-bold">{t('kgstudio.data.engage-title')}</h1>
      <br />

      <Card className="border-primary border">
        <div className="flex items-center justify-between p-6">
          <h2 className="text-2xl font-bold">{t('kgstudio.data.engage-list')}</h2>
          <div className="flex gap-4">
            <Input
              className="h-12"
              type="text"
              placeholder={t('common.search-placeholder')}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
              value={query}
            />
            <CreateEngageModal open={createEngagementModalOpen} onOpenChange={setCreateEngagementModalOpen} />
            {/*  */}
            <Button
              icon={<Plus />}
              data-cy="create-nft-collection-button"
              onClick={() => {
                setCreateEngagementModalOpen(true);
              }}
              className="min-w-[100px]"
            >
              {t('kgstudio.common.create')}
            </Button>
            {/* </Link> */}
          </div>
        </div>
        <Table
          data={parsedData}
          columnDefs={columnDefs}
          pageSize={engagements?.paging?.page_size || 10}
          totalCount={engagements?.paging?.total_count || 0}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        />
      </Card>
      <br />
    </>
  );
};
export default EngagementOverview;
