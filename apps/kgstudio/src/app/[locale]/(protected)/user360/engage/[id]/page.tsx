'use client';

import { format } from 'date-fns';
import { Download, MoreVertical, Plus, User2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useState } from 'react';

import { useMaxWidth, useTable } from '@/app/_common/hooks';
import { Badge, Button, ColumnDefType, Input, Skeleton } from '@kryptogo/2b';

import { CircularProgress } from '../../_components';
import { useAudience } from '../../_services/query/getAudience';
import { useSingleEngagement } from '../../_services/query/getEngagements';

export interface AudienceRow {
  name: React.ReactNode;
  phone: React.ReactNode;
  email: React.ReactNode;
  country: React.ReactNode;
  wallet_id: React.ReactNode;
  kyc_status: React.ReactNode;
  compliance: React.ReactNode;
  nft_projects: React.ReactNode;
  action: React.ReactNode;
}

const EngagementDetail = () => {
  const params = useParams();
  const engageId = params == null ? '' : (params.id as string);
  const { containerId, itemId } = useMaxWidth();
  const [query, setQuery] = useState('');
  const t = useTranslations();

  const { currentPage, setCurrentPage, Table } = useTable();
  const columnDefs: ColumnDefType<AudienceRow>[] = [
    { title: t('kgstudio.data.engage.name'), dataKey: 'name' },
    { title: t('kgstudio.data.engage.phone'), dataKey: 'phone' },
    { title: t('kgstudio.data.engage.email'), dataKey: 'email' },
    { title: t('kgstudio.data.engage.country'), dataKey: 'country' },
    { title: t('kgstudio.data.engage.wallet-id'), dataKey: 'wallet_id' },
    { title: t('kgstudio.data.engage.kyc'), dataKey: 'kyc_status' },
    { title: t('kgstudio.data.engage.compliance'), dataKey: 'compliance' },
    { title: t('kgstudio.data.engage.nft-projects'), dataKey: 'nft_projects' },
    { title: '', dataKey: 'action', width: 60 },
  ];
  const {
    data: engagement,
    isLoading: engagementLoading,
    error: engagementError,
  } = useSingleEngagement(engageId as string, {
    enabled: !!engageId,
  });
  const name = engagement?.engage_name;
  const target = engagement?.target && JSON.parse(engagement.target);
  const nftCollection = engagement?.nft && JSON.parse(engagement.nft);
  const currency = engagement?.token && JSON.parse(engagement.token);
  const notification = engagement?.notification && JSON.parse(engagement.notification);
  const time = engagement?.time && JSON.parse(engagement.time);
  const status = engagement?.status;

  const { data: audience, isLoading: audienceLoading } = useAudience({
    page_number: currentPage,
    page_size: 5,
  });
  const parsedData: AudienceRow[] | undefined = audienceLoading
    ? Array<AudienceRow>(5).fill({
        name: (
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 shrink-0 items-center rounded-full" />
            <Skeleton className="h-[21px] w-[100px]" />
          </div>
        ),
        email: <Skeleton className="h-[21px]" />,
        phone: <Skeleton className="h-[21px]" />,
        country: <Skeleton className="h-[21px]" />,
        wallet_id: <Skeleton className="h-[21px]" />,
        kyc_status: <Skeleton className="h-[21px]" />,
        compliance: <Skeleton className="h-[21px]" />,
        nft_projects: <Skeleton className="h-[21px]" />,
        action: <Skeleton className="h-[21px]" />,
      })
    : audience?.data.map<AudienceRow>((user) => {
        return {
          name: (
            <div className="flex items-center gap-3">
              <div className="bg-brand-primary-lighter relative flex h-10 w-10 shrink-0 items-center justify-center overflow-hidden rounded-full">
                {user.avatar_url ? (
                  <Image fill src={user.avatar_url} alt={user.wallet_id} />
                ) : (
                  <User2 className="fill-brand-primary h-6 w-6 stroke-none" />
                )}
              </div>
              <p className="text-body-2-bold text-primary whitespace-nowrap">{user.name}</p>
            </div>
          ),
          phone: <p className="text-body-2-bold text-primary">{user.phone}</p>,
          email: <p className="text-body-2-bold text-primary truncate">{user.email}</p>,
          country: <p className="text-body-2-bold text-primary truncate">{user.country}</p>,
          wallet_id: <p className="text-body-2-bold text-primary truncate">{user.wallet_id}</p>,
          kyc_status: (
            <Badge
              variant={user.kyc_status === 'approved' ? 'green' : user.kyc_status === 'rejected' ? 'red' : 'yellow'}
            >
              {user.kyc_status}
            </Badge>
          ),
          compliance: (
            <div className="flex flex-wrap items-center gap-1">
              {user.compliance.map((compliance) => (
                <Badge key={compliance} variant="grey">
                  {compliance}
                </Badge>
              ))}
            </div>
          ),
          nft_projects: (
            <div className="flex flex-wrap items-center gap-1">
              {user.nft_projects.map((nft) => (
                <Badge key={nft} variant="grey">
                  {nft}
                </Badge>
              ))}
            </div>
          ),
          action: (
            <span className="flex w-[50px] items-center justify-center gap-2">
              <Link href={`/user360/audience/${user.wallet_id}`} className="cursor-pointer">
                <MoreVertical className="text-placeholder w-5 cursor-pointer" />
              </Link>
            </span>
          ),
        };
      });

  const getStatusBadgeDisplay = (status: string) => {
    switch (status) {
      case 'published':
        return (
          <Badge variant="green" className="capitalize">
            {status}
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="yellow" className="capitalize">
            {status}
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="red" className="capitalize">
            {status}
          </Badge>
        );
      default:
        return (
          <Badge variant="grey" className="capitalize">
            {status}
          </Badge>
        );
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-h1 text-primary font-bold">{t('kgstudio.engagement.title')}</h1>

      <section className="space-y-6">
        <div className="bg-surface-primary flex flex-col gap-6 rounded-3xl p-10 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          {name && <h2 className="text-h2 font-bold">{name}</h2>}
          <div className="flex items-center gap-3">
            {name && <p className="text-body text-secondary w-[80px]">{name}</p>}
            {time && (
              <p className="text-body text-primary">{`${format(new Date(time.engage_date), 'yyyy/MM/dd')} ${
                time.engage_time
              }`}</p>
            )}
            {status && getStatusBadgeDisplay(status)}
          </div>
        </div>
      </section>

      <section className="grid grid-cols-2 gap-6" id={containerId}>
        <div className="bg-surface-primary space-y-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.engagement.target-settings')}</h3>
          <div className="space-y-3">
            <div className="text-body flex items-center gap-2">
              <p className="text-secondary" id={itemId}>
                {t('kgstudio.engagement.behavior')}
              </p>
              <p className="text-primary">{target?.target.behavior}</p>
            </div>
            <div className="text-body flex items-center gap-2">
              <p className="text-secondary" id={itemId}>
                {t('kgstudio.engagement.asset-balance')}
              </p>
              <p className="text-primary">{target?.target.asset_balance}</p>
            </div>
            <div className="text-body flex items-center gap-2">
              <p className="text-secondary" id={itemId}>
                {t('kgstudio.engagement.activity')}
              </p>
              <p className="text-primary">{target?.target.activity}</p>
            </div>
          </div>
        </div>
        <div className="bg-surface-primary space-y-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.engagement.actions-title')}</h3>
          <div className="space-y-3">
            <div className="text-body flex items-center gap-2">
              <p className="text-secondary" id={itemId}>
                {t('kgstudio.engagement.send-nft')}
              </p>
              <p className="text-primary">{nftCollection?.collection_name ?? 'N/A'}</p>
            </div>
            <div className="text-body flex items-center gap-2">
              <p className="text-secondary" id={itemId}>
                {t('kgstudio.engagement.send-currency')}
              </p>
              <p className="text-primary">{currency?.amount ? `${currency.amount} USDT` : 'N/A'}</p>
            </div>
            <div className="text-body flex items-center gap-2">
              <p className="text-secondary" id={itemId}>
                {t('kgstudio.engagement.send-notification')}
              </p>
              <p className="text-primary">{notification?.in_app_notification?.title ?? 'N/A'}</p>
            </div>
          </div>
        </div>
      </section>

      <section className="grid grid-cols-3 gap-6">
        <div className="bg-surface-primary space-y-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.engagement.notification-visit')}</h3>
          <CircularProgress title={t('kgstudio.engagement.users')} percentage={68} />
          <div className="flex items-center justify-center gap-6">
            <div className="text-primary flex flex-col items-center">
              <p className="text-small">{t('kgstudio.engagement.engaged')}</p>
              <p className="text-h3 font-bold">4,239</p>
            </div>
            <div className="text-primary flex flex-col items-center">
              <p className="text-small">{t('kgstudio.engagement.all')}</p>
              <p className="text-h3 font-bold">6,235</p>
            </div>
          </div>
        </div>
        <div className="bg-surface-primary space-y-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.engagement.nft-claim-rate')}</h3>
          <CircularProgress title={t('kgstudio.engagement.users')} percentage={73} />
          <div className="flex items-center justify-center gap-6">
            <div className="text-primary flex flex-col items-center">
              <p className="text-small">{t('kgstudio.engagement.engaged')}</p>
              <p className="text-h3 font-bold">4,551</p>
            </div>
            <div className="text-primary flex flex-col items-center">
              <p className="text-small">{t('kgstudio.engagement.all')}</p>
              <p className="text-h3 font-bold">6,235</p>
            </div>
          </div>
        </div>
        <div className="bg-surface-primary space-y-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <h3 className="text-h3 text-primary font-bold">{t('kgstudio.engagement.rewards-redeem-rate')}</h3>
          <CircularProgress title={t('kgstudio.engagement.users')} percentage={26} />
          <div className="flex items-center justify-center gap-6">
            <div className="text-primary flex flex-col items-center">
              <p className="text-small">{t('kgstudio.engagement.engaged')}</p>
              <p className="text-h3 font-bold">1,621</p>
            </div>
            <div className="text-primary flex flex-col items-center">
              <p className="text-small">{t('kgstudio.engagement.all')}</p>
              <p className="text-h3 font-bold">6,235</p>
            </div>
          </div>
        </div>
      </section>

      <section>
        <div className="bg-surface-primary space-y-4 rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]">
          <div className="flex items-center justify-between">
            <h3 className="text-h3 text-primary font-bold">{`Target Users(${
              target?.estimation?.reach?.users ?? '1654'
            })`}</h3>
            <div className="flex items-center gap-3">
              <Input
                className="h-12"
                type="text"
                placeholder={t('common.search-placeholder')}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
                value={query}
              />
              <Button variant="secondary" icon={<Download />} />
              <Button className="h-12 pl-4" data-cy="create-nft-collection-button">
                <Link href="/user360/engage/create" className="flex items-center gap-2 ">
                  <Plus className="w-5" />
                  {t('kgstudio.common.create')}
                </Link>
              </Button>
            </div>
          </div>

          <Table
            data={parsedData}
            columnDefs={columnDefs}
            pageSize={audience?.paging?.page_size || 10}
            totalCount={audience?.paging?.total_count || 0}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
          />
        </div>
      </section>
    </div>
  );
};

export default EngagementDetail;
