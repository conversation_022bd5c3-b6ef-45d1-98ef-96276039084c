export function formatNumber(number: number): string {
  const suffixes = ['', 'K', 'M', 'B', 'T']; // Add more suffixes as needed
  const suffixIndex = Math.floor(Math.log10(number) / 3);

  if (suffixIndex < 0 || suffixIndex >= suffixes.length) {
    return number.toString(); // No need for abbreviation
  }

  const abbreviatedNumber = number / Math.pow(10, suffixIndex * 3);
  const formattedNumber = abbreviatedNumber.toFixed(1).replace(/\.0$/, ''); // Remove trailing ".0"

  return `${formattedNumber}${suffixes[suffixIndex]}`;
}

interface ILetterIterator extends Iterator<string> {
  reset: () => void;
}
export class LetterIterator implements ILetterIterator {
  private currentCharCode: number = 'A'.charCodeAt(0);

  next() {
    if (this.currentCharCode > 'Z'.charCodeAt(0)) {
      this.currentCharCode = 'A'.charCodeAt(0);
    }

    const currentLetter = String.fromCharCode(this.currentCharCode);
    this.currentCharCode++;

    return {
      value: currentLetter,
      done: false,
    };
  }
  reset() {
    this.currentCharCode = 'A'.charCodeAt(0);
  }
}
