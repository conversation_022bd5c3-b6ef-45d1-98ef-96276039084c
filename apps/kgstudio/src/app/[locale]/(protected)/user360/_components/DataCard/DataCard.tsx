import Image from 'next/image';
import React, { ComponentPropsWithoutRef, ReactNode, isValidElement } from 'react';
import { P, match } from 'ts-pattern';
import { z } from 'zod';

import { cn } from '@/app/_common/lib/utils';

import lineDown from './assets/line-down.svg';
import lineUp from './assets/line-up.svg';
import usersDown from './assets/users-down.svg';
import newUsers from './assets/users-plus.svg';
import users from './assets/users.svg';
import wallet from './assets/wallet.svg';

const StockIconSchema = z.enum(['new-users', 'line-up', 'line-down', 'users-down', 'users', 'wallet']);
type StockIcon = z.infer<typeof StockIconSchema>;
const isStockIcon = (icon: unknown): icon is StockIcon => StockIconSchema.safeParse(icon).success;
export interface DataCardProps extends ComponentPropsWithoutRef<'div'> {
  title: string;
  value: number;
  format?: (value: number) => ReactNode;
  icon?: StockIcon | React.ReactNode;
}

const DataCard = ({ title, value, format, icon, className, ...props }: DataCardProps) => {
  const getIcon = (icon: StockIcon | React.ReactNode) =>
    match(icon)
      //
      .with(P.when(isStockIcon), (icon) =>
        match(icon)
          .with('new-users', () => (
            <div className="bg-success-light flex h-12 w-12 items-center justify-center rounded-full">
              <Image src={newUsers} width={24} height={24} alt="new users" />
            </div>
          ))
          .with('line-up', () => (
            <div className="bg-success-light flex h-12 w-12 items-center justify-center rounded-full">
              <Image src={lineUp} width={24} height={24} alt="line up" />
            </div>
          ))
          .with('users-down', () => (
            <div className="bg-error-light flex h-12 w-12 items-center justify-center rounded-full">
              <Image src={usersDown} width={24} height={24} alt="users down" />
            </div>
          ))

          .with('line-down', () => (
            <div className="bg-error-light flex h-12 w-12 items-center justify-center rounded-full">
              <Image src={lineDown} width={24} height={24} alt="line down" />
            </div>
          ))
          .with('users', () => (
            <div className="bg-success-light flex h-12 w-12 items-center justify-center rounded-full">
              <Image src={users} width={24} height={24} alt="line down" />
            </div>
          ))
          .with('wallet', () => (
            <div className="bg-success-light flex h-12 w-12 items-center justify-center rounded-full">
              <Image src={wallet} width={24} height={24} alt="line down" />
            </div>
          ))
          .exhaustive(),
      )
      .with(P.when(isValidElement), (icon) => icon)
      .otherwise(() => null);

  const formatedValue = format ? format(value) : value;

  return (
    <div
      className={cn(
        'bg-surface-primary flex h-full w-full items-center gap-4 rounded-3xl p-4 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]',
        className,
      )}
      {...props}
    >
      {icon && getIcon(icon)}
      <div className="flex flex-col">
        <p className="text-body-2 text-secondary font-bold">{title}</p>
        <p className="text-h2 text-primary font-bold">{formatedValue}</p>
      </div>
    </div>
  );
};

export { DataCard };
