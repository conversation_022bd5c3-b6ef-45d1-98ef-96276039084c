'use client';

import { <PERSON>, <PERSON>rk<PERSON>, Undo2, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { amountInputInterceptor } from '@/app/[locale]/(protected)/asset/_lib/utils';
import { FormattedMessage } from '@/app/_common/components';
import { FormDropdown, FormInput, FormRadioGroup } from '@/app/_common/components/form';
import { useIsClient } from '@/app/_common/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON>, Form, ReminderBlock, Separator, Step, Steps } from '@kryptogo/2b';

import { useAiRecommendations } from '../_services/command/getAiRecommendations';
import { useUpsertEngagement } from '../_services/command/upsertEngagement';
import { LetterIterator, formatNumber } from '../_utils';

const AiRecommendSchema = z.object({
  prompt: z.object({
    target: z.string().min(1),
    budget: z.coerce.number().gt(0),
  }),
  plan: z.coerce.number().gte(0),
  strategy: z
    .object({
      target: z.object({
        behavior: z.string().min(1),
        asset_balance: z.string().min(1) || '',
        activity: z.string().min(1),
      }),
      estimation: z.object({
        reach: z.object({
          users: z.number().gt(0),
          wallets: z.number().gt(0),
        }),
        gas: z.number().gt(0),
      }),
    })
    .required(),
});
export type AiRecommendFormValues = z.infer<typeof AiRecommendSchema>;

const steps: Step[] = [
  {
    stepNumber: 1,
    label: 'Prompts',
    value: 'prompts',
  },
  {
    stepNumber: 2,
    label: 'Recommendations',
    value: 'recommendations',
  },
  {
    stepNumber: 3,
    label: 'Results',
    value: 'results',
  },
];
const promptTargetOptions = [
  {
    label: <FormattedMessage id="kgstudio.data.increase-wallet-user-retention" />,
    value: 'rise_mau',
  },
  { label: <FormattedMessage id="kgstudio.data.increase-wallet-user-activity" />, value: 'rise_dau' },
];

const PROMPT = 'prompt';

export interface AiRecommendProps {
  sessionId: string;
}
const AiRecommend = (props: AiRecommendProps) => {
  const t = useTranslations();
  const { isClient } = useIsClient();
  const [currentStepValue, setCurrentStepValue] = useState('prompts');
  const [recommendations, setRecommendations] = useState<AiRecommendFormValues['strategy'][]>([]);
  const {
    mutate: aiRecommend,
    isLoading: aiRecommendationsLoading,
    isError: aiRecommendationsError,
    reset: resetAiRecommendations,
  } = useAiRecommendations({
    onSuccess: (data) => {
      setRecommendations(data);
      setCurrentStepValue('recommendations');
    },
  });
  const form = useForm<AiRecommendFormValues>({
    defaultValues: {
      prompt: {
        target: '',
        budget: 0,
      },
      plan: undefined,
      strategy: undefined,
    },
    resolver: zodResolver(AiRecommendSchema),
    mode: 'all',
  });
  const prompt = form.watch('prompt');
  const plan = form.watch('plan');

  const {
    mutate: upsertEngagement,
    isLoading: upsertEngagementLoading,
    isError: upsertEngagementError,
  } = useUpsertEngagement();

  const handleAiRecommend = async () => {
    const result = await form.trigger('prompt');
    if (result) {
      resetAiRecommendations();
      aiRecommend({
        prompt: PROMPT,
      });
    }
  };
  const items = useMemo(() => {
    const letterIterator = new LetterIterator();

    const items = recommendations.map((recommendation, index) => {
      const { target, estimation } = recommendation;
      const letter = letterIterator.next().value;
      const label = `[Plan ${letter}]\nEstimate Reach: ${formatNumber(estimation.reach.users)} users / ${formatNumber(
        estimation.reach.wallets,
      )} wallets\nEstimate Gas Fee: ${estimation.gas} POL (~${(estimation.gas * 0.5487).toFixed(2)} USD)`;
      const desc = (
        <ul className="list-inside list-disc">
          <li>Behavior: {target.behavior}</li>
          <li>Asset Balance: {target.asset_balance}</li>
          <li>Activity: {target.activity}</li>
        </ul>
      );

      return {
        label,
        desc,
        value: index.toString(),
        'data-value': {
          target,
          estimation,
        },
      };
    });
    letterIterator.reset();
    return items;
  }, [recommendations]);

  const onSubmit = (values: AiRecommendFormValues) => {
    setCurrentStepValue('results');

    upsertEngagement({
      session_id: props.sessionId,
      target: JSON.stringify(values.strategy),
    });
  };

  useLayoutEffect(() => {
    const maxWidthContainer = document.querySelector<HTMLElement>('[data-maxWidth-container]');
    if (!maxWidthContainer) return;

    const maxWidthElements = maxWidthContainer.querySelectorAll('[data-maxWidth]');
    if (!maxWidthElements) return;

    let maxWidth = 0;
    maxWidthElements.forEach((element) => {
      const width = element.clientWidth;
      if (width > maxWidth) maxWidth = width;
    });

    maxWidthContainer.style.setProperty('--title-width', `${maxWidth}px`);
  }, [currentStepValue]);

  useEffect(() => {
    if (plan !== undefined) {
      form.setValue('strategy', items[plan]['data-value'], {
        shouldValidate: true,
      });
    }
  }, [plan, recommendations, form, items]);

  if (!isClient) return null;

  return (
    <section>
      <Steps steps={steps} value={currentStepValue} onValueChange={setCurrentStepValue}>
        <Form {...form}>
          <form>
            <Steps.Content value="prompts">
              <fieldset className="space-y-6">
                <FormDropdown
                  title={t('kgstudio.data.target-title')}
                  required
                  name="prompt.target"
                  control={form.control}
                  options={promptTargetOptions}
                  placeholder={t('kgstudio.data.target-placeholder')}
                />
                <FormInput
                  name="prompt.budget"
                  control={form.control}
                  required
                  title={t('kgstudio.data.budget-title')}
                  placeholder="0.00"
                  onInput={amountInputInterceptor}
                />
                <Button
                  type="button"
                  icon={<Sparkles className="h-4 w-4 stroke-white" />}
                  onClick={handleAiRecommend}
                  disabled={aiRecommendationsLoading}
                >
                  {t('kgstudio.data.ai-generate')}
                </Button>
              </fieldset>
            </Steps.Content>

            <Steps.Content value="recommendations">
              <fieldset className="space-y-6">
                <ReminderBlock
                  variant="info"
                  title={t('kgstudio.data.your-prompt')}
                  description={
                    <div className="text-body-2 text-primary flex flex-col gap-1">
                      <p>
                        {t('kgstudio.data.target-title')}:{' '}
                        {
                          promptTargetOptions.find((option) => {
                            return option.value === prompt.target;
                          })?.label
                        }
                      </p>
                      <p>
                        {t('kgstudio.data.budget-title')}: ${prompt.budget} USD
                      </p>
                    </div>
                  }
                />
                <div className="border-primary rounded-2xl border p-6">
                  <FormRadioGroup
                    title={t('kgstudio.data.recommended-target')}
                    control={form.control}
                    name="plan"
                    items={items}
                    itemVariant="card"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Button
                      type="button"
                      variant="grey"
                      icon={<Undo2 />}
                      onClick={() => {
                        setCurrentStepValue('prompts');
                        form.reset({
                          prompt: form.getValues('prompt'),
                          plan: undefined,
                          strategy: undefined,
                        });
                      }}
                    >
                      {t('kgstudio.data.discard')}
                    </Button>
                    <Button type="button" variant="secondary" icon={<Sparkles />} onClick={handleAiRecommend}>
                      {t('kgstudio.data.try-more')}
                    </Button>
                  </div>
                  <Button
                    type="button"
                    icon={<Check />}
                    disabled={upsertEngagementLoading || !form.formState.isValid}
                    onClick={form.handleSubmit(onSubmit)}
                  >
                    {t('kgstudio.data.choose-plan')}
                  </Button>
                </div>
              </fieldset>
            </Steps.Content>
            <Steps.Content value="results" className="space-y-6">
              <div className="border-primary space-y-6 rounded-2xl border p-6" data-maxWidth-container>
                <div className="space-y-6">
                  <p className="text-body-bold text-primary">{t('kgstudio.data.your-prompt')}</p>
                  <div className="flex flex-col gap-3">
                    <div className="text-body flex gap-2">
                      <p
                        className="text-secondary flex-shrink-0 flex-grow-0 basis-[var(--title-width)] whitespace-nowrap"
                        data-maxWidth
                      >
                        {t('kgstudio.data.target-title')}
                      </p>
                      <p className="text-primary">{t('kgstudio.data.increase-user-retention')}</p>
                    </div>
                    <div className="text-body flex gap-2">
                      <p
                        className="text-secondary flex-shrink-0 flex-grow-0 basis-[var(--title-width)] whitespace-nowrap"
                        data-maxWidth
                      >
                        {t('kgstudio.data.budget-title')}
                      </p>
                      <p className="text-primary">1000 USD</p>
                    </div>
                  </div>
                </div>
                <Separator />
                <div className="space-y-6">
                  <p className="text-body-bold text-primary">{t('kgstudio.data.target-title')}</p>
                  <div className="flex flex-col gap-3">
                    <div className="text-body flex gap-2">
                      <p
                        className="text-secondary flex-shrink-0 flex-grow-0 basis-[var(--title-width)] whitespace-nowrap"
                        data-maxWidth
                      >
                        {t('kgstudio.engagement.behavior')}
                      </p>
                      <p className="text-primary">{t('kgstudio.data.registered-in-7D')}</p>
                    </div>
                    <div className="text-body flex gap-2">
                      <p
                        className="text-secondary flex-shrink-0 flex-grow-0 basis-[var(--title-width)] whitespace-nowrap"
                        data-maxWidth
                      >
                        {t('kgstudio.engagement.asset-balance')}
                      </p>
                      <p className="text-primary">{`100 >= Balance > 0`}</p>
                    </div>
                    <div className="text-body flex gap-2">
                      <p
                        className="text-secondary flex-shrink-0 flex-grow-0 basis-[var(--title-width)] whitespace-nowrap"
                        data-maxWidth
                      >
                        {t('kgstudio.engagement.activity')}
                      </p>
                      <p className="text-primary">{`Claimed Sushiswap rewards`}</p>
                    </div>
                  </div>
                </div>
                <Separator />
                <div className="space-y-6">
                  <p className="text-body-bold text-primary">{t('kgstudio.data.your-prompt')}</p>
                  <div className="flex flex-col gap-3">
                    <div className="text-body flex gap-2">
                      <p
                        className="text-secondary flex-shrink-0 flex-grow-0 basis-[var(--title-width)] whitespace-nowrap"
                        data-maxWidth
                      >
                        {t('kgstudio.data.estimated-reach')}
                      </p>
                      <p className="text-primary">{`8.5k users  / 8.7k wallets`}</p>
                    </div>
                    <div className="text-body flex gap-2">
                      <p
                        className="text-secondary flex-shrink-0 flex-grow-0 basis-[var(--title-width)] whitespace-nowrap"
                        data-maxWidth
                      >
                        {t('kgstudio.data.estimated-gas-fee')}
                      </p>
                      <p className="text-primary">{`488 POL (~435 USD)`}</p>
                    </div>
                  </div>
                </div>
              </div>
              <Button
                type="button"
                variant="grey"
                icon={<X />}
                onClick={() => {
                  setCurrentStepValue('prompts');
                  form.reset({
                    prompt: form.getValues('prompt'),
                    plan: undefined,
                    strategy: undefined,
                  });
                  upsertEngagement({
                    session_id: props.sessionId,
                    target: null,
                  });
                }}
              >
                {t('kgstudio.common.reset')}
              </Button>
            </Steps.Content>
          </form>
        </Form>
      </Steps>
    </section>
  );
};

export { AiRecommend };
