import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={37} height={37} fill="none" className="text-brand-primary" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={3}
      d="M25.083 21.5h.015M4.833 8v21a3 3 0 0 0 3 3h21a3 3 0 0 0 3-3V14a3 3 0 0 0-3-3h-21a3 3 0 0 1-3-3Zm0 0a3 3 0 0 1 3-3h18m0 16.5a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
    />
  </svg>
);
export default SvgComponent;
