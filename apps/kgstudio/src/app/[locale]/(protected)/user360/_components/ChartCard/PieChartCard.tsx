'use client';

import * as d3 from 'd3';
import * as A from 'fp-ts/Array';
import * as NA from 'fp-ts/NonEmptyArray';
import * as O from 'fp-ts/Option';
import { contramap } from 'fp-ts/Ord';
import * as R from 'fp-ts/Record';
import { identity, pipe } from 'fp-ts/lib/function';
import * as N from 'fp-ts/number';
import { AlertCircle, Info } from 'lucide-react';
import React, {
  ComponentPropsWithoutRef,
  PropsWithChildren,
  ReactElement,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { FormattedMessage } from '@/app/_common/components';
import { useDeviceSize, useMaxWidth, useMeasure } from '@/app/_common/hooks';
import { cn } from '@/app/_common/lib/utils';
import { Card, Tooltip } from '@kryptogo/2b';

import { Percentage, PercentageInfo } from '../Percentage';
import './PieChart.css';

type Datum = {
  name: string;
  value: number;
};
type ProcessedDatum = Datum & {
  color: string;
  group?: string;
};
interface PieChartContextValue {
  data: Array<ProcessedDatum>;
  valueAll: number;
  chartSize: number | null;
  setChartSize: (size: number) => void;
}
const PieChartContext = createContext<PieChartContextValue | null>(null);
const usePieChartContext = () => {
  const context = useContext(PieChartContext);

  if (!context) {
    throw new Error('PieChart compound components cannot be rendered outside');
  }

  return context;
};
export interface PieChartProps {
  data: Array<Datum>;
  groupMapping?: Record<string, string[]>;
  className?: string;
}
const PieChart = ({ children, data, groupMapping, className }: PropsWithChildren<PieChartProps>) => {
  const [chartSize, setChartSize] = useState<number | null>(null);
  const colors = d3.scaleOrdinal(d3.schemeCategory10).domain(data.map((d) => d.name));
  const getDatumGroup = (groupMapping: Record<string, string[]>, name: string) =>
    pipe(
      groupMapping,
      R.toArray,
      A.findFirst(([_, names]) => names.includes(name)),
      O.map(([group]) => group),
      O.getOrElseW(() => 'Others'),
    );
  const sortByValue = pipe(
    N.Ord,
    contramap((d: Datum) => -d.value),
  );
  const sortByName = !!groupMapping
    ? pipe(
        N.Ord,
        contramap((d: Datum) =>
          Object.keys(groupMapping).findIndex((group) => group === getDatumGroup(groupMapping, d.name)),
        ),
      )
    : undefined;
  const sortByValueByName = A.sortBy([...(!!sortByName ? [sortByName] : []), sortByValue]);
  const withColor = A.map((d: Datum) => ({ ...d, color: colors(d.name) }));
  const withGroup = !!groupMapping
    ? A.map((d: Datum) => ({ ...d, group: getDatumGroup(groupMapping, d.name) }))
    : identity;
  const dataProcessed = pipe(data, sortByValueByName, withColor, withGroup) as ProcessedDatum[];

  const valueAll = useMemo(() => data.reduce((acc, d) => acc + d.value, 0), [data]);

  return (
    <PieChartContext.Provider
      value={{
        data: dataProcessed,
        valueAll,
        chartSize,
        setChartSize,
      }}
    >
      <div className={cn('h-full w-full', className)}>{children}</div>
    </PieChartContext.Provider>
  );
};

type OverlayInfo = {
  title?: string;
  percentage?: PercentageInfo;
};
type PieChartOverlayProps =
  | (Omit<ComponentPropsWithoutRef<'div'>, 'children'> & OverlayInfo)
  | { children: (arg: { data: Array<Datum> }) => React.ReactNode };
function PieChartOverlay(props: Omit<ComponentPropsWithoutRef<'div'>, 'children'> & OverlayInfo): JSX.Element;
function PieChartOverlay(props: { children: (arg: { data: Array<Datum> }) => React.ReactNode }): JSX.Element;
function PieChartOverlay(props: PieChartOverlayProps): JSX.Element {
  const { data, valueAll } = usePieChartContext();

  if ('children' in props) {
    return <div className="h-full w-full">{props.children({ data })}</div>;
  }

  const { className, title, percentage, ...rest } = props;

  return (
    <div
      className={cn('pointer-events-none flex h-full w-full flex-col items-center justify-center p-6', className)}
      {...rest}
    >
      <p className="text-small text-placeholder font-bold">{title ?? 'All'}</p>
      <p className="text-h3 text-primary font-bold">
        {valueAll.toLocaleString('en-US', {
          maximumFractionDigits: 2,
        })}
      </p>
      {percentage && <Percentage info={percentage} />}
    </div>
  );
}
PieChartOverlay.displayName = 'PieChart.Overlay';

interface PieChartOutlineProps extends ComponentPropsWithoutRef<'div'> {
  tooltips?: Record<string, string>;
  showRawData?: boolean;
}
const PieChartOutline = ({ className, tooltips, showRawData = false, ...props }: PieChartOutlineProps) => {
  const { containerId, itemId } = useMaxWidth();
  const { data, valueAll } = usePieChartContext();

  const groupedData = pipe(
    data,
    NA.fromArray,
    O.map(NA.groupBy((d) => d.group ?? 'non-grouped')),
    O.map(R.toArray),
    O.map(A.map(([_, data]) => data)),
    O.getOrElseW(() => null),
  );

  if (!groupedData) return null;

  return (
    <div className="space-y-4" id={containerId}>
      {groupedData.map((data) => {
        const group = data[0].group;
        return (
          <div key={group} className="flex flex-col gap-2">
            {group && <p className="text-body-bold text-primary text-center">{group}</p>}
            <div
              className={cn('flex flex-wrap gap-x-4 gap-y-0 lg:!flex-col lg:!items-start lg:!gap-2', className)}
              {...props}
            >
              {data.map((d) => {
                const tooltip = tooltips?.[d.name];

                return (
                  <>
                    <div key={d.name} className="text-small text-primary flex items-center gap-1">
                      <div className="h-2 w-2 rounded-full" style={{ backgroundColor: d.color }}></div>
                      <p id={itemId}>{d.name}</p>

                      <div className="col-start-4 flex items-center gap-1">
                        <p className="font-bold">{(valueAll === 0 ? 0 : (d.value / valueAll) * 100).toFixed(2)}%</p>
                        {showRawData && <p className="font-bold">{`(${d.value})`}</p>}
                      </div>
                      {!!tooltip && (
                        <Tooltip>
                          <Tooltip.Trigger>
                            <Info className="stroke-surface-primary fill-[var(--text-disabled)]" size={16} />
                          </Tooltip.Trigger>
                          <Tooltip.Content
                            side="top"
                            className="text-caption bg-surface-primary text-primary z-tooltip max-w-[200px] overflow-visible border-none px-3 py-2 shadow md:!max-w-[300px] lg:!max-w-[400px]"
                            style={{
                              maxHeight: 'var(--radix-tooltip-content-available-height)',
                            }}
                          >
                            {tooltip}
                            <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
                          </Tooltip.Content>
                        </Tooltip>
                      )}
                    </div>
                  </>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};
PieChartOutline.displayName = 'PieChart.Outline';

const PieChartContainer = ({ children, className, ...props }: ComponentPropsWithoutRef<'div'>) => {
  const { setChartSize, valueAll } = usePieChartContext();
  const [ref, rect] = useMeasure();

  const overlay = React.Children.toArray(children).find((child) => {
    return React.isValidElement(child) && child.type === PieChartOverlay;
  }) as ReactElement<PieChartFigureProps> | undefined;
  const figure = React.Children.toArray(children).find((child) => {
    return React.isValidElement(child) && child.type === PieChartFigure;
  }) as ReactElement<PieChartFigureProps> | undefined;

  useEffect(() => {
    if (!rect) return;

    setChartSize(Math.min(rect.width, rect.height));
  }, [rect, setChartSize]);

  if (valueAll === 0) {
    return (
      <p className="text-small text-primary">
        <FormattedMessage id="kgstudio.data.compliance.no-data" />
      </p>
    );
  }

  return (
    <div ref={ref} className={cn('relative flex items-center justify-center', className)} {...props}>
      {figure && <figure.type {...figure.props} overlay={overlay} />}
    </div>
  );
};
PieChartContainer.displayName = 'PieChart.Container';
export interface PieChartFigureProps {
  size?: number;
  innerRadius?: number;
  outerRadius?: number;
  overlay?: React.ReactNode;
}
const PieChartFigure = ({ size, innerRadius, outerRadius, overlay }: PieChartFigureProps) => {
  const { data, chartSize } = usePieChartContext();

  const figureSize = size ?? chartSize ?? 0;
  const figureOuterRadius = outerRadius ?? figureSize * 0.5;
  const figureInnerRadius = innerRadius ?? figureOuterRadius * 0.75;

  const formatedData = d3.pie<unknown, Datum>().value((d) => d.value)(data);
  const arcGenerator = d3.arc<d3.PieArcDatum<Datum>>().innerRadius(figureInnerRadius).outerRadius(figureOuterRadius);

  const getColorByName = (name: string) => data.find((d) => d.name === name)?.color ?? 'transparent';

  return (
    <div className="relative">
      <svg viewBox={`0 0 ${figureSize} ${figureSize}`} width={figureSize} height={figureSize}>
        <g transform={`translate(${figureOuterRadius} ${figureOuterRadius})`}>
          {formatedData.map((d, i) => (
            <g key={i} className="text-small text-primary group">
              <path
                d={arcGenerator(d) as string}
                fill={getColorByName(d.data.name)}
                className="arc transition-opacity ease-in-out group-hover:opacity-60"
              />
              <text
                transform={`translate(${arcGenerator.centroid(d)})`}
                fill="currentColor"
                textAnchor="middle"
                className="hidden group-hover:block"
                fontWeight={700}
              >
                {d.data.value}
              </text>
            </g>
          ))}
        </g>
      </svg>
      {!!overlay && <div className="pointer-events-none absolute inset-0">{overlay}</div>}
    </div>
  );
};
PieChartFigure.displayName = 'PieChart.Figure';

PieChart.Outline = PieChartOutline;
PieChart.Overlay = PieChartOverlay;
PieChart.Container = PieChartContainer;
PieChart.Figure = PieChartFigure;

export interface PieChartCardProps {
  title: string;
  data: Array<Datum>;
  overlayInfo?: OverlayInfo;
  className?: string;
  tooltip?: string;
}
const PieChartCard = ({ title, data, className, overlayInfo, tooltip }: PieChartCardProps) => {
  const { deviceSize } = useDeviceSize();

  return (
    <Card
      className={cn(
        'grid grid-cols-1 grid-rows-[auto_1fr] gap-0 rounded-lg p-4 md:!gap-4 md:rounded-3xl md:p-6',
        className,
      )}
    >
      <div className="flex items-center gap-2">
        <h3 className="text-h3 text-primary font-bold">{title}</h3>
        {!!tooltip && (
          <Tooltip>
            <Tooltip.Trigger>
              <AlertCircle className="stroke-surface-primary fill-[var(--brand-primary)]" size={16} />
            </Tooltip.Trigger>
            <Tooltip.Content
              side="top"
              className="text-caption bg-surface-primary text-primary overflow-visible border-none px-3 py-2 shadow"
            >
              {tooltip}
              <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
            </Tooltip.Content>
          </Tooltip>
        )}
      </div>

      <PieChart
        data={data}
        className="@container/card mt-4 grid grid-cols-1 gap-0 lg:!mt-0 lg:grid-cols-[1fr_auto] lg:!gap-6"
      >
        {/* grid item by default has min-w-auto, so it can't be smaller 
            than its content, so, mt chart only growing but not shrinking...凸 so we can set 
            min-w-0 grid-item to opt-out from this behavior
        */}
        <PieChart.Container className="h-full w-full min-w-0">
          <PieChart.Figure size={deviceSize === 'sm' ? 170 : deviceSize === 'md' ? 200 : undefined} />
          {!!overlayInfo && <PieChart.Overlay {...overlayInfo} />}
        </PieChart.Container>

        <PieChart.Outline className="justify-center" />
      </PieChart>
    </Card>
  );
};

export { PieChart, PieChartCard, type Datum };
