import { useMaxWidth } from '@/app/_common/hooks';
import { Card } from '@kryptogo/2b';

export interface TwoColCardProps {
  title: string;
  schema: Record<string, React.ReactNode>;
}

const TwoColCard = ({ title, schema }: TwoColCardProps) => {
  const { containerId, itemId } = useMaxWidth();

  return (
    <Card className="space-y-4 p-6" id={containerId}>
      <h3 className="text-h3 text-primary font-bold">{title}</h3>
      <div className="space-y-3">
        {Object.entries(schema).map(([title, value]) => (
          <div key={title} className="text-body flex items-center gap-6">
            <p className="text-secondary whitespace-nowrap" id={itemId}>
              {title}
            </p>
            {typeof value === 'string' ? <p className="text-primary">{value}</p> : value}
          </div>
        ))}
      </div>
    </Card>
  );
};

export { TwoColCard };
