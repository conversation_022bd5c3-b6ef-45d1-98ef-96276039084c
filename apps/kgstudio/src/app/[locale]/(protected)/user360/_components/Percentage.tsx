import { ComponentPropsWithoutRef } from 'react';

import { cn } from '@/app/_common/lib/utils';

export type PercentageInfo = {
  value: number;
  icon?: boolean;
  prefix?: boolean;
  tone?: 'success' | 'error' | 'neutral';
};
export const Percentage = ({
  info,
  className,
  ...props
}: { info: PercentageInfo } & ComponentPropsWithoutRef<'div'>) => {
  const isGrowing = info.value > 0;

  return (
    <div
      className={cn('text-small flex items-center gap-1 font-bold', className, {
        'text-success': info.tone === 'success' || isGrowing,
        'text-error': info.tone === 'error' || !isGrowing,
        'text-secondary': info.tone === 'neutral',
      })}
      {...props}
    >
      {info.prefix && <p>{isGrowing ? '+' : '-'}</p>}
      {info.icon && <p>{isGrowing ? '↑' : '↓'}</p>}
      <p>{info.value}%</p>
    </div>
  );
};
