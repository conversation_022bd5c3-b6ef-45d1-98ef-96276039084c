import { motion } from 'framer-motion';

export interface PushNotificationPreviewerProps {
  title: string;
  body: string;
}
const PushNotificationPreviewer = ({ title, body }: PushNotificationPreviewerProps) => {
  return (
    <div className="relative mx-auto h-[600px] w-[300px] overflow-hidden rounded-[2.5rem] border-[14px] border-[#222222] bg-[#222222] shadow-xl">
      <div className="absolute left-1/2 top-0 z-[100] h-[18px]  w-[148px] -translate-x-1/2 rounded-b-[1rem] bg-[#222222]"></div>
      <div className="absolute -left-[17px] top-[124px] h-[46px] w-[3px] rounded-l-lg bg-[#222222]"></div>
      <div className="absolute -left-[17px] top-[178px] h-[46px] w-[3px] rounded-l-lg bg-[#222222]"></div>
      <div className="absolute -right-[17px] top-[142px] h-[64px] w-[3px] rounded-r-lg bg-[#222222]"></div>
      <div className="bg-surface-secondary-dark h-[572px] w-[272px] overflow-hidden rounded-[2rem] px-[10px] pt-[30px] text-black">
        <motion.div
          className="relative h-[70px] space-y-[2.18px] overflow-hidden rounded-[15px] bg-[rgba(255,255,255,0.7)] p-[0.75rem] shadow-[0_1px_3px_0_rgba(0,0,0,0.1),0_1px_2px_0_rgba(0,0,0,0.06)] backdrop-blur-[20px] backdrop-saturate-[1.3]"
          initial={{ opacity: 0, y: -100, scale: 0.4 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 1, type: 'spring', repeat: Infinity, repeatDelay: 5 }}
        >
          <p className="font-barlow leading-1 text-[14px] font-bold">{title || 'Title'}</p>
          <p className="font-barlow leading-1 truncate text-[14px]">{body || 'Body'}</p>
          <p className="font-barlow absolute right-[11px] top-[12px] text-[12px] leading-[109%]">5m ago</p>
        </motion.div>
      </div>
    </div>
  );
};

export { PushNotificationPreviewer };
