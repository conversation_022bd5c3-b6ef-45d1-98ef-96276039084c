import * as d3 from 'd3';
import { format, startOfDay } from 'date-fns';
import { motion } from 'framer-motion';

import { formatNumber } from '@/app/[locale]/(protected)/user360/_utils';
import { useMeasure } from '@/app/_common/hooks';
import { cn } from '@/app/_common/lib/utils';

type Datum = Record<string, number | Date>;

interface MultiLineChartProps<T extends Datum> {
  data: Array<T>;
  groupKeys: Array<keyof T>;
  width: number;
  height: number;
}
export const MultiLineChart = <T extends Datum>({ data, groupKeys, width, height }: MultiLineChartProps<T>) => {
  const processedData = data.map<Datum>((d) => ({
    ...d,
    all: groupKeys.reduce((acc, key) => acc + (d[key] as number), 0),
  }));
  const groupKeysWithAll = ['all', ...groupKeys] as string[];
  const margin = { top: 30, right: 35, bottom: 30, left: 0 };
  const xScale = d3
    .scaleTime()
    .domain(
      d3.extent(processedData.map((d, index, arr) => (index !== arr.length - 1 ? startOfDay(d.date) : d.date))) as [
        Date,
        Date,
      ],
    )
    .range([margin.left, width - margin.right]);

  const yScale = d3
    .scaleLinear()
    .domain([
      0,
      d3.max(processedData, (d) => Math.max(...(groupKeysWithAll.map((key) => d[key]) as number[]))) as number,
    ])
    .nice()
    .range([height - margin.bottom, margin.top]);
  const colors = d3.scaleOrdinal(d3.schemeCategory10).domain(groupKeysWithAll as string[]);
  const lines = groupKeysWithAll.map((key) => {
    const line = d3
      .line<Datum>()
      .x((d) => xScale(d.date))
      .y((d) => yScale(d[key] as number))
      .curve(d3.curveBasis);
    const stroke = colors(key as string);
    return [line(processedData) as string, stroke];
  });

  return (
    <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`}>
      {groupKeysWithAll.map((key, index) => (
        <g
          key={key as string}
          transform={`translate(${margin.left + index * 80},4)`}
          // className="flex items-center gap-1"
        >
          <circle cx="10" cy="5" r="5" fill={colors(key as string)} />
          <text x="20" y="5" fill="currentColor" className="text-primary text-small" alignmentBaseline="middle">
            {(key as string).charAt(0).toUpperCase() + (key as string).slice(1)}
          </text>
        </g>
      ))}

      {yScale.ticks(6).map((tick) => (
        <g key={tick} transform={`translate(0,${yScale(tick)})`}>
          <line
            className="text-[var(--border-primary)]"
            x1={margin.left}
            x2={width - margin.right + 10}
            stroke="currentColor"
            strokeDasharray="4,4"
          />
          <text
            x={width - margin.right + 10}
            alignmentBaseline="middle"
            fill="currentColor"
            className="text-placeholder text-small"
          >
            {formatNumber(tick)}
          </text>
        </g>
      ))}
      {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        xScale.ticks(8).map((tick) => (
          <g key={tick.toISOString()} transform={`translate(${xScale(tick)},5)`}>
            <line
              className="text-[var(--border-primary)]"
              y1={margin.top}
              y2={height - margin.bottom}
              stroke="currentColor"
              strokeDasharray="4,4"
            />
            <text
              y={height - margin.bottom}
              alignmentBaseline="hanging"
              fill="currentColor"
              className="text-placeholder text-small"
            >
              {format(tick, 'M/dd')}
            </text>
          </g>
        ))
      }
      {lines.map(([d, stroke], index) => (
        <motion.path
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1.5, type: 'spring' }}
          key={index}
          d={d as string}
          fill="none"
          stroke={stroke}
        />
      ))}
    </svg>
  );
};

export interface LineChartCardProps<T extends Datum>
  extends Omit<MultiLineChartProps<T>, 'width' | 'height'>,
    React.ComponentPropsWithoutRef<'div'> {
  title: string;
}
const LineChartCard = <T extends Datum>({ title, data, groupKeys, className, ...props }: LineChartCardProps<T>) => {
  const [ref, rect] = useMeasure();

  return (
    <div
      className={cn(
        'bg-surface-primary grid grid-cols-1 grid-rows-[auto_1fr] gap-4 overflow-hidden rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]',
        className,
      )}
      {...props}
    >
      <h3 className="text-h3 text-primary font-bold">{title}</h3>
      <div ref={ref}>
        {rect && <MultiLineChart data={data} groupKeys={groupKeys} width={rect.width} height={rect.height} />}
      </div>
    </div>
  );
};

export { LineChartCard };
