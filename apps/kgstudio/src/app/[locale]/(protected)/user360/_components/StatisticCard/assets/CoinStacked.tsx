import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={36} height={37} fill="none" className="text-brand-primary" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={3}
      d="M19.5 8c0 1.657-3.694 3-8.25 3S3 9.657 3 8m16.5 0c0-1.657-3.694-3-8.25-3S3 6.343 3 8m16.5 0v2.25M3 8v18c0 1.657 3.694 3 8.25 3m0-12c-.253 0-.503-.004-.75-.012C6.295 16.85 3 15.565 3 14m8.25 9C6.694 23 3 21.657 3 20m30-2.25c0 1.657-3.694 3-8.25 3s-8.25-1.343-8.25-3m16.5 0c0-1.657-3.694-3-8.25-3s-8.25 1.343-8.25 3m16.5 0V29c0 1.657-3.694 3-8.25 3s-8.25-1.343-8.25-3V17.75M33 23.375c0 1.657-3.694 3-8.25 3s-8.25-1.343-8.25-3"
    />
  </svg>
);
export default SvgComponent;
