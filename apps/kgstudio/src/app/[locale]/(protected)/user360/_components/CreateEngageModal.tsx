import { nanoid } from 'nanoid';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { FormattedMessage } from '@/app/_common/components';
import { FormInput } from '@/app/_common/components/form';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal } from '@kryptogo/2b';

import { useUpsertEngagement } from '../_services/command/upsertEngagement';

const createEngageModalSchema = z.object({
  engage_name: z.string().min(1),
});
type CreateEngageModalFormValues = z.infer<typeof createEngageModalSchema>;

interface CreateEngageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
const CreateEngageModal = (props: CreateEngageModalProps) => {
  const router = useRouter();
  const t = useTranslations();
  const form = useForm<CreateEngageModalFormValues>({
    defaultValues: {
      engage_name: '',
    },
    resolver: zodResolver(createEngageModalSchema),
    mode: 'all',
  });

  const {
    mutate: upsertEngagement,
    isLoading: upsertEngagementLoading,
    isError: upsertEngagementError,
  } = useUpsertEngagement();

  const onSubmit = async (values: CreateEngageModalFormValues) => {
    const sessionId = nanoid();
    upsertEngagement(
      {
        engage_name: values.engage_name,
        session_id: sessionId,
      },
      {
        onSuccess: () => {
          props.onOpenChange(false);
          router.push(`/user360/engage/create?session=${sessionId}`);
        },
      },
    );
  };

  return (
    <Modal
      open={props.open}
      onOpenChange={(open: boolean) => {
        if (!open) {
          form.reset();
        }
        props.onOpenChange(open);
      }}
    >
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>{t('kgstudio.data.engage-create')}</Modal.Title>
        </Modal.Header>
        <div>
          <Form {...form}>
            <form>
              <FormInput
                title={t('kgstudio.data.engage.name')}
                name="engage_name"
                control={form.control}
                required
                disabled={upsertEngagementLoading}
              />
            </form>
          </Form>
        </div>
        <Modal.Footer className="flex w-full items-center justify-between">
          <Modal.Close>
            <Button variant="secondary" type="button">
              <FormattedMessage id="kgstudio.common.cancel" />
            </Button>
          </Modal.Close>
          {/* <Link href="/user360/engage/create" className="flex items-center gap-2 "> */}
          <Button className="w-[200px]" disabled={upsertEngagementLoading} onClick={form.handleSubmit(onSubmit)}>
            <FormattedMessage id="kgstudio.common.next" />
          </Button>
          {/* </Link> */}
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { CreateEngageModal };
