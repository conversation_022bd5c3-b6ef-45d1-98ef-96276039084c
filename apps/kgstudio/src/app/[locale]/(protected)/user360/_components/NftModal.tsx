'use client';

import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useCollections } from '@/app/[locale]/(protected)/nft/campaign/_services';
import { NFTCollection } from '@/app/[locale]/(protected)/nft/campaign/_types';
import { FormInput, FormMediaUploader, FormRadioGroup, FormTextarea } from '@/app/_common/components/form';
import { useGcpFileUpload } from '@/app/_common/hooks';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal, Separator } from '@kryptogo/2b';
import { truncateTxhashOrAddress } from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';

import { useUpsertEngagement } from '../_services/command/upsertEngagement';

const ImageSchema = z.object({
  dataURL: z.string(),
  file: z
    .object({
      name: z.string(),
      size: z.number(),
      type: z.string(),
    })
    .optional(),
});

const NftCollectionSchema = z.object({
  type: z.enum(['create', 'from_nft_boost']),
  collection_name: z
    .string()
    .min(1)
    .max(40)
    .regex(/^[a-zA-Z0-9]+$/),
  collection_symbol: z
    .string()
    .min(1)
    .max(10)
    .regex(/^[a-zA-Z0-9]+$/),
  nft_image: z.array(ImageSchema).nonempty(),
  contract_schema: z.enum(['erc721', 'erc1155']),
  nft_description: z.string().max(1000),
  nft_opensea_banner: z.array(ImageSchema).nonempty(),
  nft_boost: z.string().optional(),
});
export type NftCollectionFormValues = z.infer<typeof NftCollectionSchema>;

export interface NftModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  sessionId: string;
}
const NftModal = (props: NftModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  const orgId = useOrganizationStore((state) => state.orgId);

  const {
    data: collections,
    isLoading: isCollectionsLoading,
    error: collectionsError,
  } = useCollections({}, orgId ?? -1);
  const form = useForm<NftCollectionFormValues>({
    defaultValues: {
      type: 'create',
    },
    resolver: zodResolver(NftCollectionSchema),
    mode: 'onChange',
  });
  const type = form.watch('type');

  const {
    isError: fileUrlsError,
    isLoading: fileUrlsLoading,
    isIdle: fileUrlsIdle,
    progress: fileUrlsProgress,
    uploadAllFiles,
  } = useGcpFileUpload();
  const {
    mutate: upsertEngagement,
    isLoading: upsertEngagementLoading,
    isError: upsertEngagementError,
  } = useUpsertEngagement({
    onSuccess: async () => {
      await queryClient.refetchQueries(['engagements', props.sessionId]);
      form.reset({
        type: 'create',
      });
      props.onOpenChange && props.onOpenChange(false);
    },
  });

  const buttonDisabled = upsertEngagementLoading || fileUrlsLoading || !form.formState.isValid;
  const formDisabled = upsertEngagementLoading || fileUrlsLoading;

  const onSubmit = async (values: NftCollectionFormValues) => {
    const imageKeys = ['nft_image', 'nft_opensea_banner'] as const;
    const filesWithKey = imageKeys.reduce((acc, key) => {
      const files = values[key];
      const localFiles = files.filter((data) => !!data.file);
      if (localFiles.length === 0) return acc;

      return {
        ...acc,
        [key]: localFiles,
      };
    }, {});

    const fileUrls = !!filesWithKey ? await uploadAllFiles(filesWithKey, 'engagement') : null;

    fileUrls &&
      Object.keys(fileUrls).forEach((key) =>
        Object.assign(values, {
          [key]: fileUrls[key][0],
        }),
      );
    upsertEngagement({
      session_id: props.sessionId,
      nft: JSON.stringify(values),
    });
  };

  //TODO: Extract to a component, together with list tiles
  const getNftCollectionsDisplay = (collection: NFTCollection) => (
    <div className="flex w-full items-center gap-3 rounded-xl p-3">
      <div className="h-10 w-10 overflow-hidden rounded-xl">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src={collection.collection_image_url}
          alt={collection.collection_name}
          className="h-full w-full object-cover"
        />
      </div>

      <div className="flex flex-col items-start justify-center">
        <p className="text-body-2 text-primary font-bold">{collection.collection_name}</p>
        <p className="text-small text-secondary">{truncateTxhashOrAddress(collection.contract_address)}</p>
      </div>
    </div>
  );
  return (
    <Modal {...props}>
      <Modal.Content className="scroll-bar-hide scrollbar-hide relative max-h-[80vh] overflow-y-auto">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.wallet.config.label.nft')}</Modal.Title>
        </Modal.Header>

        <div>
          <Form {...form}>
            <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
              <FormRadioGroup
                variant="horizontal"
                control={form.control}
                name="type"
                items={[
                  { label: t('kgstudio.data.create_nft_collection'), value: 'create' },
                  { label: t('kgstudio.data.select_from_nft_boost'), value: 'from_nft_boost' },
                ]}
              />

              {type === 'create' && (
                <>
                  <Separator />
                  <fieldset className="space-y-10" disabled={formDisabled}>
                    <FormInput
                      title={t('kgstudio.nft.form.collection-name')}
                      control={form.control}
                      name="collection_name"
                      required
                      hint={t('kgstudio.nft.collection-name-hint')}
                    />
                    <FormInput
                      title={t('kgstudio.nft.form.symbol-name')}
                      control={form.control}
                      name="collection_symbol"
                      required
                      hint={t('kgstudio.nft.collection-symbol-hint')}
                    />
                    <FormMediaUploader
                      title={t('kgstudio.nft.form.nft-image')}
                      required
                      previewInZone
                      dropZoneDesc={
                        <ul className="list-inside list-disc">
                          <li>{t('kgstudio.nft.form.banner-file-types')}</li>
                          <li>{t('kgstudio.nft.form.banner-recommended-size')}</li>
                        </ul>
                      }
                      control={form.control}
                      name="nft_image"
                      acceptType={['jpg', 'png', 'gif', 'svg']}
                      maxFileSize={100 * 1024 * 1024}
                      dropZoneRatio="1/1"
                      dropZoneWidth={200}
                    />
                    <FormRadioGroup
                      title={t('kgstudio.nft.form.contract-schema-name')}
                      name="contract_schema"
                      control={form.control}
                      required
                      items={[
                        { label: 'ERC721', value: 'erc721' },
                        { label: 'ERC1155', value: 'erc1155' },
                      ]}
                    />
                    <FormTextarea
                      title={t('kgstudio.nft.form.collection-description')}
                      name="nft_description"
                      control={form.control}
                      required
                      placeholder={t('kgstudio.nft.form.placeholder.description')}
                      hint={t('kgstudio.common.maxInputHint')}
                      className="h-[154px] resize-y"
                    />
                    <FormMediaUploader
                      title={t('kgstudio.data.nft_opensea_banner_title')}
                      name="nft_opensea_banner"
                      control={form.control}
                      required
                      previewInZone
                      dropZoneRatio="3/1"
                      dropZoneWidth={600}
                      dropZoneDesc={
                        <ul className="list-inside list-disc">
                          <li>{t('kgstudio.data.file_types_supported')}</li>
                          <li>{t('kgstudio.data.recommended_size')}</li>
                        </ul>
                      }
                    />
                    <div className="flex items-center justify-end gap-[10px]">
                      {/* <Button variant="secondary">Save Draft</Button> */}
                      <Button className="w-[152px]" disabled={buttonDisabled}>
                        {t('kgstudio.common.next-step')}
                      </Button>
                    </div>
                  </fieldset>
                </>
              )}
              {type === 'from_nft_boost' && (
                <fieldset className="space-y-6">
                  <FormRadioGroup
                    control={form.control}
                    name="nft_boost"
                    itemVariant="border"
                    variant="vertical"
                    items={
                      collections
                        ? collections.data.map((collection) => {
                            return {
                              label: getNftCollectionsDisplay(collection),
                              value: collection.event_id,
                              'data-value': collection,
                            };
                          })
                        : []
                    }
                    className="gap-2"
                  />

                  <Button className="w-[152px]">{t('kgstudio.common.next-step')}</Button>
                </fieldset>
              )}
            </form>
          </Form>
        </div>
      </Modal.Content>
    </Modal>
  );
};

export { NftModal };
