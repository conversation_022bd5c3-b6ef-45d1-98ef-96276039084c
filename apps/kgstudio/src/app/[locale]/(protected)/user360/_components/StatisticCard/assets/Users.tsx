import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={37} height={37} fill="none" className="text-brand-primary" {...props}>
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={3}
      d="M33.666 32v-3a6.003 6.003 0 0 0-4.5-5.811m-5.25-17.753a6.002 6.002 0 0 1 0 11.128M26.166 32c0-2.796 0-4.194-.456-5.296a6 6 0 0 0-3.247-3.247C21.36 23 19.963 23 17.166 23h-4.5c-2.795 0-4.193 0-5.296.457a6 6 0 0 0-3.247 3.247c-.457 1.102-.457 2.5-.457 5.296m17.25-21a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z"
    />
  </svg>
);
export default SvgComponent;
