import Image from 'next/image';

import { cn } from '@/app/_common/lib/utils';

interface InAppNotificationPreviewerProps {
  title: string;
  desc: string;
  image: string | undefined;
  buttons: {
    label: string;
    link: string;
  }[];
}
const InAppNotificationPreviewer = ({ title, desc, image, buttons }: InAppNotificationPreviewerProps) => {
  return (
    <div className="grid h-[400px] w-[214px] grid-rows-[auto_1fr_auto] space-y-[11px] overflow-hidden rounded-[13.6px] bg-[var(--primary)] p-[9.12px] shadow-[0px_2.2739064693450928px_11.369531631469727px_0px_rgba(0,0,0,0.10)]">
      <div className="flex flex-col gap-[4.38px]">
        <p className="leading-1 font-barlow text-brand-primary text-[8.768px] font-bold">Announcement</p>
        <p className="leading-1 font-barlow text-secondary text-[8.768px] font-bold">2023/08/01 11:11</p>
      </div>
      <div className="scrollbar-hide grid grid-rows-[auto_auto_1fr] gap-[2.92px] overflow-y-auto">
        <p className="font-barlow text-primary sticky text-[11.691px] font-bold leading-[125%]">{title}</p>
        {image && (
          <div className="relative h-[194px] w-[194px] overflow-hidden rounded-[4px]">
            <Image src={image} alt="avatar" fill className="object-cover" />
          </div>
        )}
        <p className="font-barlow text-primary break-all text-[10.229px] font-semibold leading-[11.691px]">{desc}</p>
      </div>
      {buttons.length > 0 && (
        <div className="space-y-[4.57px]">
          {buttons.map((button, index) => (
            <div
              key={index}
              className={cn(
                'bg-brand-primary flex w-full items-center justify-center rounded-[6.86px] px-[18.4px] py-[9.15px] text-[10px] font-bold leading-none tracking-[0.4px] text-white',
                index !== 0 && 'border-primary text-secondary border bg-white',
              )}
            >
              {button.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export { InAppNotificationPreviewer };
