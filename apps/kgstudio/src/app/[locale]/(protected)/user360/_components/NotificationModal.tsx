'use client';

import { get, set } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef, useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';

import { FormInput, FormMediaUploader, FormRadioGroup, FormTextarea } from '@/app/_common/components/form';
import { useGcpFileUpload } from '@/app/_common/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal, Separator } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import { useUpsertEngagement } from '../_services/command/upsertEngagement';
import { InAppNotificationPreviewer } from './InAppNotificationPreviewer';
import { PushNotificationPreviewer } from './PushNotificationPreviewer';

const ImageSchema = z.object({
  dataURL: z.string(),
  file: z
    .object({
      name: z.string(),
      size: z.number(),
      type: z.string(),
    })
    .optional(),
});
const ButtonSchema = z.object({
  label: z.string().min(1),
  link: z.string().url(),
});
const NotificationSchema = z.object({
  in_app_notification: z
    .object({
      title: z.string().min(1),
      image: z.array(ImageSchema),
      description: z.string().min(1),
      button_count: z.coerce.number().min(0).max(2),
      buttons: z.array(ButtonSchema),
    })
    .refine((data) => {
      const isValid = data.button_count === data.buttons.length;
      if (!isValid) {
        return {
          message: 'Button count does not match the number of buttons',
          path: ['in_app_notification', 'button_count'],
        };
      }
      return isValid;
    }),
  push_notification: z.object({
    title: z.string().min(1),
    body: z.string().min(1),
  }),
});
export type NotificationFormValues = z.infer<typeof NotificationSchema>;

export interface NotificationModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  sessionId: string;
}
const NotificationModal = (props: NotificationModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  const form = useForm<NotificationFormValues>({
    defaultValues: {
      in_app_notification: {
        title: '',
        image: undefined,
        description: '',
        button_count: 1,
        buttons: [
          {
            label: 'Button',
            link: '',
          },
        ],
      },
      push_notification: {
        title: '',
        body: '',
      },
    },
    resolver: zodResolver(NotificationSchema),
    mode: 'onChange',
  });
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'in_app_notification.buttons',
  });
  const buttonCount = form.watch('in_app_notification.button_count');
  const inAppNotiTitle = form.watch('in_app_notification.title');
  const inAppNotiImage = form.watch('in_app_notification.image');
  const inAppNotiDesc = form.watch('in_app_notification.description');
  const inAppNotiButtons = form.watch('in_app_notification.buttons');
  const pushNotiTitle = form.watch('push_notification.title');
  const pushNotiBody = form.watch('push_notification.body');

  useEffect(() => {
    if (buttonCount === fields.length) return;

    if (buttonCount > fields.length) {
      const diff = buttonCount - fields.length;
      append(Array.from({ length: diff }).map(() => ({ label: 'Button', link: '' })));
    }

    if (buttonCount < fields.length) {
      const diff = fields.length - buttonCount;
      remove(Array.from({ length: diff }).map((_, index) => index + buttonCount));
    }
  }, [buttonCount, remove, append, fields]);

  const {
    isError: fileUrlsError,
    isLoading: fileUrlsLoading,
    isIdle: fileUrlsIdle,
    progress: fileUrlsProgress,
    uploadAllFiles,
  } = useGcpFileUpload();
  const {
    mutate: upsertEngagement,
    isLoading: upsertEngagementLoading,
    isError: upsertEngagementError,
  } = useUpsertEngagement({
    onSuccess: async () => {
      await queryClient.refetchQueries(['engagements', props.sessionId]);
      props.onOpenChange && props.onOpenChange(false);
      form.reset();
    },
  });

  const onSubmit = async (values: NotificationFormValues) => {
    const imageKeys = ['in_app_notification.image'] as const;
    const filesWithKey = imageKeys.reduce((acc, key) => {
      const files = get(values, key);
      const localFiles = files.filter((data) => !!data.file);
      if (localFiles.length === 0) return acc;

      return {
        ...acc,
        [key]: localFiles,
      };
    }, {});

    const fileUrls = !!filesWithKey ? await uploadAllFiles(filesWithKey, 'engagement') : null;

    fileUrls && Object.keys(fileUrls).forEach((key) => set(values, key, fileUrls[key][0]));

    upsertEngagement({
      session_id: props.sessionId,
      notification: JSON.stringify(values),
    });
  };
  const buttonDisabled = upsertEngagementLoading || fileUrlsLoading || !form.formState.isValid;
  const formDisabled = upsertEngagementLoading || fileUrlsLoading;

  return (
    <Modal {...props}>
      <Modal.Content className="scroll-bar-hide scrollbar-hide max-h-[80vh] w-[768px] min-w-[768px] overflow-y-auto">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.common.notification')}</Modal.Title>
        </Modal.Header>

        <div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <fieldset className="space-y-6" disabled={formDisabled}>
                <section className="space-y-6">
                  <div className="flex flex-col gap-2">
                    <h3 className="text-body-bold text-primary">{t('kgstudio.common.app-notification')}</h3>
                    <p className="text-body-2 text-secondary">{t('kgstudio.common.type-some-description')}</p>
                  </div>
                  <div className="grid grid-cols-[1fr_auto] gap-10">
                    <div className="space-y-10">
                      <FormInput
                        title={t('kgstudio.nft.title-title')}
                        control={form.control}
                        name="in_app_notification.title"
                        placeholder={t('kgstudio.data.notification.enter-title')}
                        required
                      />
                      <FormMediaUploader
                        title={t('kgstudio.data.notification.image')}
                        control={form.control}
                        name="in_app_notification.image"
                        dropZoneRatio="1/1"
                        dropZoneWidth={200}
                        previewInZone
                        dropZoneDesc={
                          <ul className="list-inside list-disc">
                            <li>{t('kgstudio.data.notification.file-types-supported')}</li>
                            <li>{t('kgstudio.data.notification.recommended-size')}</li>
                          </ul>
                        }
                      />

                      <FormTextarea
                        title={t('kgstudio.common.description')}
                        control={form.control}
                        name="in_app_notification.description"
                        placeholder={t('kgstudio.nft.form.placeholder.description')}
                        required
                        className="h-[154px] resize-y"
                      />
                      <FormRadioGroup
                        title={t('kgstudio.data.notification.buttons')}
                        desc={t('kgstudio.common.type-some-description')}
                        control={form.control}
                        name="in_app_notification.button_count"
                        items={[
                          { label: t('kgstudio.data.notification.1-button'), value: '1' },
                          { label: t('kgstudio.data.notification.2-buttons'), value: '2' },
                          { label: t('kgstudio.data.notification.no-button'), value: '0' },
                        ]}
                        required
                        variant="horizontal"
                      />

                      <div className="space-y-2">
                        {fields.map((field, index) => (
                          <div key={field.id} className="space-y-2">
                            <FormInput
                              title={t('kgstudio.data.notification.first-button')}
                              control={form.control}
                              name={`in_app_notification.buttons.${index}.label`}
                              placeholder={t('kgstudio.data.notification.text-placeholder')}
                              required
                            />
                            <FormInput
                              control={form.control}
                              name={`in_app_notification.buttons.${index}.link`}
                              placeholder="https://"
                              required
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="space-y-[10px]">
                      <p className="text-body-bold text-primary">{t('kgstudio.wallet.config.label.preview')}</p>
                      <InAppNotificationPreviewer
                        title={inAppNotiTitle}
                        desc={inAppNotiDesc}
                        image={inAppNotiImage?.[0]?.dataURL}
                        buttons={inAppNotiButtons}
                      />
                    </div>
                  </div>
                </section>
                <Separator />
                <section className="space-y-6">
                  <div className="flex flex-col gap-2">
                    <h3 className="text-body-bold text-primary">{t('kgstudio.common.push-notification')}</h3>
                    <p className="text-body-2 text-secondary">{t('kgstudio.common.type-some-description')}</p>
                  </div>
                  <div className="grid max-h-[268px] grid-cols-[1fr_auto] gap-10 overflow-hidden">
                    <div className="space-y-10">
                      <FormInput
                        title={t('kgstudio.nft.title-title')}
                        control={form.control}
                        name="push_notification.title"
                        required
                      />
                      <FormInput
                        title={t('kgstudio.data.notification.body')}
                        control={form.control}
                        name="push_notification.body"
                        required
                      />
                    </div>
                    <div className="space-y-[10px]">
                      <p className="text-body-bold text-primary">{t('kgstudio.wallet.config.label.preview')}</p>
                      <PushNotificationPreviewer title={pushNotiTitle} body={pushNotiBody} />
                    </div>
                  </div>
                </section>
                <Separator />
                <div className="flex w-full items-center justify-end">
                  {/* <Button variant="secondary">Save Draft</Button> */}
                  <Button className="w-[152px]" disabled={buttonDisabled}>
                    {t('kgstudio.common.next')}
                  </Button>
                </div>
              </fieldset>
            </form>
          </Form>
        </div>
      </Modal.Content>
    </Modal>
  );
};

export { NotificationModal };
