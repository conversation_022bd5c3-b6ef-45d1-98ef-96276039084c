import { Info } from 'lucide-react';
import { ComponentPropsWithoutRef, isValidElement } from 'react';
import { P, match } from 'ts-pattern';
import { z } from 'zod';

import { cn } from '@/app/_common/lib/utils';
import { Card, Tooltip } from '@kryptogo/2b';

import { Percentage, PercentageInfo } from '../Percentage';
import Chart from './assets/Chart';
import CoinStacked from './assets/CoinStacked';
import Users from './assets/Users';
import Wallet from './assets/Wallet';

const StockIconSchema = z.enum(['chart', 'users', 'wallet', 'coins']);
type StockIcon = z.infer<typeof StockIconSchema>;
const isStockIcon = (icon: unknown): icon is StockIcon => StockIconSchema.safeParse(icon).success;

const StockFormatterSchema = z.enum(['number', 'currency']);
type StockFormatter = z.infer<typeof StockFormatterSchema>;
const isStockFormatter = (formatter: unknown): formatter is StockFormatter =>
  StockFormatterSchema.safeParse(formatter).success;

export interface StatisticCardProps extends ComponentPropsWithoutRef<'div'> {
  title: string;
  value: React.ReactNode;
  formatter?: StockFormatter | ((value: number) => string);
  percentage?: PercentageInfo;
  caption?: string;
  icon?: StockIcon | React.ReactNode;
  tooltip?: React.ReactNode;
}
const StatisticCard = ({
  title,
  value,
  formatter,
  percentage,
  caption,
  icon,
  className,
  tooltip,
  ...props
}: StatisticCardProps) => {
  const getIcon = (icon: StockIcon | React.ReactNode) =>
    match(icon)
      //
      .with(P.when(isStockIcon), (icon) =>
        match(icon)
          .with('chart', () => <Chart />)
          .with('users', () => (
            <div className="bg-brand-primary-lighter flex h-[64px] w-[64px] items-center justify-center rounded-full">
              <Users />
            </div>
          ))
          .with('wallet', () => (
            <div className="bg-brand-primary-lighter flex h-[64px] w-[64px] items-center justify-center rounded-full">
              <Wallet />
            </div>
          ))
          .with('coins', () => (
            <div className="bg-brand-primary-lighter flex h-[64px] w-[64px] items-center justify-center rounded-full">
              <CoinStacked />
            </div>
          ))
          .exhaustive(),
      )
      .with(P.when(isValidElement), (icon) => (
        <div className="bg-brand-primary-lighter [&_svg]:stroke-brand-primary flex h-12 w-12 items-center justify-center rounded-full md:h-16 md:w-16 [&_svg]:h-[27px] [&_svg]:w-[27px] [&_svg]:fill-transparent md:[&_svg]:h-9 md:[&_svg]:w-9">
          {icon}
        </div>
      ))
      .otherwise(() => null);

  const formatterFunc = match(formatter)
    //
    .with(P.when(isStockFormatter), (f) =>
      match(f)
        //
        .with(
          'number',
          () => (value: number) =>
            value.toLocaleString('en-US', {
              maximumFractionDigits: 2,
            }),
        )
        .with(
          'currency',
          () => (value: number) =>
            `$ ${value.toLocaleString('en-US', {
              maximumFractionDigits: 2,
            })}`,
        )
        .exhaustive(),
    )
    .otherwise((formatter) => formatter);

  return (
    <Card
      className={cn('@container radius-large card-padding-large flex items-center justify-between', className)}
      {...props}
    >
      <div className="flex flex-col">
        <div className="flex items-center gap-1">
          <p className="text-small text-primary font-bold">{title}</p>
          {!!tooltip && (
            <Tooltip>
              <Tooltip.Trigger>
                <Info className="stroke-[var(--text-primary)]" size={12} />
              </Tooltip.Trigger>
              <Tooltip.Content
                side="top"
                className="text-caption bg-surface-primary text-primary overflow-visible border-none px-3 py-2 shadow"
              >
                {tooltip}
                <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
              </Tooltip.Content>
            </Tooltip>
          )}
        </div>

        <div className="text-h2 text-primary flex items-center gap-2 font-bold">
          <div>{formatterFunc && typeof value === 'number' ? formatterFunc(value) : value}</div>
        </div>

        <div className="text-small flex items-center gap-1">
          {percentage && <Percentage info={percentage} />}
          <p className="text-disabled">{caption}</p>
        </div>
      </div>
      {icon && <div className="@[200px]:!block hidden shrink-0">{getIcon(icon)}</div>}
    </Card>
  );
};

export { StatisticCard };
