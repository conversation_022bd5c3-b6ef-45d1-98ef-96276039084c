import * as d3 from 'd3';
import { getDay, getHours } from 'date-fns';

import { useMeasure } from '@/app/_common/hooks';
import { cn } from '@/app/_common/lib/utils';

type Datum = Date;

interface HeatmapProps {
  data: Array<Datum>;
  width: number;
  height: number;
}
export const Heatmap = ({ data, width, height }: HeatmapProps) => {
  const xStep = 3;
  const valueSteps = [0, 10, 100, 200, 300];
  const margin = { top: 30, right: 30, bottom: 50, left: 20 };
  const xScale = d3
    .scaleLinear()
    .domain([0, 24])
    .range([margin.left, width - margin.right]);
  const yScale = d3
    .scaleLinear()
    .domain([0, 6])
    .range([height - margin.bottom, margin.top]);

  const rectWidth = xScale(1 * xStep) - xScale(0) - 2;
  const rectHeight = (yScale(0) - yScale(1)) * 0.9;

  const processedData = data
    .map((d) => {
      const dayOfWeek = getDay(d);
      const hourOfDay = getHours(d);

      return {
        day: dayOfWeek,
        hour: hourOfDay,
      };
    })
    .reduce(
      (acc, d) => {
        acc[d.day][d.hour] += 1;

        return acc;
      },
      Array.from({ length: 7 }, () => Array.from({ length: 24 }, () => 0)),
    )
    .map((day) => {
      const aggregated = Array.from({ length: 24 / xStep }, () => 0);
      return day.reduce((acc, d, i) => {
        aggregated[Math.floor(i / xStep)] += d;

        return i % xStep === xStep - 1 ? [...acc, aggregated[Math.floor(i / xStep)]] : acc;
      }, [] as number[]);
    });
  const maxValue = d3.max(processedData, (d) => Math.max(...d)) as number;
  const colorScale = d3
    .scaleQuantize<string>()
    .domain([0, maxValue])
    // .range(d3.schemeCategory10.slice(0, valueSteps.length + 1));
    .range(['#FFFFFF', '#FFF7DC', '#FFC211', '#D99D04', '#8C5B00']);

  return (
    <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`}>
      {valueSteps.reverse().map((step, i) => (
        <g key={step} transform={`translate(${margin.left + i * 60 + 10},${10})`}>
          <circle r={4} cx={-8} fill={colorScale(step - 1)} alignmentBaseline="middle" />
          <text alignmentBaseline="middle" fill="currentColor" className="text-primary text-small">
            {`> ${step}`}
          </text>
        </g>
      ))}
      {processedData.map((day, dayIndex) =>
        day.map((d, stepedHourIndex) => (
          <g
            key={`${dayIndex}_${stepedHourIndex}`}
            className="text-small text-primary group"
            transform={`translate(${xScale(stepedHourIndex * xStep)},${yScale(dayIndex)})`}
          >
            <rect
              width={rectWidth}
              height={rectHeight}
              fill={colorScale(d)}
              className="transition-opacity ease-in-out group-hover:opacity-60"
            />
            <text
              transform={`translate(${rectWidth / 2},${rectHeight / 2 + 4})`}
              fill="currentColor"
              textAnchor="middle"
              className="hidden group-hover:block"
              fontWeight={700}
            >
              {d}
            </text>
          </g>
        )),
      )}

      {yScale.ticks(7).map((tick) => (
        <g key={tick} transform={`translate(${width - margin.right + 10},${yScale(tick) + rectHeight / 2})`}>
          <text alignmentBaseline="middle" fill="currentColor" className="text-placeholder text-small">
            {getWeekdayName(tick)}
          </text>
        </g>
      ))}
      {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        xScale.ticks(24).map((tick) => {
          return (
            tick % xStep === 0 && (
              <g key={tick} transform={`translate(${xScale(tick) - 6}, ${height - 10})`}>
                <text alignmentBaseline="middle" fill="currentColor" className="text-placeholder text-small">
                  {tick}
                </text>
              </g>
            )
          );
        })
      }
    </svg>
  );
};

export interface HeatmapCardProps
  extends Omit<HeatmapProps, 'width' | 'height'>,
    React.ComponentPropsWithoutRef<'div'> {
  title: string;
}
const HeatmapCard = ({ title, data, className, ...props }: HeatmapCardProps) => {
  const [ref, rect] = useMeasure();

  return (
    <div
      className={cn(
        'bg-surface-primary grid grid-cols-1 grid-rows-[auto_1fr] gap-4 overflow-hidden rounded-3xl p-6 shadow-[14px_17px_40px_4px_rgba(112,144,176,0.08)]',
        className,
      )}
      {...props}
    >
      <h3 className="text-h3 text-primary font-bold">{title}</h3>
      <div ref={ref}>{rect && <Heatmap data={data} width={rect.width} height={rect.height} />}</div>
    </div>
  );
};

export { HeatmapCard };

export function getRandomTimestamps(start: Date, end: Date, count: number): Date[] {
  const randomTimestamps: Date[] = [];

  for (let i = 0; i < count; i++) {
    const randomTimestamp = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    randomTimestamps.push(randomTimestamp);
  }
  return randomTimestamps;
}

function getWeekdayName(dayNumber: number): string | null {
  const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  if (dayNumber >= 0 && dayNumber <= 6) {
    return weekdays[dayNumber];
  } else {
    return null;
  }
}
