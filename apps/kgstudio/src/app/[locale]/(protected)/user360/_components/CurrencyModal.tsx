'use client';

import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { amountInputInterceptor } from '@/app/[locale]/(protected)/asset/_lib/utils';
import { FormDropdown, FormInput } from '@/app/_common/components/form';
import { useTokenSelection } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiAssetProHooks } from '@/app/_common/services';
import { AssetProChainId, AssetProChainIdSchema } from '@/app/_common/services/asset-pro/model';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import { useUpsertEngagement } from '../_services/command/upsertEngagement';

const CurrencySchema = z.object({
  amount: z.coerce.number().gt(0),
  blockchain: AssetProChainIdSchema,
  token: z.string().min(1),
});
export type CurrencyFormValues = z.infer<typeof CurrencySchema>;

export interface CurrencyModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  sessionId: string;
}
const CurrencyModal = (props: CurrencyModalProps) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const form = useForm<CurrencyFormValues>({
    defaultValues: {},
    resolver: zodResolver(
      CurrencySchema.refine((data) => {
        const maxAmount = 100;
        const value = data.amount.toString();

        if (maxAmount == null) {
          return true;
        }
        if (!value) {
          return t('kgstudio.validation.required');
        }
        if (Number(value) <= 0) {
          return t('kgstudio.validation.number-greater-than-zero');
        }
        if (Number(value) > maxAmount) {
          return t('kgstudio.send.over-limit') + ` (${maxAmount.toLocaleString()}U)`;
        }
        return true;
      }),
    ),
    mode: 'onChange',
  });
  const { blockchain, token } = form.watch();
  const { data: tokenListData } = apiAssetProHooks.useGetTokenList(
    {},
    {
      onError: (error) => {
        console.error(error);
        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  const { blockchainOptions, tokenOptions } = useTokenSelection(
    tokenListData,
    blockchain,
    token,
    (c) => {
      form.setValue('blockchain', c as AssetProChainId);
    },
    (t) => {
      form.setValue('token', t);
    },
  );

  const { mutate: upsertEngagement, isLoading: upsertEngagementLoading } = useUpsertEngagement({
    onSuccess: async () => {
      await queryClient.refetchQueries(['engagements', props.sessionId]);
      props.onOpenChange && props.onOpenChange(false);
      form.reset();
    },
  });

  const onSubmit = async (values: CurrencyFormValues) => {
    upsertEngagement({
      session_id: props.sessionId,
      token: JSON.stringify(values),
    });
  };
  const buttonDisabled = upsertEngagementLoading || !form.formState.isValid;
  const formDisabled = upsertEngagementLoading;

  return (
    <Modal {...props}>
      <Modal.Content className="scroll-bar-hide scrollbar-hide max-h-[80vh] overflow-y-auto">
        <Modal.Header>
          <Modal.Title>Currency</Modal.Title>
        </Modal.Header>

        <div>
          <Form {...form}>
            <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
              <fieldset className="space-y-6" disabled={formDisabled}>
                <FormInput
                  title="Amount"
                  control={form.control}
                  name="amount"
                  placeholder="Enter amount"
                  onInput={amountInputInterceptor}
                  required
                  className="max-w-[302px]"
                />
                <FormDropdown
                  title={t('common.blockchain')}
                  data-cy="send-input-blockchain"
                  name="blockchain"
                  control={form.control}
                  options={blockchainOptions}
                  required
                  className="max-w-[302px]"
                />

                <FormDropdown
                  data-cy="send-input-token"
                  title={t('common.token')}
                  control={form.control}
                  name="token"
                  options={tokenOptions}
                  required
                  disabled={false}
                  className="max-w-[302px]"
                />
                <div className="flex w-full items-center justify-end">
                  {/* <Button variant="secondary">Save Draft</Button> */}
                  <Button className="w-[152px]" disabled={buttonDisabled}>
                    Next
                  </Button>
                </div>
              </fieldset>
            </form>
          </Form>
        </div>
      </Modal.Content>
    </Modal>
  );
};

export { CurrencyModal };
