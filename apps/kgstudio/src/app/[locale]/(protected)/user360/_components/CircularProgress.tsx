import { motion } from 'framer-motion';

import { cn } from '@/app/_common/lib/utils';

export interface CircularProgressProps extends React.ComponentPropsWithoutRef<'div'> {
  title?: string;
  percentage: number;
  size?: number;
}
const CircularProgress = ({ title, percentage, size = 126, className, ...props }: CircularProgressProps) => {
  const strokeWidth = 10;
  const innerRadius = size / 2 - strokeWidth * 2;
  const circumference = innerRadius * 2 * Math.PI;
  const progressOffset = circumference * (1 - percentage / 100);

  return (
    <div className={cn('relative flex items-center justify-center', className)}>
      <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <g transform={`rotate(-90 ${size / 2} ${size / 2})`}>
          <circle
            cx={size / 2}
            cy={size / 2}
            r={innerRadius}
            fill="transparent"
            stroke="currentColor"
            strokeWidth={strokeWidth}
            className="text-surface-secondary"
          />
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={innerRadius}
            fill="transparent"
            stroke="currentColor"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeDasharray={`${circumference} ${circumference}`}
            strokeDashoffset={progressOffset}
            className="text-brand-primary"
            transition={{ duration: 1.2, type: 'spring' }}
            initial={{ strokeDashoffset: circumference }} // Initial offset for the animation
            animate={{ strokeDashoffset: progressOffset }} // Final offset (end of animation)
          />
        </g>
      </svg>
      <div className="absolute">
        <p className="text-secondary text-xs leading-[20px] tracking-[-0.24px]">{title}</p>
        <p className="text-[20px] leading-[32px] tracking-[-0.4px]">{`${percentage}%`}</p>
      </div>
    </div>
  );
};
export { CircularProgress };
