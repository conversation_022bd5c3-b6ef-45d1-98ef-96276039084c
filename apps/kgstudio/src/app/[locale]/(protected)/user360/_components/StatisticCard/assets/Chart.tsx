import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={65}
    height={55}
    className="text-brand-primary fill-brand-primary-lighter"
    {...props}
  >
    <g transform="rotate(-90 59.284 54.167)">
      <rect width={53.333} height={5.383} x={59.284} y={54.167} rx={2.691} />
      <rect width={12.718} height={5.383} x={59.284} y={54.167} fill="currentColor" rx={2.691} />
    </g>
    <g transform="rotate(-90 44.629 54.167)">
      <rect width={53.333} height={5.383} x={44.629} y={54.167} rx={2.691} />
      <rect width={48.41} height={5.383} x={44.629} y={54.167} fill="currentColor" rx={2.691} />
    </g>
    <g transform="rotate(-90 29.976 54.167)">
      <rect width={53.333} height={5.383} x={29.976} y={54.167} rx={2.691} />
      <rect width={36.923} height={5.383} x={29.976} y={54.167} fill="currentColor" rx={2.691} />
    </g>
    <g transform="rotate(-90 15.32 54.167)">
      <rect width={53.333} height={5.383} x={15.321} y={54.167} rx={2.691} />
      <rect width={27.487} height={5.383} x={15.321} y={54.167} fill="currentColor" rx={2.691} />
    </g>
    <g transform="rotate(-90 .667 54.167)">
      <rect width={53.333} height={5.383} x={0.667} y={54.167} rx={2.691} />
      <rect width={43.897} height={5.383} x={0.667} y={54.167} fill="currentColor" rx={2.691} />
    </g>
  </svg>
);
export default SvgComponent;
