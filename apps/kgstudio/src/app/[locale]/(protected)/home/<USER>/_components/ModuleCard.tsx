import Image, { ImageProps } from 'next/image';

import { Card } from '@kryptogo/2b';

type ModuleCardProps = {
  moduleInfo: {
    name: string;
    description: string;
  };
  image: ImageProps['src'];
};
const ModuleCard = ({ moduleInfo, image }: ModuleCardProps) => {
  return (
    <Card className="border-primary bg-primary inline-flex max-w-[302px] cursor-pointer flex-col gap-3 rounded-xl border p-3">
      <div className="relative h-[150px] w-full">
        <Image
          src={image}
          alt="module"
          fill
          style={{
            objectFit: 'contain',
          }}
        />
      </div>
      <div className="flex flex-col gap-1">
        <h3 className="text-h3 text-primary">{moduleInfo.name}</h3>
        <p className="text-secondary text-small">{moduleInfo.description}</p>
      </div>
    </Card>
  );
};

export { ModuleCard };
