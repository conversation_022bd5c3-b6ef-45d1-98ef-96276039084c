import Image, { ImageProps } from 'next/image';

import { Card } from '@kryptogo/2b';

type ModuleCardProps = {
  moduleInfo: {
    name: string;
    description: string;
  };
  image: ImageProps['src'];
  onCardClick: () => void;
};
const ModuleCard = ({ moduleInfo, image, onCardClick }: ModuleCardProps) => {
  return (
    <Card
      className="border-primary bg-primary flex cursor-pointer flex-col !gap-4 rounded-2xl border !p-4"
      onClick={onCardClick}
      data-cy={`module-card-${moduleInfo.name}`}
    >
      <div className="relative aspect-video w-full lg:h-[150px]">
        <Image className="rounded-lg object-cover object-top" src={image} alt="module" fill />
      </div>
      <div className="flex flex-col gap-1">
        <h3 className="text-h3 text-primary">{moduleInfo.name}</h3>
        <p className="text-secondary text-small">{moduleInfo.description}</p>
      </div>
    </Card>
  );
};

export { ModuleCard };
