'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { FormDropdown } from '@/app/_common/components/form/FormDropdown';
import { FormInput } from '@/app/_common/components/form/FormInput';
import { FormMediaUploader } from '@/app/_common/components/form/FormMediaUploader';
import { Media, useGcpFileUpload } from '@/app/_common/hooks';
import { ApiResponse, isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { apiPaymentItemHooks, apiWalletHooks } from '@/app/_common/services';
import { useAuthStore } from '@/app/_common/store/useAuthStore';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import linkDemo from '@/assets/onboarding/link-demo.png';
import productDemo from '@/assets/onboarding/product-demo.png';
import { env } from '@/env.mjs';
import { zodResolver } from '@hookform/resolvers/zod';
import { Modal, Button, Card, Form } from '@kryptogo/2b';
import { ProgressBar } from '@kryptogo/2b';

const api = {
  get: <T,>(url: string) => internalAxiosInstance.get<ApiResponse<T>>(url).then((response) => response.data),
  post: <T,>(url: string, data: any) =>
    internalAxiosInstance.post<ApiResponse<T>>(url, data).then((response) => response.data),
  delete: <T,>(url: string) => internalAxiosInstance.delete<ApiResponse<T>>(url).then((response) => response.data),
  put: <T,>(url: string, data: any) =>
    internalAxiosInstance.put<ApiResponse<T>>(url, data).then((response) => response.data),
};

interface OAuthClientResponse {
  client_id: string;
  client_name: string;
  client_domain: string;
  client_type: string;
  main_logo: string;
  square_logo?: string;
  created_at?: number;
  app_store_link?: string;
  google_play_link?: string;
}

interface ImageFile {
  dataURL: string;
  file?: {
    name: string;
    size: number;
    type: string;
  };
}

const onboardingSchema = z.object({
  name: z.string().min(1, { message: 'Product name is required' }),
  description: z.string().optional(),
  price: z.string().refine(
    (val) => {
      const num = Number(val);
      return !isNaN(num) && num >= 0.01 && num <= 10000000;
    },
    { message: 'Price must be between 0.01 and 10,000,000' },
  ),
  currency: z.string(),
  walletAddress: z
    .string()
    .optional()
    .refine((val) => !val || /^0x[a-fA-F0-9]{40}$/.test(val), { message: 'Invalid EVM address' }),
  image: z.array(
    z.object({
      dataURL: z.string(),
      file: z
        .object({
          name: z.string(),
          size: z.number(),
          type: z.string(),
        })
        .optional(),
    }),
  ),
});

type OnboardingFormData = z.infer<typeof onboardingSchema>;

interface OnboardingProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const STORE_TYPES = [
  {
    id: 'products',
    emoji: '🛍️',
    label: 'kgstudio.onboarding.category-products',
    advice: 'kgstudio.onboarding.category-products-advice',
    defaultName: 'kgstudio.onboarding.category-products-name',
    defaultDescription: 'kgstudio.onboarding.category-products-description',
    defaultImage:
      'https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/studio/payment-items/arron001-prod/19d8eb09-6f45-402a-a410-14deb40f5f59-food.png',
  },
  {
    id: 'arts',
    emoji: '🌈',
    label: 'kgstudio.onboarding.category-arts',
    advice: 'kgstudio.onboarding.category-arts-advice',
    defaultName: 'kgstudio.onboarding.category-arts-name',
    defaultDescription: 'kgstudio.onboarding.category-arts-description',
    defaultImage:
      'https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/studio/payment-items/arron001-prod/5881f536-a81a-48e8-9863-b407888a62ec-Art.png',
  },
  {
    id: 'educational',
    emoji: '🥙',
    label: 'kgstudio.onboarding.category-educational',
    advice: 'kgstudio.onboarding.category-educational-advice',
    defaultName: 'kgstudio.onboarding.category-educational-name',
    defaultDescription: 'kgstudio.onboarding.category-educational-description',
    defaultImage:
      'https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/studio/payment-items/arron001-prod/0ea07553-d881-4608-b44a-c1b26456bff7-Educational.png',
  },
  {
    id: 'other',
    emoji: '💡',
    label: 'kgstudio.onboarding.category-other',
    advice: '',
    defaultName: '',
    defaultDescription: '',
    defaultImage: '',
  },
];

const REVENUE_OPTIONS = [
  { id: 'under3000', label: 'kgstudio.onboarding.revenue-under-3000' },
  { id: 'over3000', label: 'kgstudio.onboarding.revenue-over-3000' },
];

const CURRENCY_OPTIONS = [
  { value: 'USD', label: 'USD' },
  { value: 'TWD', label: 'TWD' },
];

export function Onboarding({ open, onOpenChange }: OnboardingProps) {
  const t = useTranslations();
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedRevenue, setSelectedRevenue] = useState<string>('');
  const [defaultAddressId, setDefaultAddressId] = useState<number>(0);
  const [paymentUrl, setPaymentUrl] = useState<string>('');
  const [oauthClients, setOAuthClients] = useState<OAuthClientResponse[]>([]);
  const { uploadAllFiles, reset: fileUrlsReset } = useGcpFileUpload();

  const orgId = useOrganizationStore((state) => state.orgId);
  const userInfo = useAuthStore((state) => state.userInfo);

  const form = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      name: '',
      description: '',
      price: '',
      currency: 'USD',
      image: [] as ImageFile[],
    },
  });

  const { mutate: createPaymentItem, isLoading: isCreating } = apiPaymentItemHooks.useCreatePaymentItem(
    {
      params: {
        org_id: orgId as number,
      },
    },
    {
      onSuccess: (response) => {
        if (response?.data?.id) {
          const isDev =
            process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT === 'local' ||
            process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT === 'development';
          const url = `https://pay.kryptogo.com/invoice/${response.data.id}${isDev ? '?isDev=true' : ''}`;
          setPaymentUrl(url);
          setCurrentStep(6);
        }
      },
      onError: (error) => {
        console.error('Error creating payment item:', error);
        toast.error(t('kgstudio.onboarding.create-error'));
      },
    },
  );

  const { data: importedAddressesResp, refetch: refetchImportedAddresses } = apiWalletHooks.useGetImportedAddresses(
    { params: { org_id: orgId ?? -1 } },
    { enabled: !!orgId },
  );

  const { mutate: importAddress } = apiWalletHooks.useImportAddress(
    { params: { org_id: orgId ?? -1 } },
    {
      onSuccess: async () => {
        toast.success(t('kgstudio.setting.user.success.address-added'));
        const resp = await refetchImportedAddresses();

        const addressId = resp.data?.data[0]?.id;
        if (addressId) {
          setDefaultAddressId(addressId);
          try {
            await api.put(`/studio/organization/${orgId}/wallet/addresses/${addressId}/default`, { default: true });
            setCurrentStep(7);
          } catch (error) {
            toast.error(t('kgstudio.setting.user.error.set-default-address'));
          }
        } else {
          setCurrentStep(7);
        }
      },
      onError: () => {
        toast.error(t('kgstudio.setting.user.error.add-address'));
      },
    },
  );

  const handleStoreTypeSelect = (type: string) => {
    const storeType = STORE_TYPES.find((t) => t.id === type);
    if (storeType && type !== 'other') {
      form.setValue('name', t(storeType.defaultName));
      form.setValue('description', t(storeType.defaultDescription));
      form.setValue('image', [{ dataURL: storeType.defaultImage }]);
    }
    setCurrentStep(3);
  };

  const handleRevenueSelect = (revenue: string) => {
    setSelectedRevenue(revenue);
    setCurrentStep(4);
  };

  const handleSkipOrNext = () => {
    if (form.getValues('walletAddress')) {
      importAddress({
        chain: 'eth',
        address: form.getValues('walletAddress') as string,
      });
    }
  };

  const handleNext = () => {
    if (currentStep === 4) {
      const { name, description } = form.getValues();
      if (!name.trim() || !description?.trim()) {
        toast.error(t('kgstudio.onboarding.fill-required-fields'));
        return;
      }
      setCurrentStep(5);
    }
  };

  const handleCreateProduct = async () => {
    const formData = form.getValues();
    const { price, currency } = formData;

    if (!price.trim()) {
      toast.error(t('kgstudio.onboarding.fill-required-fields'));
      return;
    }

    let imageUrl = '';

    if (formData.image && formData.image[0].file && formData.image[0].dataURL.includes('data:')) {
      const localFiles = formData.image.filter((img: any) => !!img.file);

      if (localFiles.length > 0) {
        const filesWithKey = {
          image: localFiles as Media[],
        };

        const fileUrls = await uploadAllFiles(filesWithKey, 'payment-items-onboarding');
        if (fileUrls) {
          imageUrl = fileUrls.image[0];
        }
      }
    }

    const payload = {
      name: formData.name,
      description: formData.description,
      price: price,
      currency: currency,
      image: imageUrl || formData.image[0].dataURL,
      client_id: oauthClients[0].client_id,
      successUrl: '',
      errorUrl: '',
      callbackUrl: '',
      successMessage: '',
      order_data_fields: [
        {
          field_name: 'email',
          field_label: 'Email',
          required: true,
          field_type: 'email',
        },
      ],
      config: {
        merchantEmail: userInfo?.email || '',
        accentColor: '#000000',
      },
      pay_token: '',
      chain_id: '',
    };

    createPaymentItem(payload);
  };

  const handleOpenStore = () => {
    if (paymentUrl) {
      window.open(paymentUrl, '_blank');
    }
  };

  const handleCopyLink = useCallback(() => {
    if (paymentUrl) {
      navigator.clipboard.writeText(paymentUrl);
      setCurrentStep(8);
    }
  }, [paymentUrl]);

  const handleClose = () => {
    onOpenChange(false);
    setCurrentStep(1);
    setSelectedRevenue('');
    setPaymentUrl('');
    fileUrlsReset();
    form.reset();
  };

  const getProgressValue = () => {
    if (currentStep <= 1) return 0;
    if (currentStep >= 7) return 6;
    return currentStep - 1;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="flex flex-col items-center gap-6 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step1.title')}</h2>
            <div className="relative flex h-[400px] w-full max-w-md items-center justify-center rounded-lg bg-gray-100">
              <Image src={productDemo} alt="Product Demo" fill className="object-contain" />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="flex flex-col gap-6 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step2.title')}</h2>
            <div className="grid grid-cols-2 gap-4">
              {STORE_TYPES.map((type) => (
                <Button
                  key={type.id}
                  variant="outline"
                  className="flex h-auto flex-col gap-1 py-6"
                  onClick={() => handleStoreTypeSelect(type.id)}
                >
                  <span className="text-2xl">{type.emoji}</span>
                  <span>{t(type.label)}</span>
                  {type.advice && <p className="text-body-2 text-secondary">({t(type.advice)})</p>}
                </Button>
              ))}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="flex flex-col gap-6 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step3.title')}</h2>
            <div className="flex flex-col gap-4">
              {REVENUE_OPTIONS.map((option) => (
                <Button
                  key={option.id}
                  variant="outline"
                  className="w-full py-4"
                  onClick={() => handleRevenueSelect(option.id)}
                >
                  {t(option.label)}
                </Button>
              ))}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="flex flex-col gap-2 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step4.title')}</h2>
            <div className="flex w-full gap-4">
              <FormInput
                key="name-input"
                id="name"
                name="name"
                title={t('kgstudio.onboarding.product-name')}
                control={form.control}
                required
                className="w-full"
                innerClassName="w-full"
                wrapperClassName="w-full"
              />
              <FormMediaUploader
                key="image-uploader"
                title={t('kgstudio.onboarding.image')}
                name="image"
                control={form.control}
                dropZoneWidth={120}
                dropZoneRatio="1/1"
                acceptType={['png', 'jpg', 'jpeg', 'webp']}
                maxFileSize={10 * 1024 * 1024}
                displayError
                previewInZone
                required
              />
            </div>
            <FormInput
              key="description-input"
              name="description"
              title={t('kgstudio.onboarding.product-description')}
              control={form.control}
            />
          </div>
        );

      case 5:
        return (
          <div className="flex flex-col gap-6 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step4.title')}</h2>
            <div className="grid grid-cols-2 gap-4">
              <FormInput
                key="price-input"
                id="price"
                name="price"
                title={t('kgstudio.onboarding.product-price')}
                control={form.control}
                required
                type="number"
                step="0.01"
                min="0.01"
              />
              <FormDropdown
                key="currency-dropdown"
                name="currency"
                title={t('kgstudio.onboarding.currency')}
                control={form.control}
                options={CURRENCY_OPTIONS}
                required
              />
            </div>
            <div className="flex flex-col gap-2">
              <label className="text-body-2-bold text-primary">{t('kgstudio.onboarding.supported-chains')}</label>
              <p className="text-body text-secondary">Arbitrum, Optimism, Base / USDC, USDT</p>
            </div>
          </div>
        );

      case 6:
        return (
          <div className="flex flex-col gap-6 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step6.title')}</h2>
            <FormInput
              key="wallet-address-input"
              name="walletAddress"
              title={t('kgstudio.onboarding.receive-address')}
              placeholder="0x..."
              control={form.control}
            />
          </div>
        );

      case 7: {
        const formData = form.getValues();
        return (
          <div className="flex flex-col gap-6 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step7.title')}</h2>
            <Card className="p-4">
              <div className="flex items-start gap-4">
                {formData.image && formData.image.length > 0 && (
                  <div className="relative h-20 w-20 flex-shrink-0">
                    <Image src={formData.image[0].dataURL} alt="Product" fill className="rounded object-cover" />
                  </div>
                )}
                <div className="flex-1">
                  <h3 className="text-h3 text-primary">{formData.name}</h3>
                  <p className="text-body text-primary">
                    {formData.price} {formData.currency}
                  </p>
                </div>
              </div>
            </Card>
            <div className="flex gap-4">
              <Button variant="primary" className="flex-1" onClick={handleOpenStore}>
                {t('kgstudio.onboarding.check-store')}
              </Button>
              <Button variant="secondary" className="flex-1" onClick={handleCopyLink}>
                {t('kgstudio.onboarding.copy-link')}
              </Button>
            </div>
          </div>
        );
      }

      case 8:
        return (
          <div className="flex flex-col items-center gap-6 py-8">
            <h2 className="text-h2 text-primary text-center">{t('kgstudio.onboarding.step8.title')}</h2>
            <p className="text-body text-secondary text-center">{t('kgstudio.onboarding.step8.description')}</p>
            <div className="relative flex h-[500px] w-full max-w-md items-center justify-center rounded-lg bg-gray-100">
              <Image src={linkDemo} alt="Link Demo" fill className="object-contain" />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const renderFooter = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="flex justify-end">
            <Button onClick={() => setCurrentStep(2)}>{t('kgstudio.onboarding.go')}</Button>
          </div>
        );

      case 3:
        return (
          <div className="flex justify-start">
            <Button variant="outline" onClick={() => setCurrentStep(2)}>
              {t('kgstudio.onboarding.prev')}
            </Button>
          </div>
        );

      case 4:
        return (
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setCurrentStep(3)}>
              {t('kgstudio.onboarding.prev')}
            </Button>
            <Button onClick={handleNext} disabled={!form.getValues('name') || !form.getValues('image')?.[0]?.dataURL}>
              {t('kgstudio.onboarding.next')}
            </Button>
          </div>
        );

      case 5:
        return (
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setCurrentStep(4)}>
              {t('kgstudio.onboarding.prev')}
            </Button>
            <Button onClick={handleCreateProduct} disabled={isCreating || !form.formState.isValid}>
              {isCreating ? t('common.loading') : t('kgstudio.onboarding.next')}
            </Button>
          </div>
        );

      case 6:
        return (
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setCurrentStep(7)}>
              {t('kgstudio.onboarding.skip')}
            </Button>
            <Button
              onClick={handleSkipOrNext}
              disabled={
                !form.getValues('walletAddress') ||
                !/^0x[a-fA-F0-9]{40}$/.test(form.getValues('walletAddress') as string)
              }
            >
              {t('kgstudio.onboarding.next')}
            </Button>
          </div>
        );

      case 7:
        return (
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleClose}>
              {t('kgstudio.onboarding.close')}
            </Button>
          </div>
        );

      case 8:
        return (
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleClose}>
              {t('kgstudio.onboarding.close')}
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  const fetchOAuthClients = async () => {
    if (!orgId) return;

    try {
      const response = await api.get<OAuthClientResponse[]>(
        `/studio/organization/${orgId}/oauth_clients?page_size=100`,
      );
      setOAuthClients(response.data);
    } catch (error) {
      if (isApiError(error)) {
        toast.error(t('kgstudio.setting.user.error.fetch-oauth-clients'));
      }
    }
  };

  useEffect(() => {
    if (open) fetchOAuthClients();
  }, [open]);

  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <Modal.Content noClose>
        {currentStep >= 2 && currentStep <= 7 && (
          <div className="w-full text-center">
            <ProgressBar className="w-full" progress={getProgressValue()} total={6} />
          </div>
        )}
        <Form {...form}>
          <form className="flex flex-col gap-6">
            <div className="px-6">{renderStepContent()}</div>
          </form>
        </Form>
        <Modal.Footer>{renderFooter()}</Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
