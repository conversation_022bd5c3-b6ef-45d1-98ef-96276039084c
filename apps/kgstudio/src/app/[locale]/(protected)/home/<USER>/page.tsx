'use client';

import { format, fromUnixTime } from 'date-fns';
import {
  Building2,
  FileClock,
  Pencil,
  ScrollText,
  UserRoundSearch,
  DollarSign,
  Wallet,
  KeyRound,
  Plus,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import Joyride, { STATUS } from 'react-joyride';
import { CallBackProps, Step } from 'react-joyride';
import { toast } from 'sonner';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormFilterGroup, FilterItem } from '@/app/_common/components/form';
import { HOME_PAGE_URL } from '@/app/_common/constant';
import { useDeviceSize, usePageHeader, usePermissions, useUpdateEffect } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { cn } from '@/app/_common/lib/utils';
import { apiAssetProHooks, apiAssetProOrderHooks, apiUser360Hooks, apiPaymentHooks } from '@/app/_common/services';
import { Module } from '@/app/_common/services/organization/model';
import { useAuthStore } from '@/app/_common/store/useAuthStore';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { Badge, Button, Card, Form, Skeleton } from '@kryptogo/2b';
import { DECIMAL_DISPLAY_MODE, formatCurrency } from '@kryptogo/utils';

import { RoleIdentifierBadge } from '../../setting/team/_components';
import { flattenFilteredRoles } from '../../setting/team/_utils';
import { StatisticCard } from '../../user360/_components';
import assetPro from '../_assets/asset-pro.png';
import compliance from '../_assets/compliance.png';
import nftBoost from '../_assets/nft-boost.png';
import user360 from '../_assets/user360.png';
import walletBuilder from '../_assets/wallet-builder.png';
import { EditOrgInfoModal, ModuleCard } from '../_components';
import { Onboarding } from './_components/Onboarding';

const MODULES = [
  {
    name: 'User 360',
    description: 'kgstudio.overview.user360-intro',
    image: user360,
    module: 'user_360',
    url: '/user360/data/compliance',
  },
  {
    name: 'AssetPro',
    description: 'kgstudio.overview.assetpro-intro',
    image: assetPro,
    module: 'asset_pro',
    url: '/asset/transfer',
  },
  {
    name: 'Compliance',
    description: 'kgstudio.overview.compliance-intro',
    image: compliance,
    module: 'compliance',
    url: '/compliance/create-a-task',
  },
  {
    name: 'NFT Boost',
    description: 'kgstudio.overview.nft-intro',
    image: nftBoost,
    module: 'nft_boost',
    url: '/nft/campaign/overview',
  },
  {
    name: 'Wallet Builder',
    description: 'kgstudio.overview.wallet-intro',
    image: walletBuilder,
    module: 'wallet_builder',
    url: '/wallet/projects/overview',
  },
];
const ARB_USDT_ADDRESS = '******************************************';

interface ApiResponse<T> {
  code: number;
  data: T;
  status?: number;
  timestamp?: number;
}

const api = {
  get: <T,>(url: string) => internalAxiosInstance.get<ApiResponse<T>>(url).then((response) => response.data),
  post: <T,>(url: string, data: any) =>
    internalAxiosInstance.post<ApiResponse<T>>(url, data).then((response) => response.data),
  delete: <T,>(url: string) => internalAxiosInstance.delete<ApiResponse<T>>(url).then((response) => response.data),
  put: <T,>(url: string, data: any) =>
    internalAxiosInstance.put<ApiResponse<T>>(url, data).then((response) => response.data),
};

const FilterSchema = z.object({
  days: z.enum(['all', '7', '14', '30']),
});
type FilterSchema = z.infer<typeof FilterSchema>;

export type ProfitData = {
  [chain: string]: {
    [address: string]: {
      amount: string;
      rate: string;
      total_usd: string;
    };
  };
};

export function calcTotalProfitAmount(data: ProfitData): number {
  let total = 0;
  Object.values(data).forEach((chainObj) => {
    Object.values(chainObj).forEach((tokenObj) => {
      total += Number(tokenObj.amount || 0);
    });
  });
  return total;
}

export default function Overview() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.common.overview') });
  const { deviceSize } = useDeviceSize();
  const router = useRouter();
  const searchParams = useSearchParams();
  const orgInfo = useOrganizationStore((state) => state.orgInfo);
  const userInfo = useAuthStore((state) => state.userInfo);
  const orgId = useOrganizationStore((state) => state.orgId);

  const [
    hasReadCompStatPermission,
    hasReadAssetProOrderPermission,
    hasReadTransactionPermission,
    hasApproveTransactionPermission,
    hasReleaseTransactionPermission,
    hasEditOrgInfoPermission,
  ] = usePermissions(
    ['user_360_statistics_compliance', 'read'],
    ['asset_pro_order', 'read'],
    ['transaction', 'read'],
    ['transaction', 'approve'],
    ['transaction', 'release'],
    ['organization_info', 'edit'],
  );

  const form = useForm<FilterSchema>({
    values: {
      days: 'all',
    },
    resolver: zodResolver(FilterSchema),
  });

  const filterItems: FilterItem<FilterSchema>[] = useMemo(
    () => [
      {
        name: 'days',
        subject: t('kgstudio.data.asset-pro.date.title'),
        type: 'select',
        hideOptionAll: true,
        options: [
          { label: t('kgstudio.data.asset-pro.date.all'), value: 'all' },
          { label: t('kgstudio.data.asset-pro.date.last-7-days'), value: '7' },
          { label: t('kgstudio.data.asset-pro.date.last-14-days'), value: '14' },
          { label: t('kgstudio.data.asset-pro.date.last-30-days'), value: '30' },
        ],
      },
    ],
    [t],
  );

  const [openEditOrgInfoModal, setOpenEditOrgInfoModal] = useState(false);
  const [openOnboardingModal, setOpenOnboardingModal] = useState(false);

  const orgModules = orgInfo?.modules ? MODULES.filter((module) => orgInfo.modules[module.module as keyof Module]) : [];

  const accessibleModules = userInfo?.modules
    ? Object.keys(userInfo.modules).filter((module) => orgModules.find((m) => m.module === module))
    : [];

  const {
    data: complianceStat,
    isLoading: isComplianceStatLoading,
    error: complianceStatError,
  } = apiUser360Hooks.useGetComplianceStat(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId && !!hasReadCompStatPermission,
      staleTime: 60_000,
    },
  );

  const {
    data: pendingOrderCount,
    isLoading: isPendingOrderCountLoading,
    error: pendingOrderCountError,
  } = apiAssetProOrderHooks.useGetPendingOrderCount(
    {
      params: { org_id: Number(orgId) },
    },
    { enabled: !!orgId && !!hasReadAssetProOrderPermission },
  );

  const {
    data: pendingTransactionCount,
    isLoading: isPendingTransactionCountLoading,
    error: pendingTransactionCountError,
  } = apiAssetProHooks.useGetPendingTxHistoryCount(
    {
      params: { org_id: Number(orgId) },
    },
    { enabled: !!orgId && !!hasReadTransactionPermission },
  );

  const modulePermissionCheck = (module: string, url: string) => {
    if (accessibleModules.includes(module)) {
      router.push(url);
    } else {
      toast.error(t('kgstudio.overview.no-access'));
    }
  };

  const { data: paymentDashboard } = apiPaymentHooks.useGetPaymentDashboard(
    {
      params: {
        org_id: Number(orgId),
      },
      queries: {
        days: form.watch('days') === 'all' ? undefined : Number(form.watch('days')),
      },
    },
    { enabled: !!orgId },
  );

  useEffect(() => {
    if (searchParams?.get('firstVisit')) {
      toast.success('Welcome to the "KryptoGO" team!');
    }
  }, [searchParams]);

  useUpdateEffect(() => {
    if (!complianceStatError) return;

    console.error(complianceStatError);
    if (isApiError(complianceStatError)) {
      showToast(t('kgstudio.common.error'), 'error');
    }
  }, [complianceStatError, t]);

  useUpdateEffect(() => {
    if (!pendingOrderCountError) return;

    console.error(pendingOrderCountError);
    if (isApiError(pendingOrderCountError)) {
      showToast(t('kgstudio.common.error'), 'error');
    }
  }, [pendingOrderCountError, t]);

  const [runTour, setRunTour] = useState(false);

  const joyrideSteps: Step[] = [
    {
      target: '.user',
      content: t('kgstudio.home.joyride.step1'),
      disableBeacon: true,
    },
    {
      target: '.payment_product',
      content: t('kgstudio.home.joyride.step2'),
      disableBeacon: true,
    },
    {
      target: '.treasury',
      content: t('kgstudio.home.joyride.step3'),
      disableBeacon: true,
    },
    {
      target: '.step4-target',
      content: t('kgstudio.home.joyride.step4'),
      disableBeacon: true,
    },
    // {
    //   target: '.payment_product > div:last-child',
    //   content: t('kgstudio.home.joyride.step5'),
    //   placementBeacon: 'top-end',
    // },
  ];

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status } = data;
    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      setRunTour(false);
      router.replace(HOME_PAGE_URL);
      // Open onboarding modal after joyride completes
      setOpenOnboardingModal(true);
    }
  };

  useEffect(() => {
    if (searchParams?.get('firstVisit')) {
      setRunTour(true);
    }
  }, [searchParams]);

  useEffect(() => {
    async function createOAuthClient() {
      const clients = await api.get(`/studio/organization/${orgId}/oauth_clients?page_size=100`);
      if (clients.data === null) {
        try {
          await internalAxiosInstance.post(`/studio/organization/${orgId}/oauth_clients`, {
            client_name: `payment_${orgInfo?.name}`,
            client_type: 'web_app',
          });
        } catch (error) {
          console.error('Failed to create OAuth client:', error);
        }
      }
    }

    const fromCreateOrg = localStorage.getItem('from-create-org');
    if (fromCreateOrg) createOAuthClient();
  }, []);

  return (
    <div className="flex flex-col gap-6">
      <Joyride
        steps={joyrideSteps}
        run={runTour}
        continuous={true}
        callback={handleJoyrideCallback}
        hideCloseButton
        locale={{
          back: t('kgstudio.setting.user.joyride.back'),
          close: t('kgstudio.setting.user.joyride.close'),
          last: t('kgstudio.setting.user.joyride.next'),
          next: t('kgstudio.setting.user.joyride.next'),
          skip: t('kgstudio.setting.user.joyride.skip'),
        }}
        styles={{
          options: {
            width: 410,
            zIndex: 101,
            primaryColor: 'var(--brand-primary)',
          },
        }}
      />
      <div className="flex-start flex items-center gap-6">
        <h3 className="text-h3 text-primary">{t('kgstudio.home.payment.data')}</h3>
        <Form {...form}>
          <FormFilterGroup control={form.control} items={filterItems} data-cy="finance-filter-group" />
        </Form>
      </div>
      <div className="grid grid-cols-3 gap-6">
        <StatisticCard
          className="flex-1"
          title={t('kgstudio.home.payment.total-revenue')}
          value={
            <span className="text-h2 text-primary">
              {formatCurrency({
                amount: calcTotalProfitAmount(paymentDashboard?.data?.total_revenue ?? {}),
                decimals: DECIMAL_DISPLAY_MODE.FIAT,
                fmt: { prefix: '$' },
              })}
            </span>
          }
          icon={<DollarSign />}
        />
        <StatisticCard
          className="flex-1"
          title={t('kgstudio.home.payment.total-order')}
          value={<span className="text-h2 text-primary">{paymentDashboard?.data?.valid_order_count ?? 0}</span>}
          icon={<FileClock />}
        />
        <StatisticCard
          className="flex-1"
          title={t('kgstudio.home.payment.unique-customer')}
          value={<span className="text-h2 text-primary">{paymentDashboard?.data?.unique_customer_count ?? 0}</span>}
          icon={<UserRoundSearch />}
        />
        {/* TODO: temporary hide statistic card */}
        {/* {hasReadCompStatPermission && (
          <StatisticCard
            className="flex-1"
            tooltip={t('kgstudio.home.kyc-pending.tooltip')}
            title={t('kgstudio.home.kyc-pending.title')}
            value={
              isComplianceStatLoading ? (
                <Skeleton className="h-[28px] w-10" />
              ) : (
                <Link
                  href={{
                    pathname: '/compliance/review',
                    query: { kyc_status: 'pending' },
                  }}
                >
                  <span className="text-h2 text-primary underline">
                    {complianceStat?.data?.kyc.pending_tasks.value ?? 0}
                  </span>
                </Link>
              )
            }
            icon={<UserRoundSearch />}
            data-cy="review-data-section-comp-stat"
          />
        )}
        {hasReadAssetProOrderPermission && (
          <StatisticCard
            className="flex-1"
            tooltip={t('kgstudio.home.orders-pending.tooltip')}
            title={t('kgstudio.home.orders-pending.title')}
            value={
              isPendingOrderCountLoading ? (
                <Skeleton className="h-[28px] w-10" />
              ) : (
                <Link
                  href={{
                    pathname: '/asset/orders',
                    query: { status: 'pending' },
                  }}
                >
                  <span className="text-h2 text-primary underline">{pendingOrderCount?.data?.count ?? 0}</span>
                </Link>
              )
            }
            icon={<ScrollText />}
            data-cy="review-data-section-pending-order-count"
          />
        )}
        {hasReleaseTransactionPermission && (
          <StatisticCard
            className="flex-1"
            title={t('kgstudio.home.awaiting-release-tx.title')}
            value={
              isPendingTransactionCountLoading ? (
                <Skeleton className="h-[28px] w-10" />
              ) : (
                <Link
                  href={{
                    pathname: '/asset/transactions',
                    // NOTE: using "status[]" to allow qs.parse always return an array
                    // https://github.com/ljharb/qs/issues/344
                    query: { 'status[]': 'awaiting_release' },
                  }}
                >
                  <span className="text-h2 text-primary underline">
                    {pendingTransactionCount?.data?.count_awaiting_release ?? 0}
                  </span>
                </Link>
              )
            }
            icon={<FileClock />}
            data-cy="review-data-section-awaiting-release-txs"
          />
        )}
        {hasApproveTransactionPermission && (
          <StatisticCard
            className="flex-1"
            title={t('kgstudio.home.awaiting-approval-tx.title')}
            value={
              isPendingTransactionCountLoading ? (
                <Skeleton className="h-[28px] w-10" />
              ) : (
                <Link
                  href={{
                    pathname: '/asset/transactions',
                    // NOTE: using "status[]" to allow qs.parse always return an array
                    // https://github.com/ljharb/qs/issues/344
                    query: { 'status[]': 'awaiting_approval' },
                  }}
                >
                  <span className="text-h2 text-primary underline">
                    {pendingTransactionCount?.data?.count_awaiting_approval ?? 0}
                  </span>
                </Link>
              )
            }
            icon={<FileClock />}
            data-cy="review-data-section-awaiting-approval-txs"
          />
        )}
        {hasReadTransactionPermission && (
          <StatisticCard
            className="flex-1"
            title={t('kgstudio.home.my-pending-tx.title')}
            value={
              isPendingTransactionCountLoading ? (
                <Skeleton className="h-[28px] w-10" />
              ) : (
                <Link
                  href={{
                    pathname: '/asset/transactions',
                    query: { status: 'awaiting_approval,sending,awaiting_release', submitter: userInfo?.uid },
                  }}
                >
                  <span className="text-h2 text-primary underline">
                    {pendingTransactionCount?.data?.count_awaiting_approval_self ?? 0}
                  </span>
                </Link>
              )
            }
            icon={<FileClock />}
            data-cy="review-data-section-my-pending-txs"
          />
        )} */}
      </div>

      <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-3">
        <Card className={cn('section-padding radius-large col-span-2 flex justify-start gap-6 bg-white')}>
          {deviceSize !== 'sm' && (
            <div className="bg-brand-primary-lighter relative flex h-[120px] min-w-[120px] items-center justify-center overflow-hidden rounded-xl">
              {orgInfo?.icon_url ? (
                <Image src={orgInfo.icon_url} alt={orgInfo?.name} fill />
              ) : (
                <Building2 className="stroke-brand-primary" size={48} />
              )}
            </div>
          )}
          <div className="flex flex-col justify-between gap-3 py-4">
            <h2 className="text-h2 text-primary">{orgInfo?.name}</h2>
            {hasEditOrgInfoPermission && (
              <Button
                variant="secondary"
                size="sm"
                icon={<Pencil className="!stroke-[var(--brand-primary-dark)]" />}
                onClick={() => setOpenEditOrgInfoModal(true)}
              >
                {t('kgstudio.common.edit')}
              </Button>
            )}
            <Badge>
              {t('kgstudio.common.created')}: {format(fromUnixTime(orgInfo?.created_at ?? 0), 'PPP')}
            </Badge>
          </div>
        </Card>
        <Card className={cn('radius-large step4-target col-span-1 flex justify-center gap-6 bg-white p-6')}>
          <div className="flex flex-col gap-3 [&>*]:min-w-[200px]">
            <Button variant="secondary" icon={<Plus />} onClick={() => router.push('/payment/payment-item-list')}>
              {t('kgstudio.home.payment.create-product')}
            </Button>
            <Button
              variant="secondary"
              icon={<KeyRound />}
              onClick={() => router.push('/setting/user#oauth-clients-section')}
            >
              {t('kgstudio.home.payment.api-key-settings')}
            </Button>
            <Button variant="secondary" icon={<Wallet />} onClick={() => router.push('/setting/user')}>
              {t('kgstudio.home.payment.manage-wallet')}
            </Button>
          </div>
        </Card>
      </div>
      <Card className={cn('section-padding radius-large flex flex-col justify-start gap-6 bg-white')}>
        <h2 className="text-h2 text-primary">{t('kgstudio.team.text')}</h2>
        <div className="flex items-start gap-3 md:items-center">
          <span className="text-body-2 text-secondary min-w-[80px]">{t('common.owners')}</span>
          <span className="text-body-2-bold text-primary" data-cy={'owner-list'}>
            {orgInfo?.owners.join(', ')}
          </span>
        </div>
        <div className="flex items-start gap-3 md:items-center">
          <span className="text-body-2 text-secondary min-w-[80px]">{t('kgstudio.common.my-role')}</span>
          <span className="flex flex-wrap gap-2" data-cy={'role-list'}>
            {userInfo?.roles &&
              flattenFilteredRoles(userInfo?.roles).map((role) => <RoleIdentifierBadge key={role} role={role} />)}
          </span>
        </div>
      </Card>
      {/* TODO: temporary hide module card */}
      {/* <Card className={cn('radius-large section-padding flex flex-col justify-start gap-10 bg-white p-6')}>
        <h2 className="text-h2 text-primary">{t('kgstudio.overview.applications')}</h2>
        <div className={cn('grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3')}>
          {orgModules.map(({ name, description, image, module, url }) => (
            <ModuleCard
              key={name}
              moduleInfo={{
                name,
                description: t(description),
              }}
              image={image}
              onCardClick={() => modulePermissionCheck(module, url)}
            />
          ))}
        </div>
      </Card> */}
      <EditOrgInfoModal
        open={openEditOrgInfoModal}
        onOpenChange={setOpenEditOrgInfoModal}
        name={orgInfo?.name || ''}
        icon_url={orgInfo?.icon_url ?? null}
      />
      <Onboarding open={openOnboardingModal} onOpenChange={setOpenOnboardingModal} />
    </div>
  );
}
