import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { FormInput, FormMediaUploader, ImageSchema } from '@/app/_common/components/form';
import { Media, useGcpFileUpload } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiOrganizationHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal, ConfirmationModal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

type EditOrgInfoModalProps = {
  name: string;
  icon_url: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

function getCustomLength(str: string) {
  let length = 0;

  for (const char of str) {
    char.match(/[\u4e00-\u9fff]/) ? (length += 2) : (length += 1);
  }
  return length;
}

const EditOrgInfoSchema = (t: any) =>
  z.object({
    name: z
      .string()
      .nonempty(t('kgstudio.home.edit-organization.name-error1'))
      .refine(
        (value) => {
          const length = getCustomLength(value);
          return length >= 2 && length <= 20;
        },
        {
          message: t('kgstudio.home.edit-organization.name-error1'),
        },
      )
      .refine(
        (value) => {
          const allowedCharacters = /^[\p{L}\p{N}\s_-]+$/u;
          return allowedCharacters.test(value);
        },
        {
          message: t('kgstudio.home.edit-organization.name-error2'),
        },
      ),
    icon_url: z.array(ImageSchema).nullable(),
  });
type EditOrgInfo = z.infer<ReturnType<typeof EditOrgInfoSchema>>;

const EditOrgInfoModal = ({ name, icon_url, ...props }: EditOrgInfoModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  const orgId = useOrganizationStore((state) => state.orgId);

  const [cancelEditModalOpen, setCancelEditModalOpen] = useState(false);

  const form = useForm<EditOrgInfo>({
    values: {
      name,
      icon_url: icon_url ? [{ dataURL: icon_url }] : null,
    },
    resolver: zodResolver(EditOrgInfoSchema(t)),
    mode: 'onChange',
  });

  const { mutate: editOrgInfo, isLoading: editOrgInfoLoading } = apiOrganizationHooks.useEditOrganizationInfo(
    {
      params: {
        org_id: orgId ?? -1,
      },
    },
    {
      onSuccess: () => {
        props.onOpenChange(false);
        toast.success(t('kgstudio.home.edit-organization.success'));
      },
      onError: (error) => {
        if (isApiError(error) && error.code === 7029) {
          toast.error(t('kgstudio.home.duplicate-org-name'));
        } else {
          toast.error(t('kgstudio.common.error'));
        }
      },
      meta: {
        awaitInvalidates: ['getOrganizationInfo', 'getOrganizations'],
      },
    },
  );

  const { isError: fileUrlsError, isLoading: fileUrlsLoading, uploadAllFiles } = useGcpFileUpload();

  const onSubmit = async (formData: EditOrgInfo) => {
    const uploadedFiles = formData.icon_url?.[0]?.file
      ? await uploadAllFiles({ image: formData.icon_url as Media[] }, 'asset_pro')
      : null;
    const iconUrl = uploadedFiles?.image[0] ?? formData.icon_url?.[0]?.dataURL ?? null;

    editOrgInfo({
      name: formData.name,
      icon_url: iconUrl,
    });
  };

  useEffect(() => {
    if (fileUrlsError) {
      toast.error(t('kgstudio.error.upload-image-failed'));
    }
  }, [fileUrlsError, t]);

  return (
    <>
      <Modal {...props}>
        <Modal.Content className="max-h-[90%] max-w-[600px]" scrollable noClose>
          <Modal.Header>
            <Modal.Title>{t('kgstudio.home.edit-organization-title')}</Modal.Title>
          </Modal.Header>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-6">
              <FormInput
                name="name"
                title={t('kgstudio.home.edit-organization.name-title')}
                placeholder={t('kgstudio.home.edit-organization.name-placeholder')}
                control={form.control}
                required
              />
              <FormMediaUploader
                name="icon_url"
                title={t('kgstudio.home.edit-organization.icon-title')}
                previewInZone
                displayError
                dropZoneRatio="1/1"
                dropZoneWidth={120}
                control={form.control}
                maxFileSize={10 * 1024 * 1024}
                acceptType={['png', 'jpg', 'jpeg', 'webp', 'svg']}
                dropZoneDesc={
                  <ul className="flex list-disc flex-col gap-1 pl-4">
                    <li className="text-caption text-secondary">{t('kgstudio.home.edit-organization.icon-hint1')}</li>
                    <li className="text-caption text-secondary">{t('kgstudio.home.edit-organization.icon-hint2')}</li>
                    <li className="text-caption text-secondary">{t('kgstudio.home.edit-organization.icon-hint3')}</li>
                  </ul>
                }
              />
              <Modal.Footer className="mt-14 flex justify-between">
                <Button type="button" variant="grey" onClick={() => setCancelEditModalOpen(true)}>
                  {t('common.cancel')}
                </Button>
                <Button
                  type="submit"
                  className="md:w-[152px]"
                  loading={editOrgInfoLoading || fileUrlsLoading}
                  disabled={!form.formState.isValid}
                >
                  {t('common.update')}
                </Button>
              </Modal.Footer>
            </form>
          </Form>
        </Modal.Content>
      </Modal>
      <ConfirmationModal
        displayIcon
        title={t('kgstudio.asset.products.cancel-modal-hint')}
        open={cancelEditModalOpen}
        onOpenChange={setCancelEditModalOpen}
        cancelState={{
          text: t('kgstudio.asset.products.cancel-modal-stay'),
        }}
        confirmState={{
          text: t('kgstudio.asset.products.cancel-modal-cancel'),
        }}
        onConfirm={() => {
          setCancelEditModalOpen(false);
          props.onOpenChange(false);
          form.reset();
        }}
      />
    </>
  );
};

export { EditOrgInfoModal };
