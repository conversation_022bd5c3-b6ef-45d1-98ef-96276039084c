import { z } from 'zod';

// identifiers per role
export const AssetProSchema = z.union([
  z.literal('asset_pro:admin'),
  z.literal('asset_pro:approver'),
  z.literal('asset_pro:trader'),
  z.literal('asset_pro:finance_manager'),
]);
export const User360Schema = z.literal('user_360:admin');
export const NFTBoostSchema = z.literal('nft_boost:admin');
export const WalletBuilderSchema = z.literal('wallet_builder:admin');
export const ComplianceSchema = z.union([z.literal('compliance:admin'), z.literal('compliance:reviewer')]);
export const AdminSchema = z.literal('owner');

// all role identifiers
export const RoleIdentifierSchema = z.union([
  AssetProSchema,
  User360Schema,
  NFTBoostSchema,
  WalletBuilderSchema,
  ComplianceSchema,
  AdminSchema,
]);
export type RoleIdentifier = z.infer<typeof RoleIdentifierSchema>;

// object of role identifiers
export const ModuleRoleSchema = z.object({
  user_360: z.array(User360Schema).optional(),
  wallet_builder: z.array(WalletBuilderSchema).optional(),
  asset_pro: z.array(AssetProSchema).optional(),
  nft_boost: z.array(NFTBoostSchema).optional(),
  compliance: z.array(ComplianceSchema).optional(),
  admin: z.array(AdminSchema).optional(),
});
export type ModuleRole = z.infer<typeof ModuleRoleSchema>;

// InvitationStatus
export const InvitationStatusSchema = z.union([
  z.literal('active'),
  z.literal('inactive'),
  z.literal('pending'),
  z.literal('expired'),
]);
export type InvitationStatus = z.infer<typeof InvitationStatusSchema>;

export const MemberSchema = z.object({
  uid: z.string(),
  name: z.string(),
  member_id: z.string(),
  // FIXME: BE v1 return empty string sometimes
  email: z.string(),
  roles: ModuleRoleSchema,
  status: InvitationStatusSchema,
});
export type Member = z.infer<typeof MemberSchema>;

// assetPro limit
export const AssetProLimitSchema = z.object({
  remain_limit: z.number(),
  daily_transfer_limit: z.number().nullable(),
  transfer_approval_threshold: z.number(),
});
export type AssetProLimit = z.infer<typeof AssetProLimitSchema>;
