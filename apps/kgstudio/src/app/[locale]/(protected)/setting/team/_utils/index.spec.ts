import { describe, it, expect } from 'vitest';

import type { ModuleRole } from '../_types';
import { flattenFilteredRoles } from './index';

describe('flattenFilteredRoles', () => {
  it('should flatten and prioritize owner role', () => {
    const roleData: ModuleRole = {
      user_360: ['user_360:admin'],
      admin: ['owner'],
      asset_pro: ['asset_pro:admin', 'asset_pro:finance_manager'],
    };

    const result = flattenFilteredRoles(roleData);

    expect(result).toEqual(['owner', 'user_360:admin', 'asset_pro:admin', 'asset_pro:finance_manager']);
  });

  it('should handle empty role data', () => {
    const roleData = {};

    const result = flattenFilteredRoles(roleData);

    expect(result).toEqual([]);
  });
});
