import { useTranslations } from 'next-intl';

import { createError } from '@/app/_common/lib/error';
import { Badge } from '@kryptogo/2b';

import { RoleIdentifier, RoleIdentifierSchema } from '../_types';
import { getRoleInfo } from '../_utils';

const moduleRoleError = createError('ModuleRoleError', 'Invalid module role');

const RoleIdentifierBadge = ({ role }: { role: RoleIdentifier }) => {
  const t = useTranslations();
  const { color, label } = getRoleInfo(role, t);

  try {
    RoleIdentifierSchema.parse(role);

    return <Badge variant={color}>{label}</Badge>;
  } catch (err) {
    throw moduleRoleError(err);
  }
};

export { RoleIdentifierBadge };
