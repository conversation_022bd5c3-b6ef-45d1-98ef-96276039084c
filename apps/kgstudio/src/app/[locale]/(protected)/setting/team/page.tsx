'use client';

import { MoreVertical, UserPlus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { match } from 'ts-pattern';

import { showToast } from '@/2b/toast';
import { useDebounce, usePageHeader, usePermissions, useUpdateEffect } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { cn } from '@/app/_common/lib/utils';
import { apiOrganizationHooks } from '@/app/_common/services';
import { MemberList } from '@/app/_common/services/organization/model';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { Avatar, Button, Card, DataTable, DropdownMenu, Input, useDataTable } from '@kryptogo/2b';
import { QueryKey } from '@tanstack/react-query';
import { ColumnDef, TableOptions, getCoreRowModel, useReactTable } from '@tanstack/react-table';

import { InvitationStatusBadge, MemberModal, RoleIdentifierBadge } from './_components';
import { DeleteModal } from './_components/DeleteModal';
import { InvitationStatus, Member, ModuleRole } from './_types';
import { flattenFilteredRoles } from './_utils';

const ActionButton = () => (
  <span className="hover:bg-surface-secondary flex h-8 w-8 items-center justify-center rounded-lg p-2">
    <MoreVertical className="text-placeholder w-5 cursor-pointer" size={16} />
  </span>
);

export type MemberModalDataProps =
  | { status: 'invite'; open: boolean; currentMember?: never; queryKey: QueryKey }
  | { status: 'edit'; open: boolean; currentMember: Member; queryKey: QueryKey }
  | { status: 'remove'; open: boolean; currentMember: Member; queryKey: QueryKey };

export default function Team() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.team.members') });
  const orgId = useOrganizationStore((state) => state.orgId);

  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);

  const [reachReinviteRateLimit, setReachReinviteRateLimit] = useState(new Set());
  const [hasEditPermission] = usePermissions(['member', 'edit']);

  const [tableOption, setTableOption] = useState<TableOptions<MemberList['data'][number]>>({
    data: [],
    columns: [],
    state: {
      columnVisibility: {
        action: hasEditPermission,
      },
    },
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size } = useDataTable(table);

  const {
    data: members,
    isLoading: membersLoading,
    invalidate: invalidateMembers,
    key,
  } = apiOrganizationHooks.useGetOrganizationMembers(
    {
      params: { org_id: Number(orgId) },
      queries: { page_number, page_size, q: debouncedQuery.trim() === '' ? undefined : debouncedQuery },
    },
    {
      enabled: !!orgId,
      keepPreviousData: true,
      onError: (error) => {
        console.error(error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );
  const queryKey = [key[0]];
  const [memberModalData, setMemberModalData] = useState<MemberModalDataProps>({
    status: 'invite',
    open: false,
    queryKey,
  });

  const { mutate: reinvite } = apiOrganizationHooks.useReinviteOrganizationMember(
    { params: { org_id: Number(orgId) } },
    {
      onSuccess: () => {
        showToast(t('kgstudio.team.invite.sent'), 'success');
        invalidateMembers();
      },
      onError: (error, variables) => {
        console.error(error);

        if (isApiError(error)) {
          const { status } = error;
          match(status)
            //
            .with(429, () => {
              showToast(t('kgstudio.team.invite.error.rate-limit'), 'error');
              setReachReinviteRateLimit((prev) => prev.add(variables.uid));

              setTimeout(() => {
                setReachReinviteRateLimit((prev) => {
                  prev.delete(variables.uid);
                  return new Set(prev);
                });
              }, 60_000);
            })
            .otherwise(() => {
              showToast(t('kgstudio.error.try-again'), 'error');
            });
        }
      },
    },
  );

  const columns: ColumnDef<MemberList['data'][number]>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: t('common.name'),
        cell: ({ row }) => {
          const { name, roles } = row.original;
          const isOwner = roles.admin?.includes('owner');
          return (
            <div className="grid grid-flow-col grid-cols-[auto,1fr] grid-rows-2 gap-x-3">
              <Avatar className="row-span-2" imageSrc={undefined} displayName={name} size="36" />
              <span className={cn('text-primary', { 'row-span-2 my-auto': !isOwner })}>{name}</span>
              {isOwner && <span className="text-secondary text-small">{t('kgstudio.team.role.owner')}</span>}
            </div>
          );
        },
      },
      {
        accessorKey: 'email',
        header: t('common.email'),
        size: 200,
        cell: ({ row }) => {
          const { email } = row.original;
          return <span className="text-primary">{email}</span>;
        },
      },
      {
        accessorKey: 'member_id',
        header: t('kgstudio.common.member-id'),
        cell: ({ row }) => {
          const { member_id } = row.original;
          return <span className="text-primary">{member_id}</span>;
        },
      },
      {
        accessorKey: 'roles',
        header: t('kgstudio.common.roles'),
        size: 250,
        cell: ({ row }) => {
          const { roles } = row.original;
          return (
            <div className="flex flex-wrap gap-2">
              {flattenFilteredRoles(roles as ModuleRole).map((role) => {
                return <RoleIdentifierBadge role={role} key={role} />;
              })}
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: t('common.status.text'),
        size: 80,
        cell: ({ row }) => {
          const { status } = row.original;
          return <InvitationStatusBadge status={status as InvitationStatus} />;
        },
      },
      {
        accessorKey: 'action',
        header: '',
        size: 60,
        cell: ({ row }) => {
          const member = row.original;
          return (
            <DropdownMenu>
              <DropdownMenu.Trigger
                className="focus-visible:outline-none"
                data-cy="team-row-action-btn"
                onClick={(e) => e.stopPropagation()}
              >
                <ActionButton />
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    setMemberModalData({
                      status: 'edit',
                      open: true,
                      currentMember: member,
                      queryKey,
                    });
                  }}
                >
                  {t('kgstudio.common.edit')}
                </DropdownMenu.Item>
                {['pending', 'expired'].includes(member.status) &&
                  (reachReinviteRateLimit.has(member.uid) ? (
                    <DropdownMenu.Item disabled>{t('kgstudio.team.rate-limit.hint')}</DropdownMenu.Item>
                  ) : (
                    <DropdownMenu.Item
                      onClick={(e) => {
                        e.stopPropagation();
                        reinvite({ uid: member.uid });
                      }}
                    >
                      {t('kgstudio.team.resend')}
                    </DropdownMenu.Item>
                  ))}
                {member.status === 'active' && (
                  <DropdownMenu.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      setMemberModalData({
                        status: 'remove',
                        open: true,
                        currentMember: member,
                        queryKey,
                      });
                    }}
                  >
                    {t('common.remove')}
                  </DropdownMenu.Item>
                )}
              </DropdownMenu.Content>
            </DropdownMenu>
          );
        },
      },
    ],
    [t],
  );

  const memberCount = members?.paging?.total_count;

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: members?.data || [],
      columns,
      state: {
        columnVisibility: {
          action: hasEditPermission,
        },
      },
    }));
  }, [columns, members?.data, hasEditPermission]);

  useUpdateEffect(() => {
    table.resetPageIndex();
  }, [debouncedQuery]);

  return (
    <div className="space-y-6">
      {memberModalData.open &&
        (memberModalData.status === 'remove' ? (
          <DeleteModal data={memberModalData} setMemberModalData={setMemberModalData} />
        ) : (
          <MemberModal data={memberModalData} setMemberModalData={setMemberModalData} />
        ))}
      <Card className="!p-0">
        <div className="flex flex-col items-start justify-between gap-4 p-4 md:!flex-row md:!items-center md:!gap-0 md:p-6">
          <h3 className="text-h3 text-primary font-bold">
            {t('kgstudio.common.members')} ({memberCount})
          </h3>
          <div className="flex gap-4">
            <Input
              className="h-12 md:!w-[231px]"
              type="text"
              placeholder={t('kgstudio.operators.placeholder')}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
              value={query}
            />
            {hasEditPermission && (
              <Button
                className="h-12 min-w-[100px] shrink-0"
                data-cy="invite-member-button"
                onClick={() => {
                  setMemberModalData({ status: 'invite', open: true, queryKey });
                }}
              >
                <UserPlus size={16} />
                {t('kgstudio.team.invite.text')}
              </Button>
            )}
          </div>
        </div>
        <DataTable
          table={table}
          isLoading={membersLoading}
          dataLength={members?.paging.total_count || 0}
          onRowClick={
            !!hasEditPermission
              ? (rowData) => {
                  setMemberModalData({
                    status: 'edit',
                    open: true,
                    currentMember: rowData,
                    queryKey,
                  });
                }
              : undefined
          }
        />
      </Card>
    </div>
  );
}
