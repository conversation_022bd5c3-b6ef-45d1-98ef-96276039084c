import { match } from 'ts-pattern';
import { ZodSchema } from 'zod';

import { VariantType } from '@kryptogo/2b';

import {
  AdminSchema,
  AssetProSchema,
  ComplianceSchema,
  ModuleRole,
  NFTBoostSchema,
  RoleIdentifier,
  User360Schema,
  WalletBuilderSchema,
} from '../_types';

export const getRoleInfo = (role: RoleIdentifier, t: any) => {
  const [module, roleStr] = role.split(':');

  const color = getColor(role) as VariantType;
  const displayRole = match({ module, roleStr })
    .with({ module: 'owner' }, () => t('common.owner'))
    .with({ roleStr: 'admin' }, () => t('common.admin'))
    .with({ roleStr: 'approver' }, () => t('kgstudio.team.role.approver'))
    .with({ roleStr: 'trader' }, () => t('kgstudio.team.role.trader'))
    .with({ roleStr: 'reviewer' }, () => t('kgstudio.team.role.reviewer'))
    .with({ roleStr: 'finance_manager' }, () => t('kgstudio.team.role.finance-manager'))
    .otherwise(() => '');
  const label = displayRole === 'Owner' ? displayRole : `${getModuleDisplayLabel(role, t)} - ${displayRole}`;
  const desc = getRoleItemDescription(role, t);

  return { color, label, desc };
};

export const flattenFilteredRoles = (roleData: ModuleRole): RoleIdentifier[] => {
  const flattenedRoles = Object.entries(roleData).flatMap(([, values]) => values) as RoleIdentifier[];
  const ownerIndex = flattenedRoles.indexOf('owner');
  if (ownerIndex > -1) {
    return ['owner', ...flattenedRoles.filter((role) => role !== 'owner')];
  }
  return flattenedRoles;
};

export const parseRoleIntoDropdownItem = (roles: RoleIdentifier[] | undefined, t: any) => {
  if (!roles) return [];
  return roles.map((role: RoleIdentifier) => {
    const { color, label, desc } = getRoleInfo(role, t);

    return { id: role, label, value: role, desc, disabled: false, color };
  });
};

export const parseTeamRoleIntoDropdownItem = (roles: ModuleRole['admin'], t: any) => {
  if (!roles)
    return [
      {
        label: t('common.member'),
        value: 'member',
      },
    ];

  return [
    {
      label: t('common.member'),
      value: 'member',
    },
    {
      label: t('common.owner'),
      value: 'owner',
    },
  ];
};

const isTypeMatching = (r: RoleIdentifier, schema: ZodSchema) => {
  return schema.safeParse(r).success;
};

export const getColor = (role: RoleIdentifier) => {
  return match(role)
    .when(
      () => isTypeMatching(role, User360Schema) || isTypeMatching(role, AdminSchema),
      () => 'yellow',
    )
    .when(
      () => isTypeMatching(role, WalletBuilderSchema),
      () => 'pink',
    )
    .when(
      () => isTypeMatching(role, AssetProSchema),
      () => 'greenblue',
    )
    .when(
      () => isTypeMatching(role, NFTBoostSchema),
      () => 'purple',
    )
    .when(
      () => isTypeMatching(role, ComplianceSchema),
      () => 'blue',
    )
    .run();
};
export const getModuleDisplayLabel = (role: RoleIdentifier, t: any) => {
  return match(role)
    .when(
      () => isTypeMatching(role, User360Schema),
      () => t('kgstudio.common.user360'),
    )
    .when(
      () => isTypeMatching(role, WalletBuilderSchema),
      () => t('kgstudio.common.data.wallet'),
    )
    .when(
      () => isTypeMatching(role, AssetProSchema),
      () => t('kgstudio.common.asset'),
    )
    .when(
      () => isTypeMatching(role, NFTBoostSchema),
      () => t('kgstudio.common.nft-boost'),
    )
    .when(
      () => isTypeMatching(role, ComplianceSchema),
      () => t('kgstudio.audience.compliance'),
    )
    .when(
      () => isTypeMatching(role, AdminSchema),
      () => t('common.owner'),
    )
    .run();
};

export const getRoleItemDescription = (role: RoleIdentifier, t: any) => {
  return match(role)
    .with('asset_pro:admin', () => t('kgstudio.team.role-decription.aseet-pro.admin'))
    .with('asset_pro:approver', () => t('kgstudio.team.role-decription.aseet-pro.approver'))
    .with('asset_pro:trader', () => t('kgstudio.team.role-decription.aseet-pro.trader'))
    .with('asset_pro:finance_manager', () => t('kgstudio.team.role-decription.aseet-pro.finance-manager'))
    .with('compliance:admin', () => t('kgstudio.team.role-decription.compliance.admin'))
    .with('compliance:reviewer', () => t('kgstudio.team.role-decription.compliance.reviewer'))
    .otherwise(() => '');
};
