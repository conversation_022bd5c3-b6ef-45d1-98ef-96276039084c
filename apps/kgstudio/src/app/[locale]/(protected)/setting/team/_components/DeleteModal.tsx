import { Trash2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

import alertCircle from '@/2b/icons/AlertCircle.svg';
import { showToast } from '@/2b/toast';
import { isApiError } from '@/app/_common/lib/api';
import { apiOrganizationHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { Button, Modal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import { MemberModalDataProps } from '../page';

export interface DeleteModalProps {
  data: MemberModalDataProps;
  setMemberModalData: (data: MemberModalDataProps) => void;
}

const DeleteModal = (props: DeleteModalProps) => {
  const t = useTranslations();
  const open = props.data.open;
  const onOpenChange = (open: boolean) => props.setMemberModalData({ ...props.data, open });

  const queryClient = useQueryClient();
  const orgId = useOrganizationStore((state) => state.orgId);
  const user_id = props.data.currentMember?.uid as string;
  const { mutate } = apiOrganizationHooks.useDeleteOrganizationMember(
    { params: { uid: user_id, org_id: Number(orgId) } },
    {
      onSuccess: (data) => {
        showToast(t('kgstudio.team.delete.success', { name: data.data.name, email: data.data.email }), 'success');

        queryClient.refetchQueries({
          queryKey: props.data.queryKey,
        });
        onOpenChange(false);
      },
      onError: (error) => {
        console.error(error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );
  const onDelete = () => {
    mutate(undefined);
  };

  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <Modal.Content noClose className="grid h-[400px] max-w-[600px] grid-rows-[1fr_auto] gap-6 overflow-auto">
        <div className="flex flex-col items-center gap-2 pt-[60px]">
          <Image src={alertCircle} alt="alert" className="h-9 w-9" />
          <h2 className="text-h2 text-primary text-center">{t('common.confirmation')}</h2>
          <p className="text-body-2 text-secondary text-center">
            {t('kgstudio.team.delete.description', {
              currentMemberName: props.data.currentMember?.name || '',
              currentMemberEmail: props.data.currentMember?.email || '',
            })}
          </p>
        </div>

        <Modal.Footer className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            <Button
              type="button"
              variant="grey"
              onClick={() => props.setMemberModalData({ ...props.data, status: 'edit' } as MemberModalDataProps)}
            >
              {t('kgstudio.common.cancel')}
            </Button>

            <Button type="button" variant="danger" onClick={onDelete} data-cy="member-delete-button">
              <Trash2 size={24} />
              {t('common.confirm')}
            </Button>
          </div>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { DeleteModal };
