import { flatten, get, isEmpty, keyBy, mapValues, omit, pick, some, debounce } from 'lodash-es';
import { Trash2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useState, useMemo, useEffect } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormDropdown, FormInput, FormInputError, FormMultiSelectDropdown } from '@/app/_common/components/form';
import { KG_HELP_CENTER_URL } from '@/app/_common/constant';
import { isApiError } from '@/app/_common/lib/api';
import { apiOrganizationHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Label, Modal, Spinner } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import { RoleIdentifier } from '../_types';
import { parseRoleIntoDropdownItem, parseTeamRoleIntoDropdownItem } from '../_utils';
import { MemberModalDataProps } from '../page';

const generalFormErrorKey = 'general';

const RoleDropdownItemSchema = {
  id: z.string(),
  label: z.string(),
  value: z.string(),
  desc: z.string().optional(),
  disabled: z.boolean(),
  color: z.string(),
};
const TeamRoleSchema = z.union([z.literal('owner'), z.literal('member')]);

const InviteUserSchema = (t: any) =>
  z
    .object({
      name: z.string().nonempty({
        message: t('kgstudio.team.name.validation.required'),
      }),
      member_id: z.string(),
      email: z.string().email(),
      team: TeamRoleSchema,
      asset_pro: z.object(RoleDropdownItemSchema).array(),
      nft_boost: z.object(RoleDropdownItemSchema).array(),
      compliance: z.object(RoleDropdownItemSchema).array(),
      wallet_builder: z.object(RoleDropdownItemSchema).array(),
      user_360: z.object(RoleDropdownItemSchema).array(),
    })
    .refine(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ({ email, member_id, name, team, ...roles }) => {
        return some(roles, (array) => !isEmpty(array)) || team === 'owner';
      },
      {
        message: t('kgstudio.team.role.validation'),
        path: [generalFormErrorKey],
      },
    );

export type InviteUserFormSchema = z.infer<ReturnType<typeof InviteUserSchema>>;

interface MemberModalProps {
  data: MemberModalDataProps;
  setMemberModalData: (data: MemberModalDataProps) => void;
}

const MemberModal = (props: MemberModalProps) => {
  const open = props.data.open;
  const onOpenChange = (open: boolean) => props.setMemberModalData({ ...props.data, open });

  const queryClient = useQueryClient();
  const [queryingExist, setQueryingExist] = useState(false);
  const [orgId] = useOrganizationStore((state) => [state.orgId, state.orgInfo?.modules]);
  const t = useTranslations();
  const isEdit = props.data.status === 'edit';
  const form = useForm<InviteUserFormSchema>({
    defaultValues: {
      name: isEdit ? props.data.currentMember?.name : '',
      member_id: isEdit ? props.data.currentMember?.member_id : '',
      email: isEdit ? props.data.currentMember?.email : '',
      team: isEdit ? (props.data.currentMember?.roles.admin?.[0] === 'owner' ? 'owner' : 'member') : 'member',
      ...mapValues(keyBy(['user_360', 'wallet_builder', 'asset_pro', 'compliance', 'nft_boost']), (key) =>
        isEdit ? parseRoleIntoDropdownItem(get(props.data.currentMember?.roles, key), t) : [],
      ),
    },
    mode: 'onBlur',
    resolver: zodResolver(InviteUserSchema(t)),
  });
  const teamRole = form.watch('team');

  const modalTitle = isEdit ? t('kgstudio.team.edit.title') : t('kgstudio.team.invite.title');
  const confirmButtonText = isEdit ? t('common.update') : t('kgstudio.team.invite.text');
  const user_id = isEdit ? (props.data.currentMember?.uid as string) : '';

  const { data: roles } = apiOrganizationHooks.useGetOrganizationRoles(
    { params: { org_id: orgId ?? -1 } },
    { enabled: !!orgId, cacheTime: 0 },
  );
  const {
    data: existData,
    isFetching: existFetching,
    remove: removeExistData,
  } = apiOrganizationHooks.useCheckUserExists(
    { params: { org_id: Number(orgId) }, queries: { email: form.getValues('email') } },
    {
      enabled: !!orgId && !isEdit && queryingExist,
      retry: false,
      onSuccess: (data) => {
        if (data.data.exist)
          form.setError('email', { message: t('kgstudio.team.invite.error.existed'), type: 'manual' });
      },
      onError: (error) => {
        console.error(error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  const userExisted = existData?.data?.exist;

  const { mutate: inviteMember, isLoading: inviteMemberLoading } = apiOrganizationHooks.useInviteOrganizationMember(
    { params: { org_id: Number(orgId) } },
    {
      onSuccess: (data) => {
        queryClient.refetchQueries(props.data.queryKey);
        onOpenChange(false);
        showToast(t('kgstudio.team.invite.sent'), 'success');
      },
      onError: (error) => {
        console.error(error);
        if (isApiError(error)) {
          const { status } = error;

          match(status)
            //
            .with(429, () => {
              showToast(t('kgstudio.team.invite.error.rate-limit'), 'error');
            })
            .with(409, () => {
              showToast(t('kgstudio.team.invite.error.existed'), 'error');
            })
            .otherwise(() => {
              showToast(t('kgstudio.common.error'), 'error');
            });
        }
        //
      },
    },
  );
  const { mutate: editMember, isLoading: editMemberLoading } = apiOrganizationHooks.useEditOrganizationMember(
    { params: { org_id: Number(orgId), user_id: user_id } },
    {
      onSuccess: () => {
        queryClient.refetchQueries(props.data.queryKey);
        onOpenChange(false);

        showToast(t('kgstudio.common.save-changes'), 'success');
      },
      onError: (error) => {
        console.error(error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  const onSubmit = () => {
    const { name, member_id, email, team, ...roles } = form.getValues();
    const parsedTeamRole = team === 'owner' ? ['owner'] : [];
    const parsedModuleRoles =
      team === 'owner' ? [] : flatten(Object.values(roles).map((module) => module.map((item) => item.value)));
    const parsedRoles = [...parsedTeamRole, ...parsedModuleRoles];
    const requestBody = { name: name, member_id: member_id, email: email, roles: parsedRoles as RoleIdentifier[] };

    match(props.data.status)
      .with('edit', () => {
        editMember(omit(requestBody, ['email']));
      })
      .with('invite', () => {
        !userExisted && inviteMember(requestBody);
      })
      .run();
  };

  const resetQueryingExist = () => {
    setQueryingExist(false);
    removeExistData();
  };

  const debouncedCheckEmail = useMemo(
    () =>
      debounce((form: UseFormReturn<InviteUserFormSchema>) => {
        form.trigger('email').then((res) => res && setQueryingExist(true));
      }, 1000),
    [],
  );

  useEffect(() => {
    return () => {
      debouncedCheckEmail.cancel();
    };
  }, [debouncedCheckEmail]);

  return (
    <Modal
      open={open}
      onOpenChange={(open: boolean) => {
        if (!open) {
          form.reset();
        }
        onOpenChange(open);
      }}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Modal.Content className="*:scrollbar-hide max-h-[80%] overflow-auto" data-cy="invite-member-form" noClose>
            <Modal.Header>
              <Modal.Title>{modalTitle}</Modal.Title>
            </Modal.Header>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <FormInput
                  title={t('common.name')}
                  placeholder={t('kgstudio.team.member.name.placeholder')}
                  name="name"
                  control={form.control}
                  required
                  data-cy="invite-member-input-name"
                />
                <FormInput
                  title={t('kgstudio.team.member.member-id.title')}
                  placeholder="e.g.: 00001"
                  name="member_id"
                  control={form.control}
                  data-cy="invite-member-input-id"
                />
              </div>
              <FormInput
                title={t('common.email')}
                name="email"
                control={form.control}
                required
                disabled={isEdit}
                data-cy="invite-member-input-email"
                onFocus={resetQueryingExist}
                onChange={() => {
                  debouncedCheckEmail(form);
                }}
              />
              {existFetching && (
                <div className="mt-[-12px] flex flex-row items-center gap-2">
                  <Spinner className="h-3 w-3 animate-spin" />
                  <Label className="text-body-2 text-primary">{t('common.checking')}...</Label>
                </div>
              )}
              <FormDropdown
                title={t('kgstudio.team.role.title')}
                {...(teamRole === 'owner' && {
                  desc: t('kgstudio.team.role.description'),
                })}
                name="team"
                control={form.control}
                options={parseTeamRoleIntoDropdownItem(roles?.data?.admin, t)}
              />
              {teamRole === 'member' && (
                <>
                  <FormMultiSelectDropdown
                    placeholder={t('common.no-access')}
                    title={t('kgstudio.team.role.user360')}
                    name="user_360"
                    control={form.control}
                    disabled={!roles?.data?.user_360?.length}
                    items={parseRoleIntoDropdownItem(roles?.data?.user_360, t)}
                  />
                  <FormMultiSelectDropdown
                    placeholder={t('common.no-access')}
                    title={t('kgstudio.team.role.wallet')}
                    name="wallet_builder"
                    control={form.control}
                    disabled={!roles?.data?.wallet_builder?.length}
                    items={parseRoleIntoDropdownItem(roles?.data?.wallet_builder, t)}
                  />
                  <FormMultiSelectDropdown
                    placeholder={t('common.no-access')}
                    title={t('kgstudio.team.role.asset-pro')}
                    name="asset_pro"
                    control={form.control}
                    items={parseRoleIntoDropdownItem(roles?.data?.asset_pro, t)}
                    data-cy="invite-member-select-asset-pro"
                  />
                  <FormMultiSelectDropdown
                    placeholder={t('common.no-access')}
                    title={t('kgstudio.team.role.compliance')}
                    name="compliance"
                    control={form.control}
                    disabled={!roles?.data?.compliance?.length}
                    items={parseRoleIntoDropdownItem(roles?.data?.compliance, t)}
                  />
                  <FormMultiSelectDropdown
                    placeholder={t('common.no-access')}
                    title={t('kgstudio.team.role.nft-boost')}
                    name="nft_boost"
                    control={form.control}
                    disabled={!roles?.data?.nft_boost?.length}
                    items={parseRoleIntoDropdownItem(roles?.data?.nft_boost, t)}
                  />
                </>
              )}
              {!isEmpty(pick(form.formState.errors, [generalFormErrorKey])) && (
                <FormInputError error={t('kgstudio.team.role.validation')} />
              )}

              <p className="text-secondary text-caption">
                {t('common.learn-more-about')}{' '}
                <Link href={KG_HELP_CENTER_URL} target="_blank" rel="noreferrer">
                  <span className="text-small text-secondary underline decoration-1">
                    {t('kgstudio.team.permission-settings')}
                  </span>
                </Link>
              </p>
            </div>

            <Modal.Footer className="space-y-6">
              {isEdit && (
                <Button
                  type="button"
                  variant="danger"
                  onClick={() => {
                    props.setMemberModalData({ ...props.data, status: 'remove' } as MemberModalDataProps);
                  }}
                >
                  <Trash2 size={24} />
                  {t('kgstudio.team.remove-member')}
                </Button>
              )}
              <div className="flex w-full items-center justify-between">
                <Button type="button" variant="grey" onClick={() => onOpenChange(false)}>
                  {t('kgstudio.common.cancel')}
                </Button>
                <Button
                  className="w-[200px]"
                  onClick={form.handleSubmit(onSubmit)}
                  data-cy="invite-member-confirm"
                  disabled={(!isEdit && (userExisted === true || userExisted === undefined)) || !form.formState.isValid}
                  loading={inviteMemberLoading || editMemberLoading}
                >
                  {confirmButtonText}
                </Button>
              </div>
            </Modal.Footer>
          </Modal.Content>
        </form>
      </Form>
    </Modal>
  );
};

export { MemberModal };
