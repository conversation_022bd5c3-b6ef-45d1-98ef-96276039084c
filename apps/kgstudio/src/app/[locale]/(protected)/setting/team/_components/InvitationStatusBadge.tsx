import { AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { match } from 'ts-pattern';

import { Badge } from '@kryptogo/2b';

import { InvitationStatus } from '../_types';

const InvitationStatusBadge = ({ status }: { status: InvitationStatus }) => {
  const t = useTranslations();

  return match(status)
    .with('active', () => <Badge variant="green">{t('common.status.active')}</Badge>)
    .with('inactive', () => <Badge variant="grey">{t('common.status.inactive')}</Badge>)
    .with('pending', () => <Badge variant="yellow">{t('common.status.pending')}</Badge>)
    .with('expired', () => (
      <Badge variant="red" icon={<AlertCircle size={'16px'} strokeWidth={'2px'} />}>
        {t('common.status.expired')}
      </Badge>
    ))
    .exhaustive();
};

export { InvitationStatusBadge };
