'use client';

import { useTransition } from 'react';

import { revalidatei18nTranslations } from '../_action';

const RevalidateI18nButton = () => {
  const [isPending, startTransition] = useTransition();

  return (
    <button
      disabled={isPending}
      onClick={() => {
        startTransition(() => {
          revalidatei18nTranslations();
        });
      }}
      className="text-primary rounded-xl px-3 py-2 bg-brand-primary hover:bg-brand-primary-light border border-[#000000] hover:-translate-x-2 hover:-translate-y-2 transition-all hover:shadow-[8px_8px_0px_2px_black] active:-translate-x-1 active:-translate-y-1 active:shadow-[4px_4px_0px_2px_black] disabled:opacity-50"
    >
      Update Translations
    </button>
  );
};

export { RevalidateI18nButton };
