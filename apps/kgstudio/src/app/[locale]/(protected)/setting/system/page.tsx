'use client';

import { Pencil } from 'lucide-react';
import { Building2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

import { usePageHeader, usePermissions } from '@/app/_common/hooks';
import { useOrganizationStore } from '@/app/_common/store';
import { Button, Card, Separator } from '@kryptogo/2b';

import { EditOrgInfoModal } from '../../home/<USER>';

export default function System() {
  const t = useTranslations();
  const orgInfo = useOrganizationStore((state) => state.orgInfo);
  usePageHeader({ title: t('kgstudio.common.system-settings') });

  const [openEditOrgInfoModal, setOpenEditOrgInfoModal] = useState(false);

  const [hasEditOrgInfoPermission] = usePermissions(['organization_info', 'edit']);

  return (
    <>
      <Card className="flex flex-col gap-10">
        <div className="flex items-center justify-between">
          <h2 className="text-h2 text-primary">{t('kgstudio.setting.org-settings')}</h2>
          {hasEditOrgInfoPermission && (
            <Button
              variant="secondary"
              size="sm"
              icon={<Pencil className="!stroke-[var(--brand-primary-dark)]" />}
              onClick={() => setOpenEditOrgInfoModal(true)}
            >
              {t('kgstudio.common.edit')}
            </Button>
          )}
        </div>
        <Separator />
        <div className="flex items-center gap-2">
          <div className="text-body text-secondary basis-[120px]">
            {t('kgstudio.home.edit-organization.name-title')}
          </div>
          <p className="text-body text-primary">{orgInfo?.name}</p>
        </div>
        <div className="flex items-start gap-2">
          <div className="text-body text-secondary basis-[120px]">
            {t('kgstudio.home.edit-organization.icon-title')}
          </div>
          <div className="bg-brand-primary-lighter relative flex size-[120px] items-center justify-center overflow-hidden rounded-[4px]">
            {orgInfo?.icon_url ? (
              <Image src={orgInfo.icon_url} alt={orgInfo?.name} fill />
            ) : (
              <Building2 className="stroke-brand-primary" size={48} />
            )}
          </div>
        </div>
      </Card>
      <EditOrgInfoModal
        open={openEditOrgInfoModal}
        onOpenChange={setOpenEditOrgInfoModal}
        name={orgInfo?.name || ''}
        icon_url={orgInfo?.icon_url ?? null}
      />
    </>
  );
}
