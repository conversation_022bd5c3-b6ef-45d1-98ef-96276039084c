'use client';

import { <PERSON>ert<PERSON><PERSON>cle, <PERSON>py, Globe, Key, Pencil, PlusCircle, Trash2, Wallet } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useRef, useState } from 'react';
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride';
import { toast } from 'sonner';

import { useDeviceSize, usePageHeader } from '@/app/_common/hooks';
import { useGoogleAds } from '@/app/_common/hooks/useGoogleAds';
import { isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { apiOrganizationHooks, apiWalletHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { Button, Card, Input, Label, Modal, Separator, Skeleton, Textarea } from '@kryptogo/2b';

import getPrompt from './prompt';

// API response type from internal API
interface ApiResponse<T> {
  code: number;
  data: T;
  status?: number;
  timestamp?: number;
}

// Custom API client using the internal axios instance
const api = {
  get: <T,>(url: string) => internalAxiosInstance.get<ApiResponse<T>>(url).then((response) => response.data),
  post: <T,>(url: string, data: any) =>
    internalAxiosInstance.post<ApiResponse<T>>(url, data).then((response) => response.data),
  delete: <T,>(url: string) => internalAxiosInstance.delete<ApiResponse<T>>(url).then((response) => response.data),
  put: <T,>(url: string, data: any) =>
    internalAxiosInstance.put<ApiResponse<T>>(url, data).then((response) => response.data),
};

// Define interface types based on backend API responses
interface APIKeyResponse {
  id: number;
  name: string;
  key_prefix: string;
  description: string | null;
  last_used_at: number | null;
  created_at: number;
}

interface CreateAPIKeyResponse {
  api_key: string;
}

interface OAuthClientResponse {
  client_id: string;
  client_name: string;
  client_domain: string;
  client_type: string;
  main_logo: string;
  square_logo?: string;
  created_at?: number;
  app_store_link?: string;
  google_play_link?: string;
}

interface CreateOAuthClientResponse {
  client_id: string;
  client_secret: string;
  client_name: string;
  client_domain: string;
}

interface UpdateOAuthClientRequest {
  client_name?: string;
  client_domain?: string;
  client_type?: string;
  main_logo?: string;
  square_logo?: string;
  app_store_link?: string;
  google_play_link?: string;
}

// Define a simple function to truncate addresses
const truncateAddress = (address: string): string => {
  if (!address) return '';
  return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
};

export default function UserSettings() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.setting.user.account-settings') });
  const { deviceSize } = useDeviceSize();

  const [orgId, orgInfo] = useOrganizationStore((state) => [state.orgId, state.orgInfo]);

  const [isLoading, setIsLoading] = useState(false);
  const [apiKeys, setApiKeys] = useState<APIKeyResponse[]>([]);
  const [oauthClients, setOAuthClients] = useState<OAuthClientResponse[]>([]);

  // API Key creation dialog state
  const [showCreateKeyDialog, setShowCreateKeyDialog] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');
  const [newKeyDescription, setNewKeyDescription] = useState('');
  const [newKeyValue, setNewKeyValue] = useState<string | null>(null);

  // OAuth client creation dialog state
  const [showCreateClientDialog, setShowCreateClientDialog] = useState(false);
  const [newClientName, setNewClientName] = useState('');
  const [newClientSecret, setNewClientSecret] = useState<string | null>(null);
  const [newClientId, setNewClientId] = useState<string | null>(null);

  // OAuth client update dialog state
  const [showUpdateClientDialog, setShowUpdateClientDialog] = useState(false);
  const [selectedClient, setSelectedClient] = useState<OAuthClientResponse | null>(null);
  const [updatedClientName, setUpdatedClientName] = useState('');
  const [updatedClientDomain, setUpdatedClientDomain] = useState('');
  const [updatedClientType, setUpdatedClientType] = useState('');

  // Delete confirmation dialog state
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{ type: 'apiKey' | 'oauthClient'; id: number | string } | null>(
    null,
  );

  // Payment address
  const {
    data: kgAccountResp,
    error: kgAccountError,
    isLoading: kgAccountLoading,
  } = apiOrganizationHooks.useGetOrganizationAccounts(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
    },
  );

  // Get EVM address (same approach as in treasury page)
  const evmAddress = useMemo(() => {
    if (!kgAccountResp?.data) return undefined;

    // Find the first EVM account (eth, matic, bsc, arb, etc)
    const evmAccount = kgAccountResp.data.find((account) => {
      const chainId = account.chain_id;
      // Common EVM chains
      return ['eth'].includes(chainId);
    });

    return evmAccount?.address;
  }, [kgAccountResp]);

  // Wallet address hooks
  const {
    data: importedAddressesResp,
    isLoading: addressesLoading,
    refetch: refetchAddresses,
  } = apiWalletHooks.useGetImportedAddresses({ params: { org_id: orgId ?? -1 } }, { enabled: !!orgId });

  const [showAddressModal, setShowAddressModal] = useState(false);

  const importAddressMutation = apiWalletHooks.useImportAddress(
    { params: { org_id: orgId ?? -1 } },
    {
      onSuccess: () => {
        refetchAddresses();
        toast.success(t('kgstudio.setting.user.success.address-added'));
      },
      onError: () => {
        toast.error(t('kgstudio.setting.user.error.add-address'));
      },
    },
  );

  // Add state for addressID to delete
  const [addressIdToDelete, setAddressIdToDelete] = useState<number | null>(null);

  const deleteAddressMutation = apiWalletHooks.useDeleteImportedAddress(
    addressIdToDelete !== null && orgId
      ? { params: { org_id: orgId, addressID: addressIdToDelete } }
      : { params: { org_id: orgId ?? -1, addressID: 0 } },
    {
      onSuccess: () => {
        refetchAddresses();
        toast.success(t('kgstudio.setting.user.success.address-deleted'));
        setAddressIdToDelete(null);
      },
      onError: () => {
        toast.error(t('kgstudio.setting.user.error.delete-address'));
        setAddressIdToDelete(null);
      },
    },
  );

  const handleDeleteAddress = async (id: number) => {
    if (!orgId) return;
    setAddressIdToDelete(id);
    await deleteAddressMutation.mutateAsync(undefined);
  };

  const [defaultAddressId, setDefaultAddressId] = useState<number | null>(null);

  // Combine organization wallet and imported addresses
  const addresses = useMemo(() => {
    const importedAddrs = (importedAddressesResp?.data ?? []).map((addr) => ({
      ...addr,
      name: addr.chain.toUpperCase(),
      isOrgWallet: false,
    }));

    // Add organization wallet if it exists
    const allAddresses = [...importedAddrs];
    const hasDefaultAddress = importedAddrs.some((addr) => addr.default_receive_address);
    if (evmAddress) {
      allAddresses.unshift({
        name: 'Default',
        isOrgWallet: true,
        id: -1,
        organization_id: orgId ?? -1,
        chain: 'eth',
        address: evmAddress,
        added_by_user_id: '',
        added_at: '',
        default_receive_address: !hasDefaultAddress,
      });
    }

    return allAddresses;
  }, [importedAddressesResp?.data, evmAddress, t]);

  // Set default address mutation
  const setDefaultAddressMutation = apiWalletHooks.useSetDefaultImportedAddress(
    { params: { org_id: orgId ?? -1, addressID: defaultAddressId ?? 0 } },
    {
      onSuccess: () => {
        toast.success(t('kgstudio.setting.user.success.address-set-default'));
        refetchAddresses();
      },
      onError: () => {
        toast.error(t('kgstudio.setting.user.error.set-default-address'));
      },
    },
  );

  // Set address as default (call API)
  const handleSetDefaultAddress = async (id: number, isOrgWallet: boolean) => {
    if (!orgId) return;

    try {
      // If setting org wallet as default, we need to unset all other addresses
      if (isOrgWallet) {
        // Find any address that is currently default and unset it
        const currentDefault = addresses.find((addr) => addr.default_receive_address && !addr.isOrgWallet);
        if (currentDefault) {
          setDefaultAddressId(currentDefault.id);
          await setDefaultAddressMutation.mutateAsync({
            default: false,
          });
        }
        toast.success(t('kgstudio.setting.user.success.org-wallet-default'));
        return;
      } else {
        setDefaultAddressId(id);
        console.log('id', id);
        // Use the mutation with the correct addressID
        await setDefaultAddressMutation.mutateAsync({
          default: true,
        });
      }
    } catch (error) {
      console.error('Failed to set default address:', error);
      toast.error(t('kgstudio.setting.user.error.set-default-address'));
    }
  };

  // Add new address
  const [newAddress, setNewAddress] = useState('');
  const handleAddAddress = async () => {
    if (!newAddress || !orgId) return;
    await importAddressMutation.mutateAsync({
      chain: 'eth',
      address: newAddress,
    });
    setNewAddress('');
  };

  const runTourRef = useRef(false);

  const joyrideSteps: Step[] = [
    {
      target: '.step1-target',
      content: t('kgstudio.setting.user.joyride.step1'),
      disableBeacon: true,
    },
    {
      target: '.step2-target',
      content: t('kgstudio.setting.user.joyride.step2'),
      disableBeacon: true,
    },
  ];

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status } = data;
    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      runTourRef.current = false;
    }
  };

  // Load API keys on component mount
  useEffect(() => {
    const fromCreateOrg = localStorage.getItem('from-create-org');

    if (orgId) {
      fetchAPIKeys();
      fetchOAuthClients().then(() => {
        if (fromCreateOrg) {
          localStorage.removeItem('from-create-org');
          runTourRef.current = true;
        }
      });
    }
  }, [orgId]);

  // Fetch API keys from the backend
  const fetchAPIKeys = async () => {
    if (!orgId) return;

    setIsLoading(true);
    try {
      const response = await api.get<APIKeyResponse[]>(`/studio/organization/${orgId}/api_keys`);
      setApiKeys(response.data);
    } catch (error) {
      if (isApiError(error)) {
        toast.error(t('kgstudio.setting.user.error.fetch-api-keys'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch OAuth clients from the backend
  const fetchOAuthClients = async () => {
    if (!orgId) return;

    setIsLoading(true);
    try {
      const response = await api.get<OAuthClientResponse[]>(
        `/studio/organization/${orgId}/oauth_clients?page_size=100`,
      );
      setOAuthClients(response.data);
    } catch (error) {
      if (isApiError(error)) {
        toast.error(t('kgstudio.setting.user.error.fetch-oauth-clients'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new API key
  const createAPIKey = async () => {
    if (!orgId || !newKeyName) return;

    setIsLoading(true);
    try {
      const requestData = {
        name: newKeyName,
        description: newKeyDescription || null,
      };

      const response = await api.post<CreateAPIKeyResponse>(`/studio/organization/${orgId}/api_keys`, requestData);

      // Show the new key once, and never again
      setNewKeyValue(response.data.api_key);

      // Reset form and close dialog
      setNewKeyName('');
      setNewKeyDescription('');

      // Refresh the list of API keys
      fetchAPIKeys();

      toast.success(t('kgstudio.setting.user.success.api-key-created'));
    } catch (error) {
      if (isApiError(error)) {
        toast.error(t('kgstudio.setting.user.error.create-api-key'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Delete an API key with confirmation
  const confirmDeleteAPIKey = (keyId: number) => {
    setItemToDelete({ type: 'apiKey', id: keyId });
    setShowDeleteConfirmation(true);
  };

  // Delete an API key
  const deleteAPIKey = async (keyId: number) => {
    if (!orgId) return;

    setIsLoading(true);
    try {
      await api.delete(`/studio/organization/${orgId}/api_keys/${keyId}`);

      // Refresh the list of API keys
      fetchAPIKeys();

      toast.success(t('kgstudio.setting.user.success.api-key-deleted'));
    } catch (error) {
      if (isApiError(error)) {
        toast.error(t('kgstudio.setting.user.error.delete-api-key'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Update an OAuth client
  const updateOAuthClient = async () => {
    if (!orgId || !selectedClient) return;

    setIsLoading(true);
    try {
      const updateData: UpdateOAuthClientRequest = {};

      if (updatedClientName && updatedClientName !== selectedClient.client_name) {
        updateData.client_name = updatedClientName;
      }

      if (updatedClientDomain && updatedClientDomain !== selectedClient.client_domain) {
        updateData.client_domain = updatedClientDomain;
      }

      if (updatedClientType && updatedClientType !== selectedClient.client_type) {
        updateData.client_type = updatedClientType;
      }

      // Only make API call if there are changes
      if (Object.keys(updateData).length > 0) {
        await api.put<OAuthClientResponse>(
          `/studio/organization/${orgId}/oauth_clients/${selectedClient.client_id}`,
          updateData,
        );

        // Refresh the list of OAuth clients
        fetchOAuthClients();

        toast.success(t('kgstudio.setting.user.success.oauth-client-updated'));
      }

      // Close the dialog
      setShowUpdateClientDialog(false);
    } catch (error) {
      if (isApiError(error)) {
        toast.error(t('kgstudio.setting.user.error.update-oauth-client'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Open the update dialog for an OAuth client
  const openUpdateClientDialog = (client: OAuthClientResponse) => {
    setSelectedClient(client);
    setUpdatedClientName(client.client_name);
    setUpdatedClientDomain(client.client_domain);
    setUpdatedClientType(client.client_type);
    setShowUpdateClientDialog(true);
  };

  // Handle the create OAuth client form submission
  const handleCreateOAuthClient = () => {
    // Proceed with creation using the formatted domain
    createOAuthClient();
  };

  // Create a new OAuth client
  const createOAuthClient = async () => {
    if (!orgId || !newClientName) return;

    setIsLoading(true);
    try {
      const requestData = {
        client_name: newClientName,
      };

      const response = await api.post<CreateOAuthClientResponse>(
        `/studio/organization/${orgId}/oauth_clients`,
        requestData,
      );

      // Show the new client ID and secret once, and never again
      setNewClientId(response.data.client_id);
      setNewClientSecret(response.data.client_secret);

      // Reset form fields
      setNewClientName('');

      // Refresh the list of OAuth clients
      fetchOAuthClients();

      toast.success(t('kgstudio.setting.user.success.oauth-client-created'));
    } catch (error) {
      if (isApiError(error)) {
        toast.error(t('kgstudio.setting.user.error.create-oauth-client'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle delete confirmation
  const handleDeleteConfirmation = async () => {
    if (!itemToDelete) return;

    if (itemToDelete.type === 'apiKey') {
      await deleteAPIKey(itemToDelete.id as number);
    }

    // Close the confirmation dialog
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  };

  // Handle copying the payment address
  const handleCopyAddress = (address: string) => {
    navigator.clipboard.writeText(address);
    toast.success(t('kgstudio.common.address-copied'));
  };

  // Handle copy to clipboard
  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => toast.success(message))
      .catch(() => toast.error(t('kgstudio.setting.user.error.copy-clipboard')));
  };

  const { trackConversion } = useGoogleAds();

  // Handle copy to clipboard for prompt
  const copyPromptToClipboard = (text: string, message: string) => {
    trackConversion({
      sendTo: 'AW-17057468787/QGidCOqO_scaEPOi0cU_',
    });
    navigator.clipboard
      .writeText(getPrompt(text))
      .then(() => toast.success(message))
      .catch(() => toast.error(t('kgstudio.setting.user.error.copy-clipboard')));
  };

  return (
    <>
      <Joyride
        steps={joyrideSteps}
        run={runTourRef.current}
        continuous={true}
        callback={handleJoyrideCallback}
        hideCloseButton
        locale={{
          back: t('kgstudio.setting.user.joyride.back'),
          close: t('kgstudio.setting.user.joyride.close'),
          last: t('kgstudio.setting.user.joyride.finish'),
          next: t('kgstudio.setting.user.joyride.next'),
          skip: t('kgstudio.setting.user.joyride.skip'),
        }}
        styles={{
          options: {
            width: 410,
            zIndex: 101,
            primaryColor: 'var(--brand-primary)',
          },
        }}
      />

      {/* Payment Address Card */}
      <Card className="mb-6 flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Wallet size={20} />
            <h2 className="text-h2 text-primary">{t('kgstudio.data.evm-wallet')}</h2>
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowAddressModal(true)}
            icon={<Pencil className="h-4 w-4" />}
          >
            {t('kgstudio.setting.user.edit')}
          </Button>
        </div>

        <Separator />

        <div className="text-body text-secondary">
          {t('kgstudio.setting.user.payment-address-description', {
            defaultValue: 'This is the address used to receive payments for your organization.',
          })}
        </div>

        {kgAccountLoading ? (
          <div className="py-4">
            <Skeleton className="h-6 w-[300px]" />
          </div>
        ) : evmAddress ? (
          <div className="flex flex-col gap-2 rounded-md border p-4">
            <div className="space-y-1">
              <div className="font-medium">
                {t('kgstudio.data.evm-wallet', {
                  defaultValue: 'EVM Wallet',
                })}
              </div>
              <div className="text-secondary flex items-center gap-2 text-sm">
                <span>
                  {deviceSize !== 'sm'
                    ? (addresses.find((addr) => addr.default_receive_address)?.address ?? evmAddress)
                    : truncateAddress(addresses.find((addr) => addr.default_receive_address)?.address ?? evmAddress)}
                </span>
                <Button
                  size="sm"
                  variant="text"
                  className="h-7 w-7"
                  icon={<Copy className="h-3.5 w-3.5" />}
                  onClick={() => handleCopyAddress(evmAddress)}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="rounded-md border border-dashed py-8 text-center">
            <div className="flex justify-center">
              <AlertCircle className="text-secondary h-8 w-8" />
            </div>
            <h3 className="mt-2 text-lg font-medium">
              {t('kgstudio.setting.user.no-payment-address', {
                defaultValue: 'No payment address found',
              })}
            </h3>
            <p className="text-secondary mt-1 text-sm">
              {t('kgstudio.setting.user.contact-support', {
                defaultValue: 'Please contact support to set up your payment address.',
              })}
            </p>
          </div>
        )}
      </Card>

      {/* API Keys Section */}
      <Card className="mb-6 flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Key size={20} />
            <h2 className="text-h2 text-primary">{t('kgstudio.setting.user.api-keys')}</h2>
          </div>
          <Button
            size="sm"
            variant="primary"
            onClick={() => setShowCreateKeyDialog(true)}
            icon={<PlusCircle className="h-4 w-4" />}
            disabled={isLoading}
          >
            {t('kgstudio.setting.user.create-api-key')}
          </Button>
        </div>

        <Separator />

        <div className="text-body text-secondary">
          {t('kgstudio.setting.user.api-keys-description')}{' '}
          <a
            href="https://payment.kryptogo.com/docs"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary/80 underline"
          >
            {t('kgstudio.setting.user.check-documentation')}
          </a>
        </div>

        {isLoading && (!apiKeys || apiKeys.length === 0) ? (
          <div className="py-8 text-center">
            <p>{t('kgstudio.setting.user.loading')}</p>
          </div>
        ) : apiKeys && apiKeys.length > 0 ? (
          <div className="grid gap-4">
            {apiKeys.map((apiKey) => (
              <div
                key={apiKey.id}
                className="flex flex-col justify-between gap-4 rounded-md border p-4 md:flex-row md:items-center"
              >
                <div className="space-y-1">
                  <div className="font-medium">{apiKey.name}</div>
                  <div className="text-secondary flex items-center gap-2 text-sm">
                    {t('kgstudio.setting.user.key-prefix')}: {apiKey.key_prefix}.•••••••••••••••••••
                  </div>
                  {apiKey.description && (
                    <div className="text-secondary text-sm">
                      <span className="font-medium">{t('kgstudio.setting.user.description')}:</span>{' '}
                      {apiKey.description}
                    </div>
                  )}
                  <div className="text-muted-foreground text-xs">
                    {t('kgstudio.setting.user.created')}: {new Date(apiKey.created_at * 1000).toLocaleDateString()}
                    {apiKey.last_used_at && (
                      <>
                        {' '}
                        • {t('kgstudio.setting.user.last-used')}:{' '}
                        {new Date(apiKey.last_used_at * 1000).toLocaleDateString()}
                      </>
                    )}
                  </div>
                </div>
                <Button
                  variant="danger"
                  size="sm"
                  className="shrink-0"
                  onClick={() => confirmDeleteAPIKey(apiKey.id)}
                  disabled={isLoading}
                  icon={<Trash2 className="h-4 w-4" />}
                >
                  {t('kgstudio.setting.user.delete')}
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="rounded-md border border-dashed py-8 text-center">
            <div className="flex justify-center">
              <AlertCircle className="text-secondary h-8 w-8" />
            </div>
            <h3 className="mt-2 text-lg font-medium">{t('kgstudio.setting.user.no-api-keys')}</h3>
            <p className="text-secondary mt-1 text-sm">{t('kgstudio.setting.user.create-first-api-key')}</p>
          </div>
        )}
      </Card>

      {/* OAuth Clients Section */}
      <Card className="flex flex-col gap-6" id="oauth-clients-section">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Globe size={20} />
            <h2 className="text-h2 text-primary">{t('kgstudio.setting.user.oauth-clients')}</h2>
          </div>
          <Button
            size="sm"
            variant="primary"
            onClick={() => setShowCreateClientDialog(true)}
            icon={<PlusCircle className="h-4 w-4" />}
            disabled={isLoading}
          >
            {t('kgstudio.setting.user.create-oauth-client')}
          </Button>
        </div>

        <Separator />

        <div className="text-body text-secondary">{t('kgstudio.setting.user.oauth-clients-description')}</div>

        {isLoading && (!oauthClients || oauthClients.length === 0) ? (
          <div className="py-8 text-center">
            <p>{t('kgstudio.setting.user.loading')}</p>
          </div>
        ) : oauthClients && oauthClients.length > 0 ? (
          <div className="grid gap-4">
            {oauthClients.map((client, index) => (
              <div
                key={client.client_id}
                className={`flex flex-col gap-2 rounded-md border p-4 ${index === 0 ? 'step1-target' : ''}`}
              >
                <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
                  <div className="space-y-1">
                    <div className="font-medium">{client.client_name}</div>
                    <div className="text-secondary flex items-center gap-2 text-sm">
                      {t('kgstudio.setting.user.client-id')}:{' '}
                      {client.client_id.length > 50 ? client.client_id.substring(0, 50) + '...' : client.client_id}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        onClick={() =>
                          copyToClipboard(
                            client.client_id,
                            t('kgstudio.setting.user.success.copied', { item: t('kgstudio.setting.user.client-id') }),
                          )
                        }
                      >
                        <Copy className="h-3.5 w-3.5" />
                        <span className="sr-only">Copy client ID</span>
                      </Button>
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {t('kgstudio.setting.user.created')}:{' '}
                      {client.created_at ? new Date(client.created_at * 1000).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>

                  <div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mr-2 shrink-0"
                      onClick={() => openUpdateClientDialog(client)}
                      disabled={isLoading}
                      icon={<Pencil className="h-4 w-4" />}
                    >
                      {t('kgstudio.setting.user.edit')}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className={`shrink-0 ${index === 0 ? 'step2-target' : ''}`}
                      onClick={() => copyPromptToClipboard(client.client_id, t('kgstudio.setting.user.copy-prompt'))}
                      disabled={isLoading}
                      icon={<Copy className="h-4 w-4" />}
                    >
                      {t('kgstudio.setting.user.copy-prompt')}
                    </Button>
                  </div>
                </div>
                {client.main_logo && (
                  <div className="mt-2">
                    <Label className="text-secondary text-xs">{t('kgstudio.setting.user.logo')}</Label>
                    <div className="mt-1 h-12 w-12 overflow-hidden rounded">
                      <img
                        src={client.main_logo}
                        alt={`${client.client_name} Logo`}
                        className="h-full w-full object-contain"
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="rounded-md border border-dashed py-8 text-center">
            <div className="flex justify-center">
              <AlertCircle className="text-secondary h-8 w-8" />
            </div>
            <h3 className="mt-2 text-lg font-medium">{t('kgstudio.setting.user.no-oauth-clients')}</h3>
            <p className="text-secondary mt-1 text-sm">{t('kgstudio.setting.user.create-first-oauth-client')}</p>
          </div>
        )}
      </Card>

      {/* Create API Key Modal */}
      <Modal open={showCreateKeyDialog} onOpenChange={setShowCreateKeyDialog}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>{t('kgstudio.setting.user.create-key-title')}</Modal.Title>
          </Modal.Header>

          {newKeyValue ? (
            <div className="space-y-4">
              <div className="rounded-md bg-amber-50 p-4 text-amber-800">
                <p className="font-medium">{t('kgstudio.setting.user.save-key-title')}</p>
                <p className="mt-1 text-sm">{t('kgstudio.setting.user.save-key-message')}</p>
              </div>
              <div className="flex items-center justify-between rounded-md border p-3">
                <code className="max-w-full overflow-x-auto break-all font-mono text-sm">{newKeyValue}</code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(
                      newKeyValue,
                      t('kgstudio.setting.user.success.copied', { item: t('kgstudio.setting.user.api-keys') }),
                    )
                  }
                  className="ml-2 shrink-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <Modal.Footer className="flex items-center justify-between">
                <Button
                  onClick={() => {
                    setNewKeyValue(null);
                    setShowCreateKeyDialog(false);
                  }}
                >
                  {t('kgstudio.setting.user.close')}
                </Button>
              </Modal.Footer>
            </div>
          ) : (
            <>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="key-name">{t('kgstudio.setting.user.key-name')}</Label>
                  <Input
                    id="key-name"
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                    placeholder={t('kgstudio.setting.user.key-name')}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="key-description">{t('kgstudio.setting.user.key-description')}</Label>
                  <Textarea
                    id="key-description"
                    value={newKeyDescription}
                    onChange={(e) => setNewKeyDescription(e.target.value)}
                    placeholder={t('kgstudio.setting.user.description')}
                    rows={3}
                  />
                </div>
              </div>
              <Modal.Footer className="flex items-center justify-between">
                <Button variant="grey" onClick={() => setShowCreateKeyDialog(false)}>
                  {t('kgstudio.setting.user.cancel')}
                </Button>
                <Button onClick={createAPIKey} disabled={!newKeyName || isLoading}>
                  {t('kgstudio.setting.user.create-api-key')}
                </Button>
              </Modal.Footer>
            </>
          )}
        </Modal.Content>
      </Modal>

      {/* Create OAuth Client Modal */}
      <Modal open={showCreateClientDialog} onOpenChange={setShowCreateClientDialog}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>{t('kgstudio.setting.user.create-client-title')}</Modal.Title>
          </Modal.Header>

          {newClientId && newClientSecret ? (
            <div className="space-y-4">
              <div className="rounded-md bg-amber-50 p-4 text-amber-800">
                <p className="font-medium">{t('kgstudio.setting.user.save-client-title')}</p>
                <p className="mt-1 text-sm">{t('kgstudio.setting.user.save-client-message')}</p>
              </div>
              <div className="space-y-2">
                <Label>{t('kgstudio.setting.user.client-id')}</Label>
                <div className="flex items-center justify-between rounded-md border p-3">
                  <code className="max-w-full overflow-x-auto break-all font-mono text-sm">{newClientId}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      copyToClipboard(
                        newClientId,
                        t('kgstudio.setting.user.success.copied', { item: t('kgstudio.setting.user.client-id') }),
                      )
                    }
                    className="ml-2 shrink-0"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label>{t('kgstudio.setting.user.client-secret')}</Label>
                <div className="flex items-center justify-between rounded-md border p-3">
                  <code className="max-w-full overflow-x-auto break-all font-mono text-sm">{newClientSecret}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      copyToClipboard(
                        newClientSecret,
                        t('kgstudio.setting.user.success.copied', { item: t('kgstudio.setting.user.client-secret') }),
                      )
                    }
                    className="ml-2 shrink-0"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <Modal.Footer className="flex items-center justify-between">
                <Button
                  onClick={() => {
                    setNewClientId(null);
                    setNewClientSecret(null);
                    setShowCreateClientDialog(false);
                  }}
                >
                  {t('kgstudio.setting.user.close')}
                </Button>
              </Modal.Footer>
            </div>
          ) : (
            <div className="flex h-full flex-col">
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="client-name">{t('kgstudio.setting.user.client-name')}</Label>
                  <Input
                    id="client-name"
                    value={newClientName}
                    onChange={(e) => setNewClientName(e.target.value)}
                    placeholder={t('kgstudio.setting.user.client-name')}
                  />
                </div>
              </div>
              <div className="flex-grow"></div>
              <Modal.Footer className="flex items-center justify-between">
                <Button variant="grey" onClick={() => setShowCreateClientDialog(false)}>
                  {t('kgstudio.setting.user.cancel')}
                </Button>
                <Button onClick={handleCreateOAuthClient} disabled={!newClientName || isLoading}>
                  {t('kgstudio.setting.user.create-oauth-client')}
                </Button>
              </Modal.Footer>
            </div>
          )}
        </Modal.Content>
      </Modal>

      {/* Update OAuth Client Modal */}
      <Modal open={showUpdateClientDialog} onOpenChange={setShowUpdateClientDialog}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>{t('kgstudio.setting.user.update-oauth-client')}</Modal.Title>
          </Modal.Header>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="update-client-name">{t('kgstudio.setting.user.client-name')}</Label>
              <Input
                id="update-client-name"
                value={updatedClientName}
                onChange={(e) => setUpdatedClientName(e.target.value)}
                placeholder={t('kgstudio.setting.user.client-name')}
              />
            </div>
          </div>
          <Modal.Footer className="flex items-center justify-between">
            <Button variant="grey" onClick={() => setShowUpdateClientDialog(false)}>
              {t('kgstudio.setting.user.cancel')}
            </Button>
            <Button onClick={updateOAuthClient} disabled={isLoading}>
              {t('kgstudio.setting.user.update-oauth-client')}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>{t('kgstudio.setting.user.confirm-deletion')}</Modal.Title>
          </Modal.Header>
          <div className="py-4">
            <p>
              {itemToDelete?.type === 'apiKey'
                ? t('kgstudio.setting.user.confirm-delete-api-key')
                : t('kgstudio.setting.user.confirm-delete-oauth-client')}
            </p>
          </div>
          <Modal.Footer className="flex items-center justify-between">
            <Button variant="grey" onClick={() => setShowDeleteConfirmation(false)}>
              {t('kgstudio.setting.user.cancel')}
            </Button>
            <Button variant="danger" onClick={handleDeleteConfirmation} disabled={isLoading}>
              {t('kgstudio.setting.user.delete')}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>

      {/* Payment Address Modal */}
      <Modal open={showAddressModal} onOpenChange={setShowAddressModal}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>
              {t('kgstudio.setting.user.manage-payment-addresses', {
                defaultValue: 'Manage Payment Addresses',
              })}
            </Modal.Title>
          </Modal.Header>

          <div className="py-4">
            <h3 className="mb-2 font-medium">
              {t('kgstudio.setting.user.your-addresses', {
                defaultValue: 'Your Addresses',
              })}
            </h3>

            <div className="mb-4 space-y-2">
              {addresses.map((addr) => (
                <div key={addr.id} className="flex items-center justify-between rounded-md border p-3">
                  <div className="space-y-0.5">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{addr.name}</span>
                      {addr.isOrgWallet && (
                        <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-800">
                          {t('kgstudio.setting.user.org-wallet', {
                            defaultValue: 'Org Wallet',
                          })}
                        </span>
                      )}
                      {addr.default_receive_address && (
                        <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
                          {t('kgstudio.setting.user.default', {
                            defaultValue: 'Default',
                          })}
                        </span>
                      )}
                    </div>
                    <div className="text-secondary text-sm">
                      {deviceSize !== 'sm' ? addr.address : truncateAddress(addr.address)}
                      <Button
                        size="sm"
                        variant="text"
                        className="h-8 w-8"
                        icon={<Copy className="h-4 w-4" />}
                        onClick={() => handleCopyAddress(addr.address)}
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {!addr.default_receive_address && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSetDefaultAddress(addr.id, addr.isOrgWallet)}
                      >
                        {t('kgstudio.setting.user.set-as-default', {
                          defaultValue: 'Set as Default',
                        })}
                      </Button>
                    )}
                    {!addr.isOrgWallet && (
                      <Button size="sm" variant="danger" onClick={() => handleDeleteAddress(addr.id)}>
                        {t('kgstudio.setting.user.delete', { defaultValue: 'Delete' })}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <Separator />

            <h3 className="mb-2 mt-4 font-medium">
              {t('kgstudio.setting.user.add-new-address', {
                defaultValue: 'Add New Address',
              })}
            </h3>

            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="wallet-address">
                  {t('kgstudio.data.evm-wallet', {
                    defaultValue: 'Wallet Address',
                  })}
                </Label>
                <Input
                  id="wallet-address"
                  value={newAddress}
                  onChange={(e) => setNewAddress(e.target.value)}
                  placeholder="0x..."
                />
              </div>

              <Button onClick={handleAddAddress} disabled={!newAddress} className="w-full">
                {t('kgstudio.setting.user.add-address', {
                  defaultValue: 'Add Address',
                })}
              </Button>
            </div>
          </div>
        </Modal.Content>
      </Modal>
    </>
  );
}
