const getPrompt = function (clientId: string) {
  return `
       **# KryptogoKit Quick Start Guide**
  
      KryptogoKit is for web3 payment.
  
      **## Installation**
      npm install @kryptogo/kryptogokit-sdk-react@1.1.84 wagmi@2.14.14 viem@2.x @tanstack/react-query@5.68.0
  
      **## Basic Setup**
  
      **### 1. Import Required Packages**
      \`\`\`javascript
      import { WagmiProvider } from "wagmi";
      import { getDefaultConfig } from "@kryptogo/kryptogokit-sdk-react";
      import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
  
      // Import pre-built styles
      import "@kryptogo/kryptogokit-sdk-react/styles.css";
      \`\`\`
  
      **### 2. Configure Providers in your app.js**
      \`\`\`javascript
      const queryClient = new QueryClient();
  
      // Your organization's KryptoGO clientId
      const clientId = "${clientId}";
  
      const config = getDefaultConfig();
      \`\`\`
  
      **### 3. Wrap Providers in your app.js**
      Make sure to wrap your app with the \`<KryptogoKitProvider>\`, \`<QueryClientProvider>\`, and \`<WagmiProvider>\`.
      \`\`\`jsx
      <WagmiProvider config={config}>
        <QueryClientProvider client={queryClient}>
          <KryptogoKitProvider clientId={clientId}>
            {/* Your App */}
          </KryptogoKitProvider>
        </QueryClientProvider>
      </WagmiProvider>
      \`\`\`
  
      **## Using Payment Features**
  
      **### usePayment Hook**
      The \`usePayment\` hook provides payment modal controls and real-time payment intent status. It handles the complete payment flow from modal interactions to transaction monitoring.
      \`\`\`javascript
      const {
        openPaymentModal,
        closePaymentModal,
        data,
        txHash,
        error,
        isLoading,
        isSuccess,
        isError,
      } = usePayment();
  
      <Button
        onClick={() =>
          openPaymentModal({
            fiat_amount: "10",
            fiat_currency: "TWD",
            callback_url: "https://{{endpoint-to-server}}",
            order_data: {
              order_product_id: "**********",
              order_product_name: "Good Thing",
            },
          })
        }
      >
        Open Payment Modal
      </Button>;
      \`\`\`
  
      **#### Parameters**
      \`\`\`json
      [
        {
          "Name": "openPaymentModal",
          "Type": "function(fiat_amount: string, fiat_currency: 'TWD' | 'USD', callback_url?: string, order_data?: Record<string, any>)",
          "Description": "Opens the payment modal and starts a new payment process."
        },
        {
          "Name": "closePaymentModal",
          "Type": "function(): void",
          "Description": "Closes the payment modal and cancels the ongoing payment process."
        },
        {
          "Name": "data",
          "Type": "{ payment_intent_id: string; payment_chain_id: string; payment_address: string; token_address: string; symbol: string; decimals: number; crypto_amount: string; fiat_amount: string; fiat_currency: 'TWD' | 'USD'; payment_deadline: number; status: PaymentStatus; payment_tx_hash: string | null; callback_url: string | null; order_data: Record<string, any> | null; } | undefined",
          "Description": "The details of the current payment, including its status and metadata."
        },
        {
          "Name": "txHash",
          "Type": "string | undefined",
          "Description": "The transaction hash of the current payment."
        },
        {
          "Name": "error",
          "Type": "Error | undefined",
          "Description": "The error of the current payment."
        },
        {
          "Name": "isLoading",
          "Type": "boolean",
          "Description": "Indicates whether the payment process is in progress."
        },
        {
          "Name": "isSuccess",
          "Type": "boolean",
          "Description": "Indicates whether the payment was successfully completed."
        },
        {
          "Name": "isError",
          "Type": "boolean",
          "Description": "Indicates whether the payment has failed."
        }
      ]
      \`\`\`
  
      > Note: The **callback_url** is optional. If provided, a notification will be sent to the specified callback endpoint when the payment status becomes successful.
  
      > Note: When using **order_data**, you can include any order-related information, and your server will receive this information at your configured callback endpoint.
      **
    `;
};

export default getPrompt;
