import { ApiResponse, ApiResponseWithPaging } from '@/app/_common/lib/api';
import { AssetProChainId } from '@/app/_common/services/asset-pro/model';
import { KGAccount, MemberActiveStatus, TxStatus } from '@/app/_common/types';

export type SendType = 'email' | 'phone' | 'address';

export type SendTokenFormData = {
  blockchain: AssetProChainId | null;
  token: string;
  amount: string;
  sendType: SendType;
  countryCode: string;
  phone: string;
  email: string;
};

export type TokenTransferRequest = {
  org_id: string;
  chain_id: AssetProChainId;
  contract_address: string;
  amount: number;
  wallet_address: string;
};

// convert form data to transfer request body
export const toTransferRequest = (
  orgId: string,
  walletAddress: string,
  data: SendTokenFormData,
): TokenTransferRequest => {
  const { blockchain, token, amount } = data;
  const requestBody: TokenTransferRequest = {
    org_id: orgId,
    chain_id: blockchain as AssetProChainId,
    contract_address: token,
    amount: Number(amount),
    wallet_address: walletAddress,
  };
  return requestBody;
};

export type TransferTokenResponse = ApiResponse<{
  tx_hash: string;
  transfer_time: number;
}>;

export type Token = {
  name: string;
  symbol: string;
  chain_id: AssetProChainId;
  chain_name: string;
  contract_address: string;
  logo_url: string;
};

export type TokenListResponse = ApiResponse<Token[]>;

export type User = {
  member_id: string;
  permissions: string[];
  wallet_address: string;
  status: MemberActiveStatus;
};
export type UsersResponse = ApiResponseWithPaging<User[]>;

export type Paging = {
  page_number: number;
  page_size: number;
  total_count: number;
  page_sort: string;
};
export type TransferHistory = {
  member_id: string;
  member_address: string;
  chain_id: AssetProChainId;
  contract_address: string;
  token_name: string;
  token_logo_url: string;
  amount: string;
  transfer_time: number;
  recipient: {
    phone_number?: string;
    email?: string;
    wallet_address: string;
  };
  status: TxStatus;
  tx_hash: string;
};

export type TransferHistoriesResponse = ApiResponseWithPaging<TransferHistory[]>;

export type KycStatus = 'verified' | 'pending' | 'rejected' | 'unverified';

export type CustomerResponse = ApiResponse<{
  display_name: string;
  wallets: KGAccount[];
  avatar_url: string;
  kyc_status: KycStatus;
}>;
