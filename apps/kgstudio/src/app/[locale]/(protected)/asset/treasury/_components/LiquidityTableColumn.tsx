import BigNumber from 'bignumber.js';
import { AlertCircle, Info, LineChart } from 'lucide-react';
import { Dispatch, SetStateAction } from 'react';

import { TokenIcon } from '@/app/_common/components';
import { Liquidity } from '@/app/_common/services/asset-pro-extend/model';
import { Button, Tooltip } from '@kryptogo/2b';
import { getChainFullName } from '@kryptogo/utils';
import { ColumnDef, Row } from '@tanstack/react-table';

type AllBalances = {
  symbol: string;
  quantity?: number;
  chain_id: string;
};

interface LiquidityTableColumnArgs {
  t: any;
  allBalances: AllBalances[];
  hasReadStatisticsPermission: boolean;
  actionFn: () => void;
  setQrCodeModal: Dispatch<
    SetStateAction<{
      open: boolean;
      chainType: string;
    }>
  >;
}

const LiquidityTableColumn = ({
  t,
  allBalances,
  hasReadStatisticsPermission,
  actionFn,
  setQrCodeModal,
}: LiquidityTableColumnArgs): ColumnDef<Liquidity>[] =>
  [
    {
      accessorKey: 'liquidity_type',
      size: 120,
      header: () => <div className="pl-2">{t('kgstudio.treasury.liquidity-type')}</div>,
      cell: ({ row }: { row: Row<Liquidity> }) => {
        const { liquidity_type } = row.original;
        return (
          <div className="flex items-center gap-1 pl-2">
            <p className="text-primary text-body-2">
              {liquidity_type === 'buy_crypto'
                ? t('kgstudio.treasury.buy-crypto-title')
                : t('kgstudio.treasury.gas-swap-title')}
            </p>
            <Tooltip>
              <Tooltip.Trigger>
                <Info size={12} className="stroke-[var(--text-secondary)]" />
              </Tooltip.Trigger>
              <Tooltip.Content className="w-[350px]">
                {liquidity_type === 'buy_crypto'
                  ? t('kgstudio.treasury.buy-crypto.desc')
                  : t('kgstudio.treasury.gas-swap.desc')}
                <Tooltip.Arrow />
              </Tooltip.Content>
            </Tooltip>
          </div>
        );
      },
    },
    {
      accessorKey: 'symbol',
      size: 120,
      header: t('kgstudio.treasury.token'),
      cell: ({ row }: { row: Row<Liquidity> }) => {
        const { symbol, token_url, chain_id } = row.original;
        return (
          <div className="flex items-center gap-2">
            <TokenIcon
              token={{
                logoUrl: token_url,
                name: symbol,
              }}
              size="32"
              chain={chain_id}
            />
            <div>
              <p className="text-primary text-body-2-bold">{symbol}</p>
              <p className="text-secondary text-small">{getChainFullName(chain_id)}</p>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'price',
      size: 120,
      header: t('kgstudio.treasury.token-price'),
      cell: ({ row }: { row: Row<Liquidity> }) => {
        const { price, unit } = row.original;
        const formatUnit = unit === 'usd' ? 'USD' : 'USDT';

        return <p className="text-primary text-body-2">{`${unit === 'usd' ? '$' : ''}${price} ${formatUnit}`}</p>;
      },
    },
    {
      accessorKey: 'profit_margin',
      size: 120,
      header: `${t('kgstudio.treasury.profit-margin')} (%)`,
      cell: ({ row }: { row: Row<Liquidity> }) => {
        const { profit_margin } = row.original;
        return (
          <p className="text-primary text-body-2-bold">{BigNumber(profit_margin).multipliedBy(100).toString()} %</p>
        );
      },
    },
    {
      accessorKey: 'alert_threshold',
      size: 150,
      header: t('kgstudio.data.balance'),
      cell: ({ row }: { row: Row<Liquidity> }) => {
        const { symbol, chain_id, alert_threshold } = row.original;
        const token = allBalances.find((balance) => balance.symbol === symbol && balance.chain_id === chain_id);

        if (alert_threshold === null) {
          return (
            <p className="text-primary text-body-2">
              {token?.quantity} {token?.symbol}
            </p>
          );
        }

        const isUnderThreshold = BigNumber(token?.quantity || 0).isLessThan(BigNumber(alert_threshold));

        return isUnderThreshold ? (
          <Tooltip>
            <Tooltip.Trigger>
              <div className="flex items-center gap-1">
                <p className="text-error text-body-2">
                  {token?.quantity} {token?.symbol}
                </p>
                <AlertCircle size={16} className="stroke-white" fill="var(--alert-error)" />
              </div>
            </Tooltip.Trigger>
            <Tooltip.Content
              className="cursor-pointer"
              side="bottom"
              onClick={() =>
                setQrCodeModal(() => ({
                  open: true,
                  chainType: 'tron',
                }))
              }
            >
              {t('kgstudio.treasury.deposit-tooltip', {
                alert_threshold: alert_threshold,
                symbol: token?.symbol,
              })}
              <Tooltip.Arrow />
            </Tooltip.Content>
          </Tooltip>
        ) : (
          <p className="text-primary text-body-2">
            {token?.quantity} {token?.symbol}
          </p>
        );
      },
    },
  ].concat(
    hasReadStatisticsPermission
      ? [
          {
            accessorKey: 'chart',
            header: () => <p className="text-center">{t('kgstudio.treasury.chart')}</p>,
            size: 60,
            cell: () => {
              return (
                <div className="flex justify-end pr-3">
                  <Button
                    size="md"
                    variant="grey"
                    icon={<LineChart className="stroke-secondary" strokeWidth={3} size={16} />}
                    onClick={actionFn}
                  />
                </div>
              );
            },
          },
        ]
      : [],
  );

export { LiquidityTableColumn };
