'use client';

import BigNumber from 'bignumber.js';
import * as A from 'fp-ts/Array';
import * as O from 'fp-ts/Option';
import { pipe } from 'fp-ts/lib/function';
import * as S from 'fp-ts/string';
import { map, pick, sumBy, uniq, uniqBy } from 'lodash-es';
import { ArrowUpRight, Copy, Eye, Plus, Search, Settings2, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { QRCodeCanvas } from 'qrcode.react';
import qs from 'qs';
import { useEffect, useMemo, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { formatUnits } from 'viem';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { ChainBadge } from '@/app/_common/components/badge';
import { FormDropdown, FormInput } from '@/app/_common/components/form';
import { useDeviceSize, usePageHeader, usePermissions } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { getClient } from '@/app/_common/lib/clients';
import { decryptMnemonic, generateX25519KeyPair } from '@/app/_common/lib/crypto';
import { cn } from '@/app/_common/lib/utils';
import { apiAssetProExtendHooks, apiAssetProHooks, apiOrganizationHooks, apiWalletHooks } from '@/app/_common/services';
import { Liquidity } from '@/app/_common/services/asset-pro-extend/model';
import { AssetProEvmChainId, AssetProNonEvmChainId } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import tronIcon from '@/assets/chains/icon-tron.png';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, DataTable, Form, Modal, Skeleton, Tabs } from '@kryptogo/2b';
import {
  ASSETPRO_SUPPORTED_EVM_CHAINS,
  ASSETPRO_SUPPORTED_TRON_CHAINS,
  DECIMAL_DISPLAY_MODE,
  ERC20ABI,
  formatCurrency,
  getChainFullName,
  getChainIcon,
  getChainInfo,
  truncateTxhashOrAddress,
} from '@kryptogo/utils';
import { useQueries } from '@tanstack/react-query';
import {
  ColumnDef,
  TableOptions,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import evmWalletIcon from './_assets/evmWallet.png';
import { LiquidityModal, LiquidityTableColumn } from './_components';

type TokenDatum = {
  logo_url: string | React.ReactNode;
  symbol: string;
  name: string;
  chain_id: string;
  contract_address: string | null;
  decimals: number;
  chain_name: string | null;
  coingecko_id?: string;
  balance?: number;
  price?: number;
  quantity?: number;
  value?: number;
};

type TableTab = 'assets' | 'liquidity';

const FilterSchema = z.object({
  token: z.string().optional(),
  chain: z.string().optional(),
});
type Filter = z.infer<typeof FilterSchema>;

const fixedNumber = (num: BigNumber | undefined, decimals = DECIMAL_DISPLAY_MODE.TOKEN) =>
  pipe(
    num,
    O.fromNullable,
    O.map((n) => Number(n.toFixed(decimals))),
    O.getOrElseW(() => undefined),
  );

export default function Treasury() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.common.treasury') });
  const router = useRouter();
  const [hasEditLiquidityPermission, hasReadLiquidityPermission, hasReadStatisticsPermission] = usePermissions(
    ['asset_pro_liquidity', 'edit'],
    ['asset_pro_liquidity', 'read'],
    ['user_360_statistics_asset_pro', 'read'],
  );
  const { deviceSize } = useDeviceSize();

  const form = useForm<Filter>({
    defaultValues: {
      token: '',
      chain: undefined,
    },
    mode: 'onChange',
    resolver: zodResolver(FilterSchema),
  });
  const [watchedToken, watchedChain] = useWatch({ control: form.control, name: ['token', 'chain'] });

  const [orgId] = useOrganizationStore((state) => [state.orgId]);
  const [tokenIds, setTokenIds] = useState<string[]>([]);
  const [tableTab, setTableTab] = useState<TableTab>('assets');
  const [seedPhrase, setSeedPhrase] = useState<string | null>(null);
  const [qrCodeModal, setQrCodeModal] = useState({
    open: false,
    chainType: 'evm',
  });
  const [seedPhraseModal, setSeedPhraseModal] = useState({
    open: false,
    showPhrase: false,
    step: 'warning' as 'warning' | 'phrase',
    isLoading: false,
  });
  const [openLiquidityModal, setOpenLiquidityModal] = useState(false);

  const [tableOption, setTableOption] = useState<TableOptions<TokenDatum>>({
    data: [],
    columns: [],
    manualSorting: false,
    initialState: {
      sorting: [
        {
          id: 'quantity',
          desc: true,
        },
      ],
      columnVisibility: {
        chain_id: false,
      },
    },
    getPaginationRowModel: getPaginationRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });
  const table = useReactTable(tableOption);

  const [liquidityTableOption, setLiquidityTableOption] = useState<TableOptions<Liquidity>>({
    data: [],
    columns: [],
    getCoreRowModel: getCoreRowModel(),
  });
  const liquidityTable = useReactTable(liquidityTableOption);

  const {
    data: liquidityData,
    error: liquidityError,
    isLoading: liquidityLoading,
  } = apiAssetProExtendHooks.useGetLiquidity(
    {
      params: {
        org_id: orgId ?? -1,
      },
    },
    {
      enabled: !!orgId,
    },
  );

  const {
    data: tokenListData,
    error: tokenListError,
    isLoading: tokenListLoading,
  } = apiAssetProHooks.useGetTokenList();
  const {
    data: kgAccountResp,
    error: kgAccountError,
    isLoading: kgAccountLoading,
  } = apiOrganizationHooks.useGetOrganizationAccounts(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
    },
  );
  const {
    data: tokenPrices,
    error: assetPriceError,
    isLoading: assetPriceLoading,
  } = apiAssetProHooks.useGetAssetPrices(
    {
      queries: { assets_cid: tokenIds },
      paramsSerializer: (params) => qs.stringify(params, { indices: false }),
    },
    {
      enabled: tokenIds.length > 0,
    },
  );

  const chainIds = useMemo(
    () =>
      pipe(
        tokenListData?.data,
        O.fromNullable,
        O.map(A.map((token) => token.chain_id)),
        O.map(A.uniq(S.Eq)),
        O.getOrElseW(() => undefined),
      ),
    [tokenListData],
  );
  const [nonTronChainIds, tronChainIds] = useMemo(() => {
    if (!chainIds) return [undefined, undefined];

    const nonTronChainIds = chainIds.filter((chain) => chain !== 'tron' && chain !== 'shasta');
    const tronChainIds = chainIds.filter((chain) => chain === 'tron' || chain === 'shasta');

    return [nonTronChainIds, tronChainIds];
  }, [chainIds]);
  const evmAddress = useMemo(() => {
    return pipe(
      kgAccountResp?.data,
      O.fromNullable,
      O.flatMap(
        A.findFirst((account) => ASSETPRO_SUPPORTED_EVM_CHAINS.includes(account.chain_id as AssetProEvmChainId)),
      ),
      O.map((account) => account.address),
      O.getOrElseW(() => undefined),
    );
  }, [kgAccountResp]);
  const tronAddress = useMemo(() => {
    return pipe(
      kgAccountResp?.data,
      O.fromNullable,
      O.flatMap(A.findFirst((account) => account.chain_id === 'tron' || account.chain_id === 'shasta')),
      O.map((account) => account.address),
      O.getOrElseW(() => undefined),
    );
  }, [kgAccountResp]);
  const trc20TokensRaw = useMemo(() => {
    return pipe(
      tokenListData?.data,
      O.fromNullable,
      O.map(
        A.filter(
          (token) =>
            ASSETPRO_SUPPORTED_TRON_CHAINS.includes(token.chain_id as AssetProNonEvmChainId) &&
            token.contract_address !== '',
        ),
      ),
      O.getOrElseW(() => undefined),
    );
  }, [tokenListData]);

  const tronAddressTokens = useQueries({
    queries: (tronChainIds ?? []).map((chain) => {
      const params = new URLSearchParams({
        address: tronAddress as string,
        start: '0',
        limit: '20',
        hidden: '0',
        show: '0',
        sortType: '0',
        sortBy: '0',
      });
      const baseUrl = chain === 'tron' ? 'https://apilist.tronscanapi.com/api' : 'https://shastapi.tronscan.org/api';

      return {
        queryKey: ['asset-pro', 'token', chain, tronAddress],
        queryFn: () =>
          fetch(`${baseUrl}/account/tokens?${params.toString()}`)
            .then((res) => res.json())
            .then(
              (res) =>
                res.data as {
                  tokenAbbr: string;
                  balance: string;
                  quantity: string;
                }[],
            ),
        enabled: !!tronChainIds && !!tronAddress,
      };
    }),
  });
  const tronAddressTokensData = useMemo(() => tronAddressTokens.map((token) => token.data), [tronAddressTokens]);
  const tronAddressTokensLoading = useMemo(
    () => tronAddressTokens.some((token) => token.isLoading),
    [tronAddressTokens],
  );
  const tronAddressTokensError = useMemo(() => tronAddressTokens.some((token) => token.isError), [tronAddressTokens]);

  const tronTokens = useMemo(() => {
    if (!tronChainIds || !trc20TokensRaw || tronAddressTokensLoading) return [];

    const nativeTokensRaw = tronChainIds.map((chain) => {
      const { icon, symbol, name, coingeckoId } = getChainInfo(chain);

      return {
        logo_url: icon as string,
        symbol: symbol,
        name: name as string,
        chain_name: getChainFullName(chain) as string,
        contract_address: null,
        decimals: 18,
        chain_id: chain,
        coingecko_id: coingeckoId,
      };
    });

    const tokensRaw = trc20TokensRaw ? [...trc20TokensRaw, ...nativeTokensRaw] : [];

    const tokens = tokensRaw.map((token) => {
      const tokensData = tronAddressTokensData[tronChainIds.indexOf(token.chain_id as AssetProNonEvmChainId)];

      const tokenData = pipe(
        tokensData,
        O.fromNullable,
        O.flatMap(A.findFirst((asset) => asset.tokenAbbr.toLowerCase() === token.symbol.toLowerCase())),
      );
      const balance = pipe(
        tokenData,
        O.map((asset) => BigNumber(asset.balance)),
        O.getOrElseW(() => BigNumber(0)),
      );
      const price = !!tokenPrices ? BigNumber(tokenPrices.data[token.coingecko_id]?.price) : undefined;
      const quantity = pipe(
        tokenData,
        O.map((asset) => BigNumber(asset.quantity)),
        O.getOrElseW(() => BigNumber(0)),
      );
      const value = !!quantity && !!price ? price.times(quantity) : undefined;

      return {
        ...token,
        balance: fixedNumber(balance) || '',
        price: fixedNumber(price, DECIMAL_DISPLAY_MODE.FIAT),
        quantity: fixedNumber(quantity),
        value: fixedNumber(value, DECIMAL_DISPLAY_MODE.FIAT),
      };
    });

    return tokens;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trc20TokensRaw, tronAddressTokensLoading, tronChainIds, tokenPrices]);

  const erc20TokensRaw = useMemo(() => {
    return pipe(
      tokenListData?.data,
      O.fromNullable,
      O.map(
        A.filter(
          (token) =>
            ASSETPRO_SUPPORTED_EVM_CHAINS.includes(token.chain_id as AssetProEvmChainId) &&
            token.contract_address !== '',
        ),
      ),
      O.getOrElseW(() => undefined),
    );
  }, [tokenListData]);
  const erc20TokenBalance = useQueries({
    queries: (erc20TokensRaw ?? []).map((token) => {
      const client = getClient(token.chain_id);

      return {
        queryKey: ['asset-pro', 'token', token.chain_id, token.contract_address, evmAddress],
        queryFn: async () =>
          client?.readContract({
            address: token.contract_address as `0x${string}`,
            abi: ERC20ABI,
            functionName: 'balanceOf',
            args: [evmAddress as `0x${string}`],
          }),
        enabled: !!erc20TokensRaw && !!evmAddress,
      };
    }),
  });
  const erc20TokenBalanceData = useMemo(() => erc20TokenBalance.map((token) => token.data), [erc20TokenBalance]);
  const erc20TokensBalanceLoading = useMemo(
    () => erc20TokenBalance.some((token) => token.isLoading),
    [erc20TokenBalance],
  );
  const erc20TokensBalanceError = useMemo(() => erc20TokenBalance.some((token) => token.isError), [erc20TokenBalance]);

  const erc20Tokens = useMemo(() => {
    if (!erc20TokensRaw || erc20TokensBalanceLoading) return [];

    const tokens = erc20TokensRaw.map((token, index) => {
      const balanceBigint = erc20TokenBalanceData[index];
      const balance = pipe(
        balanceBigint,
        O.fromNullable,
        O.map((balance) => BigNumber(balance.toString())),
        O.getOrElseW(() => undefined),
      );
      const price = !!tokenPrices ? BigNumber(tokenPrices.data[token.coingecko_id]?.price) : undefined;
      const quantity =
        balanceBigint !== undefined ? BigNumber(Number(formatUnits(balanceBigint, token.decimals))) : undefined;
      const value = quantity !== undefined && !!price ? price.times(quantity) : undefined;

      return {
        ...token,
        balance: fixedNumber(balance) || '',
        price: fixedNumber(price, DECIMAL_DISPLAY_MODE.FIAT),
        quantity: fixedNumber(quantity),
        value: fixedNumber(value, DECIMAL_DISPLAY_MODE.FIAT),
      };
    });
    return tokens;
    // add erc20TokensBalanceData to deps will cause infinite loop, not sure why
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [erc20TokensRaw, erc20TokensBalanceLoading, tokenPrices]);

  const nativeTokensBalance = useQueries({
    queries: (nonTronChainIds ?? []).map((chain) => {
      const client = getClient(chain);

      return {
        queryKey: ['asset-pro', 'main-token', chain, evmAddress],
        queryFn: () =>
          client?.getBalance({
            address: evmAddress as `0x${string}`,
          }),
        enabled: !!nonTronChainIds && !!evmAddress,
      };
    }),
  });
  const nativeTokensBalanceData = useMemo(() => nativeTokensBalance.map((token) => token.data), [nativeTokensBalance]);
  const nativeTokensBalanceLoading = useMemo(
    () => nativeTokensBalance.some((token) => token.isLoading),
    [nativeTokensBalance],
  );
  const nativeTokensBalanceError = useMemo(
    () => nativeTokensBalance.some((token) => token.isError),
    [nativeTokensBalance],
  );
  const nativeTokens = useMemo(() => {
    if (!nonTronChainIds || nativeTokensBalanceLoading) return [];

    const tokens = nonTronChainIds.map((chain, index) => {
      const { icon, symbol, name, coingeckoId } = getChainInfo(chain);
      const { icon: ethIcon } = getChainInfo('eth');

      const balanceBigint = nativeTokensBalanceData[index];
      const balance = pipe(
        balanceBigint,
        O.fromNullable,
        O.map((balance) => BigNumber(balance.toString())),
        O.getOrElseW(() => undefined),
      );
      const price = !!tokenPrices ? BigNumber(tokenPrices.data[coingeckoId as string]?.price) : undefined;
      const quantity = balanceBigint !== undefined ? BigNumber(Number(formatUnits(balanceBigint, 18))) : undefined;
      const value = quantity !== undefined && !!price ? price.times(quantity) : undefined;

      return {
        // We use eth as native token for base and optimism because of payment
        logo_url: ['base', 'optimism'].includes(chain) ? (ethIcon as string) : (icon as string),
        symbol: symbol,
        name: name as string,
        chain_name: getChainFullName(chain) as string,
        contract_address: null,
        decimals: 18,
        chain_id: chain,
        balance: fixedNumber(balance) || '',
        price: fixedNumber(price, DECIMAL_DISPLAY_MODE.FIAT),
        quantity: fixedNumber(quantity),
        value: fixedNumber(value, DECIMAL_DISPLAY_MODE.FIAT),
      };
    });
    return tokens;
    // add nativeTokensBalanceData to deps will cause infinite loop, not sure why
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nativeTokensBalanceLoading, nonTronChainIds, tokenPrices]);

  const allTokens = useMemo(
    () => [...erc20Tokens, ...nativeTokens, ...tronTokens] as TokenDatum[],
    [erc20Tokens, nativeTokens, tronTokens],
  );
  const allChains = useMemo(
    () =>
      uniqBy(
        allTokens.map(({ chain_id, chain_name }) => ({ label: chain_name as string, value: chain_id as string })),
        'value',
      ),
    [allTokens],
  );
  const allBalances = useMemo(
    () => map(allTokens, (item) => pick(item, ['symbol', 'chain_id', 'quantity'])),
    [allTokens],
  );
  const totalBalance = useMemo(
    () =>
      formatCurrency({ amount: sumBy(allTokens, 'value'), decimals: DECIMAL_DISPLAY_MODE.FIAT, fmt: { prefix: '$' } }),
    [allTokens],
  );

  const columns: ColumnDef<TokenDatum>[] = useMemo(
    () => [
      {
        accessorKey: 'symbol',
        id: 'symbol',
        maxSize: 240,
        size: 180,
        header: t('kgstudio.treasury.asset'),
        meta: {
          sortable: true,
        },
        filterFn: 'includesString',
        cell: ({ row }) => {
          const { logo_url, symbol, chain_name, chain_id } = row.original;
          return (
            <div className="flex items-center gap-3">
              <div className="relative h-9 w-9 shrink-0">
                {typeof logo_url === 'string' ? (
                  <Image src={logo_url} alt={symbol} fill className="rounded-full" />
                ) : (
                  <div className="relative h-full w-full overflow-hidden rounded-full">{logo_url}</div>
                )}

                <div className="absolute bottom-[-2px] right-0 h-1/2 w-1/2 overflow-hidden rounded-full border-[2px] border-white">
                  {<Image src={getChainIcon(chain_id) ?? ''} alt={chain_id} fill className="object-cover" />}
                </div>
              </div>

              <div className="flex flex-col justify-center">
                <p className="text-body-2-bold text-primary">{symbol}</p>
                {chain_name && <p className="text-small text-secondary">{chain_name}</p>}
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'chain_id',
        id: 'chain_id',
        maxSize: 0,
        header: '',
      },
      {
        accessorKey: 'price',
        size: 120,
        header: t('kgstudio.treasury.price'),
        cell: ({ row }) => {
          const { price } = row.original;
          const emptyPrice = Number.isNaN(price) || price === undefined;
          return `${
            emptyPrice
              ? 'unknown'
              : `${formatCurrency({ amount: price, decimals: DECIMAL_DISPLAY_MODE.FIAT, fmt: { prefix: '$' } })}`
          }`;
        },
      },
      {
        accessorKey: 'quantity',
        size: 180,
        header: t('kgstudio.data.balance'),
        meta: {
          sortable: true,
        },
        sortUndefined: 'last',
        cell: ({ row }) => {
          const { quantity } = row.original;
          return quantity === undefined ? quantity : formatCurrency({ amount: quantity });
        },
      },
      {
        accessorKey: 'value',
        header: t('kgstudio.treasury.value'),
        size: 180,
        meta: {
          sortable: true,
        },
        sortUndefined: 'last',
        cell: ({ row }) => {
          const { value } = row.original;
          return value === undefined
            ? value
            : `${formatCurrency({ amount: value, decimals: DECIMAL_DISPLAY_MODE.FIAT, fmt: { prefix: '$' } })}`;
        },
      },
      {
        accessorKey: 'action',
        header: '',
        size: 60,
        cell: ({ row }) => {
          const { chain_id } = row.original;
          const chain = chain_id === 'tron' || chain_id === 'shasta' ? 'tron' : 'evm';
          return (
            <Button
              size="sm"
              variant="grey"
              icon={<Plus className="stroke-secondary" strokeWidth={3} size={12} />}
              onClick={() => handleModalOpen(chain)}
            />
          );
        },
      },
    ],
    [t],
  );
  const tableLoading =
    tokenListLoading ||
    kgAccountLoading ||
    erc20TokensBalanceLoading ||
    nativeTokensBalanceLoading ||
    assetPriceLoading;

  const handleModalOpen = (chainType: 'tron' | 'evm') => {
    setQrCodeModal({ open: true, chainType });
  };

  const handleCopyAddress = (address: string, t: any) => {
    navigator.clipboard.writeText(address);
    showToast(t('kgstudio.common.address-copied'), 'success');
  };

  const directFinance = () => {
    router.push('/asset/finance');
  };

  useEffect(() => {
    if (!erc20TokensRaw || !nonTronChainIds || !trc20TokensRaw || !tronChainIds) return;

    const erc20TokenIds = erc20TokensRaw.map((token) => token.coingecko_id);
    const nativeTokenIds = nonTronChainIds.map((chain) => getChainInfo(chain).coingeckoId);
    const trc20TokenIds = trc20TokensRaw.map((token) => token.coingecko_id);
    const tronNativeTokenIds = tronChainIds?.map((chain) => getChainInfo(chain).coingeckoId);

    const tokenIds = [...erc20TokenIds, ...nativeTokenIds, ...trc20TokenIds, ...tronNativeTokenIds];
    setTokenIds(uniq(tokenIds));
  }, [nonTronChainIds, erc20TokensRaw, trc20TokensRaw, tronChainIds]);

  useEffect(() => {
    if (tableTab === 'assets') {
      setTableOption((prev) => ({
        ...prev,
        data: allTokens,
        columns,
      }));
    } else {
      if (!liquidityData) return;
      setLiquidityTableOption((prev) => ({
        ...prev,
        data: liquidityData.data,
        columns: LiquidityTableColumn({
          t,
          allBalances,
          hasReadStatisticsPermission,
          actionFn: directFinance,
          setQrCodeModal,
        }),
      }));
    }
  }, [allTokens, columns, t, tableTab, liquidityData]);

  useEffect(() => {
    if (!erc20TokensBalanceError) return;

    const errors = erc20TokenBalance.filter((token) => token.isError).map((token) => token.error);
    console.error(errors);

    showToast(t('kgstudio.treasury.retrieve-balance-error'), 'error');
  }, [erc20TokensBalanceError, erc20TokenBalance, t]);

  useEffect(() => {
    if (!nativeTokensBalanceError) return;

    const errors = nativeTokensBalance.filter((token) => token.isError).map((token) => token.error);
    console.error(errors);

    showToast(t('kgstudio.treasury.retrieve-balance-error'), 'error');
  }, [nativeTokensBalanceError, nativeTokensBalance, t]);

  useEffect(() => {
    if (!tronAddressTokensError) return;

    const errors = tronAddressTokens.filter((token) => token.isError).map((token) => token.error);
    console.error(errors);

    showToast(t('kgstudio.treasury.retrieve-balance-error'), 'error');
  }, [tronAddressTokensError, tronAddressTokens, t]);

  useEffect(() => {
    if (!kgAccountError && !tokenListError && !assetPriceError && !liquidityError) return;

    const error = kgAccountError || tokenListError || assetPriceError || liquidityError;

    console.error(error);
    if (isApiError(kgAccountError || tokenListError || assetPriceError || liquidityError)) {
      showToast(t('kgstudio.treasury.retrieve-balance-error'), 'error');
    }
  }, [kgAccountError, tokenListError, assetPriceError, liquidityError, t]);

  useEffect(() => {
    table.getColumn('symbol')?.setFilterValue(watchedToken);
    table.getColumn('chain_id')?.setFilterValue(watchedChain);
  }, [table, watchedToken, watchedChain]);

  // Retrieve the actual seed phrase
  const retrieveMnemonicMutation = apiWalletHooks.useRetrieveOrganizationWalletMnemonic(
    { params: { org_id: orgId ?? -1 } },
    {
      onSuccess: (data) => {
        setSeedPhrase(data.data.encrypted_mnemonic);
        setSeedPhraseModal((prev) => ({ ...prev, isLoading: false }));
      },
      onError: (error) => {
        console.error('Failed to retrieve mnemonic:', error);
        showToast(t('kgstudio.treasury.failed-to-retrieve-seedphrase'), 'error');
        setSeedPhraseModal((prev) => ({ ...prev, isLoading: false }));
      },
    },
  );

  // Function to fetch seed phrase
  const fetchSeedPhrase = async () => {
    if (!orgId) return;

    setSeedPhraseModal((prev) => ({ ...prev, isLoading: true }));
    try {
      // Generate an X25519 key pair for secure exchange
      const { privateKey, publicKey } = await generateX25519KeyPair();

      // Send the public key to server
      const response = await retrieveMnemonicMutation.mutateAsync({
        wallet_type: 'evm',
        public_key: publicKey,
      });
      // Decrypt the received encrypted mnemonic
      const decrypted = await decryptMnemonic(privateKey, response.data.encrypted_mnemonic, response.data.public_key);
      setSeedPhrase(decrypted);

      setSeedPhraseModal((prev) => ({ ...prev, isLoading: false }));
    } catch (error) {
      console.error('Error retrieving mnemonic:', error);
      showToast(t('kgstudio.treasury.failed-to-retrieve-seedphrase'), 'error');
      setSeedPhraseModal((prev) => ({ ...prev, isLoading: false }));
    }
  };

  return (
    <div className="space-y-6">
      <Card className="flex flex-col justify-between bg-white p-4 md:!flex-row lg:!p-6">
        <div className="flex w-full flex-col justify-center gap-4">
          <div className="space-y-1">
            <p className="text-caption-bold text-primary">{t('kgstudio.data.total-balance')}</p>
            <div className="flex flex-wrap items-center gap-3">
              {totalBalance ? (
                <p className="text-h2 text-primary font-bold">{totalBalance}</p>
              ) : (
                <Skeleton className="h-7 w-[120px]" />
              )}
              <Button
                size="sm"
                variant="secondary"
                icon={<Plus className="h-4 w-4 !stroke-[var(--brand-primary-dark)] stroke-[4px]" />}
                onClick={() => handleModalOpen('evm')}
              >
                {t('kgstudio.common.add-fund')}
              </Button>
            </div>
          </div>
          <div className="space-y-2">
            {evmAddress && (
              <div className="flex items-center gap-2">
                <Image src={evmWalletIcon} alt="evm-wallet" width={16} height={16} />
                <p className="text-caption-bold text-primary">{t('kgstudio.data.evm-wallet')}</p>
                <p className="text-small text-disabled">
                  {deviceSize !== 'sm' ? evmAddress : truncateTxhashOrAddress(evmAddress)}
                </p>
                <Button
                  size="sm"
                  variant="text"
                  className="size-4"
                  icon={<Copy className="!stroke-disabled" />}
                  onClick={() => handleCopyAddress(evmAddress, t)}
                />
              </div>
            )}
            {tronAddress && (
              <div className="flex items-center gap-2">
                <Image className="rounded-sm" src={tronIcon} alt="tron-wallet" width={16} height={16} />
                <p className="text-caption-bold text-primary">{t('kgstudio.data.tron-wallet')}</p>
                <p className="text-small text-disabled">
                  {deviceSize !== 'sm' ? tronAddress : truncateTxhashOrAddress(tronAddress || '')}
                </p>
                <Button
                  size="sm"
                  variant="text"
                  className="size-4"
                  icon={<Copy className="!stroke-disabled" />}
                  onClick={() => handleCopyAddress(tronAddress, t)}
                />
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button
            className="w-max"
            variant="secondary"
            size="lg"
            icon={<ArrowUpRight className="h-4 w-4" />}
            onClick={() => {
              setSeedPhraseModal({ open: true, showPhrase: false, step: 'warning', isLoading: false });
              setSeedPhrase(null);
            }}
          >
            {t('kgstudio.common.export-private-key')}
          </Button>
          <Button
            className="w-max"
            size="lg"
            icon={<ArrowUpRight className="h-4 w-4" />}
            onClick={() => {
              router.push('/asset/transfer');
            }}
          >
            {t('kgstudio.common.transfer')}
          </Button>
        </div>
        <Modal open={qrCodeModal.open} onOpenChange={(open) => setQrCodeModal((prev) => ({ ...prev, open }))}>
          <Modal.Content className="!w-[90%] rounded-xl md:max-w-[384px]">
            <div className="flex flex-col items-center gap-6 text-center">
              <p className="text-h2 text-primary">{t('kgstudio.treasury.add-fund')}</p>
              <p className="text-h3 text-primary">{t('kgstudio.treasury.add-fund-modal.desc')}</p>
              <Tabs
                className="w-full"
                value={qrCodeModal.chainType}
                onValueChange={(value) => setQrCodeModal((prev) => ({ ...prev, chainType: value }))}
              >
                <Tabs.List>
                  <Tabs.Trigger value="evm">
                    <div className="flex items-center gap-1">
                      <Image src={evmWalletIcon} alt="evm-wallet" width={16} height={16} />
                      <p
                        className={cn('text-button-md text-primary font-bold', {
                          'text-disabled': qrCodeModal.chainType !== 'evm',
                        })}
                      >
                        {t('kgstudio.data.evm-wallet')}
                      </p>
                    </div>
                  </Tabs.Trigger>
                  <Tabs.Trigger value="tron">
                    <div className="flex items-center gap-1">
                      <Image className="rounded-sm" src={tronIcon} alt="tron-wallet" width={16} height={16} />
                      <p
                        className={cn('text-button-md text-primary font-bold', {
                          'text-disabled': qrCodeModal.chainType !== 'tron',
                        })}
                      >
                        {t('kgstudio.data.tron-wallet')}
                      </p>
                    </div>
                  </Tabs.Trigger>
                </Tabs.List>

                <Tabs.Content value={qrCodeModal.chainType}>
                  <div className="flex flex-col items-center gap-6">
                    <QRCodeCanvas
                      size={205}
                      level="H"
                      value={(qrCodeModal.chainType === 'evm' ? evmAddress : tronAddress) as string}
                    />
                    <p className="text-body-bold text-primary break-all">
                      {qrCodeModal.chainType === 'evm' ? evmAddress : tronAddress}
                      <Button
                        size="sm"
                        variant="text"
                        className="ml-1 size-4"
                        icon={<Copy className="!stroke-disabled" />}
                        onClick={() => {
                          const address = (qrCodeModal.chainType === 'evm' ? evmAddress : tronAddress) as string;
                          handleCopyAddress(address, t);
                        }}
                      />
                    </p>
                    <div className="flex flex-col items-center gap-3 text-center">
                      <p className="text-small text-placeholder">{t('kgstudio.common.supported-chains')}</p>
                      <div className="flex w-full flex-wrap items-center justify-center gap-[10px]">
                        {chainIds
                          ?.filter((chain) =>
                            qrCodeModal.chainType === 'evm'
                              ? ASSETPRO_SUPPORTED_EVM_CHAINS.includes(chain as AssetProEvmChainId)
                              : ASSETPRO_SUPPORTED_TRON_CHAINS.includes(chain as AssetProNonEvmChainId),
                          )
                          .map((chain) => (
                            <ChainBadge
                              chain={chain === 'eth' ? 'ethereum' : chain === 'matic' ? 'polygon' : chain}
                              key={chain}
                            />
                          ))}
                      </div>
                    </div>
                  </div>
                </Tabs.Content>
              </Tabs>
            </div>
          </Modal.Content>
        </Modal>

        {/* Seed Phrase Export Modal */}
        <Modal
          open={seedPhraseModal.open}
          onOpenChange={(open) =>
            setSeedPhraseModal((prev) => ({ ...prev, open: open, showPhrase: false, step: 'warning' }))
          }
        >
          <Modal.Content className="!w-[90%] rounded-xl md:max-w-[500px]">
            <div className="flex flex-col items-center gap-6 p-4">
              <div className="w-full text-center">
                <p className="text-h2 text-primary mb-4">{t('kgstudio.common.export-private-key')}</p>

                {seedPhraseModal.step === 'warning' ? (
                  <>
                    <div className="mb-6 rounded-lg border border-yellow-300 bg-yellow-50 p-4 text-left">
                      <p className="text-body-bold mb-2 text-yellow-800">
                        {t('kgstudio.treasury.seedphrase-warning-title')}
                      </p>
                      <ul className="text-body list-disc space-y-2 pl-5 text-yellow-700">
                        <li>{t('kgstudio.treasury.seedphrase-warning-1')}</li>
                        <li>{t('kgstudio.treasury.seedphrase-warning-2')}</li>
                        <li>{t('kgstudio.treasury.seedphrase-warning-3')}</li>
                        <li>{t('kgstudio.treasury.seedphrase-warning-4')}</li>
                      </ul>
                    </div>
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={() => {
                        setSeedPhraseModal((prev) => ({ ...prev, step: 'phrase' }));
                        fetchSeedPhrase();
                      }}
                    >
                      {t('kgstudio.treasury.agree-and-continue')}
                    </Button>
                  </>
                ) : (
                  <>
                    <p className="text-body text-secondary mb-4">{t('kgstudio.treasury.click-to-reveal')}</p>
                    <div className="relative mb-6 w-full">
                      <div
                        className={cn('w-full break-all rounded-lg bg-gray-100 p-6 text-center', {
                          'blur-sm': !seedPhraseModal.showPhrase,
                        })}
                      >
                        {seedPhraseModal.isLoading ? (
                          <div className="flex justify-center p-4">
                            <Skeleton className="h-6 w-[300px]" />
                          </div>
                        ) : (
                          seedPhrase || t('kgstudio.treasury.no-seedphrase-available')
                        )}
                      </div>
                      {!seedPhraseModal.showPhrase && !seedPhraseModal.isLoading && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Button
                            variant="primary"
                            size="lg"
                            icon={<Eye className="h-4 w-4" />}
                            onClick={() => setSeedPhraseModal((prev) => ({ ...prev, showPhrase: true }))}
                          >
                            {t('kgstudio.treasury.reveal-seedphrase')}
                          </Button>
                        </div>
                      )}
                    </div>
                    <div className="flex w-full gap-3">
                      <Button
                        className="flex-1"
                        variant="secondary"
                        size="lg"
                        icon={<Copy className="h-4 w-4" />}
                        onClick={() => {
                          if (seedPhrase) {
                            navigator.clipboard.writeText(seedPhrase);
                            showToast(t('kgstudio.common.successfully-copied'), 'success');
                          }
                        }}
                        disabled={seedPhraseModal.isLoading || !seedPhrase}
                      >
                        {t('common.copy')}
                      </Button>
                      <Button
                        className="flex-1"
                        size="lg"
                        onClick={() =>
                          setSeedPhraseModal({ open: false, showPhrase: false, step: 'warning', isLoading: false })
                        }
                      >
                        {t('kgstudio.common.close')}
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </Modal.Content>
        </Modal>
      </Card>

      {hasReadLiquidityPermission && (
        <Tabs
          className="w-full"
          value={tableTab}
          onValueChange={(value) => setTableTab(value as TableTab)}
          data-cy="table-tab"
        >
          <Tabs.List>
            <Tabs.Trigger value="assets">
              <p
                className={cn('text-button-md text-primary font-bold', {
                  'text-disabled': tableTab === 'liquidity',
                })}
              >
                {t('kgstudio.common.assets')}
              </p>
            </Tabs.Trigger>
            <Tabs.Trigger value="liquidity">
              <p
                className={cn('text-button-md text-primary font-bold', {
                  'text-disabled': tableTab === 'assets',
                })}
              >
                {t('kgstudio.common.liquidity')}
              </p>
            </Tabs.Trigger>
          </Tabs.List>
        </Tabs>
      )}

      <Card className="!p-0">
        {tableTab === 'assets' ? (
          <>
            <div className="gap-list-medium flex flex-col justify-between p-4 md:p-6">
              <h2 className="text-h2 text-primary">{t('kgstudio.common.assets')}</h2>
              <Form {...form}>
                <form className="flex items-center gap-3">
                  <div className="relative w-[255px]">
                    <FormInput
                      className="*:py-[6px]"
                      name="token"
                      control={form.control}
                      placeholder={t('kgstudio.treasury.asset-name')}
                      data-cy="treausry-token-filter"
                    />
                    <Search className="absolute right-3 top-1/2 size-4 -translate-y-1/2 stroke-[var(--text-placeholder)]" />
                  </div>
                  <FormDropdown
                    className="w-[150px] *:h-[38px] *:min-h-[38px]"
                    name="chain"
                    control={form.control}
                    key={form.watch('chain')}
                    options={allChains}
                    placeholder={t('kgstudio.treasury.network')}
                  />
                  <Button
                    variant="text"
                    type="button"
                    className="text-secondary active:text-secondary"
                    icon={<X size={16} className="!stroke-[var(--text-secondary)]" />}
                    onClick={() => {
                      form.reset();
                      table.getAllColumns().forEach((column) => column.clearSorting());
                    }}
                  >
                    {t('kgstudio.common.clear-filter')}
                  </Button>
                </form>
              </Form>
            </div>
            <DataTable table={table} isLoading={tableLoading} dataLength={allTokens?.length || 0} />
          </>
        ) : (
          <>
            <div className="flex items-center justify-between p-4 md:p-6">
              <h2 className="text-h2 text-primary">{t('kgstudio.common.liquidity')}</h2>
              <div className="flex items-center gap-3">
                {hasEditLiquidityPermission && (
                  <Button
                    size="md"
                    variant="primary"
                    icon={<Settings2 />}
                    onClick={() => router.push('/asset/finance/settings')}
                  >
                    {t('kgstudio.treasury.profit-margin-setting')}
                  </Button>
                )}
              </div>
            </div>
            <DataTable
              table={liquidityTable}
              isLoading={liquidityLoading}
              dataLength={liquidityData?.data.length || 0}
            />
          </>
        )}
        <LiquidityModal
          open={openLiquidityModal}
          onOpenChange={setOpenLiquidityModal}
          gasSwapMargin={liquidityData?.data.find((d) => d.liquidity_type === 'gas_swap')?.profit_margin || 0}
          buyCryptoMargin={liquidityData?.data.find((d) => d.liquidity_type === 'buy_crypto')?.profit_margin || 0}
        />
      </Card>
    </div>
  );
}
