import BigNumber from 'bignumber.js';
import { TrendingUp } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { amountInputInterceptor } from '@/app/[locale]/(protected)/asset/_lib/utils';
import { FormInput } from '@/app/_common/components/form';
import { isApiError } from '@/app/_common/lib/api';
import { apiAssetProExtendHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal, ConfirmationModal } from '@kryptogo/2b';

interface LiquidityModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gasSwapMargin: number;
  buyCryptoMargin: number;
}

const GAS_SWAP_MAX_PROFIT = 50;
const BUT_CRYPTO_MAX_PROFIT = 47.5;

const EditLiquiditySchema = (t: any) =>
  z.object({
    gas_swap: z.string().refine((value) => Number(value) >= 1 && Number(value) <= GAS_SWAP_MAX_PROFIT, {
      message: t('kgstudio.error.out-of-range'),
    }),
    buy_crypto: z.string().refine((value) => Number(value) >= 1 && Number(value) <= BUT_CRYPTO_MAX_PROFIT, {
      message: t('kgstudio.error.out-of-range'),
    }),
  });
type EditLiquidity = z.infer<ReturnType<typeof EditLiquiditySchema>>;

const LiquidityModal = ({ gasSwapMargin, buyCryptoMargin, ...props }: LiquidityModalProps) => {
  const t = useTranslations();
  const orgId = useOrganizationStore((state) => state.orgId);
  const gasSwapMarginPercent = BigNumber(gasSwapMargin).multipliedBy(100).toString();
  const buyCryptoMarginPercent = BigNumber(buyCryptoMargin).multipliedBy(100).toString();

  const [confirmEditModalOpen, setConfirmEditModalOpen] = useState(false);

  const { mutate: editProfitMargin, isLoading: editProfitMarginLoading } = apiAssetProExtendHooks.useEditProfitMargin(
    {
      params: {
        org_id: orgId ?? -1,
      },
    },
    {
      meta: {
        awaitInvalidates: [apiAssetProExtendHooks.getKeyByAlias('getLiquidity')],
      },
      onSuccess: () => {
        setConfirmEditModalOpen(false);
        props.onOpenChange(false);
        toast.success(t('kgstudio.treasury.liquidity-update-success'));
      },
      onError: (error) => {
        if (isApiError(error) && error.status === 400) {
          toast.error(t('kgstudio.error.out-of-range'));
        } else {
          toast.error(t('kgstudio.common.error'));
        }
      },
    },
  );

  const form = useForm<EditLiquidity>({
    values: {
      gas_swap: gasSwapMarginPercent,
      buy_crypto: buyCryptoMarginPercent,
    },
    resolver: zodResolver(EditLiquiditySchema(t)),
    mode: 'onChange',
  });

  const onSubmit = () => {
    const formData = form.getValues();

    editProfitMargin({
      profit_margin: {
        gas_swap: BigNumber(formData.gas_swap).dividedBy(100).toNumber(),
        buy_crypto: BigNumber(formData.buy_crypto).dividedBy(100).toNumber(),
      },
    });
  };

  return (
    <>
      <Modal {...props}>
        <Modal.Content className="max-h-[90%]">
          <Modal.Header>
            <Modal.Title>{t('kgstudio.treasury.liquidity-settings')}</Modal.Title>
          </Modal.Header>
          <Form {...form}>
            <form onSubmit={() => setConfirmEditModalOpen(true)} className="flex flex-col gap-6">
              <div className="space-y-2">
                <div className="flex items-center gap-[10px]">
                  <TrendingUp size={24} className="stroke-[var(--text-primary)]" />
                  <h2 className="text-h2 text-primary">{t('kgstudio.treasury.profit-margin')}</h2>
                </div>
                <p className="text-body-2 text-secondary">{t('kgstudio.treasury.profit-margin-desc')}</p>
              </div>
              <FormInput
                name="gas_swap"
                title={t('kgstudio.treasury.gas-swap.margin')}
                data-cy="gas-swap-input"
                control={form.control}
                required
                onInput={(e) => amountInputInterceptor(e, 2)}
                hint={
                  <p className="">
                    {t('kgstudio.treasury.liquidity-settings-min-max', {
                      min: 1,
                      max: GAS_SWAP_MAX_PROFIT,
                    })}
                  </p>
                }
              />
              <FormInput
                name="buy_crypto"
                title={t('kgstudio.treasury.buy-crypto.margin')}
                data-cy="buy-crypto-input"
                control={form.control}
                required
                onInput={(e) => amountInputInterceptor(e, 2)}
                hint={
                  <p className="">
                    {t('kgstudio.treasury.liquidity-settings-min-max', {
                      min: 1,
                      max: BUT_CRYPTO_MAX_PROFIT,
                    })}
                  </p>
                }
              />
              <Modal.Footer className="mt-14 flex justify-between">
                <Button type="button" variant="grey" onClick={() => props.onOpenChange(false)}>
                  {t('common.cancel')}
                </Button>
                <Button
                  type="button"
                  className="md:w-[152px]"
                  data-cy="update-liquidity-btn"
                  loading={editProfitMarginLoading}
                  disabled={!form.formState.isValid}
                  onClick={() => setConfirmEditModalOpen(true)}
                >
                  {t('common.update')}
                </Button>
              </Modal.Footer>
            </form>
          </Form>
        </Modal.Content>
      </Modal>
      <ConfirmationModal
        open={confirmEditModalOpen}
        onOpenChange={setConfirmEditModalOpen}
        title={t('kgstudio.treasury.liquidity-update-confirm.text')}
        desc={t('kgstudio.treasury.liquidity-update-confirm.message')}
        cancelState={{
          text: t('common.cancel'),
        }}
        confirmState={{
          text: t('kgstudio.treasury.liquidity-update-confirm.btn'),
          variant: 'primary',
        }}
        onConfirm={onSubmit}
      />
    </>
  );
};

export { LiquidityModal };
