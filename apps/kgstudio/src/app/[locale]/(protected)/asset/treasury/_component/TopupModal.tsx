import * as A from 'fp-ts/Array';
import * as O from 'fp-ts/Option';
import { pipe } from 'fp-ts/lib/function';
import * as S from 'fp-ts/string';
import { Copy } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { QRCodeCanvas } from 'qrcode.react';
import { useEffect, useMemo } from 'react';

import { showToast } from '@/2b/toast';
import { ChainBadge } from '@/app/_common/components/badge';
import { isApiError } from '@/app/_common/lib/api';
import { cn } from '@/app/_common/lib/utils';
import { apiAssetProHooks, apiOrganizationHooks } from '@/app/_common/services';
import { AssetProEvmChainId, AssetProNonEvmChainId } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import tronIcon from '@/assets/chains/icon-tron.png';
import { <PERSON><PERSON>, <PERSON><PERSON>, Tabs } from '@kryptogo/2b';
import { ASSETPRO_SUPPORTED_EVM_CHAINS, ASSETPRO_SUPPORTED_TRON_CHAINS } from '@kryptogo/utils';

import evmWalletIcon from '../_assets/evmWallet.png';

interface TopupModalProps {
  qrCodeModal: {
    open: boolean;
    chainType: string;
  };
  setQrCodeModal: (value: any) => void;
}

const handleCopyAddress = (address: string, t: any) => {
  navigator.clipboard.writeText(address);
  showToast(t('kgstudio.common.address-copied'), 'success');
};

const TopupModal = (props: TopupModalProps) => {
  const t = useTranslations();
  const [orgId] = useOrganizationStore((state) => [state.orgId]);
  const { qrCodeModal, setQrCodeModal } = props;

  const {
    data: kgAccountResp,
    error: kgAccountError,
    isLoading: kgAccountLoading,
  } = apiOrganizationHooks.useGetOrganizationAccounts(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
    },
  );
  const {
    data: tokenListData,
    error: tokenListError,
    isLoading: tokenListLoading,
  } = apiAssetProHooks.useGetTokenList();

  const evmAddress = useMemo(() => {
    return pipe(
      kgAccountResp?.data,
      O.fromNullable,
      O.flatMap(
        A.findFirst((account) => ASSETPRO_SUPPORTED_EVM_CHAINS.includes(account.chain_id as AssetProEvmChainId)),
      ),
      O.map((account) => account.address),
      O.getOrElseW(() => undefined),
    );
  }, [kgAccountResp]);

  const tronAddress = useMemo(() => {
    return pipe(
      kgAccountResp?.data,
      O.fromNullable,
      O.flatMap(A.findFirst((account) => account.chain_id === 'tron' || account.chain_id === 'shasta')),
      O.map((account) => account.address),
      O.getOrElseW(() => undefined),
    );
  }, [kgAccountResp]);

  const chainIds = useMemo(
    () =>
      pipe(
        tokenListData?.data,
        O.fromNullable,
        O.map(A.map((token) => token.chain_id)),
        O.map(A.uniq(S.Eq)),
        O.getOrElseW(() => undefined),
      ),
    [tokenListData],
  );

  useEffect(() => {
    if (!kgAccountError && !tokenListError) return;

    const error = kgAccountError || tokenListError;

    console.error(error);
    if (isApiError(kgAccountError || tokenListError)) {
      showToast(t('kgstudio.common.error'), 'error');
    }
  }, [kgAccountError, tokenListError, t]);

  return (
    <Modal open={qrCodeModal.open} onOpenChange={(open) => setQrCodeModal((prev: any) => ({ ...prev, open }))}>
      <Modal.Content className="!w-[90%] rounded-xl md:max-w-[384px]">
        <div className="flex flex-col items-center gap-6 text-center">
          <p className="text-h2 text-primary">{t('kgstudio.treasury.add-fund')}</p>
          <p className="text-h3 text-primary">{t('kgstudio.treasury.add-fund-modal.desc')}</p>
          <Tabs
            className="w-full"
            value={qrCodeModal.chainType}
            onValueChange={(value) => setQrCodeModal((prev: any) => ({ ...prev, chainType: value }))}
          >
            <Tabs.List>
              <Tabs.Trigger value="evm">
                <div className="flex items-center gap-1">
                  <Image src={evmWalletIcon} alt="evm-wallet" width={16} height={16} />
                  <p
                    className={cn('text-button-md text-primary font-bold', {
                      'text-disabled': qrCodeModal.chainType !== 'evm',
                    })}
                  >
                    {t('kgstudio.data.evm-wallet')}
                  </p>
                </div>
              </Tabs.Trigger>
              <Tabs.Trigger value="tron">
                <div className="flex items-center gap-1">
                  <Image className="rounded-sm" src={tronIcon} alt="tron-wallet" width={16} height={16} />
                  <p
                    className={cn('text-button-md text-primary font-bold', {
                      'text-disabled': qrCodeModal.chainType !== 'tron',
                    })}
                  >
                    {t('kgstudio.data.tron-wallet')}
                  </p>
                </div>
              </Tabs.Trigger>
            </Tabs.List>

            <Tabs.Content value={qrCodeModal.chainType}>
              <div className="flex flex-col items-center gap-6">
                <QRCodeCanvas
                  size={205}
                  level="H"
                  value={(qrCodeModal.chainType === 'evm' ? evmAddress : tronAddress) as string}
                />
                <p className="text-body-bold text-primary break-all">
                  {qrCodeModal.chainType === 'evm' ? evmAddress : tronAddress}
                  <Button
                    size="sm"
                    variant="text"
                    className="ml-1 size-4"
                    icon={<Copy className="!stroke-disabled" />}
                    onClick={() => {
                      const address = (qrCodeModal.chainType === 'evm' ? evmAddress : tronAddress) as string;
                      handleCopyAddress(address, t);
                    }}
                  />
                </p>
                <div className="flex flex-col items-center gap-3 text-center">
                  <p className="text-small text-placeholder">{t('kgstudio.common.supported-chains')}</p>
                  <div className="flex w-full flex-wrap items-center justify-center gap-[10px]">
                    {chainIds
                      ?.filter((chain) =>
                        qrCodeModal.chainType === 'evm'
                          ? ASSETPRO_SUPPORTED_EVM_CHAINS.includes(chain as AssetProEvmChainId)
                          : ASSETPRO_SUPPORTED_TRON_CHAINS.includes(chain as AssetProNonEvmChainId),
                      )
                      .map((chain) => (
                        <ChainBadge
                          chain={chain === 'eth' ? 'ethereum' : chain === 'matic' ? 'polygon' : chain}
                          key={chain}
                        />
                      ))}
                  </div>
                </div>
              </div>
            </Tabs.Content>
          </Tabs>
        </div>
      </Modal.Content>
    </Modal>
  );
};

export default TopupModal;
