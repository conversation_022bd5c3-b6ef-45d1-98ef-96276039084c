'use client';

import { VariantProps, cva } from 'class-variance-authority';
import { motion } from 'framer-motion';
import { DownloadIcon, ZoomIn } from 'lucide-react';
import React, { useId, useState } from 'react';

import { cn, formatFileSize } from '@/app/_common/lib/utils';
import { Skeleton } from '@kryptogo/2b';
import { useQuery } from '@tanstack/react-query';

const imageViewerVariants = cva(cn('border-primary h-full w-full rounded-xl border'), {
  variants: {
    size: {
      sm: 'w-[160px] h-[172px] min-w-[160px] min-h-[172px] [&>div[data-state=closed]]:w-full [&>div[data-state=closed]]:h-[120px]',
      md: 'w-[200px] h-[202px] min-w-[200px] min-h-[202px] [&>div[data-state=closed]]:w-full [&>div[data-state=closed]]:h-[150px]',
    },
  },
  defaultVariants: {
    size: 'sm',
  },
});

interface ImageViewerProps extends React.ComponentPropsWithoutRef<'div'>, VariantProps<typeof imageViewerVariants> {
  imageUrl: string;
  imageAlt?: string;
}

const ImageViewer = React.forwardRef<HTMLDivElement, ImageViewerProps>(
  ({ imageUrl, imageAlt, className, size, ...props }, ref) => {
    const [imageLoading, setImageLoading] = useState(true);
    const [open, setOpen] = useState(false);
    const randomId = useId();

    const { data: imageBlob } = useQuery({
      queryKey: ['image', imageUrl],
      queryFn: async () => fetch(imageUrl).then((r) => r.blob()),
      cacheTime: Infinity,
    });
    const imageSize = imageBlob && formatFileSize(imageBlob.size);
    const imageExtension = imageBlob && imageBlob.type.split('/')[1];

    const fileName = imageExtension && `image-${randomId.replace(':', '')}.${imageExtension}`;

    const handleToggleImage = () => {
      setOpen((prev) => !prev);
    };
    const handleImageViewer = () => {
      setOpen(true);
    };
    const handleCloseImage = () => {
      setOpen(false);
    };

    const handleDownloadImage = (fileName: string) => {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    return (
      <div
        ref={ref}
        className={cn(imageViewerVariants({ size }), className)}
        data-state={open ? 'open' : 'closed'}
        {...props}
      >
        <div
          className={cn('border-primary relative border-b', {
            'z-front fixed inset-0 cursor-zoom-out': open,
          })}
          data-state={open ? 'open' : 'closed'}
        >
          <motion.div
            animate={{ opacity: open ? 0.5 : 0, display: open ? 'block' : 'none' }}
            className={cn('h-full w-full select-none bg-[var(--text-primary)] backdrop-blur-md', {
              'pointer-events-none': !open,
            })}
            onClick={handleCloseImage}
          />

          <motion.img
            layout
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 120,
            }}
            className={cn(
              'rounded-inherit absolute inset-0 h-full w-full select-none object-cover',
              !open && 'cursor-zoom-in',
              '[&[data-state="open"]]:m-auto [&[data-state="open"]]:h-auto [&[data-state="open"]]:max-h-[80%] [&[data-state="open"]]:w-auto [&[data-state="open"]]:max-w-[80%]',
            )}
            data-state={open ? 'open' : 'closed'}
            src={imageUrl}
            alt={imageAlt ?? 'image'}
            onClick={handleToggleImage}
            onLoad={() => setImageLoading(false)}
          />

          {imageLoading && <Skeleton className="rounded-inherit absolute inset-0 h-full w-full select-none" />}
        </div>

        <div className="p-card-small flex items-center justify-between">
          <div className="text-small flex flex-col overflow-hidden">
            {fileName ? (
              <span className="text-primary truncate">{fileName}</span>
            ) : (
              <Skeleton className="h-[18px] w-[100px]" />
            )}

            {imageSize ? (
              <span className="text-secondary">{imageSize}</span>
            ) : (
              <Skeleton className="h-[18px] w-[36px]" />
            )}
          </div>

          <div className="item-gap-small text-secondary flex items-center">
            <button className="cursor-pointer" onClick={() => handleDownloadImage(fileName ?? '')}>
              <DownloadIcon size={16} className="stroke-current" />
            </button>

            <button className="cursor-pointer" onClick={handleImageViewer}>
              <ZoomIn size={16} className="stroke-current" />
            </button>
          </div>
        </div>
      </div>
    );
  },
);
ImageViewer.displayName = 'ImageViewer';

export { ImageViewer };
