'use client';

import { X } from 'lucide-react';
import { ComponentPropsWithoutRef, useId, useState } from 'react';

import { cn, formatFileSize } from '@/app/_common/lib/utils';
import { Skeleton } from '@kryptogo/2b';
import { useQuery } from '@tanstack/react-query';

type File = {
  name: string;
  size: number; // bytes
};

interface AttachmentCardProps extends ComponentPropsWithoutRef<'div'> {
  imageSrc: string;
  imageAlt?: string | null;
  file?: File;
  onImageRemove: () => void;
}

const AttachmentCard = ({ imageSrc, imageAlt, file, onImageRemove, className, ...props }: AttachmentCardProps) => {
  const [imageLoading, setImageLoading] = useState(true);

  const randomId = useId();

  const { data: imageBlob } = useQuery({
    queryKey: ['image', imageSrc],
    queryFn: async () => fetch(imageSrc).then((r) => r.blob()),
    staleTime: Infinity,
    enabled: !file,
  });
  const imageSize = file?.size ?? imageBlob?.size;
  const imageExtension = imageBlob && imageBlob.type.split('/')[1];

  const imageName = file?.name ?? (imageExtension && `image-${randomId.replaceAll(':', '')}.${imageExtension}`);

  return (
    <div
      className={cn(
        'p-card-small border-primary gap-item-small relative flex h-[160px] w-[136px] flex-col rounded-xl border',
        className,
      )}
      {...props}
    >
      <div className="rounded-inherit relative h-[100px] w-full shrink-0 select-none overflow-hidden">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src={imageSrc}
          alt={imageAlt ?? 'image'}
          className="rounded-inherit h-full w-full select-none object-cover"
          onLoad={() => setImageLoading(false)}
        />
        {imageLoading && <Skeleton className="rounded-inherit absolute inset-0 h-full w-full select-none" />}
      </div>

      <div className="text-small flex flex-col overflow-hidden">
        {imageName ? <span className="text-primary truncate">{imageName}</span> : <Skeleton className="w-1/2" />}
        {imageSize ? (
          <span className="text-secondary">{formatFileSize(imageSize)}</span>
        ) : (
          <Skeleton className="w-1/3" />
        )}
      </div>

      <button
        type="button"
        className="bg-surface-primary border-primary text-secondary absolute right-[5px] top-[5px] flex h-5 w-5 items-center justify-center rounded-full border transition-transform ease-in-out hover:scale-105"
        onClick={onImageRemove}
      >
        <X className="h-3 w-3 stroke-current" />
      </button>
    </div>
  );
};

export { AttachmentCard };
