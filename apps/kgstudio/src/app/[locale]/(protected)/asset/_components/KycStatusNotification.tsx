import { match } from 'ts-pattern';

import { FormattedMessage } from '@/app/_common/components';
import { ReminderBlock } from '@kryptogo/2b';

import { KycStatus } from '../_types';

const KycStatusNotification = ({ status }: { status: KycStatus }) => {
  return match(status)
    .with('pending', () => (
      <ReminderBlock
        variant="warning"
        description={<FormattedMessage id="kgstudio.kyc-status.transfer-hint.pending" />}
      />
    ))
    .with('verified', () => (
      <ReminderBlock
        variant="success"
        description={<FormattedMessage id="kgstudio.kyc-status.transfer-hint.verified" />}
      />
    ))
    .with('rejected', () => (
      <ReminderBlock
        variant="warning"
        description={<FormattedMessage id="kgstudio.kyc-status.transfer-hint.rejected" />}
      />
    ))
    .with('unverified', () => (
      <ReminderBlock
        variant="warning"
        description={<FormattedMessage id="kgstudio.kyc-status.transfer-hint.unverified" />}
      />
    ))
    .exhaustive();
};

export { KycStatusNotification };
