import { ReactNode } from 'react';

import { formatDate } from '@/app/_common/lib/utils';
import { ImageBox } from '@kryptogo/2b';

interface ProcessByProps {
  name: string;
  profileImage?: string | null;
  email?: string;
  date?: Date;
  additionalInfo?: ReactNode;
}

const ProcessBy = ({ name, profileImage, email, date, additionalInfo }: ProcessByProps) => {
  return (
    <div className="flex items-start gap-3">
      <ImageBox imageSrc={profileImage} size="40" emptyImageType="user" />

      <div className="flex flex-col items-start">
        <p className="text-body-2-bold text-primary">{name}</p>
        {!!email && <p className="text-small text-secondary">{email}</p>}
        {!!date && <p className="text-small text-secondary">{formatDate(date)}</p>}
        {additionalInfo}
      </div>
    </div>
  );
};

export { ProcessBy };
