'use client';

import * as A from 'fp-ts/Array';
import * as O from 'fp-ts/Option';
import * as R from 'fp-ts/Record';
import { pipe } from 'fp-ts/lib/function';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { PieChart } from '@/app/[locale]/(protected)/user360/_components';
import { useDeviceSize } from '@/app/_common/hooks';
import { ReminderBlock } from '@kryptogo/2b';

import { LOW_RISK_SCORE_UPPER, MEDIUM_RISK_SCORE_UPPER } from '../_constants';
import { calculateScore, preprocessSignalsProfile } from '../_lib/utils';

interface KyaRiskChartProps {
  signals: Record<string, number> | null;
  signalsProfile: Record<string, number>;
  riskScore: number | null;
}

const signalSourcesToi18nKey: Record<string, string> = {
  atm: 'kgstudio.asset.kya-status.atm',
  child_exploitation: 'kgstudio.asset.kya-status.child-exploitation',
  dark_market: 'kgstudio.asset.kya-status.dark-market',
  dark_service: 'kgstudio.asset.kya-status.dark-service',
  enforcement_action: 'kgstudio.asset.kya-status.enforcement-action',
  exchange_fraudulent: 'kgstudio.asset.kya-status.exchange-fraudulent',
  exchange_licensed: 'kgstudio.asset.kya-status.exchange-licensed',
  exchange_unlicensed: 'kgstudio.asset.kya-status.exchange-unlicensed',
  gambling: 'kgstudio.asset.kya-status.gambling',
  illegal_service: 'kgstudio.asset.kya-status.illegal-service',
  liquidity_pools: 'kgstudio.asset.kya-status.liquidity-pools',
  marketplace: 'kgstudio.asset.kya-status.marketplace',
  miner: 'kgstudio.asset.kya-status.miner',
  mixer: 'kgstudio.asset.kya-status.mixer',
  other: 'kgstudio.asset.kya-status.other',
  p2p_exchange_licensed: 'kgstudio.asset.kya-status.p2p-exchange-licensed',
  p2p_exchange_unlicensed: 'kgstudio.asset.kya-status.p2p-exchange-unlicensed',
  payment: 'kgstudio.asset.kya-status.payment',
  ransom: 'kgstudio.asset.kya-status.ransom',
  sanctions: 'kgstudio.asset.kya-status.sanctions',
  scam: 'kgstudio.asset.kya-status.scam',
  seized_assets: 'kgstudio.asset.kya-status.seized-assets',
  stolen_coins: 'kgstudio.asset.kya-status.stolen-coins',
  terrorism_financing: 'kgstudio.asset.kya-status.terrorist-financing',
  wallet: 'kgstudio.asset.kya-status.wallet',
};

const groupByValue = (obj: Record<string, string>): Record<string, string[]> => {
  const result: Record<string, string[]> = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      if (!result[value]) {
        result[value] = [];
      }
      result[value].push(key);
    }
  }
  return result;
};

const transformSignalsToData = (data: Record<string, number> | null) =>
  pipe(
    data,
    O.fromNullable,
    O.map(R.mapWithIndex((key, value) => ({ name: key, value }))),
    O.map(R.toArray),
    O.map(A.map(([_, value]) => value)),
    O.map(A.filter(({ value }) => value > 0)),
  );

const categorizeSignalsProfile = (sourceRiskValue: number, t: any) =>
  sourceRiskValue >= 0.5
    ? t('kgstudio.asset.kya-status.source.suspicious')
    : t('kgstudio.asset.kya-status.source.trusted');

const KyaRiskChart = ({ signals, signalsProfile, riskScore }: KyaRiskChartProps) => {
  const { deviceSize } = useDeviceSize();
  const t = useTranslations();

  const preprocessedSignalsProfile = useMemo(() => preprocessSignalsProfile(signalsProfile), [signalsProfile]);

  const signalsProfileMapping = useMemo(
    () =>
      pipe(
        preprocessedSignalsProfile,
        R.toArray,
        A.map(([name, value]) => [t(`${signalSourcesToi18nKey[name]}.name`), categorizeSignalsProfile(value, t)]),
        Object.fromEntries,
        groupByValue,
      ),
    [preprocessedSignalsProfile, t],
  );

  const signalsToDisplay = useMemo(
    () =>
      pipe(
        signals,
        transformSignalsToData,
        O.map(
          A.map((signal) => {
            const i18nKey = signalSourcesToi18nKey[signal.name];
            if (!i18nKey) return signal;

            signal.name = t(`${i18nKey}.name`);
            return signal;
          }),
        ),
        O.getOrElseW(() => []),
      ),
    [signals, t],
  );
  const processedScore = useMemo(() => calculateScore(signals, signalsProfile), [signals, signalsProfile]);

  const tooltips = useMemo(
    () =>
      Object.values(signalSourcesToi18nKey).reduce<Record<string, string>>((acc, i18nKey) => {
        acc[t(`${i18nKey}.name`)] = t(`${i18nKey}.desc`);
        return acc;
      }, {}),
    [t],
  );

  if (processedScore === null)
    return (
      <ReminderBlock
        variant={'info'}
        description={t('kgstudio.asset.kya-status.not-enough-info')}
        className={'w-full'}
      />
    );

  return (
    <div className="grid w-full grid-rows-[auto_1fr] gap-4 rounded-lg p-3">
      <PieChart
        data={signalsToDisplay}
        groupMapping={signalsProfileMapping}
        className="@container/card grid grid-cols-1 justify-items-center gap-2 md:!max-w-[600px] md:!grid-cols-[1fr_auto] md:!items-center"
      >
        {/* grid item by default has min-w-auto, so it can't be smaller
          than its content, so, mt chart only growing but not shrinking...凸 so we can set
          min-w-0 grid-item to opt-out from this behavior
      */}
        <PieChart.Container className="h-full w-full min-w-0">
          <PieChart.Figure size={deviceSize === 'sm' ? 170 : deviceSize === 'md' ? 200 : 230} />
          <PieChart.Overlay>
            {() => {
              const scoreInt = Math.round(Math.min(riskScore || 0, 1) * 100);
              const scoreLevel =
                scoreInt > LOW_RISK_SCORE_UPPER
                  ? scoreInt > MEDIUM_RISK_SCORE_UPPER
                    ? t('kgstudio.asset.kya-status.high-risk')
                    : t('kgstudio.asset.kya-status.medium-risk')
                  : t('kgstudio.asset.kya-status.low-risk');
              const scoreColor =
                scoreInt > LOW_RISK_SCORE_UPPER
                  ? scoreInt > MEDIUM_RISK_SCORE_UPPER
                    ? 'alert-error'
                    : 'alert-warning'
                  : 'alert-success';

              return (
                <div
                  className="text-primary flex h-full w-full flex-col items-center justify-center gap-2"
                  style={{
                    color: `var(--${scoreColor})`,
                  }}
                >
                  <p className="text-h3 font-bold">{scoreLevel}</p>
                  <p className="text-h2">{scoreInt}</p>
                </div>
              );
            }}
          </PieChart.Overlay>
        </PieChart.Container>

        <PieChart.Outline className="grid grid-cols-1 justify-items-start gap-y-2 lg:!gap-y-4" tooltips={tooltips} />
      </PieChart>
    </div>
  );
};

export { KyaRiskChart };
