import { Scan } from 'lucide-react';
import Image from 'next/image';

import { ExplorerLink } from '@/app/_common/components';
import { KycStatusBadge } from '@/app/_common/components/badge';
import { proxyHooks } from '@/app/_common/services';
import { AssetProChainId, KycStatus } from '@/app/_common/services/asset-pro/model';
import { ProxyKyaResponse } from '@/app/_common/services/proxy/model';
import { Button, ImageBox, Separator } from '@kryptogo/2b';
import { getCrystalCurrency } from '@kryptogo/utils';

import pendingIcon from '../_assets/icon-pending.svg';
import { KyaRiskReminderAndModal } from './KyaRiskReminderAndModal';
import { KycStatusNotification } from './KycStatusNotification';

export type Customer = {
  displayName?: string;
  profileImage?: string;
  kycStatus?: KycStatus;
  wallet: {
    address: string;
    chain: AssetProChainId;
  };
  email?: string;
  phone?: string;
};

interface RiskScanPanelProps {
  customer: Customer;
  scannable?: boolean;
  t: any;
}

const RiskScanPanel = ({ customer, scannable = true, t }: RiskScanPanelProps) => {
  const {
    mutate: getKyaProfile,
    data: kyaProfileData,
    error: kyaProfileError,
    isLoading: kyaProfileLoading,
  } = proxyHooks.useProxy();

  const handleRiskCheck = (address: string, chain: AssetProChainId) => {
    getKyaProfile({
      body: {
        address,
        currency: getCrystalCurrency(chain) as 'eth' | 'matic' | 'arb',
      },
    });
  };

  return (
    <div className="space-y-item-small w-full">
      <div className="space-y-item-small">
        <div className="border-primary space-y-list-small p-card-medium w-full rounded-lg border">
          <div className="gap-list-small flex items-center">
            <ImageBox
              size="40"
              imageSrc={customer.profileImage}
              imageAlt={customer.displayName}
              emptyImageType="user"
            />
            <div className="flex flex-col justify-center">
              <p className="text-body-2 font-bold">{customer.displayName}</p>
              {!!customer.kycStatus && (
                <div>
                  <KycStatusBadge status={customer.kycStatus} />
                </div>
              )}
            </div>
          </div>
          <Separator />
          <div className="text-small text-secondary min-gap-8 grid grid-cols-[112px_1fr]">
            <div className="gap-item-small flex flex-col items-start">
              <p>{t('common.wallet-address')}</p>
              {!!customer.email && <p>{t('common.email')}</p>}
              {!!customer.phone && <p>{t('common.phone-number')}</p>}
            </div>

            <div className="text-primary gap-item-small flex flex-col overflow-hidden">
              <ExplorerLink
                chain={customer.wallet.chain}
                hex={customer.wallet.address as `0x${string}`}
                type="address"
                t={t}
                copyable
              />
              {!!customer.email && <p className="truncate">{customer.email}</p>}
              {!!customer.phone && <p className="truncate">{customer.phone}</p>}
            </div>
          </div>
        </div>
        {!!customer.kycStatus && <KycStatusNotification status={customer.kycStatus} />}
        {scannable &&
          (kyaProfileLoading ? (
            <div className="text-body-2 text-secondary flex gap-1 leading-9">
              <Image src={pendingIcon} alt="loading" className="animate-spin" />
              {t('kgstudio.asset.kya-status.wallet-risk-checkin')}
            </div>
          ) : !kyaProfileData ? (
            <Button
              icon={<Scan />}
              loading={kyaProfileLoading}
              onClick={() => handleRiskCheck(customer.wallet.address, customer.wallet.chain)}
              data-cy="risk-scan-button"
            >
              {t('kgstudio.asset.wallet-risk-check')}
            </Button>
          ) : (
            <KyaRiskReminderAndModal
              address={customer.wallet.address}
              riskScore={(kyaProfileData as ProxyKyaResponse).data.riskscore}
              signals={(kyaProfileData as ProxyKyaResponse).data.signals}
              signalsProfile={(kyaProfileData as ProxyKyaResponse).data.riskscore_profile?.signals || null}
            />
          ))}
      </div>
    </div>
  );
};

export { RiskScanPanel };
