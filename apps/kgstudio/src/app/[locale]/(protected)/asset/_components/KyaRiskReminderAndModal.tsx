'use client';

import WAValidator from 'multicoin-address-validator';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { match, P } from 'ts-pattern';
import { formatUnits } from 'viem';

import { BLOCKCHAIR_BASE_URL } from '@/app/_common/constant';
import { proxyHooks } from '@/app/_common/services';
import { ProxyBlockchairResponse } from '@/app/_common/services/proxy/model';
import { Modal, ReminderBlock, Separator } from '@kryptogo/2b';

import { LOW_RISK_SCORE_UPPER, MEDIUM_RISK_SCORE_UPPER } from '../_constants';
import { calculateScore } from '../_lib/utils';
import { KyaRiskChart } from './KyaRiskChart';

interface KyaRiskReminderAndModalProps {
  signals: Record<string, number> | null;
  signalsProfile: Record<string, number> | null;
  riskScore: number | null;
  address: string;
}

const KyaRiskReminderAndModal = ({ address, signals, signalsProfile, riskScore }: KyaRiskReminderAndModalProps) => {
  const [modalOpen, setModalOpen] = useState(false);
  const t = useTranslations();

  const { mutate: getBlockchairProfile, data: dataResp } = proxyHooks.useProxy();

  const processedScore = useMemo(
    () => (!!signalsProfile ? calculateScore(signals, signalsProfile) : null),
    [signals, signalsProfile],
  );
  const scoreInt = Math.round(Math.min(riskScore || 0, 1) * 100);
  const reminderVariant =
    scoreInt > LOW_RISK_SCORE_UPPER ? (scoreInt > MEDIUM_RISK_SCORE_UPPER ? 'error' : 'warning') : 'success';

  const formattedAddress = address.toLowerCase();
  const [chain, token] = match(address)
    .with(
      P.when((addr) => WAValidator.validate(addr, 'Ethereum')),
      () => ['ethereum', 'ETH'],
    )
    .with(
      P.when((addr) => WAValidator.validate(addr, 'Bitcoin')),
      () => ['bitcoin', 'BTC'],
    )
    .otherwise(() => [null, null]);

  const blockchairProfileData = dataResp as ProxyBlockchairResponse;

  useEffect(() => {
    if (processedScore && chain) {
      const queryString = chain === 'bitcoin' ? '?offset=0,0&limit=10,0&transaction_details=false' : '';
      getBlockchairProfile({
        path: `${BLOCKCHAIR_BASE_URL}/${chain}/dashboards/address/${address}${queryString}`,
      });
    }
  }, [address, chain, getBlockchairProfile, processedScore]);

  if (processedScore === null)
    return (
      <ReminderBlock
        variant={'info'}
        description={t('kgstudio.asset.kya-status.not-enough-info')}
        className={'w-full'}
      />
    );

  return (
    <>
      <Modal open={modalOpen} onOpenChange={setModalOpen}>
        <Modal.Content className="[&_.overflow-x-hidden]:overflow-x-visible">
          <Modal.Header>
            <Modal.Title>{t('kgstudio.asset.kya-status.potential-risk')}</Modal.Title>
          </Modal.Header>
          <div className="space-y-6">
            <ReminderBlock
              variant={reminderVariant}
              {...(!!address && { title: address })}
              description={
                <>
                  {t('kgstudio.asset.kya-status.wallet-address-risk')}:{' '}
                  {scoreInt > LOW_RISK_SCORE_UPPER
                    ? scoreInt > MEDIUM_RISK_SCORE_UPPER
                      ? t('kgstudio.asset.kya-status.high')
                      : t('kgstudio.asset.kya-status.medium')
                    : t('kgstudio.asset.kya-status.low')}
                  ({scoreInt}%).
                </>
              }
              className={'w-full'}
            />
            {!!blockchairProfileData?.data[formattedAddress] && (
              <div className="flex w-full flex-col gap-1">
                <div className="flex items-center justify-between">
                  <span className="text-primary text-body-bold">{t('kgstudio.asset.kya-info.total-transactions')}</span>
                  <span className="text-primary">
                    {blockchairProfileData.data[formattedAddress].address.transaction_count}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-primary text-body-bold">{t('kgstudio.asset.kya-info.total-received')}</span>
                  <span className="text-primary">
                    {chain === 'bitcoin'
                      ? formatUnits(BigInt(blockchairProfileData.data[formattedAddress].address.received as number), 8)
                      : formatUnits(
                          BigInt(blockchairProfileData.data[formattedAddress].address.received_approximate as string),
                          18,
                        )}{' '}
                    {token}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-primary text-body-bold">{t('kgstudio.asset.kya-info.total-spent')}</span>
                  <span className="text-primary">
                    {chain === 'bitcoin'
                      ? formatUnits(BigInt(blockchairProfileData.data[formattedAddress].address.spent as number), 8)
                      : formatUnits(
                          BigInt(blockchairProfileData.data[formattedAddress].address.spent_approximate as string),
                          18,
                        )}{' '}
                    {token}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-primary text-body-bold">{t('kgstudio.asset.kya-info.total-balance')}</span>
                  <span className="text-primary">
                    {formatUnits(
                      BigInt(blockchairProfileData.data[formattedAddress].address.balance),
                      chain === 'bitcoin' ? 8 : 18,
                    )}{' '}
                    {token}
                  </span>
                </div>
              </div>
            )}
            <Separator />
            {!!signalsProfile && (
              <KyaRiskChart signals={signals} signalsProfile={signalsProfile} riskScore={riskScore} />
            )}
          </div>
        </Modal.Content>
      </Modal>
      <ReminderBlock
        variant={reminderVariant}
        description={
          <>
            {t('kgstudio.asset.kya-status.wallet-address-risk')}:{' '}
            {scoreInt > LOW_RISK_SCORE_UPPER
              ? scoreInt > MEDIUM_RISK_SCORE_UPPER
                ? t('kgstudio.asset.kya-status.high')
                : t('kgstudio.asset.kya-status.medium')
              : t('kgstudio.asset.kya-status.low')}{' '}
            ({scoreInt}%) .
            <span className="cursor-pointer underline" onClick={() => setModalOpen(true)}>
              ({t('kgstudio.asset.kya-status.view-potential-risk-details')})
            </span>
          </>
        }
        className={'w-full'}
      />
    </>
  );
};

export { KyaRiskReminderAndModal };
