import { Info, Pencil } from 'lucide-react';
import React, { ComponentPropsWithoutRef } from 'react';

import { cn } from '@/app/_common/lib/utils';
import { Tooltip } from '@kryptogo/2b';

interface DataListProps extends ComponentPropsWithoutRef<'div'> {
  title: string;
  value: string | React.ReactNode;
  tooltip?: string | React.ReactNode;
  onEdit?: React.MouseEventHandler<HTMLButtonElement>;
  'data-cy'?: string;
}

export const DataList = React.forwardRef<HTMLDivElement, DataListProps>(
  ({ title, value, tooltip, onEdit, className, ...props }, ref) => {
    return (
      <div ref={ref} className={cn('flex flex-col items-start gap-2', className)} {...props}>
        <div className="flex items-center gap-2">
          <span className="text-body-2 text-secondary">{title}</span>
          {tooltip && (
            <Tooltip>
              <Tooltip.Trigger>
                <Info size={12} />
              </Tooltip.Trigger>
              <Tooltip.Content>
                {tooltip}
                <Tooltip.Arrow />
              </Tooltip.Content>
            </Tooltip>
          )}
          {onEdit ? (
            <button className="text-secondary cursor-pointer" onClick={onEdit}>
              <Pencil size={12} />
            </button>
          ) : null}
        </div>
        {typeof value === 'string' ? (
          <span className="text-body text-primary whitespace-pre-wrap">{value}</span>
        ) : (
          value
        )}
      </div>
    );
  },
);
DataList.displayName = 'DataList';
