'use client';

import { CheckCircle2, XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ReactNode, useEffect } from 'react';
import { toast } from 'sonner';
import { match } from 'ts-pattern';

import { noop } from '@/app/_common/lib/utils';
import { apiAssetProHooks } from '@/app/_common/services';
import { TxStatus, TxStatusSchema, TxStatusWithApproval } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import { usePathname } from '@/i18n/navigation';
import { Step, Steps } from '@kryptogo/2b';

import { ActionCard, HistoryCard, TxInfoCard } from './_components/cards';

export default function TransactionDetails({
  params,
}: {
  params: {
    id: string;
  };
}) {
  const { id: txId } = params;

  const t = useTranslations();
  const pathname = usePathname();
  const txOrgId = pathname.split('/').at(-1)?.split('-')[0];

  const [orgId, orgInfo] = useOrganizationStore((state) => [state.orgId, state.orgInfo]);

  const { data: txDetails, error: txDetailsError } = apiAssetProHooks.useGetTransactionDetails(
    {
      params: { org_id: Number(orgId), tx_id: txId },
    },
    {
      enabled: !!orgId && !!txId && Number(txOrgId) === orgId,
    },
  );
  // NOTE: 判斷二級放款：訂單狀態會是 sending or send success/failed，此狀態下 finance_manager, approver 會是 null，詳細頁上方 step 會是三步驟
  const isApprovalAndReleaseBypassed =
    TxStatusSchema.safeParse(txDetails?.data.tx_info.status).success &&
    txDetails?.data.operators.finance_manager === null &&
    txDetails?.data.operators.approver === null;

  // 三級放款
  const getStepsData = (status: TxStatusWithApproval, isApprovalRejection: boolean): Step[] =>
    match(status)
      .with('rejected', () => {
        if (!isApprovalRejection)
          return [
            { stepNumber: 1, label: t('kgstudio.asset.tx-detail.steps.submitted'), value: 'submitted' },
            { stepNumber: 2, label: t('kgstudio.asset.tx-detail.steps.awaiting-approval'), value: 'awaiting_approval' },
            { stepNumber: 3, label: t('kgstudio.asset.tx-detail.steps.awaiting-release'), value: 'awaiting_release' },
            {
              stepNumber: 3,
              label: t('kgstudio.asset.tx-detail.steps.rejected'),
              value: 'rejected',
              customDot: () => <XCircle className="fill-error stroke-surface-primary h-6 w-6" />,
              customLabel: ({ label }: { label: string | ReactNode }) => (
                <span className="text-body-2-bold text-error">{label}</span>
              ),
            },
          ];

        return [
          { stepNumber: 1, label: t('kgstudio.asset.tx-detail.steps.submitted'), value: 'submitted' },
          { stepNumber: 2, label: t('kgstudio.asset.tx-detail.steps.awaiting-approval'), value: 'awaiting_approval' },
          {
            stepNumber: 3,
            label: t('kgstudio.asset.tx-detail.steps.rejected'),
            value: 'rejected',
            customDot: () => <XCircle className="fill-error stroke-surface-primary h-6 w-6" />,
            customLabel: ({ label }: { label: string | ReactNode }) => (
              <span className="text-body-2-bold text-error">{label}</span>
            ),
          },
        ];
      })
      .with('send_success', () => {
        return [
          { stepNumber: 1, label: t('kgstudio.asset.tx-detail.steps.submitted'), value: 'submitted' },
          { stepNumber: 2, label: t('kgstudio.asset.tx-detail.steps.awaiting-approval'), value: 'awaiting_approval' },
          { stepNumber: 3, label: t('kgstudio.asset.tx-detail.steps.awaiting-release'), value: 'awaiting_release' },
          { stepNumber: 4, label: t('kgstudio.asset.tx-detail.steps.sending'), value: 'sending' },
          {
            stepNumber: 5,
            label: t('kgstudio.asset.tx-detail.steps.send-success'),
            value: 'send_success',
            customDot: () => <CheckCircle2 className="fill-success stroke-surface-primary h-6 w-6" />,
            customLabel: ({ label }: { label: string | ReactNode }) => (
              <span className="text-body-2-bold text-success">{label}</span>
            ),
          },
        ];
      })
      .otherwise(() => {
        return [
          { stepNumber: 1, label: t('kgstudio.asset.tx-detail.steps.submitted'), value: 'submitted' },
          { stepNumber: 2, label: t('kgstudio.asset.tx-detail.steps.awaiting-approval'), value: 'awaiting_approval' },
          { stepNumber: 3, label: t('kgstudio.asset.tx-detail.steps.awaiting-release'), value: 'awaiting_release' },
          { stepNumber: 4, label: t('kgstudio.asset.tx-detail.steps.sending'), value: 'sending' },
          {
            stepNumber: 5,
            label: t('kgstudio.asset.tx-detail.steps.send-success'),
            value: 'send_success',
          },
        ];
      });

  // 二級放款
  const getStepsDataWhenApprovalAndReleaseBypassed = (status: TxStatus): Step[] =>
    match(status)
      .with('send_success', () => {
        return [
          { stepNumber: 1, label: t('kgstudio.asset.tx-detail.steps.submitted'), value: 'submitted' },
          { stepNumber: 2, label: t('kgstudio.asset.tx-detail.steps.sending'), value: 'sending' },
          {
            stepNumber: 3,
            label: t('kgstudio.asset.tx-detail.steps.send-success'),
            value: 'send_success',
            customDot: () => <CheckCircle2 className="fill-success stroke-surface-primary h-6 w-6" />,
            customLabel: ({ label }: { label: string | ReactNode }) => (
              <span className="text-body-2-bold text-success">{label}</span>
            ),
          },
        ];
      })
      .with('send_failed', () => {
        return [
          { stepNumber: 1, label: t('kgstudio.asset.tx-detail.steps.submitted'), value: 'submitted' },
          { stepNumber: 2, label: t('kgstudio.asset.tx-detail.steps.sending'), value: 'sending' },
          {
            stepNumber: 3,
            label: t('kgstudio.asset.tx-detail.steps.send-failed'),
            value: 'send_failed',
            customDot: () => <XCircle className="fill-error stroke-surface-primary h-6 w-6" />,
            customLabel: ({ label }: { label: string | ReactNode }) => (
              <span className="text-body-2-bold text-error">{label}</span>
            ),
          },
        ];
      })
      .otherwise(() => {
        return [
          { stepNumber: 1, label: t('kgstudio.asset.tx-detail.steps.submitted'), value: 'submitted' },
          { stepNumber: 2, label: t('kgstudio.asset.tx-detail.steps.sending'), value: 'sending' },
          {
            stepNumber: 3,
            label: t('kgstudio.asset.tx-detail.steps.send-success'),
            value: 'send_success',
          },
        ];
      });

  const isPermissionLessError = match(txDetailsError)
    .with(
      {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        status: 403,
      },
      () => true,
    )
    .otherwise(() => false);
  if (isPermissionLessError) {
    throw new Error('Permission denied');
  }

  const isTxNotFoundError = match(txDetailsError)
    .with(
      {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        status: 404,
      },
      () => true,
    )
    .otherwise(() => false);
  if (isTxNotFoundError) {
    throw new Error('Resource not found');
  }

  useEffect(() => {
    if (!!txDetailsError) {
      toast.error(t('kgstudio.common.error'));
    }
  }, [txDetailsError, t]);

  if (txOrgId && orgId && Number(txOrgId) !== orgId) {
    throw new Error('Resource not found', {
      cause: t('kgstudio.asset.tx-detail.not-exist', {
        orgName: orgInfo?.name || '' || '',
      }),
    });
  }

  // FIXME: Add loading state
  if (!txDetails) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Steps */}
      <Steps
        steps={
          isApprovalAndReleaseBypassed
            ? getStepsDataWhenApprovalAndReleaseBypassed(txDetails.data.tx_info.status as TxStatus)
            : getStepsData(
                txDetails.data.tx_info.status as TxStatusWithApproval,
                !txDetails.data.operators.finance_manager,
              )
        }
        value={txDetails.data.tx_info.status}
        onValueChange={noop}
      >
        <Steps.Display data-cy="order-steps" />
      </Steps>
      <article className="grid w-full grid-cols-[1fr_380px] gap-10">
        {/* Cards */}
        <section className="space-y-10">
          <TxInfoCard txInfo={txDetails.data.tx_info} />
        </section>
        <aside className="space-y-4">
          <ActionCard txInfo={txDetails.data.tx_info} />
          <HistoryCard
            txHashes={txDetails.data.tx_info.tx_hashes}
            chainId={txDetails.data.tx_info.token.chain_id}
            txStatus={txDetails.data.tx_info.status}
            operators={txDetails.data.operators}
          />
        </aside>
      </article>
    </div>
  );
}
