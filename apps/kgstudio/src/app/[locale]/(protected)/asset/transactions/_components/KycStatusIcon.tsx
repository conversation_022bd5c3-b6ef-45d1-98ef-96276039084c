import { Check<PERSON>ircle2, XCircle, Clock4, HelpCircle } from 'lucide-react';
import { match } from 'ts-pattern';

import { KycStatus } from '@/app/_common/services/asset-pro/model';

const KycStatusIcon = ({ status }: { status: KycStatus }) => {
  const displayIcon = match(status)
    .with('verified', () => ({ variant: 'success', icon: CheckCircle2 }))
    .with('pending', () => ({ variant: 'warning', icon: Clock4 }))
    .with('rejected', () => ({ variant: 'error', icon: XCircle }))
    .with('unverified', () => ({ variant: 'disabled', icon: HelpCircle }))
    .exhaustive();
  const color = `var(--${status === 'unverified' ? 'text' : 'alert'}-${displayIcon.variant})`;

  return <displayIcon.icon size={12} color={color} />;
};

export { KycStatusIcon };
