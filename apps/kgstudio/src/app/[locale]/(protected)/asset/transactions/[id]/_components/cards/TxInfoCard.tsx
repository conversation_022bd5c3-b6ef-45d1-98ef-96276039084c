'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { CSSProperties, ComponentProps } from 'react';

import { DataList, ImageViewer, RiskScanPanel } from '@/app/[locale]/(protected)/asset/_components';
import { TokenIcon } from '@/app/_common/components';
import { TxStatusBadge } from '@/app/_common/components/badge';
import { usePageHeader, useTokenPrice } from '@/app/_common/hooks';
import { TxInfo } from '@/app/_common/services/asset-pro/model';
import { Badge, Card } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';
import { DECIMAL_DISPLAY_MODE, formatCurrency, getChainFullName, getChainIcon } from '@kryptogo/utils';

interface TxInfoCardProps {
  txInfo: TxInfo;
}

const TxInfoCard = ({ txInfo }: TxInfoCardProps) => {
  usePageHeader({
    title: 'Transaction Details',
    backLink: '/asset/transactions',
  });
  const t = useTranslations();

  const { tokenPriceUSD } = useTokenPrice(txInfo.token.contract_address, txInfo.token.chain_id);

  // const [editTxNoteModalOpen, setEditTxNoteModalOpen] = useState(false);

  interface ExtendedDataListProps extends ComponentProps<typeof DataList> {
    span?: number;
  }
  const getTxInfoData = (txInfo: TxInfo): ExtendedDataListProps[] => [
    {
      title: t('kgstudio.asset.tx-detail.tx-info-card.tx-id'),
      'data-cy': 'tx-info-tx-id',
      value: (
        <Badge variant={'grey'} size={'md'}>
          {`TX-${txInfo.id}`}
        </Badge>
      ),
    },
    {
      title: t('kgstudio.asset.tx-info-card.tx-status'),
      'data-cy': 'tx-info-tx-status',
      value: <TxStatusBadge status={txInfo.status} t={t} />,
    },
    {
      title: t('kgstudio.asset.tx-info-card.send-token'),
      'data-cy': 'tx-info-send-token',
      value: (
        <div className="gap-item-small flex flex-col">
          <div className="gap-item-small text-h2 text-primary flex flex-wrap items-center font-bold">
            <TokenIcon
              token={{
                logoUrl: txInfo.token.logo_url,
                name: txInfo.token.name,
              }}
              chain={txInfo.token.chain_id}
            />
            <span>{formatCurrency({ amount: txInfo.amount })}</span>

            <span>{txInfo.token.symbol}</span>
          </div>
          <span className="text-secondary text-small">{`≈ ${formatCurrency({
            amount: tokenPriceUSD?.times(txInfo.amount) ?? 0,
            decimals: DECIMAL_DISPLAY_MODE.FIAT,
            fmt: { suffix: ' USD' },
          })}`}</span>
        </div>
      ),
    },
    {
      title: t('kgstudio.asset.tx-info-card.blockchain'),
      'data-cy': 'tx-info-blockchain',
      value: (
        <div className="gap-item-small text-h2 text-primary flex items-center font-bold">
          <div className="relative h-6 w-6 overflow-clip rounded-full">
            {
              <Image
                src={getChainIcon(txInfo.token.chain_id) ?? ''}
                alt={txInfo.token.chain_id}
                fill
                className="object-cover"
              />
            }
          </div>
          <span>{getChainFullName(txInfo.token.chain_id)}</span>
        </div>
      ),
    },
    {
      title: t('kgstudio.asset.order-detail.order-information.customer'),
      'data-cy': 'order-info-customer',
      value: (
        <RiskScanPanel
          customer={{
            displayName: txInfo.recipient.customer?.name,
            email: txInfo.recipient.customer?.email ?? undefined,
            phone: txInfo.recipient.customer?.phone ?? undefined,
            wallet: {
              chain: txInfo.token.chain_id,
              address: txInfo.recipient.wallet_address,
            },
            kycStatus: txInfo.recipient.customer?.kyc_status,
          }}
          t={t}
        />
      ),
      span: 2,
    },
    {
      title: t('kgstudio.asset.tx-info-card.tx-note'),
      'data-cy': 'tx-info-tx-note',
      value: <span className="text-body text-primary">{txInfo.notes.submission_note ?? 'N/A'}</span>,
      // onEdit: txInfo.status === 'awaiting_approval' ? handleEditTxNoteOrAttachments : undefined,
      span: 2,
    },
    {
      title: t('kgstudio.asset.tx-info-card.attachments'),
      'data-cy': 'tx-info-attachments',
      value: (
        <>
          {txInfo.attachments ? (
            <div className="item-gap-small flex">
              {txInfo.attachments.map((attachment, index) => (
                <ImageViewer key={index} imageUrl={attachment} />
              ))}
            </div>
          ) : (
            'N/A'
          )}
        </>
      ),
      // onEdit: txInfo.status === 'awaiting_approval' ? handleEditTxNoteOrAttachments : undefined,
      span: 2,
    },
  ];

  // const handleEditTxNoteOrAttachments = () => {
  //   setEditTxNoteModalOpen(true);
  // };

  return (
    <>
      {/* Modals */}
      {/* <EditTxNoteModal
        open={editTxNoteModalOpen}
        onOpenChange={setEditTxNoteModalOpen}
        txId={txInfo.id}
        note={txInfo.notes.submission_note}
        attachments={txInfo.attachments}
      /> */}

      {/* Main Card */}
      <Card className="gap-list-medium flex flex-col" data-cy="tx-info-card">
        <SectionHeader>
          <SectionHeader.Title title={t('kgstudio.asset.tx-info-card.title')} />
        </SectionHeader>
        <div className="gap-x-item-large grid grid-cols-2 gap-y-9">
          {getTxInfoData(txInfo).map((data) => (
            <DataList
              key={data.title}
              className="col-[span_var(--data-col-span)]"
              style={
                {
                  '--data-col-span': data.span || 1,
                } as CSSProperties
              }
              {...data}
            />
          ))}
        </div>
      </Card>
    </>
  );
};

export { TxInfoCard };
