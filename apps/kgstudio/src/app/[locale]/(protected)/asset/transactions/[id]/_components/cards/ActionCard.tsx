import { last } from 'lodash-es';
import { ExternalLink } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { match } from 'ts-pattern';

import { DataList } from '@/app/[locale]/(protected)/asset/_components';
import { useNativeTokenBalanceAndFee, usePermissions, useTokenBalance } from '@/app/_common/hooks';
import { AssetProChainId, TxInfo, TxStatusWithApproval } from '@/app/_common/services/asset-pro/model';
import { Button, Card } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';
import { getExplorerUrl, truncateTxhashOrAddress } from '@kryptogo/utils';

import { ReleaseCheckList } from '../ReleaseCheckList';
import { ConfirmApprovalOrReleaseModal, ConfirmRejectionModal } from '../modals';

interface ActionCardProps {
  txInfo: TxInfo;
}

const ActionCard = ({ txInfo }: ActionCardProps) => {
  const t = useTranslations();

  const [hasApproverPermission, hasFinanceManagerPermission] = usePermissions(
    ['transaction', 'approve'],
    ['transaction', 'release'],
  );

  const [isConfirmRejectionModalOpen, setIsConfirmRejectionModalOpen] = useState(false);
  const [isConfirmApprovalOrReleaseModalOpen, setIsConfirmApprovalOrReleaseModalOpen] = useState(false);

  const handleTxRejection = () => {
    setIsConfirmRejectionModalOpen(true);
  };
  const handleTxApproval = () => {
    setIsConfirmApprovalOrReleaseModalOpen(true);
  };
  const handleTxRelease = () => {
    setIsConfirmApprovalOrReleaseModalOpen(true);
  };

  const txId = txInfo.id;
  const txStatus = txInfo.status;
  const latestTxHash = last(txInfo.tx_hashes);
  const chainId = txInfo.token.chain_id;
  const rejectionNote = txInfo.notes.rejection_note;

  const { isBalanceEnough, tokenBalance } = useTokenBalance({
    token: {
      chainId,
      contractAddress: txInfo.token.contract_address,
      symbol: txInfo.token.symbol,
      decimals: txInfo.token.decimals,
    },
    txTokenAmount: txInfo.amount,
  });

  const { nativeTokenBalance } = useNativeTokenBalanceAndFee({
    chainId,
    contractAddress: txInfo.token.contract_address,
    tokenBalance,
  });

  const actionCardContent: {
    msg: string | React.ReactNode;
    action: {
      left: () => void;
      right: () => void;
    } | null;
    disabled?: {
      left?: boolean;
      right?: boolean;
    };
  } = match(txStatus)
    .with('awaiting_approval', () =>
      hasApproverPermission
        ? {
            msg: t('kgstudio.asset.tx-action-card.awaiting-approval.msg-approver'),
            action: { left: handleTxRejection, right: handleTxApproval },
          }
        : {
            msg: t('kgstudio.asset.tx-action-card.awaiting-approval.msg-normal'),
            action: null,
          },
    )
    .with('awaiting_release', 'send_failed', () =>
      hasFinanceManagerPermission
        ? {
            msg: (
              <>
                {!!latestTxHash && (
                  <TxSent chainId={txInfo.token.chain_id} txHash={latestTxHash} txStatus="send_failed" />
                )}
                <p className="text-secondary text-body whitespace-pre-wrap">
                  {t('kgstudio.asset.tx-action-card.awaiting-release.msg-finance-manager')}
                </p>
                <ReleaseCheckList token={txInfo.token} amount={txInfo.amount} />
              </>
            ),
            action: { left: handleTxRejection, right: handleTxRelease },
            disabled: {
              right:
                txInfo.token.contract_address === ''
                  ? (nativeTokenBalance?.isLessThan(txInfo.amount) ?? true)
                  : !isBalanceEnough,
            },
          }
        : {
            msg: !!latestTxHash ? (
              <TxSent
                chainId={chainId}
                txHash={latestTxHash}
                txStatus="send_failed"
                additionalInfo={t('kgstudio.asset.tx-action-card.awaiting-release.msg-normal')}
              />
            ) : (
              t('kgstudio.asset.tx-action-card.awaiting-release.msg-normal')
            ),
            action: null,
          },
    )
    .with('sending', () => ({
      msg: <TxSent chainId={chainId} txHash={latestTxHash as string} txStatus="sending" />,
      action: null,
    }))
    .with('send_success', () => ({
      msg: <TxSent chainId={chainId} txHash={latestTxHash as string} txStatus="send_success" />,
      action: null,
    }))
    .with('rejected', () => ({
      msg: (
        <>
          <span className="text-body-bold text-error">{t('kgstudio.asset.tx-action-card.rejected.title')}</span>
          <DataList
            title={t('kgstudio.asset.tx-action-card.rejected.review-note.title')}
            value={<span className="text-body text-primary">{rejectionNote}</span>}
          />
        </>
      ),
      action: null,
    }))
    .exhaustive();

  return (
    <>
      {/* Modals */}
      <ConfirmRejectionModal
        open={isConfirmRejectionModalOpen}
        onOpenChange={setIsConfirmRejectionModalOpen}
        txId={txId}
        txStatus={txStatus as TxStatusWithApproval}
      />
      <ConfirmApprovalOrReleaseModal
        open={isConfirmApprovalOrReleaseModalOpen}
        onOpenChange={setIsConfirmApprovalOrReleaseModalOpen}
        txInfo={txInfo}
      />
      <Card className="flex flex-col gap-6">
        <SectionHeader>
          <SectionHeader.Title title={t('kgstudio.asset.tx-action-card.title')} />
        </SectionHeader>
        {typeof actionCardContent.msg === 'string' ? (
          <p className="text-body text-secondary whitespace-pre-wrap">{actionCardContent.msg}</p>
        ) : (
          actionCardContent.msg
        )}

        {actionCardContent.action && (
          <div className="grid grid-cols-2 gap-6">
            <Button
              variant="dangerDark"
              disabled={!!actionCardContent.disabled?.left}
              onClick={actionCardContent.action.left}
              data-cy="reject-tx-button"
            >
              {t('kgstudio.asset.tx-action-card.reject')}
            </Button>
            <Button
              variant="green"
              disabled={!!actionCardContent.disabled?.right}
              onClick={actionCardContent.action.right}
              data-cy="approve-or-release-tx-button"
            >
              {(txStatus === 'awaiting_release' || txStatus === 'send_failed') &&
                t('kgstudio.asset.tx-action-card.release')}
              {txStatus === 'awaiting_approval' && t('kgstudio.asset.tx-action-card.approve')}
            </Button>
          </div>
        )}
      </Card>
    </>
  );
};

interface TxSentProps {
  chainId: AssetProChainId;
  txStatus: 'send_success' | 'send_failed' | 'sending';
  txHash: string;
  additionalInfo?: string;
}
const TxSent = ({ chainId, txHash, txStatus, additionalInfo }: TxSentProps) => {
  const t = useTranslations();

  const title = match(txStatus)
    .with('send_success', () => (
      <span className="text-success font-bold">{t('kgstudio.asset.tx-action-card.send-success.title')}</span>
    ))
    .with('send_failed', () => (
      <span className="text-error font-bold">{t('kgstudio.asset.tx-action-card.send-failed.title')}</span>
    ))
    .with('sending', () => null)
    .exhaustive();

  const msg = match(txStatus)
    .with('send_success', () => (
      <span className="whitespace-pre-wrap">{t('kgstudio.asset.tx-action-card.send-success.msg')}</span>
    ))
    .with('send_failed', () => (
      <span className="whitespace-pre-wrap">{t('kgstudio.asset.tx-action-card.send-failed.msg')}</span>
    ))
    .with('sending', () => (
      <span className="whitespace-pre-wrap">{t('kgstudio.asset.tx-action-card.sending.msg')}</span>
    ))
    .exhaustive();

  return (
    <div className="text-secondary text-body flex w-full flex-col gap-2">
      {title}
      {msg}
      {additionalInfo && <span>{additionalInfo}</span>}
      <Button
        variant="grey"
        icon={<ExternalLink />}
        className="w-full"
        onClick={() => {
          const txExplorerUrl = getExplorerUrl('tx', chainId, txHash);
          if (!txExplorerUrl) return;

          window.open(txExplorerUrl, '_blank');
        }}
      >
        {truncateTxhashOrAddress(txHash)}
      </Button>
    </div>
  );
};

export { ActionCard };
