import BigNumber from 'bignumber.js';
import { AlertTriangle, CheckCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { useNativeTokenBalanceAndFee, useTokenBalance } from '@/app/_common/hooks';
import { TxToken } from '@/app/_common/services/asset-pro/model';
import { Spinner } from '@kryptogo/2b';
import { formatCurrency, getChainFullName } from '@kryptogo/utils';

interface ReleaseCheckListProps {
  token: TxToken;
  amount: string;
}
export const ReleaseCheckList = ({ token, amount }: ReleaseCheckListProps) => {
  const t = useTranslations();

  const chainId = token.chain_id;
  const isNativeTokenSelected = token.contract_address === '';

  const {
    isLoading: tokenSufficiencyLoading,
    tokenBalance,
    shortfallAmount,
    formattedTokenBalance,
    isBalanceEnough,
  } = useTokenBalance({
    token: {
      chainId: chainId,
      contractAddress: token.contract_address,
      symbol: token.symbol,
      decimals: token.decimals,
    },
    txTokenAmount: amount,
  });

  const {
    isLoading: nativeTokenSufficiencyLoading,
    formattedFee,
    formattedNativeTokenBalance,
    formattedNativeTokenShortfallAmount,
    isFeeEnough,
    nativeTokenSymbol,
    nativeTokenBalance,
  } = useNativeTokenBalanceAndFee({
    chainId,
    contractAddress: token.contract_address,
    tokenBalance,
  });

  const tokenBalanceLoading = isNativeTokenSelected ? nativeTokenSufficiencyLoading : tokenSufficiencyLoading;

  const isTokenBalanceEnough = isNativeTokenSelected
    ? (nativeTokenBalance?.isGreaterThan(amount) ?? false)
    : isBalanceEnough;
  const tokenShortfallAmount = isNativeTokenSelected
    ? isTokenBalanceEnough
      ? BigNumber(0)
      : BigNumber(amount).minus(nativeTokenBalance ?? 0)
    : shortfallAmount;

  return (
    <div className="w-full space-y-2">
      <div className="flex items-start">
        {!tokenBalanceLoading ? (
          <>
            {isTokenBalanceEnough ? (
              <>
                <CheckCircle className="fill-success stroke-surface-primary shrink-0" size={20} />
                <span className="text-success text-body">
                  {t('kgstudio.asset.tx-action-card.release-check-list.balance-enough', {
                    tokenBalance: isNativeTokenSelected
                      ? formattedNativeTokenBalance || ''
                      : formattedTokenBalance || '',
                    tokenSymbol: token.symbol,
                  })}
                </span>
              </>
            ) : (
              <>
                <AlertTriangle className="fill-warning stroke-surface-primary shrink-0" size={20} />
                <div className="text-body text-highlight flex flex-col">
                  <span className="font-bold">
                    {t('kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.title', {
                      tokenBalance: isNativeTokenSelected
                        ? formattedNativeTokenBalance || ''
                        : formattedTokenBalance || '',
                      tokenSymbol: token.symbol,
                    })}
                  </span>
                  <span>
                    {t('kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.desc', {
                      shortfallAmount: formatCurrency({ amount: tokenShortfallAmount ?? 0 }),
                      tokenSymbol: token.symbol,
                      chainName: getChainFullName(chainId) || '',
                    })}
                  </span>
                </div>
              </>
            )}
          </>
        ) : (
          <div className="flex items-center gap-2">
            <Spinner size="sm" className="stroke-[var(--text-secondary)]" />
            <p className="text-secondary">{t('kgstudio.asset.tx-action-card.release-check-list.balance-loading')}</p>
          </div>
        )}
      </div>
      <div className="flex items-start">
        {!nativeTokenSufficiencyLoading ? (
          <>
            {isFeeEnough ? (
              <>
                <CheckCircle className="fill-success stroke-surface-primary shrink-0" size={20} />
                <span className="text-success text-body">
                  {t('kgstudio.asset.tx-action-card.release-check-list.fee-enough', {
                    fee: formattedFee || '',
                    tokenSymbol: nativeTokenSymbol,
                  })}
                </span>
              </>
            ) : (
              <>
                <AlertTriangle className="fill-warning stroke-surface-primary shrink-0" size={20} />
                <div className="text-body text-highlight flex flex-col">
                  <span className="font-bold">
                    {t('kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.title')}
                  </span>
                  <span>
                    {t('kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.desc', {
                      shortfallAmount: formattedNativeTokenShortfallAmount || '',
                      tokenSymbol: nativeTokenSymbol,
                      chainName: getChainFullName(chainId) || '',
                    })}
                  </span>
                </div>
              </>
            )}
          </>
        ) : (
          <div className="flex items-center gap-2">
            <Spinner size="sm" className="stroke-[var(--text-secondary)]" />
            <p className="text-secondary">{t('kgstudio.asset.tx-action-card.release-check-list.fee-loading')}</p>
          </div>
        )}
      </div>
    </div>
  );
};
