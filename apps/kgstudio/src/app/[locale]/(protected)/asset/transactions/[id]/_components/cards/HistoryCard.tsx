import { CheckCircle2, XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { match } from 'ts-pattern';

import { DataList, ProcessBy } from '@/app/[locale]/(protected)/asset/_components';
import { AllTxStatus, AssetProChainId, TxOperators, TxStatus } from '@/app/_common/services/asset-pro/model';
import { Card } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';

import { TxHahsWithStatus } from '../../../../orders/[id]/_components';

interface HistoryCardProps {
  txHashes: string[] | null;
  chainId: AssetProChainId;
  txStatus: AllTxStatus;
  operators: TxOperators;
}

const HistoryCard = ({ operators, chainId, txHashes, txStatus }: HistoryCardProps) => {
  const t = useTranslations();

  const getStatusForTxHashes = (isLastTxHash: boolean, txStatus: AllTxStatus) => {
    if (!isLastTxHash) return 'send_failed';

    return match(txStatus)
      .with('awaiting_release', 'send_failed', () => 'send_failed' as const)
      .otherwise(() => txStatus);
  };

  const isRejectedByFinanceManager = txStatus === 'rejected' && !!operators.finance_manager;
  const isRejectedByApprover = txStatus === 'rejected' && !isRejectedByFinanceManager;

  return (
    <Card className=" flex flex-col gap-9">
      <SectionHeader>
        <SectionHeader.Title title={t('kgstudio.asset.tx-history-card.title')} />
      </SectionHeader>
      <DataList
        title={t('kgstudio.asset.tx-history-card.submitted-by')}
        value={
          <ProcessBy
            name={operators.trader.operator.name}
            profileImage={operators.trader.operator.profile_img}
            date={operators.trader.time}
          />
        }
      />
      {!!operators.approver && (
        <DataList
          title={t('kgstudio.asset.tx-history-card.approver')}
          value={
            <ProcessBy
              name={operators.approver.operator.name}
              profileImage={operators.approver.operator.profile_img}
              date={operators.approver.time}
              additionalInfo={isRejectedByApprover ? <RejectedSpan /> : <ApprovedSpan />}
            />
          }
        />
      )}
      {!!operators.finance_manager && (
        <DataList
          title={t('kgstudio.asset.tx-history-card.finance-manager')}
          value={
            <ProcessBy
              name={operators.finance_manager.operator.name}
              profileImage={operators.finance_manager.operator.profile_img}
              date={operators.finance_manager.time}
              additionalInfo={isRejectedByFinanceManager ? <RejectedSpan /> : <ApprovedSpan />}
            />
          }
        />
      )}
      {!!txHashes && (
        <DataList
          title={t('kgstudio.asset.tx-history-card.tx-hashes')}
          value={txHashes.map((txHash, index) => (
            <TxHahsWithStatus
              key={txHash}
              txHash={txHash}
              chain={chainId}
              status={getStatusForTxHashes(index === txHashes.length - 1, txStatus) as TxStatus | 'awaiting_release'}
            />
          ))}
        />
      )}
    </Card>
  );
};

const RejectedSpan = () => {
  const t = useTranslations();

  return (
    <div className="text text-error text-caption flex items-center gap-1">
      <XCircle size={16} className="fill-error stroke-surface-primary" />
      <span>{t('kgstudio.common.rejected')}</span>
    </div>
  );
};

const ApprovedSpan = () => {
  const t = useTranslations();

  return (
    <div className="text text-success text-caption flex items-center gap-1">
      <CheckCircle2 size={16} className="fill-success stroke-surface-primary" />
      <span>{t('kgstudio.common.approved')}</span>
    </div>
  );
};

export { HistoryCard };
