import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { FormTextarea } from '@/app/_common/components/form';
import { noop } from '@/app/_common/lib/utils';
import { apiAssetProHooks } from '@/app/_common/services';
import { TxStatusWithApproval } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal, SuccessModal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

interface ConfirmRejectionModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  open: boolean;
  onOpenChange: (open: boolean) => void;

  txId: string;
  txStatus: TxStatusWithApproval;
}

const TxRejectionSchema = (t: any) =>
  z.object({
    review_note: z
      .string()
      .trim()
      .nonempty({
        message: t('kgstudio.asset.tx-rejection-modal.review-note.required-error'),
      }),
  });
type TxRejectionFormValues = z.infer<ReturnType<typeof TxRejectionSchema>>;

const ConfirmRejectionModal = ({ txId, txStatus, ...props }: ConfirmRejectionModalProps) => {
  const t = useTranslations();

  const queryClient = useQueryClient();

  const [orgId] = useOrganizationStore((state) => [state.orgId]);

  const {
    mutate: rejectApprovalTx,
    isLoading: rejectApprovalTxLoading,
    error: rejectApprovalTxError,
    isSuccess: rejectApprovalTxSuccess,
    reset: resetRejectApprovalTx,
  } = apiAssetProHooks.useTxApproval(
    {
      params: {
        org_id: Number(orgId),
        tx_id: txId,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionDetails'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionHistory'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getPendingTxHistoryCount'));
        props.onOpenChange(false);

        setTimeout(() => {
          resetRejectApprovalTx();
        }, 2000);
      },
    },
  );

  const {
    mutate: rejectReleaseTx,
    isLoading: rejectReleaseTxLoading,
    error: rejectReleaseTxError,
    isSuccess: rejectReleaseTxSuccess,
    reset: resetRejectReleaseTx,
  } = apiAssetProHooks.useTxRelease(
    {
      params: {
        org_id: Number(orgId),
        tx_id: txId,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionDetails'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionHistory'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getPendingTxHistoryCount'));
        props.onOpenChange(false);

        setTimeout(() => {
          resetRejectReleaseTx();
        }, 2000);
      },
    },
  );

  const form = useForm<TxRejectionFormValues>({
    defaultValues: {},
    resolver: zodResolver(TxRejectionSchema(t)),
    mode: 'onChange',
  });

  const onSubmit = (data: TxRejectionFormValues) =>
    match(txStatus)
      .with('awaiting_approval', () => {
        rejectApprovalTx({
          note: data.review_note,
          operation: 'reject',
        });
      })
      .with('awaiting_release', () => {
        rejectReleaseTx({
          note: data.review_note,
          operation: 'reject',
        });
      })
      .otherwise(noop);

  const rejectionButtonLoading = match(txStatus)
    .with('awaiting_approval', () => rejectApprovalTxLoading)
    .with('awaiting_release', () => rejectReleaseTxLoading)
    .otherwise(() => false);
  const rejectionButtonDisabled =
    !form.formState.isValid ||
    match(txStatus)
      .with('awaiting_approval', () => rejectApprovalTxSuccess)
      .with('awaiting_release', () => rejectReleaseTxSuccess)
      .otherwise(() => true);

  const cancelButtonDisabled = match(txStatus)
    .with('awaiting_approval', () => rejectApprovalTxLoading)
    .with('awaiting_release', () => rejectReleaseTxLoading)
    .otherwise(() => true);

  useEffect(() => {
    if (rejectApprovalTxError || rejectReleaseTxError) {
      toast.error(t('kgstudio.common.error'));
    }
  }, [rejectApprovalTxError, rejectReleaseTxError, t]);

  if (rejectApprovalTxSuccess || rejectReleaseTxSuccess)
    return <SuccessModal open title={t('kgstudio.asset.tx-rejection-modal.success.title')} />;

  return (
    <Modal {...props}>
      <Modal.Content className="max-h-[90%] max-w-[688px]" scrollable>
        <Modal.Header>
          <Modal.Title>{t('kgstudio.asset.tx-rejection-modal.title')}</Modal.Title>
        </Modal.Header>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-3">
              <h2 className="text-section-title text-error">{t('kgstudio.asset.tx-rejection-modal.warning.title')}</h2>
              <p className="text-body text-primary">{t('kgstudio.asset.tx-rejection-modal.warning.desc')}</p>
            </div>

            <FormTextarea
              name="review_note"
              control={form.control}
              title={t('kgstudio.asset.tx-rejection-modal.review-note.title')}
              required
              data-cy={'tx-rejection-note-textarea'}
              hint={t('kgstudio.asset.tx-rejection-modal.review-note.hint')}
            />

            <Modal.Footer className="flex items-center justify-between">
              <Button
                variant="grey"
                onClick={() => props.onOpenChange(false)}
                size="lg"
                disabled={cancelButtonDisabled}
              >
                {t('kgstudio.common.cancel')}
              </Button>
              <Button
                variant="dangerDark"
                size="lg"
                className="min-w-[200px]"
                loading={rejectionButtonLoading}
                disabled={rejectionButtonDisabled}
                data-cy="tx-rejection-button"
              >
                {t('kgstudio.asset.tx-rejection-modal.confirm-rejection')}
              </Button>
            </Modal.Footer>
          </form>
        </Form>
      </Modal.Content>
    </Modal>
  );
};

export { ConfirmRejectionModal };
