import { Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import ImageUploader, { ImageListType } from 'react-images-uploading';
import { toast } from 'sonner';
import { z } from 'zod';

import { AttachmentCard } from '@/app/[locale]/(protected)/asset/_components';
import { FormTextarea } from '@/app/_common/components/form';
import { Media, useGcpFileUpload } from '@/app/_common/hooks';
import { apiAssetProHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, FormLabel, InputError, InputHint, Label, Modal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

interface EditTxNoteModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  txId: string;
  note: string | null;
  attachments: string[] | null;
}

const EditTxNoteSchema = z.object({
  note: z
    .string()
    .transform((value) => (typeof value === 'string' ? value.trim() : value))
    .transform((value) => (value === '' ? null : value)),
});
type EditTxNoteFormValues = z.infer<typeof EditTxNoteSchema>;

const EditTxNoteModal = ({ txId, note, attachments, ...props }: EditTxNoteModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  const [orgId] = useOrganizationStore((state) => [state.orgId]);

  const form = useForm<EditTxNoteFormValues>({
    defaultValues: {
      note: note ?? '',
    },
    resolver: zodResolver(EditTxNoteSchema),
    mode: 'onChange',
  });

  const initialImageList = useMemo(
    () => attachments?.map((attachment) => ({ dataURL: attachment })) ?? [],
    [attachments],
  );
  const [imageList, setImageList] = useState<ImageListType>(() => initialImageList);

  const { isError: fileUrlsError, isLoading: fileUrlsLoading, uploadAllFiles } = useGcpFileUpload();

  const {
    mutate: updateTxNoteOrAttachments,
    isLoading: updateTxNoteOrAttachmentsLoading,
    isSuccess: updateTxNoteOrAttachmentsSuccess,
    isError: updateTxNoteOrAttachmentsError,
    reset: resetUpdateTxNoteOrAttachments,
  } = apiAssetProHooks.useUpdateTxNoteOrAttachments(
    {
      params: {
        org_id: Number(orgId),
        tx_id: txId,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionDetails'));

        props.onOpenChange(false);
      },
    },
  );

  const onSubmit = async (values: EditTxNoteFormValues) => {
    const localFiles = imageList.filter((image) => image.file);

    const payloadNote = note === values.note ? undefined : values.note;
    let payloadAttachments = undefined;

    if (localFiles.length === 0) {
      if (imageList.length !== initialImageList.length) {
        payloadAttachments = imageList.map((image) => image.dataURL as string);
      }
    } else {
      const localFileUrls = await uploadAllFiles({ attachments: localFiles as Media[] }, 'asset_pro');
      if (!localFileUrls) return;

      const unchangedImgs = imageList.filter((image) => !image.file).map((image) => image.dataURL as string);

      payloadAttachments = [...unchangedImgs, ...localFileUrls.attachments];
    }

    updateTxNoteOrAttachments({
      note: payloadNote,
      attachments: payloadAttachments,
    });
  };

  const updateButtonLoading = updateTxNoteOrAttachmentsLoading || fileUrlsLoading;
  const updateButtonDisabled = !form.formState.isValid || updateTxNoteOrAttachmentsSuccess;

  useEffect(() => {
    if (updateTxNoteOrAttachmentsError || fileUrlsError) {
      toast.error(t('kgstudio.asset.edit-tx-note-modal.update-failed-error'));
    }
  }, [updateTxNoteOrAttachmentsError, fileUrlsError, t]);

  useEffect(() => {
    if (props.open) {
      resetUpdateTxNoteOrAttachments();
      form.reset();
      setImageList(initialImageList);
    }
  }, [form, initialImageList, props.open, resetUpdateTxNoteOrAttachments]);

  return (
    <Modal {...props}>
      <Modal.Content scrollable className="max-h-[90%]">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.asset.edit-tx-note-modal.title')}</Modal.Title>
        </Modal.Header>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-list-medium">
            <FormTextarea
              name="note"
              title={t('kgstudio.asset.edit-tx-note-modal.note.title')}
              placeholder={t('kgstudio.asset.edit-tx-note-modal.note.placeholder')}
              control={form.control}
              className="min-h-[154px]"
              data-cy="edit-tx-note-textarea"
              hint={t('kgstudio.asset.edit-tx-note-modal.note.hint')}
            />
            <div>
              <FormLabel>{t('kgstudio.asset.edit-tx-note-modal.attachments.title')}</FormLabel>
              <ImageUploader
                multiple
                maxNumber={10}
                value={imageList}
                onChange={setImageList}
                maxFileSize={1024 * 1024}
                acceptType={['png', 'jpg', 'jpeg', 'webp']}
              >
                {({ imageList, onImageUpload, onImageRemove, dragProps, errors }) => {
                  return (
                    <div className="space-y-2">
                      <Label title={t('kgstudio.asset.edit-order-modal.upload-attachments')} />
                      <Button
                        type="button"
                        variant="grey-outlined"
                        icon={<Plus />}
                        size="lg"
                        onClick={onImageUpload}
                        {...dragProps}
                      />
                      <div className="flex flex-wrap items-center gap-[10px]">
                        {imageList.map((image, index) => (
                          <AttachmentCard
                            key={index}
                            imageSrc={image.dataURL ?? ''}
                            imageAlt={image.file?.name}
                            file={image.file}
                            onImageRemove={() => onImageRemove(index)}
                          />
                        ))}
                      </div>

                      <InputHint>{t('kgstudio.asset.edit-order-modal.max-file-upload-info')}</InputHint>
                      {errors?.acceptType && (
                        <InputError>{t('kgstudio.asset.edit-order-modal.accepted-file-types')}</InputError>
                      )}
                      {errors?.maxFileSize && (
                        <InputError>{t('kgstudio.asset.edit-order-modal.file-size-error')}</InputError>
                      )}
                      {errors?.maxNumber && (
                        <InputError>{t('kgstudio.asset.edit-order-modal.max-files-error')}</InputError>
                      )}
                    </div>
                  );
                }}
              </ImageUploader>
            </div>
            <Modal.Footer className="flex items-center justify-between">
              <Button
                variant="grey"
                onClick={() => props.onOpenChange(false)}
                size="lg"
                disabled={updateTxNoteOrAttachmentsLoading}
              >
                {t('kgstudio.common.cancel')}
              </Button>
              <Button
                variant="primary"
                size="lg"
                className="min-w-[200px]"
                loading={updateButtonLoading}
                disabled={updateButtonDisabled}
                data-cy="payment-update-button"
              >
                {t('kgstudio.common.update')}
              </Button>
            </Modal.Footer>
          </form>
        </Form>
      </Modal.Content>
    </Modal>
  );
};

export { EditTxNoteModal };
