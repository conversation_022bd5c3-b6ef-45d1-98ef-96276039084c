import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { ComponentPropsWithoutRef, useEffect, useMemo } from 'react';
import { toast } from 'sonner';
import { P, match } from 'ts-pattern';

import { DataList, RiskScanPanel } from '@/app/[locale]/(protected)/asset/_components';
import { TokenIcon } from '@/app/_common/components';
import { useTokenPrice } from '@/app/_common/hooks';
import { apiAssetProHooks } from '@/app/_common/services';
import { TxInfo } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import { Button, ErrorModal, Modal, SuccessModal } from '@kryptogo/2b';
import { DECIMAL_DISPLAY_MODE, formatCurrency, getChainFullName, getChainIcon } from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';

import { ReleaseCheckList } from '../ReleaseCheckList';

interface ConfirmApprovalOrReleaseModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  txInfo: TxInfo;
}

const ConfirmApprovalOrReleaseModal = ({ txInfo, ...props }: ConfirmApprovalOrReleaseModalProps) => {
  const t = useTranslations();

  const queryClient = useQueryClient();

  const [orgId] = useOrganizationStore((state) => [state.orgId]);

  const { tokenPriceUSD } = useTokenPrice(txInfo.token.contract_address, txInfo.token.chain_id);

  const {
    mutate: approveTx,
    isLoading: approveTxLoading,
    error: approveTxError,
    isSuccess: approveTxSuccess,
    reset: resetApproveTx,
  } = apiAssetProHooks.useTxApproval(
    {
      params: {
        org_id: Number(orgId),
        tx_id: txInfo.id,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionDetails'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionHistory'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getPendingTxHistoryCount'));
        props.onOpenChange(false);
        setTimeout(() => {
          resetApproveTx();
        }, 2000);
      },
    },
  );

  const {
    mutate: releaseTx,
    isLoading: releaseTxLoading,
    error: releaseTxError,
    isSuccess: releaseTxSuccess,
    reset: resetReleaseTx,
  } = apiAssetProHooks.useTxRelease(
    {
      params: {
        org_id: Number(orgId),
        tx_id: txInfo.id,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionDetails'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionHistory'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getPendingTxHistoryCount'));
        props.onOpenChange(false);
        setTimeout(() => {
          resetReleaseTx();
        }, 2000);
      },
    },
  );

  const {
    mutate: resendTx,
    isLoading: resendTxLoading,
    error: resendTxError,
    isSuccess: resendTxSuccess,
    reset: resetResendTx,
  } = apiAssetProHooks.useResendFailedTx(
    {
      params: {
        org_id: orgId ?? -1,
        tx_id: txInfo.id,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionDetails'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getTransactionHistory'));
        queryClient.refetchQueries(apiAssetProHooks.getKeyByAlias('getPendingTxHistoryCount'));
        props.onOpenChange(false);
        setTimeout(() => {
          resetResendTx();
        }, 2000);
      },
    },
  );

  const modalContent = match(txInfo.status)
    .with('awaiting_approval', () => ({
      title: t('kgstudio.asset.tx-confirm-approval-modal.title'),
      warning: {
        title: t('kgstudio.asset.tx-confirm-approval-modal.warning.title'),
        description: t('kgstudio.asset.tx-confirm-approval-modal.warning.description'),
      },
      rightButton: {
        text: t('kgstudio.asset.tx-confirm-approval-modal.approve'),
        action: () => approveTx({ operation: 'approve' }),
      },
    }))
    .with('awaiting_release', () => ({
      title: t('kgstudio.asset.tx-conform-release-modal.title'),
      warning: {
        title: t('kgstudio.asset.tx-confirm-release-modal.warning.title'),
        description: t('kgstudio.asset.tx-confirm-release-modal.warning.description'),
      },
      rightButton: {
        text: t('kgstudio.asset.tx-confirm-release-modal.release'),
        action: () => releaseTx({ operation: 'release' }),
      },
    }))
    .with('send_failed', () => ({
      title: t('kgstudio.asset.tx-conform-release-modal.title'),
      warning: {
        title: t('kgstudio.asset.tx-confirm-release-modal.warning.title'),
        description: t('kgstudio.asset.tx-confirm-release-modal.warning.description'),
      },
      rightButton: {
        text: t('kgstudio.asset.tx-confirm-release-modal.release'),
        action: () => resendTx(undefined),
      },
    }))
    .otherwise(() => null);

  const cancelButtonDisabled = match(txInfo.status)
    .with('awaiting_approval', () => approveTxLoading)
    .with('awaiting_release', () => releaseTxLoading)
    .with('send_failed', () => resendTxLoading)
    .otherwise(() => true);

  const rightButtonLoading = match(txInfo.status)
    .with('awaiting_approval', () => approveTxLoading)
    .with('awaiting_release', () => releaseTxLoading)
    .with('send_failed', () => resendTxLoading)
    .otherwise(() => false);
  const rightButtonDisabled = match(txInfo.status)
    .with('awaiting_approval', () => approveTxSuccess)
    .with('awaiting_release', () => releaseTxSuccess)
    .with('send_failed', () => resendTxSuccess)
    .otherwise(() => true);

  const [isInsufficientBalanceError, insufficientAmount] = useMemo(
    () =>
      match(releaseTxError || resendTxError)
        .with(
          {
            code: 4015,
            data: {
              insufficient_amount: P.select(),
            },
          },
          (amount) => [true, amount as string] as const,
        )
        .otherwise(() => [false, null] as const),
    [releaseTxError, resendTxError],
  );

  useEffect(() => {
    if (!!approveTxError || ((!!releaseTxError || !!resendTxError) && !isInsufficientBalanceError)) {
      toast.error(t('kgstudio.common.error'));
    }
  }, [approveTxError, releaseTxError, t, isInsufficientBalanceError, resendTxError]);

  if (isInsufficientBalanceError)
    return (
      <ErrorModal
        title={t('kgstudio.asset.tx-approval-modal.insufficient-balance-error.title', {
          tokenSymbol: txInfo.token.symbol,
          chainName: getChainFullName(txInfo.token.chain_id) || '',
        })}
        description={t('kgstudio.asset.tx-approval-modal.insufficient-balance-error.description', {
          tokenSymbol: txInfo.token.symbol,
          chainName: getChainFullName(txInfo.token.chain_id) || '',
          shortfallAmount: formatCurrency({ amount: insufficientAmount }),
        })}
        buttonText={t('kgstudio.common.i-understand')}
      />
    );

  if (approveTxSuccess || releaseTxSuccess)
    return <SuccessModal open title={t('kgstudio.asset.tx-approval-modal.success.title')} />;

  return (
    <Modal {...props}>
      <Modal.Content className="max-h-[90%] max-w-[688px]" scrollable>
        <Modal.Header>
          <Modal.Title>{modalContent?.title}</Modal.Title>
        </Modal.Header>
        <div className="space-y-6">
          <div className="space-y-3">
            <h2 className="text-success text-section-title">{modalContent?.warning.title}</h2>
            <p className="text-body text-primary">{modalContent?.warning.description}</p>
          </div>
          {(txInfo.status === 'awaiting_release' || txInfo.status === 'send_failed') && (
            <ReleaseCheckList token={txInfo.token} amount={txInfo.amount} />
          )}
          <div className="p-card-large border-primary grid grid-cols-2 gap-6 rounded-3xl border">
            <DataList
              title={t('kgstudio.asset.tx-confirm-approval-modal.send-token')}
              value={
                <div className="gap-item-small flex flex-col">
                  <div className="gap-item-small text-h2 text-primary flex flex-wrap items-center font-bold">
                    <TokenIcon
                      token={{
                        logoUrl: txInfo.token.logo_url,
                        name: txInfo.token.name,
                      }}
                      chain={txInfo.token.chain_id}
                    />
                    <span>{formatCurrency({ amount: txInfo.amount })}</span>

                    <span>{txInfo.token.symbol}</span>
                  </div>
                  <span className="text-secondary text-small">{`≈ ${formatCurrency({
                    amount: tokenPriceUSD?.times(txInfo.amount) ?? 0,
                    decimals: DECIMAL_DISPLAY_MODE.FIAT,
                    fmt: { suffix: ' USD' },
                  })}`}</span>
                </div>
              }
            />
            <DataList
              title={t('kgstudio.asset.tx-info-card.blockchain')}
              value={
                <div className="gap-item-small text-h2 text-primary flex items-center font-bold">
                  <div className="relative h-6 w-6 overflow-clip rounded-full">
                    {
                      <Image
                        src={getChainIcon(txInfo.token.chain_id) ?? ''}
                        alt={txInfo.token.chain_id}
                        fill
                        className="object-cover"
                      />
                    }
                  </div>
                  <span>{getChainFullName(txInfo.token.chain_id)}</span>
                </div>
              }
            />
            <DataList
              title={t('kgstudio.asset.tx-info-card.recipient')}
              value={
                <RiskScanPanel
                  scannable={false}
                  customer={{
                    displayName: txInfo.recipient.customer?.name,
                    email: txInfo.recipient.customer?.email ?? undefined,
                    phone: txInfo.recipient.customer?.phone ?? undefined,
                    wallet: {
                      chain: txInfo.token.chain_id,
                      address: txInfo.recipient.wallet_address,
                    },
                    kycStatus: txInfo.recipient.customer?.kyc_status,
                  }}
                  t={t}
                />
              }
              className="col-span-2"
            />
          </div>
        </div>
        <Modal.Footer className="flex items-center justify-between">
          <Button variant="grey" onClick={() => props.onOpenChange(false)} size="lg" disabled={cancelButtonDisabled}>
            {t('kgstudio.common.cancel')}
          </Button>
          <Button
            variant="green"
            size="lg"
            loading={rightButtonLoading}
            disabled={rightButtonDisabled}
            data-cy="tx-approval-release-button"
            onClick={modalContent?.rightButton.action}
          >
            {modalContent?.rightButton.text}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { ConfirmApprovalOrReleaseModal };
