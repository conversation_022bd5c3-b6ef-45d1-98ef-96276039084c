'use client';

import BigNumber from 'bignumber.js';
import * as A from 'fp-ts/Array';
import * as O from 'fp-ts/Option';
import { pipe } from 'fp-ts/lib/function';
import * as S from 'fp-ts/string';
import { some, uniqBy } from 'lodash-es';
import { FileText, Search } from 'lucide-react';
import moment from 'moment';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import qs from 'qs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { match, P } from 'ts-pattern';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { TokenIcon } from '@/app/_common/components';
import { TxStatusBadge } from '@/app/_common/components/badge';
import { FilterItem, FormFilterGroup } from '@/app/_common/components/form';
import { useDebounce, usePageHeader, usePermissions, useUpdateEffect } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { cn, removeEmptyArray, removeEmptyString } from '@/app/_common/lib/utils';
import { zodFilterSchema } from '@/app/_common/lib/zod';
import { apiAssetProHooks } from '@/app/_common/services';
import {
  AllTxStatus,
  AllTxStatusSchema,
  AssetProChainIdSchema,
  AssetProTransaction,
  TransactionFilterOptions,
} from '@/app/_common/services/asset-pro/model';
import { useAuthStore } from '@/app/_common/store';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { usePathname, useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, DataTable, Form, Input, Skeleton, useDataTable } from '@kryptogo/2b';
import { formatCurrency, getExplorerUrl, truncateTxhashOrAddress, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';
import { ColumnDef, getCoreRowModel, TableOptions, useReactTable } from '@tanstack/react-table';

import { KycStatusIcon } from './_components/KycStatusIcon';

const TransactionFilterSchema = z
  .object({
    status: z.array(AllTxStatusSchema).optional(),
    submitter: z.string().optional(),
    approver: z.string().optional(),
    rejecter: z.string().optional(),
    finance_manager: z.string().optional(),
    token: z.string().optional(),
    chain_id: AssetProChainIdSchema.optional(),
    amount_from: z.coerce.number().optional(),
    amount_to: z.coerce.number().optional(),
    submit_time_from: z.coerce
      .date()
      .optional()
      .transform((date) => (!!date ? Math.floor(date.getTime() / 1000) : undefined)),
    submit_time_to: z.coerce
      .date()
      .optional()
      .transform((date) => (!!date ? Math.floor(date.getTime() / 1000) : undefined)),
  })
  .strip();
type TransactionFilter = z.infer<typeof TransactionFilterSchema>;

export default function Transactions() {
  const pathname = usePathname();
  const router = useRouter();
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.transactions.title') });
  const params = useSearchParams();
  const parsedParams = zodFilterSchema(TransactionFilterSchema).parse(
    qs.parse(decodeURIComponent(params.toString()), { comma: true }),
  );
  const [hasReadAllPermission, hasApproveTransactionPermission, hasReleaseTransactionPermission] = usePermissions(
    ['transaction', 'read_all'],
    ['transaction', 'approve'],
    ['transaction', 'release'],
  );

  const userInfo = useAuthStore((state) => state.userInfo);
  const orgId = useOrganizationStore((state) => state.orgId);
  const [tableOption, setTableOption] = useState<TableOptions<AssetProTransaction['data'][number]>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size, page_sort } = useDataTable(table);

  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);

  const form = useForm<TransactionFilter>({
    defaultValues: {
      submitter: undefined,
      approver: undefined,
      finance_manager: undefined,
      status: undefined,
      token: undefined,
      chain_id: undefined,
      amount_from: undefined,
      amount_to: undefined,
      submit_time_from: undefined,
      submit_time_to: undefined,
    },
    values: {
      ...parsedParams,
      submitter: parsedParams.submitter ? parsedParams.submitter : hasReadAllPermission ? '' : userInfo?.uid,
    },
    mode: 'onChange',
    resolver: zodResolver(TransactionFilterSchema),
  });

  const formStatus = form.watch('status');

  const isFilterSelected = pipe(form.watch(), removeEmptyString, removeEmptyArray, some);

  const { data: transactions, isLoading: transactionsLoading } = apiAssetProHooks.useGetTransactionHistory(
    {
      params: { org_id: Number(orgId) },
      queries: {
        page_size: page_size,
        page_number: page_number,
        q: debouncedQuery.trim() === '' ? undefined : debouncedQuery,
        page_sort: page_sort === '' ? undefined : page_sort,
        ...removeEmptyString(TransactionFilterSchema.parse(form.watch())),
      },
      paramsSerializer: (params) => {
        return qs.stringify(params, { indices: false });
      },
    },
    {
      enabled: !!orgId,
      onError: (error) => {
        console.error('error', error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchInterval: 30_000,
    },
  );

  const { data: tokenList } = apiAssetProHooks.useGetTokenList(
    {},
    {
      onError: (error) => {
        console.error('error', error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  const uniqChains = useMemo(
    () =>
      uniqBy(tokenList?.data, 'chain_id').map((d) => {
        return { label: d.chain_name, value: d.chain_id };
      }),
    [tokenList],
  );
  const uniqTokenCoinGeckoIds = useMemo(
    () =>
      uniqBy(tokenList?.data, 'coingecko_id').map((d) => {
        return d.coingecko_id;
      }),
    [tokenList],
  );

  const { data: tokenPrices, error: assetPriceError } = apiAssetProHooks.useGetAssetPrices(
    {
      queries: { assets_cid: uniqTokenCoinGeckoIds },
      paramsSerializer: (params) => qs.stringify(params, { indices: false }),
    },
    {
      enabled: uniqTokenCoinGeckoIds.length > 0,
    },
  );

  const { data: txOptions, isLoading: txOptionsLoading } = apiAssetProHooks.useGetTransactionHistoryFilterOptions(
    {
      params: { org_id: Number(orgId) },
    },
    {
      enabled: !!orgId,
      onError: (error) => {
        console.error('error', error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  const getFilterItems = useCallback(
    (txOptions: TransactionFilterOptions | undefined) => {
      if (!txOptions) return undefined;
      const {
        status,
        chain_id,
        token,
        amount_from,
        amount_to,
        submit_time_from,
        submit_time_to,
        submitter,
        approver,
        finance_manager,
        rejecter,
      } = txOptions.data;

      const amount = {
        from: amount_from,
        to: amount_to,
      };
      const submitTime = {
        from: submit_time_from,
        to: submit_time_to,
      };

      const getFilterFieldsByStatus = (status: AllTxStatus) => {
        return match(status)
          .with('awaiting_approval', () => [])
          .with('awaiting_release', () => ['approver'])
          .with('rejected', () => ['approver', 'rejecter'])
          .with('sending', () => ['approver', 'finance_manager'])
          .otherwise(() => ['approver', 'finance_manager', 'rejecter']);
      };
      const isStatusUnChecked = Array.isArray(formStatus) && formStatus.length === 0;
      const responsiveFilterFields = pipe(
        formStatus,
        O.fromNullable,
        O.map(A.flatMap(getFilterFieldsByStatus)),
        O.map(A.uniq(S.Eq)),
        O.match(
          () => ({ approver, finance_manager, rejecter }),
          (uniqueOptions) =>
            Object.fromEntries(
              uniqueOptions.map((d) => [d, txOptions.data?.[d as keyof TransactionFilterOptions['data']]]),
            ),
        ),
      );

      const options = {
        status,
        submit_time: submitTime,
        submitter,
        chain_id,
        token,
        amount,
        ...(isStatusUnChecked ? { approver, finance_manager, rejecter } : responsiveFilterFields),
      };

      const filteritems = Object.keys(options).reduce<FilterItem<TransactionFilter>[]>((items, key) => {
        const item = match(key)
          //
          .with('status', (k) => ({
            name: k,
            subject: t('common.status.title'),
            type: 'checkbox',
            options: txOptions.data[k]
              .filter((status) => !['rejected', 'awaiting_approval', 'awaiting_release'].includes(status))
              .map((status) =>
                match(status)
                  .with('send_success', () => ({ label: t('kgstudio.transaction.status-success'), value: status }))
                  .with('sending', () => ({ label: t('kgstudio.asset.tx-detail.steps.sending'), value: status }))
                  .with('send_failed', () => ({ label: t('kgstudio.transaction.status-failed'), value: status }))
                  // .with('rejected', () => ({ label: t('kgstudio.asset.tx-detail.steps.rejected'), value: status }))
                  // .with('awaiting_approval', () => ({
                  //   label: t('kgstudio.asset.tx-detail.steps.awaiting-approval'),
                  //   value: status,
                  // }))
                  // .with('awaiting_release', () => ({
                  //   label: t('kgstudio.transaction.status-awaiting-release'),
                  //   value: status,
                  // }))
                  .run(),
              ),
          }))
          // .with('submitter', (k) => ({
          //   name: `submitter`,
          //   subject: t('kgstudio.asset.tx-history-card.submitted-by'),
          //   type: 'select',
          //   hideOptionAll: !hasReadAllPermission,
          //   options: hasReadAllPermission
          //     ? txOptions.data[k].map((op) => ({ label: op.name, value: op.uid }))
          //     : [{ label: 'Me', value: userInfo?.uid }],
          // }))
          // .with('approver', (k) => ({
          //   name: 'approver',
          //   subject: t('kgstudio.asset.tx-history.approved-by'),
          //   type: 'select',
          //   hideOptionAll: !hasReadAllPermission,
          //   options: hasReadAllPermission
          //     ? txOptions.data[k].map((op) => ({ label: op.name, value: op.uid }))
          //     : [{ label: 'Me', value: userInfo?.uid }],
          // }))
          // .with('finance_manager', (k) => ({
          //   name: 'finance_manager',
          //   subject: t('kgstudio.asset.tx-history.released-by'),
          //   type: 'select',
          //   hideOptionAll: !hasReadAllPermission,
          //   options: hasReadAllPermission
          //     ? txOptions.data[k].map((op) => ({ label: op.name, value: op.uid }))
          //     : [{ label: 'Me', value: userInfo?.uid }],
          // }))
          // .with('rejecter', (k) => ({
          //   name: 'rejecter',
          //   subject: t('kgstudio.asset.tx-history.rejected-by'),
          //   type: 'select',
          //   hideOptionAll: !hasReadAllPermission,
          //   options: hasReadAllPermission
          //     ? txOptions.data[k].map((op) => ({ label: op.name, value: op.uid }))
          //     : [{ label: 'Me', value: userInfo?.uid }],
          // }))
          .with('chain_id', (k) => ({
            name: k,
            subject: t('common.blockchain'),
            type: 'select',
            options: txOptions.data[k].map((chainId) => ({
              label: uniqChains.find((chain) => chain.value === chainId)?.label || '',
              value: chainId,
            })),
          }))
          .with('token', (k) => ({
            name: k,
            subject: t('common.token'),
            type: 'select',
            options: txOptions.data[k].map((token) => ({ label: token, value: token })),
          }))
          .with('amount', (k) => ({
            names: [`${k}_from`, `${k}_to`],
            subject: t('common.amount'),
            type: 'range',
            min: options[k].from,
            max: options[k].to,
          }))
          .with('submit_time', (k) => ({
            names: [`${k}_from`, `${k}_to`],
            subject: t('kgstudio.transaction.submit-time'),
            type: 'date-range',
            fromDate: options[k].from && new Date((options[k].from ?? 0) * 1000),
            toDate: options[k].to && new Date((options[k].to ?? 0) * 1000),
          }))
          .otherwise(() => undefined);

        if (!item) return items as FilterItem<TransactionFilter>[];

        return [...items, item] as FilterItem<TransactionFilter>[];
      }, [] as FilterItem<TransactionFilter>[]);

      return filteritems;
    },
    [hasReadAllPermission, t, uniqChains, userInfo?.uid, formStatus],
  );
  const filterItems = useMemo(() => getFilterItems(txOptions), [getFilterItems, txOptions]);

  const columns: ColumnDef<AssetProTransaction['data'][number]>[] = useMemo(
    () => [
      {
        accessorKey: 'submit_time',
        header: t('kgstudio.transaction.submit-time'),
        meta: {
          sortable: true,
          withoutPadding: true,
        },
        cell: ({ row }) => {
          const { submit_time, status } = row.original;
          const borderColor = match(status)
            .with('sending', () => 'border-processing')
            .with('awaiting_approval', 'awaiting_release', () => 'border-warning')
            .with('send_success', () => 'border-success')
            .with('send_failed', 'rejected', () => 'border-error')
            .exhaustive();
          return (
            <>
              <div className={cn('absolute left-0 top-0 h-full border-l-4', borderColor)}></div>
              <div className="w-full p-4">
                <p className="text-primary text-body-2">{moment(submit_time * 1000).format('YYYY/MM/DD')}</p>
                <p className="text-primary text-body-2">{moment(submit_time * 1000).format('HH:mm')}</p>
              </div>
            </>
          );
        },
      },
      {
        accessorKey: 'token_name',
        header: t('common.send-token'),
        cell: ({ row }) => {
          const { token_logo_url, token_name, chain_id } = row.original;
          return (
            <div className="flex items-center gap-3">
              <TokenIcon size="40" token={{ logoUrl: token_logo_url as string, name: token_name }} chain={chain_id} />

              <div>
                <p className="text-primary text-body-2-bold">{token_name === 'Matic' ? 'POL' : token_name}</p>
                <p className="text-secondary text-small">
                  {uniqChains.find((chain) => chain.value === chain_id)?.label}
                </p>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'amount',
        header: t('common.amount'),
        size: 120,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { amount, token_coingecko_id } = row.original;
          // NOTE: Since BE data is not guaranteed to be correct, we need to handle the case where token_coingecko_id is empty string ''
          const price = !!tokenPrices ? BigNumber(tokenPrices?.data?.[token_coingecko_id]?.price) : undefined;
          const value = !!price
            ? pipe(
                price.times(BigNumber(amount)),
                O.fromNullable,
                O.map((n) =>
                  formatCurrency({
                    amount: n,
                    decimals: DECIMAL_DISPLAY_MODE.FIAT,
                    fmt: { suffix: ' USD' },
                  }),
                ),
                O.getOrElseW(() => undefined),
              )
            : undefined;

          return (
            <>
              <p className="text-primary text-body-2-bold">
                {formatCurrency({ amount: amount, fmt: { prefix: '$' } })}
              </p>
              {!token_coingecko_id ? null : !!value ? (
                <p className="text-secondary text-small">≈ {value}</p>
              ) : (
                <Skeleton className="h-[18px] w-[60px]" />
              )}
            </>
          );
        },
      },
      {
        accessorKey: 'recipient',
        header: t('common.recipient'),
        cell: ({ row }) => {
          const { recipient, chain_id } = row.original;

          const name = recipient.name ? recipient.name : t('common.anonymous');

          return (
            <>
              <div className="flex items-center gap-2">
                <p className="text-primary text-body-2">{name}</p>
                {recipient.kyc_status && <KycStatusIcon status={recipient.kyc_status} />}
              </div>
              {recipient.phone && <p className="text-secondary text-small">{recipient.phone}</p>}
              {recipient.email && <p className="text-secondary text-small truncate">{recipient.email}</p>}
              <Link
                className="text-brand-primary text-small"
                href={getExplorerUrl('address', chain_id, recipient.wallet_address) ?? ''}
              >
                {truncateTxhashOrAddress(recipient.wallet_address)}
              </Link>
            </>
          );
        },
      },
      {
        accessorKey: 'status',
        header: t('common.status.text'),
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { status } = row.original;
          return <TxStatusBadge status={status} t={t} />;
        },
      },
      {
        accessorKey: 'tx_hash',
        header: t('common.tx-hash'),
        cell: ({ row }) => {
          const { tx_hash, chain_id } = row.original;

          return !tx_hash ? (
            ''
          ) : (
            <Link
              className="text-primary text-body-2 underline"
              href={getExplorerUrl('tx', chain_id, tx_hash) ?? ''}
              target="_blank"
              rel="noopener noreferrer"
              onClick={(e) => e.stopPropagation()}
            >
              {truncateTxhashOrAddress(tx_hash)}
            </Link>
          );
        },
      },
      {
        accessorKey: 'update_time',
        header: t('kgstudio.asset.tx-history.latest-update'),
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { update_time, status, updated_by } = row.original;
          const description = match(status)
            .with('awaiting_approval', () => t('kgstudio.asset.tx-history.submitted', { name: updated_by || '' }))
            .with('awaiting_release', () => t('kgstudio.asset.tx-history.approved', { name: updated_by || '' }))
            .with('sending', () => t('kgstudio.asset.tx-history.released', { name: updated_by || '' }))
            .with('rejected', () => t('kgstudio.asset.tx-history.rejected', { name: updated_by || '' }))
            .otherwise(() => null);
          return (
            <>
              <p className="text-primary text-body-2">{moment(update_time * 1000).format('YYYY/MM/DD HH:mm')}</p>
              {description && <p className="text-secondary text-small">{description}</p>}
            </>
          );
        },
      },
      {
        id: 'action',
        header: () => <div className="flex justify-end">{t('common.action')}</div>,
        cell: ({ row }) => {
          const { id, status } = row.original;
          return (
            <div className="flex justify-end gap-2">
              {match([hasApproveTransactionPermission, hasReleaseTransactionPermission, status])
                .with([true, P._, 'awaiting_approval'], () => (
                  <Button
                    size="md"
                    className="leading-none"
                    onClick={() => {
                      router.push(`/asset/transactions/${id}`);
                    }}
                  >
                    {t('kgstudio.review.btn')}
                  </Button>
                ))
                .with([P._, true, 'awaiting_release'], () => (
                  <Button
                    size="md"
                    className="leading-none"
                    onClick={() => {
                      router.push(`/asset/transactions/${id}`);
                    }}
                  >
                    {t('kgstudio.review.btn')}
                  </Button>
                ))
                .otherwise(() => (
                  <Button
                    variant="grey"
                    className="h-9 w-9"
                    onClick={() => {
                      router.push(`/asset/transactions/${id}`);
                    }}
                    icon={<FileText />}
                  ></Button>
                ))}
            </div>
          );
        },
      },
    ],
    [t, uniqChains, tokenPrices, hasApproveTransactionPermission, hasReleaseTransactionPermission, router],
  );

  useEffect(() => {
    if (!assetPriceError) return;

    console.error('error', assetPriceError);
    if (isApiError(assetPriceError)) {
      showToast(t('kgstudio.common.error'), 'error');
    }
  }, [assetPriceError, t]);

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: transactions?.data || [],
      columns,
    }));
  }, [columns, transactions]);

  useUpdateEffect(() => {
    table.resetPageIndex();
  }, [JSON.stringify(form.watch()), debouncedQuery]);

  return (
    <>
      <Form {...form}>
        <div className="my-6 flex flex-wrap items-center gap-3">
          {txOptionsLoading || !filterItems ? (
            <div className="flex items-center gap-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-9 w-[150px]" />
              ))}
            </div>
          ) : (
            <FormFilterGroup
              control={form.control}
              items={filterItems}
              data-cy="txs-filter-group"
              {...(isFilterSelected && {
                onClearFilter: () => {
                  params.toString() ? router.push(pathname) : form.reset();
                },
              })}
            />
          )}
        </div>
      </Form>
      <Card className="!p-0">
        <div className="w-full p-4 md:p-6">
          <Input
            className="w-full md:!w-[312px]"
            placeholder={t('kgstudio.transaction.placeholder')}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
            value={query}
            suffix={<Search />}
          />
        </div>
        <DataTable
          table={table}
          isLoading={transactionsLoading}
          dataLength={transactions?.paging.total_count || 0}
          onRowClick={(rowData) => {
            router.push(`/asset/transactions/${rowData.id}`);
          }}
        />
      </Card>
    </>
  );
}
