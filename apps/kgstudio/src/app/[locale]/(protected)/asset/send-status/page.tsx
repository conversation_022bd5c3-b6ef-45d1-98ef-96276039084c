'use client';

import { ChevronLeft } from 'lucide-react';
import moment from 'moment';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { AssetProChainId } from '@/app/_common/services/asset-pro/model';
import { useRouter } from '@/i18n/navigation';
import { Button, Card, Label, Spinner } from '@kryptogo/2b';
import { getExplorerUrl, truncateTxhashOrAddress, getChainFullName } from '@kryptogo/utils';

import errorImg from '../_assets/error.svg';
import linkImg from '../_assets/link.svg';
import successImg from '../_assets/success.svg';
import { useTransactionStatus } from '../_hooks/status';

export default function SendTokenStatus() {
  const t = useTranslations();
  const router = useRouter();
  const searchParams = useSearchParams();
  const tx_hash = searchParams?.get('tx_hash');
  const chain = searchParams?.get('chain');
  const amount = searchParams?.get('amount');
  const symbol = searchParams?.get('symbol');
  const recipient = searchParams?.get('recipient');

  const chainId = (chain as AssetProChainId) || '';
  const txHash = (tx_hash as string) || '';

  const [time, setTime] = useState<Date | null>(null);

  useEffect(() => {
    setTime(new Date());
  }, []);

  const { loading, success } = useTransactionStatus(chainId, txHash);

  return !chainId || !txHash ? (
    <div>Invalid data</div>
  ) : (
    <div className="flex flex-col items-center justify-center" data-cy="send-token-status-dialog">
      <Card className="section-padding flex flex-col items-center justify-center gap-6 md:mx-[100px]">
        {loading && (
          <div className="flex flex-col items-center justify-center">
            <Spinner className="h-[60px] w-[60px] animate-spin" />
            <Label className="mt-8 text-center text-2xl">
              {t('kgstudio.send.tx-in-progress')}
              <br />
              {t('kgstudio.send.do-not-leave-page')}
            </Label>
          </div>
        )}
        {!loading && success && (
          <div className="flex flex-col items-center justify-center">
            <Image src={successImg} alt="success" />
            <Label className="mt-8 text-2xl">{t('kgstudio.send.tx-success')}</Label>
          </div>
        )}

        {!loading && !success && (
          <div className="flex flex-col items-center justify-center">
            <Image src={errorImg} alt="error" />
            <Label className="mt-8 text-2xl">{t('kgstudio.send.tx-failed')}</Label>
            <Label className="mt-6 text-center text-base font-normal">{t('kgstudio.send.tx-failed-description')}</Label>
          </div>
        )}

        <div className="flex w-full flex-row items-center justify-center">
          <Label className="text-base font-normal">{t('common.tx-hash')}: </Label>
          <Link
            href={getExplorerUrl('tx', chainId, txHash) ?? '#'}
            target="_blank"
            rel="noreferrer"
            className="flex flex-row items-center justify-center"
          >
            <Label className="text-brand-500 ml-2 mr-1 text-lg font-bold hover:cursor-pointer">
              {truncateTxhashOrAddress(txHash)}
            </Label>
            <Image src={linkImg} alt="link" className="mx-0" />
          </Link>
        </div>

        {loading && (
          <Label className="max-w-[600px] items-center text-center text-sm font-normal text-[#909AB6]">
            {t('kgstudio.send.loading-hint')}
          </Label>
        )}

        <Card className="flex w-full flex-col items-center gap-2 bg-[#F9F9F9] p-4 shadow-none">
          <Label className="text-base font-normal text-[#172133]">
            {time && moment(time).format('YYYY/MM/DD HH:mm')}
          </Label>
          <div className="text-center text-base font-normal tracking-[0.01em] text-[#172133]">
            {t('common.send')}{' '}
            <span className="font-bold">
              {Number(amount).toLocaleString(undefined, {
                minimumFractionDigits: 0,
                maximumFractionDigits: 18,
              })}{' '}
              {symbol} ({getChainFullName(chainId)})
            </span>{' '}
            {t('kgstudio.send.to-user')} <span className="font-bold">&quot;{recipient}&quot;</span>
          </div>
        </Card>

        {!loading && (
          <Button
            data-cy="send-token-status-go-back-btn"
            className="mt-8 max-w-[150px]"
            variant="outline"
            onClick={() => {
              router.push('/asset/transfer');
            }}
          >
            <ChevronLeft height="16" width="16" /> {t('common.go-back')}
          </Button>
        )}
      </Card>
    </div>
  );
}
