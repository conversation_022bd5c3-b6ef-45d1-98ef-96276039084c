import { AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormTextarea } from '@/app/_common/components/form';
import { apiAssetProOrderHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

interface CancelOrderModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderId: string;
}

const CancelOrderFormSchema = (t: any) =>
  z.object({
    internal_note: z.string().nonempty(),
  });
type CancelOrderFormValues = z.infer<ReturnType<typeof CancelOrderFormSchema>>;

const CancelOrderModal = ({ orderId, ...props }: CancelOrderModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  const orgId = useOrganizationStore((state) => state.orgId);

  const {
    mutate: cancelOrder,
    isLoading: cancelOrderLoading,
    reset: resetCancelOrder,
  } = apiAssetProOrderHooks.useCancelOrder(
    {
      params: {
        order_id: orderId,
        org_id: orgId ?? -1,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProOrderHooks.getKeyByAlias('getOrderDetails'));
        queryClient.refetchQueries(apiAssetProOrderHooks.getKeyByAlias('getPendingOrderCount'));
        props.onOpenChange(false);
      },
      onError: () => {
        showToast(t('kgstudio.common.error'), 'error');
      },
      cacheTime: 0,
    },
  );

  const form = useForm<CancelOrderFormValues>({
    defaultValues: {
      internal_note: '',
    },
    resolver: zodResolver(CancelOrderFormSchema(t)),
  });

  const onSubmit = (data: CancelOrderFormValues) => {
    cancelOrder(data);
  };

  useEffect(() => {
    if (!props.open) {
      resetCancelOrder();
      form.reset();
    }
  }, [props.open, form, resetCancelOrder]);

  return (
    <Modal {...props}>
      <Modal.Content data-cy="cancel-order-modal">
        <Form {...form}>
          <div className="space-y-item-medium text-center">
            <AlertCircle size={36} className="fill-error stroke-surface-primary mx-auto" />
            <h2 className="text-h2 text-primary">{t('kgstudio.asset.order-detail.cancel-modal.title')}</h2>
            <p className="text-body text-secondary">{t('kgstudio.asset.order-detail.cancel-modal.description')}</p>
            <FormTextarea
              control={form.control}
              title={t('kgstudio.asset.order-detail.summary.internal-note')}
              name="internal_note"
              required
              className="min-h-[154px]"
            />
          </div>
        </Form>
        <Modal.Footer className="gap-item-medium grid grid-cols-2">
          <Modal.Close asChild>
            <Button type="button" variant={'grey'} size={'lg'}>
              {t('kgstudio.common.back')}
            </Button>
          </Modal.Close>
          <Button
            variant={'primary'}
            size={'lg'}
            type="submit"
            onClick={form.handleSubmit(onSubmit)}
            disabled={!form.formState.isValid}
            loading={cancelOrderLoading}
          >
            {t('kgstudio.asset.order-detail.cancel-modal.cancel')}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { CancelOrderModal };
