import { useTranslations } from 'next-intl';
import { CSSProperties, ComponentProps } from 'react';

import { DataList, ProcessBy } from '@/app/[locale]/(protected)/asset/_components';
import { ExplorerLink } from '@/app/_common/components';
import { TxStatusBadge } from '@/app/_common/components/badge';
import { formatDate } from '@/app/_common/lib/utils';
import { OrderDetails } from '@/app/_common/services/asset-pro-order/model';
import { TxStatus } from '@/app/_common/services/asset-pro/model';
import { ChainId } from '@/app/_common/types/web3';
import { But<PERSON>, Card, ReminderBlock } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';

import { TxHahsWithStatus } from '../TxHahsWithStatus';

interface ShipmentDetailsCardProps {
  orderDetails: OrderDetails;
  handleOpenTxRequestModal: () => void;
}

const ShipmentDetailsCard = ({ orderDetails, handleOpenTxRequestModal }: ShipmentDetailsCardProps) => {
  const t = useTranslations();

  interface ExtendedDataListProps extends ComponentProps<typeof DataList> {
    span?: number;
  }
  const getShipmentDetailsData = (
    shipmentDetails: NonNullable<OrderDetails['shipment_details']>,
    chain: ChainId,
  ): ExtendedDataListProps[] => {
    const txStatus = shipmentDetails.tx_status;
    const txHash = shipmentDetails.tx_hash as `0x${string}`;
    const processedBy = shipmentDetails.processed_by;

    return [
      {
        title: t('kgstudio.asset.order-detail.order-information.tx-id'),
        value: shipmentDetails.tx_id ?? '-',
      },
      {
        title: t('kgstudio.common.transaction-status'),
        value: !!txStatus ? <TxStatusBadge status={txStatus} t={t} /> : '-',
      },
      {
        title: t('kgstudio.common.send-to'),
        value: <ExplorerLink hex={shipmentDetails.send_to as `0x${string}`} type="address" chain={chain} truncate />,
      },
      {
        title: t('kgstudio.common.tx-hash'),
        value: txHash ? <TxHahsWithStatus txHash={txHash} status={txStatus as TxStatus} chain={chain} /> : '-',
      },
      {
        title: t('kgstudio.asset.order-detail.summary.process-by'),
        value: processedBy ? (
          <ProcessBy name={processedBy.name} profileImage={processedBy.profile_img} email={processedBy.email} />
        ) : (
          '-'
        ),
      },
    ];
  };

  if (!orderDetails.shipment_details) return null;

  return (
    //FIXME: replace gap-list-medium with gap-list-large, and implement the value of the gap-list-large
    <Card className="gap-list-medium flex flex-col" data-cy="order-shipment-details-card">
      <SectionHeader>
        <SectionHeader.Title title="Shipment Details" />
        {orderDetails.shipment_details.tx_status === 'send_failed' && (
          <SectionHeader.Actions>
            <Button variant={'primary'} size="md" onClick={handleOpenTxRequestModal}>
              {t('kgstudio.common.send-now')}
            </Button>
          </SectionHeader.Actions>
        )}
      </SectionHeader>
      {orderDetails.shipping_status === 'send_failed' && orderDetails.shipment_details.shipped_at && (
        <ReminderBlock
          variant="warning"
          description={`${t('kgstudio.asset.tx-failed')} (${formatDate(orderDetails.shipment_details.shipped_at)})`}
        />
      )}
      <div className="grid w-full grid-cols-2 gap-6">
        {getShipmentDetailsData(orderDetails.shipment_details, orderDetails.purchase.chain_id).map((data) => (
          <DataList
            key={data.title}
            className="col-[span_var(--data-col-span)]"
            style={
              {
                '--data-col-span': data.span || 1,
              } as CSSProperties
            }
            {...data}
          />
        ))}
      </div>
    </Card>
  );
};

export { ShipmentDetailsCard };
