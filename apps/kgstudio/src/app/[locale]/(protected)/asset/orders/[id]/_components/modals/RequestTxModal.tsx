'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { ComponentProps, useEffect } from 'react';
import { match } from 'ts-pattern';

import { Customer, DataList, RiskScanPanel } from '@/app/[locale]/(protected)/asset/_components';
import { TokenIcon } from '@/app/_common/components';
import { apiAssetProOrderHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { Button, ErrorModal, Modal, ReminderBlock } from '@kryptogo/2b';
import { getChainFullName, getChainIcon } from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';

interface RequestTxModalProps extends ComponentProps<typeof Modal> {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderId: string;
  recipient: Customer;
  purchase: {
    token: ComponentProps<typeof TokenIcon>['token'];
    chain: ComponentProps<typeof TokenIcon>['chain'];
    amount: string;
  };
}

const RequestTxModal = ({ orderId, recipient, purchase, ...props }: RequestTxModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  const orgId = useOrganizationStore((state) => state.orgId);
  const {
    mutate: transferAssets,
    isLoading: transferAssetsLoading,
    error: transferAssetsError,
    reset: resetTransferAssets,
  } = apiAssetProOrderHooks.useTransferOrderAssets(
    {
      params: {
        order_id: orderId,
        org_id: orgId ?? -1,
      },
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(apiAssetProOrderHooks.getKeyByAlias('getOrderDetails'));
        queryClient.refetchQueries(apiAssetProOrderHooks.getKeyByAlias('getPendingOrderCount'));
        props.onOpenChange(false);
      },
      cacheTime: 0,
    },
  );

  const errorTexts = match(transferAssetsError as any)
    .with({ code: 6003 }, () => ({
      title: t('kgstudio.asset.tx-limit-exceeded'),
      description: t('kgstudio.asset.tx-limit-exceeded-contact-admin'),
    }))
    .with({ code: 4015 }, () => ({
      title: t('kgstudio.asset.tx-insufficient-balance'),
      description: t('kgstudio.asset.tx-insufficient-balance-admin-recharge', {
        // FIXME: fix type
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        insufficientAmount: transferAssetsError?.data?.insufficient_amount,
        tokenName: purchase.token.name || '',
        chainName: getChainFullName(recipient.wallet.chain) || '' || '',
      }),
    }))
    .otherwise(() => ({
      title: t('kgstudio.asset.tx-error-generic'),
      description: t('kgstudio.asset.tx-error-processing'),
    }));

  const handleCancel = () => {
    props.onOpenChange(false);
  };

  const handleSubmitTx = () => {
    transferAssets(null);
  };

  useEffect(() => {
    if (!props.open) {
      resetTransferAssets();
    }
  }, [props.open, resetTransferAssets]);

  return transferAssetsError ? (
    <ErrorModal
      {...props}
      title={errorTexts.title}
      description={errorTexts.description}
      buttonText={t('kgstudio.asset.understand')}
      data-cy="request-tx-error-modal"
    />
  ) : (
    <Modal {...props}>
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>{t('kgstudio.asset.check-tx')}</Modal.Title>
        </Modal.Header>

        <div className="space-y-list-medium">
          <div className="gap-list-medium grid grid-cols-2">
            <DataList
              title={t('kgstudio.common.send-token')}
              value={
                <div className="gap-item-small text-h2 text-primary flex flex-wrap items-center font-bold">
                  <TokenIcon
                    token={{
                      logoUrl: purchase.token.logoUrl,
                      name: purchase.token.name,
                    }}
                    chain={purchase.chain}
                    showChainIcon={false}
                  />
                  <span>{purchase.amount}</span>

                  <span>{purchase.token.name?.toUpperCase()}</span>
                </div>
              }
            />
            <DataList
              title={t('kgstudio.common.blockchain')}
              value={
                <div className="gap-item-small text-h2 text-primary flex items-center">
                  <div className="relative h-6 w-6 shrink-0 overflow-hidden rounded-full">
                    {
                      <Image
                        src={getChainIcon(purchase.chain) ?? ''}
                        alt={purchase.chain}
                        fill
                        className="object-cover"
                      />
                    }
                  </div>
                  <span>{getChainFullName(purchase.chain)}</span>
                </div>
              }
            />

            <DataList
              title={t('kgstudio.common.recipient')}
              value={<RiskScanPanel customer={recipient} scannable={false} t={t} />}
              className="col-span-2"
            />
          </div>
          <ReminderBlock
            variant="warning"
            title={t('kgstudio.asset.tx-need-approval')}
            description={t('kgstudio.asset.tx-need-approval-hint')}
          />
        </div>

        <Modal.Footer className="flex w-full items-center justify-between">
          <Button variant="grey" size="lg" onClick={handleCancel} disabled={transferAssetsLoading}>
            {t('kgstudio.common.cancel')}
          </Button>
          <Button
            variant="primary"
            size="lg"
            onClick={handleSubmitTx}
            loading={transferAssetsLoading}
            data-cy="order-submit-request-btn"
          >
            {t('kgstudio.common.submit-request')}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { RequestTxModal };
