import { Alert<PERSON>riangle, CheckCircle2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { P, match } from 'ts-pattern';

import { usePermissions } from '@/app/_common/hooks';
import { cn, formatDate } from '@/app/_common/lib/utils';
import { OrderDetails } from '@/app/_common/services/asset-pro-order/model';
import { useAuthStore } from '@/app/_common/store';
import { Link } from '@/i18n/navigation';
import { But<PERSON>, Card, ReminderBlock } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';
import { formatCurrency, truncateTxhashOrAddress, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';

interface ActionsCardProps {
  orderDetails: OrderDetails;
  handleOpenTxRequestModal: () => void;
  handleOpenCancelOrderModal: () => void;
  onMarkAsPaid: () => void;
}
const ActionsCard = ({
  orderDetails,
  handleOpenCancelOrderModal,
  onMarkAsPaid,
  handleOpenTxRequestModal,
}: ActionsCardProps) => {
  const t = useTranslations();

  return (
    <Card className="space-y-list-medium" data-cy="order-actions-card">
      <SectionHeader>
        <SectionHeader.Title title={t('kgstudio.asset.order-detail.actions.title')} />
      </SectionHeader>
      {match(orderDetails.order_status)
        .with(P.union('unpaid', 'awaiting_confirmation'), () => (
          <div className="gap-list-medium flex w-full flex-col">
            <p className="text-description whitespace-pre-wrap" data-cy="payment-deadline">
              {t('kgstudio.asset.order-detail.actions.payment-deadline', {
                deadline: formatDate(orderDetails.payment_details.deadline),
              })}
            </p>
            {orderDetails.order_status === 'awaiting_confirmation' && (
              <ReminderBlock
                variant="warning"
                title={t('kgstudio.asset.order-detail.actions.awaiting-confirmation')}
                description={t('kgstudio.asset.order-detail.actions.confirmation-hint')}
                data-cy="actions-card-reminder-block-awaiting-confirmation"
              />
            )}
            <div className="flex w-full items-center gap-6">
              <Button variant={'grey'} size={'lg'} onClick={handleOpenCancelOrderModal} data-cy="order-actions-cancel">
                {t('kgstudio.asset.order-detail.actions.cancel-order')}
              </Button>
              <Button variant={'primary'} size={'lg'} onClick={onMarkAsPaid} data-cy="order-actions-mark-as-paid">
                {t('kgstudio.asset.order-detail.actions.mark-as-paid')}
              </Button>
            </div>
          </div>
        ))
        .with('awaiting_shipment', () => (
          <ActionCardAwaitingShipment
            orderDetails={orderDetails}
            handleOpenTxRequestModal={handleOpenTxRequestModal}
            handleOpenCancelOrderModal={handleOpenCancelOrderModal}
          />
        ))
        .with('shipping', () => (
          <p className="text-body text-primary">{t('kgstudio.asset.order-detail.actions.order-shipping')}</p>
        ))
        .with('delivered', () => (
          <p className="text-body text-primary">{t('kgstudio.asset.order-detail.actions.order-done')}</p>
        ))
        .with('cancelled', () => (
          <p className="text-body text-primary">{t('kgstudio.asset.order-detail.actions.order-cancelled')}</p>
        ))
        .exhaustive()}
    </Card>
  );
};

export { ActionsCard };

interface ActionCardAwaitingShipmentProps {
  orderDetails: OrderDetails;
  handleOpenTxRequestModal: () => void;
  handleOpenCancelOrderModal: () => void;
}
const ActionCardAwaitingShipment = ({
  orderDetails,
  handleOpenTxRequestModal,
  handleOpenCancelOrderModal,
}: ActionCardAwaitingShipmentProps) => {
  const t = useTranslations();

  const userInfo = useAuthStore((state) => state.userInfo);
  const remainLimit = userInfo?.asset_pro.remain_limit;
  const dailyTransferLimit = userInfo?.asset_pro.daily_transfer_limit;
  const [hasEditOperatorPermission] = usePermissions(['asset_pro_operator', 'edit']);
  const amountToTransfer = orderDetails.purchase.usd_amount;
  const [remainLimitChecked, checkRemainLimitText] = match(dailyTransferLimit)
    .with(0, P.nullish, () => {
      if (!hasEditOperatorPermission) {
        // eslint-disable-next-line react/jsx-key
        return [false, <span>{t('kgstudio.asset.checklist.contact-hint')}</span>];
      }
      return [
        false,
        // eslint-disable-next-line react/jsx-key
        <div className="inline-flex flex-col">
          <span>{`${t('kgstudio.asset.checklist.remain-hint', { remainLimit: 0, dailyTransferLimit: 0 })}`}</span>
          <Link href="/asset/operators?edit=true" className="text-brand-primary underline">
            {t('kgstudio.asset.checklist.edit-hint')}
          </Link>
        </div>,
      ];
    })
    .otherwise((dLimit) => {
      const sufficient = remainLimit && remainLimit >= Number(amountToTransfer);

      return [
        sufficient,
        // eslint-disable-next-line react/jsx-key
        <span>
          {t('kgstudio.asset.order-detail.action-card.remain-balance', {
            balance: formatCurrency({
              amount: (remainLimit as number) || '',
              decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
              fmt: { prefix: '$' },
            }),
            limit: formatCurrency({ amount: dLimit, decimals: DECIMAL_DISPLAY_MODE.ORDER_USD, fmt: { prefix: '$' } }),
          })}
        </span>,
      ];
    });
  // FIXME: backend does not provide gas token information, so we just assume it is checked
  const gasTokenChecked = true;
  const sendable = remainLimitChecked && gasTokenChecked;

  // Order Status: awaiting_shipment & shipment_status: send_failed
  const isPreviousFailed = orderDetails.shipment_details?.tx_status === 'send_failed';

  return (
    <div className="gap-list-medium flex w-full flex-col">
      <p className="text-primary whitespace-pre-wrap">{t('kgstudio.asset.edit-order-modal.payment-done-hint')}</p>
      {remainLimit !== undefined && dailyTransferLimit !== undefined && (
        <div
          className={cn('text-caption flex items-center gap-1', {
            'text-success': remainLimitChecked || '',
            'text-warning': !remainLimitChecked || '',
          })}
        >
          {!remainLimitChecked ? (
            <AlertTriangle className="stroke-surface-primary fill-warning h-4 w-4 shrink-0" />
          ) : (
            <CheckCircle2 className="stroke-surface-primary fill-success h-4 w-4 shrink-0" />
          )}
          {checkRemainLimitText}
        </div>
      )}
      {isPreviousFailed && (
        <ReminderBlock
          variant={'warning'}
          description={t('kgstudio.asset.order-detail.action-card.tx-failed', {
            txHash: truncateTxhashOrAddress(orderDetails.shipment_details?.tx_hash || ''),
            shippedAt: formatDate(orderDetails.shipment_details?.shipped_at as Date, undefined, 'yyyy/MM/dd HH:mm:ss'),
          })}
        />
      )}

      <div className="gap-list-medium flex w-full items-center">
        <Button variant={'grey'} size={'lg'} onClick={handleOpenCancelOrderModal}>
          {t('kgstudio.asset.order-detail.actions.cancel-order')}
        </Button>
        <Button
          variant={'primary'}
          size={'lg'}
          onClick={handleOpenTxRequestModal}
          disabled={!sendable}
          data-cy="order-actions-send-now"
        >
          {t('kgstudio.common.send-now')}
        </Button>
      </div>
    </div>
  );
};
