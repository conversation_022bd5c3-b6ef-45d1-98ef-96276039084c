import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormTextarea } from '@/app/_common/components/form';
import { apiAssetProOrderHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

const EditInternalNoteFormSchema = (t: any) =>
  z.object({
    internal_note: z.string().nonempty(),
  });
type EditInternalNoteFormValues = z.infer<ReturnType<typeof EditInternalNoteFormSchema>>;

interface EditInternalNoteModalProps extends ComponentPropsWithoutRef<typeof Modal> {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderId: string;
  internalNote: string | null;
}

const EditInternalNoteModal = ({ orderId, internalNote, ...props }: EditInternalNoteModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  const orgId = useOrganizationStore((state) => state.orgId);

  const { mutate: editInternalNote, isLoading: editInternalNoteLoading } = apiAssetProOrderHooks.useEditOrder(
    {
      params: {
        org_id: orgId ?? -1,
        order_id: orderId,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProOrderHooks.getKeyByAlias('getOrderDetails'));

        props.onOpenChange(false);
      },
      onError: () => {
        showToast(t('kgstudio.common.error'), 'error');
      },
      cacheTime: 0,
    },
  );

  const form = useForm<EditInternalNoteFormValues>({
    values: {
      internal_note: internalNote ?? '',
    },
    resolver: zodResolver(EditInternalNoteFormSchema(t)),
  });

  const onSubmit = (data: EditInternalNoteFormValues) => {
    editInternalNote(data);
  };

  useEffect(() => {
    if (props.open) {
      form.reset();
    }
  }, [props.open, form]);

  return (
    <Modal {...props}>
      <Modal.Content data-cy="edit-internal-note-modal">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.asset.order-detail.edit-internal-note')}</Modal.Title>
        </Modal.Header>
        <Form {...form}>
          <FormTextarea
            control={form.control}
            name="internal_note"
            title={t('kgstudio.asset.order-detail.summary.internal-note')}
            required
            hint={t('kgstudio.asset.order-detail.summary.internal-note-hint')}
            className="min-h-[154px]"
            data-cy="edit-internal-note-textarea"
          />
        </Form>

        <Modal.Footer className="flex w-full items-center justify-between">
          <Modal.Close asChild>
            <Button variant="grey" disabled={editInternalNoteLoading}>
              {t('kgstudio.common.cancel')}
            </Button>
          </Modal.Close>
          <Button
            variant="primary"
            onClick={form.handleSubmit(onSubmit)}
            disabled={!form.formState.isValid}
            loading={editInternalNoteLoading}
            className="min-w-[152px]"
          >
            {t('kgstudio.common.update')}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { EditInternalNoteModal };
