import { <PERSON><PERSON><PERSON><PERSON>, Loader, XCircle } from 'lucide-react';
import { match } from 'ts-pattern';

import { ExplorerLink } from '@/app/_common/components';
import { TxStatus } from '@/app/_common/services/asset-pro/model';
import { ChainId } from '@/app/_common/types/web3';

interface TxHahsWithStatusProps {
  txHash: string;
  status: TxStatus | 'awaiting_release';
  chain: ChainId;
}

const TxHahsWithStatus = ({ txHash, status, chain }: TxHahsWithStatusProps) => {
  return (
    <div className="flex items-center gap-1 [&>svg]:h-4 [&>svg]:w-4">
      {match(status)
        .with('send_success', () => <CheckCircle className="stroke-success" />)
        .with('sending', () => <Loader className="stroke-brand-primary-dark animate-spin" />)
        // NOTE: If the status is 'awaiting_release' with tx hashes, it means the last transaction is failed and the tx is awaiting release again
        .with('send_failed', 'awaiting_release', () => <XCircle className="stroke-error" />)
        .exhaustive()}
      <ExplorerLink chain={chain} hex={txHash} type="txHash" truncate />
    </div>
  );
};

export { TxHahsWithStatus };
