'use client';

import { utcToZonedTime } from 'date-fns-tz';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { match, P } from 'ts-pattern';

import { usePageHeader } from '@/app/_common/hooks';
import { noop } from '@/app/_common/lib/utils';
import { apiAssetProOrderHooks } from '@/app/_common/services';
import { OrderStatus, PaymentStatus } from '@/app/_common/services/asset-pro-order/model';
import { useOrganizationStore } from '@/app/_common/store';
import { usePathname } from '@/i18n/navigation';
import { Steps } from '@kryptogo/2b';

import {
  ActionsCard,
  CancelOrderModal,
  EditInternalNoteModal,
  EditPaymentDetailModel,
  OrderInfoCard,
  PaymentDetailsCard,
  RequestTxModal,
  ShipmentDetailsCard,
  SummaryCard,
} from './_components';

const OrderDetail = ({
  params,
}: {
  params: {
    id: string;
  };
}) => {
  const t = useTranslations();
  const pathname = usePathname();
  const orderOrgId = pathname.split('/').at(-1)?.split('-')[0];
  usePageHeader({ title: t('kgstudio.asset.order-detail.order-details'), backLink: '/asset/orders' });

  const [orgId, orgInfo] = useOrganizationStore((state) => [state.orgId, state.orgInfo]);
  const orderId = params.id;

  const [editPaymentDetailModelOpen, setEditModalDetailOpen] = useState(false);
  const [requestTxModalOpen, setRequestTxModalOpen] = useState(false);
  const [cancelOrderModalOpen, setCancelOrderModalOpen] = useState(false);
  const [editInternalNoteModalOpen, setEditInternalNoteModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const { data: orderDetails, isLoading: orderDetailsLoading } = apiAssetProOrderHooks.useGetOrderDetails(
    {
      params: { org_id: Number(orgId), order_id: orderId },
    },
    {
      enabled: !!orgId && !!orderId && Number(orderOrgId) === orgId,
    },
  );

  const getStepsData = (paymentStatus: PaymentStatus, orderStatus: OrderStatus) => {
    return match(orderStatus)
      .with(P.union('unpaid', 'awaiting_confirmation'), () => ({
        steps: [
          { stepNumber: 1, label: t('kgstudio.asset.orders.steps.order-created'), value: 'unpaid' },
          { stepNumber: 2, label: t('kgstudio.asset.orders.steps.awaiting-payment'), value: 'awaiting_payment' },
          { stepNumber: 3, label: t('kgstudio.asset.orders.steps.awaiting-shipment'), value: 'awaiting_shipment' },
          { stepNumber: 4, label: t('kgstudio.asset.orders.steps.shipping'), value: 'shipping' },
          { stepNumber: 5, label: t('kgstudio.asset.orders.steps.order-completed'), value: 'delivered' },
        ],
        value: 'awaiting_payment',
      }))
      .with(P.union('awaiting_shipment', 'delivered'), () => ({
        steps: [
          { stepNumber: 1, label: t('kgstudio.asset.orders.steps.order-created'), value: 'unpaid' },
          { stepNumber: 2, label: t('kgstudio.asset.orders.steps.paid'), value: 'paid' },
          { stepNumber: 3, label: t('kgstudio.asset.orders.steps.awaiting-shipment'), value: 'awaiting_shipment' },
          { stepNumber: 4, label: t('kgstudio.asset.orders.steps.shipping'), value: 'shipping' },
          { stepNumber: 5, label: t('kgstudio.asset.orders.steps.order-completed'), value: 'delivered' },
        ],
        value: orderStatus,
      }))
      .with('shipping', () => ({
        steps: [
          { stepNumber: 1, label: t('kgstudio.asset.orders.steps.order-created'), value: 'unpaid' },
          { stepNumber: 2, label: t('kgstudio.asset.orders.steps.paid'), value: 'paid' },
          { stepNumber: 3, label: t('kgstudio.asset.orders.steps.sent'), value: 'set' },
          { stepNumber: 4, label: t('kgstudio.asset.orders.steps.shipping'), value: 'shipping' },
          { stepNumber: 5, label: t('kgstudio.asset.orders.steps.order-completed'), value: 'delivered' },
        ],
        value: 'shipping',
      }))
      .with('cancelled', () => ({
        steps: [
          { stepNumber: 1, label: t('kgstudio.asset.orders.steps.order-created'), value: 'creta' },
          { stepNumber: 2, label: t('kgstudio.asset.orders.steps.awaiting-shipment'), value: 'awaiting_shipment' },
          { stepNumber: 3, label: t('kgstudio.asset.orders.steps.order-cancelled'), value: 'cancelled' },
        ],
        value: 'cancelled',
      }))
      .exhaustive();
  };

  if (orderOrgId && orgId && Number(orderOrgId) !== orgId) {
    throw new Error('Resource not found', {
      cause: t('kgstudio.asset.tx-detail.not-exist', {
        orgName: orgInfo?.name || '' || '',
      }),
    });
  }

  // TODO: Add loading UI
  if (!orderDetails) return 'loading';

  return (
    <div className="space-y-6">
      <Steps {...getStepsData(orderDetails?.data.payment_status, orderDetails?.data.order_status)} onValueChange={noop}>
        <Steps.Display data-cy="order-steps" />
      </Steps>

      {/* Modals */}
      <EditPaymentDetailModel
        open={editPaymentDetailModelOpen}
        onOpenChange={setEditModalDetailOpen}
        details={{
          orderId: orderId,
          transferTime: orderDetails.data.payment_details.customer?.transferred.updated_at
            ? utcToZonedTime(
                new Date(orderDetails.data.payment_details.customer.transferred.updated_at),
                Intl.DateTimeFormat().resolvedOptions().timeZone,
              )
            : null,
          attachments: orderDetails.data.payment_details.attachments ?? null,
          amount: orderDetails.data.total_price.amount,
          lastFiveDigits: orderDetails.data.payment_details.last_five_digits ?? null,
          note: orderDetails.data.payment_details.note ?? null,
          orderStatus: orderDetails.data.order_status,
          isEditing: isEditing,
        }}
      />
      <CancelOrderModal orderId={orderId} open={cancelOrderModalOpen} onOpenChange={setCancelOrderModalOpen} />
      <RequestTxModal
        open={requestTxModalOpen}
        onOpenChange={setRequestTxModalOpen}
        orderId={orderId}
        purchase={{
          token: {
            logoUrl: orderDetails.data.purchase.logo_url,
            name: orderDetails.data.purchase.base_currency,
          },
          chain: orderDetails.data.purchase.chain_id,
          amount: orderDetails.data.purchase.amount,
        }}
        recipient={{
          displayName: orderDetails.data.customer.name,
          email: orderDetails.data.customer.email ?? undefined,
          phone: orderDetails.data.customer.phone ?? undefined,
          wallet: {
            chain: orderDetails.data.purchase.chain_id,
            address: orderDetails.data.customer.wallet_address,
          },
          kycStatus: orderDetails.data.customer.kyc_status,
        }}
      />
      <EditInternalNoteModal
        orderId={orderId}
        internalNote={orderDetails.data.internal_note}
        open={editInternalNoteModalOpen}
        onOpenChange={setEditInternalNoteModalOpen}
      />
      <article className="grid w-full grid-cols-[1fr_380px] gap-10">
        <section className="space-y-10">
          {/* Order Information */}
          <OrderInfoCard orderDetails={orderDetails.data} />
          {/* Payment Details */}
          <PaymentDetailsCard
            orderDetails={orderDetails.data}
            onEdit={() => {
              setIsEditing(true);
              setEditModalDetailOpen(true);
            }}
            onMarkAsPaid={() => {
              setIsEditing(false);
              setEditModalDetailOpen(true);
            }}
          />
          {/* Shipment Details */}
          <ShipmentDetailsCard
            orderDetails={orderDetails.data}
            handleOpenTxRequestModal={() => setRequestTxModalOpen(true)}
          />
        </section>
        <aside className="space-y-4">
          <ActionsCard
            orderDetails={orderDetails.data}
            onMarkAsPaid={() => {
              setIsEditing(false);
              setEditModalDetailOpen(true);
            }}
            handleOpenCancelOrderModal={() => setCancelOrderModalOpen(true)}
            handleOpenTxRequestModal={() => setRequestTxModalOpen(true)}
          />
          <SummaryCard
            orderDetails={orderDetails.data}
            handleEditInternalModalOpen={() => setEditInternalNoteModalOpen(true)}
          />
        </aside>
      </article>
    </div>
  );
};

export default OrderDetail;
