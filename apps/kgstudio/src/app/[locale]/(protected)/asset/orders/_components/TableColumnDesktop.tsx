import { format, intervalToDuration } from 'date-fns';
import { CheckCircle2, ChevronRight } from 'lucide-react';
import { match } from 'ts-pattern';

import { Image, TokenIcon } from '@/app/_common/components';
import { OrderStatusBadge, PaymentStatusBadge } from '@/app/_common/components/badge';
import { cn } from '@/app/_common/lib/utils';
import { Order } from '@/app/_common/services/asset-pro-order/model';
import taiwanFlagIcon from '@/assets/flag/icon-taiwan.png';
import { formatCurrency, getChainFullName, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';
import { ColumnDef } from '@tanstack/react-table';

import { TxHahsWithStatus } from '../[id]/_components';

const TableColumnDesktop = (t: any): ColumnDef<Order>[] => [
  {
    accessorKey: 'created_at',
    header: t('kgstudio.asset.orders.list.order-created'),
    meta: {
      withoutPadding: true,
      sortable: true,
    },
    size: 120,
    cell: ({ row }) => {
      const { create_time, order_status } = row.original;
      const borderColor = match(order_status)
        .with('unpaid', () => '')
        .with('shipping', () => 'border-processing')
        .with('awaiting_confirmation', () => 'border-warning')
        .with('awaiting_shipment', () => 'border-warning')
        .with('delivered', () => 'border-success')
        .with('cancelled', () => 'border-error')
        .exhaustive();
      return (
        <>
          <div className={cn('absolute left-0 top-0  h-full border-l-4', borderColor)}></div>
          <div className="flex w-full items-start justify-between p-4">
            <p className="text-primary text-body-2">{format(new Date(create_time * 1000), 'yyyy/MM/dd HH:mm:ss')}</p>
          </div>
        </>
      );
    },
  },
  {
    accessorKey: 'order_id',
    header: t('kgstudio.asset.orders.list.order-id'),
    size: 120,
    cell: ({ row }) => {
      const { order_id } = row.original;

      return <p className="text-primary text-body-2">{`OD-${order_id}`}</p>;
    },
  },
  {
    accessorKey: 'customer',
    header: t('kgstudio.asset.orders.list.customer'),
    cell: ({ row }) => {
      const {
        customer: { name, email, kyc_status, phone },
      } = row.original;

      return (
        <>
          <p className="text-primary text-body-2 flex items-center gap-[2px]">
            {name}
            {kyc_status === 'verified' && <CheckCircle2 size={12} className="stroke-success" />}
          </p>
          <p className="text-secondary text-small">{phone}</p>
          <p className="text-secondary text-small">{email}</p>
        </>
      );
    },
  },
  {
    accessorKey: 'purchase',
    header: t('kgstudio.asset.orders.list.order-purchase'),
    cell: ({ row }) => {
      const { purchase } = row.original;

      return (
        <div className="gap-item-small flex items-center">
          <TokenIcon
            token={{
              logoUrl: purchase.logo_url,
              name: purchase.name,
            }}
            chain={purchase.chain_id}
          />
          <div className="flex flex-col">
            <span className="text-body-2 text-primary">{formatCurrency({ amount: purchase.amount })}</span>
            <span className="text-body-2 text-primary">{`${purchase.base_currency}(${getChainFullName(
              purchase.chain_id,
            )})`}</span>
            <span className="text-secondary text-small">{`≈ ${formatCurrency({
              amount: purchase.usd_price,
              decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
              roundingMode: 'ceil',
              fmt: { suffix: ' USD' },
            })}`}</span>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'total_cost', // Since BE response' structure is quite complcated, use a unique key "total_cost" to sort the data "total_price.amount" in the column "Total Price
    header: t('kgstudio.asset.orders.list.total-price'),
    meta: {
      sortable: true,
    },
    cell: ({ row }) => {
      const { total_price } = row.original;

      return (
        <div className="gap-item-small flex items-center">
          <span className="relative h-6 w-6 overflow-hidden rounded-full">
            <Image src={taiwanFlagIcon} alt="flag" fill className="object-cover" />
          </span>
          <div className="flex flex-col">
            <span className="text-body-2 text-primary">
              {formatCurrency({
                amount: total_price.amount,
                decimals: DECIMAL_DISPLAY_MODE.ORDER_TWD,
                roundingMode: 'ceil',
              })}
            </span>
            <span className="text-body-2 text-primary">{`${total_price.quote_currency}`}</span>
            <span className="text-secondary text-small">{`≈ ${formatCurrency({
              amount: total_price.usd_amount,
              decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
              roundingMode: 'ceil',
              fmt: { suffix: ' USD' },
            })}`}</span>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'payment_status',
    header: t('kgstudio.asset.orders.list.payment'),
    cell: ({ row }) => {
      const { payment_status } = row.original;

      return <PaymentStatusBadge status={payment_status} />;
    },
  },
  {
    accessorKey: 'shipment_details',
    header: t('kgstudio.asset.orders.list.shipment'),
    cell: ({ row }) => {
      const { shipment_details, purchase, shipping_status } = row.original;
      const duration = !!shipment_details?.shipped_at
        ? intervalToDuration({ start: shipment_details?.shipped_at * 1000, end: new Date() })
        : null;

      const displayDuration = (timeObject: any) => {
        // Define time components and their singular names
        const timeComponents = [
          { key: 'years', name: 'year' },
          { key: 'months', name: 'month' },
          { key: 'days', name: 'day' },
          { key: 'hours', name: 'hr' },
          { key: 'minutes', name: 'min' },
        ];

        // Find and return the first non-zero time component
        for (const { key, name } of timeComponents) {
          const value = timeObject[key];
          if (value) {
            return `${value} ${name}${value > 1 ? 's' : ''} ago`;
          }
        }

        return t('kgstudio.asset.orders.just-now');
      };

      const txStatus = match(shipping_status)
        .with('send_failed', () => 'send_failed' as const)
        .with('pending', () => 'sending' as const)
        .with('send_success', () => 'send_success' as const)
        .with('not_shipped', () => undefined)
        .exhaustive();

      return (
        <>
          {!!txStatus && !!shipment_details?.tx_hash && (
            <>
              <TxHahsWithStatus txHash={shipment_details?.tx_hash} status={txStatus} chain={purchase?.chain_id} />
              <span className="text-secondary text-small">{displayDuration(duration)}</span>
            </>
          )}
        </>
      );
    },
  },
  {
    accessorKey: 'order_status',
    header: t('kgstudio.asset.orders.list.order-status'),
    cell: ({ row }) => {
      const { order_status } = row.original;

      return <OrderStatusBadge status={order_status} />;
    },
  },
  {
    id: 'action',
    header: () => <></>,
    size: 50,
    cell: () => {
      return (
        <div className="flex items-center justify-center">
          <ChevronRight className="text-placeholder" />
        </div>
      );
    },
  },
];

export { TableColumnDesktop };
