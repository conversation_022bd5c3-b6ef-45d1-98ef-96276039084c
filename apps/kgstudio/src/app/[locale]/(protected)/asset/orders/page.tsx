'use client';

import { Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

import { useDebounce, useDeviceSize, usePageHeader, useUpdateEffect } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiAssetProOrderHooks } from '@/app/_common/services';
import { Order } from '@/app/_common/services/asset-pro-order/model';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { useRouter } from '@/i18n/navigation';
import { Card, DataTable, Input, useDataTable } from '@kryptogo/2b';
import { TableOptions, getCoreRowModel, useReactTable } from '@tanstack/react-table';

import { TableColumnDesktop } from './_components/TableColumnDesktop';

const Orders = () => {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.asset.orders.title') });
  const router = useRouter();
  const orgId = useOrganizationStore((state) => state.orgId);
  const [query, setQuery] = useState('');
  const { deviceSize } = useDeviceSize();
  const debouncedQuery = useDebounce(query, 500);
  const [tableOption, setTableOption] = useState<TableOptions<Order>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size, page_sort } = useDataTable(table);
  const {
    data: ordersData,
    isLoading: ordersLoading,
    error: ordersError,
  } = apiAssetProOrderHooks.useGetOrdersList(
    {
      params: { org_id: Number(orgId) },
      queries: {
        page_size,
        page_number,
        q: debouncedQuery.trim() === '' ? undefined : debouncedQuery,
        page_sort: page_sort === '' ? undefined : page_sort,
      },
    },
    {
      enabled: !!orgId,
      refetchInterval: 18_000,
    },
  );

  const MemoDesktopColumn = useMemo(() => TableColumnDesktop(t), [t]);

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: ordersData?.data || [],
      columns: MemoDesktopColumn,
    }));
  }, [MemoDesktopColumn, deviceSize, ordersData]);

  useUpdateEffect(() => {
    if (ordersError) {
      console.error(ordersError);

      if (isApiError(ordersError)) {
        toast.error(t('kgstore.common.error'));
      }
    }
  }, [ordersError, t]);

  return (
    <main className="space-y-6">
      <Card className="!p-0">
        {/* TODO: display after BE api implements the query param */}
        {/* <div className="p-4 md:p-6">
          <Input
            className="w-full md:!w-[403px]"
            placeholder={t('kgstudio.asset.orders.search.placeholder')}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
            value={query}
            suffix={<Search />}
          />
        </div> */}
        <DataTable
          className="border-y border-[var(--border-primary)] md:border-none"
          table={table}
          isLoading={ordersLoading}
          dataLength={ordersData?.paging.total_count || 0}
          onRowClick={(rowData) => {
            router.push(`/asset/orders/${rowData.order_id}`);
          }}
        />
      </Card>
    </main>
  );
};

export default Orders;
