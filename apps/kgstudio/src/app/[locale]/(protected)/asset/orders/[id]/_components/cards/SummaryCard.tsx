import { useTranslations } from 'next-intl';
import { ComponentProps } from 'react';

import { DataList, ProcessBy } from '@/app/[locale]/(protected)/asset/_components';
import { OrderStatusBadge, PaymentStatusBadge, ShipmentStatusBadge } from '@/app/_common/components/badge';
import { OrderDetails } from '@/app/_common/services/asset-pro-order/model';
import { TxStatus } from '@/app/_common/services/asset-pro/model';
import { Card } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';

import { TxHahsWithStatus } from '../TxHahsWithStatus';

interface SummaryCardProps {
  orderDetails: OrderDetails;
  handleEditInternalModalOpen: () => void;
}

const SummaryCard = ({ orderDetails, handleEditInternalModalOpen }: SummaryCardProps) => {
  interface ExtendedDataListProps extends ComponentProps<typeof DataList> {
    span?: number;
  }

  const t = useTranslations();
  const getSummaryData = (orderDetails: OrderDetails): ExtendedDataListProps[] => {
    const processedBy = orderDetails.shipment_details?.processed_by;
    const txHash = orderDetails.shipment_details?.tx_hash;
    const txStatus = orderDetails.shipment_details?.tx_status;
    const chain = orderDetails.purchase.chain_id;

    return [
      {
        title: t('kgstudio.asset.orders.list.order-status'),
        value: <OrderStatusBadge status={orderDetails.order_status} />,
        'data-cy': 'order-summary-order-status',
      },
      {
        title: t('kgstudio.asset.order-detail.summary.payment-status'),
        value: <PaymentStatusBadge status={orderDetails.payment_status} />,
        'data-cy': 'order-summary-payment-status',
      },
      {
        title: t('kgstudio.asset.order-detail.summary.shipment-status'),
        value: <ShipmentStatusBadge status={orderDetails.shipping_status} />,
        'data-cy': 'order-summary-shipment-status',
      },
      {
        title: t('kgstudio.asset.order-detail.summary.shipping-proof'),
        value:
          txHash && txStatus ? (
            <TxHahsWithStatus txHash={txHash as `0x${string}`} status={txStatus as TxStatus} chain={chain} />
          ) : (
            'N/A'
          ),
        'data-cy': 'order-summary-shipping-proof',
      },
      {
        title: t('kgstudio.asset.order-detail.summary.process-by'),
        value: processedBy ? (
          <ProcessBy name={processedBy.name} profileImage={processedBy.profile_img} email={processedBy.email} />
        ) : (
          'N/A'
        ),
        'data-cy': 'order-summary-process-by',
      },
      {
        title: t('kgstudio.asset.order-detail.summary.internal-note'),
        value: orderDetails.internal_note ?? 'N/A',
        onEdit: handleEditInternalModalOpen,
        'data-cy': 'order-summary-internal-note',
      },
    ];
  };
  return (
    <Card className="gap-list-medium flex flex-col">
      <SectionHeader>
        <SectionHeader.Title title={t('kgstudio.asset.order-detail.summary.title')} />
      </SectionHeader>
      <div className="grid w-full grid-cols-1 gap-6">
        {getSummaryData(orderDetails).map((data) => (
          <DataList key={data.title} {...data} data-cy={data['data-cy']} />
        ))}
      </div>
    </Card>
  );
};

export { SummaryCard };
