import { formatDistanceToNow } from 'date-fns';
import { Check } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { CSSProperties, ComponentProps } from 'react';
import { P, match } from 'ts-pattern';

import { DataList, ImageViewer } from '@/app/[locale]/(protected)/asset/_components';
import { formatDate, maskSecretString } from '@/app/_common/lib/utils';
import { OrderDetails } from '@/app/_common/services/asset-pro-order/model';
import { Accordion, Badge, Button, Card, ReminderBlock, Separator } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';
import { formatCurrency, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';

interface PaymentDetailsCardProps {
  orderDetails: OrderDetails;
  onEdit: () => void;
  onMarkAsPaid: () => void;
}

const PaymentDetailsCard = ({ orderDetails, onEdit, onMarkAsPaid }: PaymentDetailsCardProps) => {
  const t = useTranslations();

  type PaymentStatus = OrderDetails['payment_status'];
  const getPaymentStatusBadge = (paymentStatus: PaymentStatus) => {
    const badgeVariantMapping: Record<PaymentStatus, ComponentProps<typeof Badge>['variant']> = {
      unpaid: 'grey',
      paid: 'green',
      awaiting_refund: 'yellow',
      refunded: 'darkGrey',
    };

    return (
      <Badge variant={badgeVariantMapping[paymentStatus]} size="md">
        {paymentStatus}
      </Badge>
    );
  };
  const getPaymentMethodDisplay = (paymentMethod: OrderDetails['payment_details']['payment_method']) =>
    match(paymentMethod)
      //
      .with('bank_transfer', () => 'Bank Transfer')
      .exhaustive();
  interface ExtendedDataListProps extends ComponentProps<typeof DataList> {
    span?: number;
  }
  const getPaymentDetailsData = (orderDetails: OrderDetails): ExtendedDataListProps[] => [
    {
      title: t('kgstudio.asset.order-detail.summary.payment-status'),
      value: getPaymentStatusBadge(orderDetails.payment_status),
      'data-cy': 'order-payment-status',
    },
    {
      title: t('kgstudio.asset.transfer-amount'),
      value: formatCurrency({
        amount: Number(orderDetails.payment_details.transfer_amount),
        decimals: DECIMAL_DISPLAY_MODE.ORDER_TWD,
        roundingMode: 'ceil',
        fmt: { prefix: 'NT$' },
      }),
      'data-cy': 'order-payment-transfer-amount',
    },
    {
      title: t('kgstudio.asset.payment-info.payment-method'),
      value: getPaymentMethodDisplay(orderDetails.payment_details.payment_method),
      'data-cy': 'order-payment-method',
    },
    {
      title: t('kgstudio.asset.transfer-to'),
      value: t('kgstudio.asset.order-detail.payment-details.account-info', {
        bankName: orderDetails.payment_details.transfer_to.bank_name,
        branchName: orderDetails.payment_details.transfer_to.branch_name,
        accountNumber: orderDetails.payment_details.transfer_to.account_number,
        accountHolderName: orderDetails.payment_details.transfer_to.account_holder_name,
      }),
      'data-cy': 'order-payment-transfer-to',
    },
    {
      title: t('kgstudio.asset.customer-transfer-time'),
      value: orderDetails.payment_details.customer_transfer_time
        ? formatDate(orderDetails.payment_details.customer_transfer_time)
        : 'N/A',
      'data-cy': 'order-payment-customer-transfer-time',
    },
    {
      title: t('kgstudio.asset.order-detail.payment-details.last-five-digits'),
      value: orderDetails.payment_details.last_five_digits ?? 'N/A',
      'data-cy': 'order-payment-account-five-digits',
    },
    {
      title: t('kgstudio.common.note-and-attachments'),
      value: (
        <div className="item-gap-small flex w-full flex-col">
          <span className="text-primary">{orderDetails.payment_details.note ?? 'N/A'}</span>
          {orderDetails.payment_details.attachments && (
            <div className="item-gap-small flex">
              {orderDetails.payment_details.attachments.map((attachment, index) => (
                <ImageViewer key={index} imageUrl={attachment} />
              ))}
            </div>
          )}
        </div>
      ),
      span: 2,
      'data-cy': 'order-payment-note-attachments',
    },
  ];
  const getCustomerPaymentInfoData = (
    customerPaymentInfo: NonNullable<OrderDetails['payment_details']['customer']>,
  ): ExtendedDataListProps[] => [
    {
      title: t('kgstudio.common.update-time'),
      value: formatDate(customerPaymentInfo.transferred.updated_at, undefined, 'yyyy-MM-dd HH:mm:ss'),
      span: 2,
    },
    {
      title: t('kgstudio.common.attachments'),
      value: (
        <div className="item-gap-small flex">
          {customerPaymentInfo.transferred.attachments.map((attachment, index) => (
            <ImageViewer key={index} imageUrl={attachment} />
          ))}
        </div>
      ),
      span: 2,
    },
  ];

  const computeHeaderActions = (
    paymentStatus: OrderDetails['payment_status'],
    orderStatus: OrderDetails['order_status'],
  ) => {
    return match([paymentStatus, orderStatus])
      .with(['unpaid', 'unpaid'], () => (
        <>
          <Button variant={'secondary'} size="md" onClick={onEdit}>
            {t('kgstudio.common.edit')}
          </Button>
          <Button variant={'primary'} size="md" icon={<Check />} iconPosition="left" onClick={onMarkAsPaid}>
            {t('kgstudio.asset.order-detail.actions.mark-as-paid')}
          </Button>
        </>
      ))
      .with([P._, 'awaiting_confirmation'], () => (
        <>
          {/* NOTE: the refund feature is upcoming in v2 */}
          {/* <Button variant={'danger'} size="md">
            {t('kgstudio.common.revert')}
          </Button> */}
          <Button variant={'primary'} size="md" icon={<Check />} iconPosition="left" onClick={onMarkAsPaid}>
            {t('kgstudio.asset.order-detail.actions.mark-as-paid')}
          </Button>
        </>
      ))
      .with(['unpaid', 'cancelled'], () => null)
      .with(['awaiting_refund', 'cancelled'], () => (
        <>
          <Button variant={'secondary'} size="md" onClick={onEdit}>
            {t('kgstudio.common.edit')}
          </Button>
          {/* V1 does not have a refund functionalities */}
          {/* <Button variant={'primary'} size="md" onClick={noop}>
            Mark as Refunded
          </Button> */}
        </>
      ))
      .otherwise(() => (
        <Button variant={'secondary'} size="md" onClick={onEdit}>
          {t('kgstudio.common.edit')}
        </Button>
      ));
  };

  return (
    <Card className="gap-list-medium flex flex-col">
      <SectionHeader>
        <SectionHeader.Title title={t('kgstudio.asset.order-detail.payment-details.title')} />
        <SectionHeader.Actions>
          <div className="item-gap-medium flex items-center" data-cy="order-payment-card-actions">
            {computeHeaderActions(orderDetails.payment_status, orderDetails.order_status)}
          </div>
        </SectionHeader.Actions>
      </SectionHeader>

      {match(orderDetails.payment_status)
        .with('unpaid', () => {
          return orderDetails.order_status === 'awaiting_confirmation' ? (
            <ReminderBlock
              variant="warning"
              title={t('kgstudio.asset.order-detail.actions.awaiting-confirmation')}
              description={t('kgstudio.asset.order-detail.actions.confirmation-hint')}
              data-cy="payment-detail-reminder-block-awaiting-confirmation"
            />
          ) : (
            <ReminderBlock
              variant="info"
              title={t('kgstudio.asset.order-detail.payment-details.unpaid')}
              description={t('kgstudio.asset.order-detail.payment-details.unpaid-time-reminder', {
                deadline: formatDate(orderDetails.payment_details.deadline),
                timeLeft: formatDistanceToNow(orderDetails.payment_details.deadline),
              })}
            />
          );
        })
        .otherwise(() => (
          <ReminderBlock
            variant="success"
            description={`${t('kgstudio.asset.order-detail.payment-details.confirmed-as-paid')}  ${formatDate(
              orderDetails.payment_details.customer_transfer_time as Date,
            )}`}
            data-cy="payment-detail-reminder-block-success"
          />
        ))}

      {orderDetails.customer.bank_account && (
        <ReminderBlock
          variant={'info'}
          //FIXME: Implement the user KYC details link
          title={t('kgstudio.asset.order-detail.payment-details.customer-account')}
          description={t('kgstudio.asset.order-detail.payment-details.account-info-less-content', {
            bankName: orderDetails.customer.bank_account.bank_name,
            branchName: orderDetails.customer.bank_account.branch_name,
            accountNumber: maskSecretString(orderDetails.customer.bank_account.account_number, 0, -5),
            accountHolderName: orderDetails.customer.bank_account.account_holder_name,
          })}
        />
      )}

      <div className="grid w-full grid-cols-2 gap-6">
        {getPaymentDetailsData(orderDetails).map((data) => (
          <DataList
            key={data.title}
            className="col-[span_var(--data-col-span)]"
            style={
              {
                '--data-col-span': data.span || 1,
              } as CSSProperties
            }
            {...data}
          />
        ))}
      </div>

      {!!orderDetails.payment_details.customer && (
        <>
          <Separator />
          <Accordion type="single" collapsible defaultValue="999">
            <Accordion.Item value="999" className="font bold border-none">
              <Accordion.Trigger className="p-0">
                {t('kgstudio.asset.order-detail.payment-details.info-from-customer')}
              </Accordion.Trigger>
              <Accordion.Content className="p-0 pt-6 data-[state=closed]:hidden" forceMount>
                <div className="grid w-full grid-cols-2 gap-6">
                  {getCustomerPaymentInfoData(orderDetails.payment_details.customer).map((data) => (
                    <DataList
                      key={data.title}
                      {...data}
                      className="col-[span_var(--data-col-span)]"
                      style={
                        {
                          '--data-col-span': data.span || 1,
                        } as CSSProperties
                      }
                    />
                  ))}
                </div>
              </Accordion.Content>
            </Accordion.Item>
          </Accordion>
        </>
      )}

      {orderDetails.payment_details.last_edited_at !== null && orderDetails.payment_details.editor !== null && (
        <>
          <Separator />
          <div className="grid grid-cols-2 gap-6">
            <DataList
              title={t('kgstudio.common.last-edited-time')}
              value={formatDate(orderDetails.payment_details.last_edited_at)}
            />
            <DataList
              title={t('kgstudio.common.editor')}
              value={`${orderDetails.payment_details.editor.name} - ${orderDetails.payment_details.editor.email}`}
            />
          </div>
        </>
      )}
    </Card>
  );
};

export { PaymentDetailsCard };
