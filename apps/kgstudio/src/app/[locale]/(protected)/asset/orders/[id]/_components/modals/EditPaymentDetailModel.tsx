'use client';

import { ArrowRight, Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef, useEffect, useMemo, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import ImageUploader, { ImageListType } from 'react-images-uploading';
import { toast } from 'sonner';
import { z } from 'zod';

import { AttachmentCard } from '@/app/[locale]/(protected)/asset/_components';
import { amountInputInterceptor } from '@/app/[locale]/(protected)/asset/_lib/utils';
import { PaymentStatusBadge } from '@/app/_common/components/badge';
import { FormDropdown, FormInput, FormTextarea } from '@/app/_common/components/form';
import { Media, useGcpFileUpload } from '@/app/_common/hooks';
import { getUnixTimeStampFromZoned } from '@/app/_common/lib/utils';
import { apiAssetProOrderHooks } from '@/app/_common/services';
import { OrderStatus } from '@/app/_common/services/asset-pro-order/model';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, FormLabel, InputError, InputHint, Label, Modal, ReminderBlock, Separator } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

interface EditPaymentDetailModelProps extends ComponentPropsWithoutRef<typeof Modal> {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  details: {
    orderId: string;
    transferTime: Date | null;
    amount: string;
    lastFiveDigits: string | null;
    note: string | null;
    attachments: string[] | null;
    orderStatus: OrderStatus;
    isEditing: boolean;
  };
}

const EditPaymentDetailSchema = (t: any, isEditingUnpaid: boolean) =>
  z.object({
    status: z.enum(['paid', 'unpaid']).default(isEditingUnpaid ? 'unpaid' : 'paid'),
    quote_currency: z.literal('TWD').default('TWD'),
    note: z
      .string()
      .transform((value) => (typeof value === 'string' ? value.trim() : value))
      .transform((value) => (value === '' ? null : value)),
    ...(!isEditingUnpaid && {
      customer_transfer_time: z.string().nonempty(),
      last_five_digits: z
        .string()
        .length(5, {
          message: 'Last five digits of actual payment account number must be 5 digits',
        })
        .refine((value) => /^\d+$/.test(value), {
          message: 'Last five digits of actual payment account number must be numbers',
        }),
      transfer_amount: z.coerce.string(),
    }),
  });

type EditPaymentDetail = z.infer<ReturnType<typeof EditPaymentDetailSchema>>;

const EditPaymentDetailModel = ({ details, ...props }: EditPaymentDetailModelProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  const orgId = useOrganizationStore((state) => state.orgId);
  const isUnpaid = details.orderStatus === 'unpaid' || details.orderStatus === 'awaiting_confirmation';
  const isEditingUnpaid = details.isEditing && isUnpaid;
  const isMarkingPaid = !details.isEditing && isUnpaid;

  const form = useForm<EditPaymentDetail>({
    defaultValues: {
      customer_transfer_time: isEditingUnpaid ? '' : details.transferTime?.toISOString().slice(0, 16),
      transfer_amount: isEditingUnpaid ? '' : details.amount,
      quote_currency: 'TWD',
      last_five_digits: details.lastFiveDigits ?? '',
      note: details.note ?? '',
    },
    resolver: zodResolver(EditPaymentDetailSchema(t, isEditingUnpaid)),
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });
  const transferAmount = useWatch({
    control: form.control,
    name: 'transfer_amount',
  });
  const lastFiveDigits = useWatch({
    control: form.control,
    name: 'last_five_digits',
  });

  const initialAttachments = useMemo(
    () => details.attachments?.map((attachment) => ({ dataURL: attachment })) ?? [],
    [details.attachments],
  );
  const [attachments, setAttachments] = useState<ImageListType>(() => initialAttachments);

  const { isError: fileUrlsError, isLoading: fileUrlsLoading, uploadAllFiles } = useGcpFileUpload();

  const { mutate: editOrder, isLoading: editeditOrderLoading } = apiAssetProOrderHooks.useEditOrder(
    {
      params: {
        org_id: orgId ?? -1,
        order_id: details.orderId,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(apiAssetProOrderHooks.getKeyByAlias('getOrderDetails'));

        props.onOpenChange(false);
      },
      onError: () => {
        toast.error(t('kgstudio.common.error'));
      },
      cacheTime: 0,
    },
  );

  const {
    isLoading: confirmPaymentLoading,
    isSuccess: confirmPaymentSuccess,
    isError: confirmPaymentError,
    mutate: confirmPayment,
  } = apiAssetProOrderHooks.useConfirmPayment(
    {
      params: {
        org_id: orgId ?? -1,
        order_id: details.orderId,
      },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries({
          queryKey: apiAssetProOrderHooks.getKeyByAlias('getOrderDetails'),
        });
        queryClient.refetchQueries({
          queryKey: apiAssetProOrderHooks.getKeyByAlias('getOrdersList'),
        });

        toast.success('Payment detail updated successfully!');
        props.onOpenChange(false);
      },
    },
  );

  const onSubmit = async (data: EditPaymentDetail) => {
    const paymentData = {
      note: data.note || '',
      customer_transfer_time: getUnixTimeStampFromZoned(new Date(data.customer_transfer_time as string)),
      transfer_amount: data.transfer_amount as string,
      last_five_digits: data.last_five_digits as string,
      attachments: null, // Default to null, update if attachments exist
    };

    const newAttachments = attachments.filter((attachment) => attachment.file);
    if (newAttachments.length === 0 && attachments.length === initialAttachments.length) {
      if (isEditingUnpaid) {
        editOrder({
          payment_details: {
            note: data.note || '',
          },
        });
      } else if (isMarkingPaid) {
        confirmPayment(paymentData);
      } else {
        editOrder({ payment_details: paymentData });
      }

      return;
    }

    let fileUrls;
    const localFiles = attachments.filter((attachment) => attachment.file);
    if (localFiles.length > 0) {
      fileUrls = await uploadAllFiles({ attachments: localFiles as Media[] }, 'asset_pro');
      // fileUrls will be null if there is an error
      if (!fileUrls) return;
    }

    const unchangedImgs = attachments
      .filter((attachment) => !attachment.file)
      .map((attachment) => attachment.dataURL)
      .filter((d) => d !== undefined);
    const updatedAttachments = [...unchangedImgs, ...(fileUrls?.attachments ? fileUrls.attachments : [])] as string[];

    if (isEditingUnpaid) {
      editOrder({
        payment_details: {
          note: data.note || '',
          attachments: updatedAttachments,
        },
      });
    } else if (isMarkingPaid) {
      confirmPayment({
        ...paymentData,
        attachments: updatedAttachments,
      });
    } else {
      editOrder({
        payment_details: {
          ...paymentData,
          attachments: updatedAttachments,
        },
      });
    }
  };

  const updateButtonLoading = confirmPaymentLoading || fileUrlsLoading;
  const updateButtonDisabled = !form.formState.isValid || confirmPaymentSuccess;

  useEffect(() => {
    if (confirmPaymentError || fileUrlsError) {
      toast.error('Failed to update payment detail');
    }
  }, [confirmPaymentError, fileUrlsError]);

  useEffect(() => {
    if (props.open) {
      form.reset();
      setAttachments(initialAttachments);
    }
  }, [form, initialAttachments, props.open]);

  return (
    <Modal {...props}>
      <Modal.Content scrollable className="max-h-[90%]">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.asset.edit-order-modal.title')}</Modal.Title>
        </Modal.Header>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-list-medium">
            <div className="gap-list-medium grid grid-cols-2">
              <div className="gap-item-small flex flex-col">
                <p className="text-body-bold text-primary">{t('kgstudio.asset.order-detail.summary.payment-status')}</p>
                <div className="flex items-center justify-between">
                  <PaymentStatusBadge status="unpaid" size="md" />
                  <ArrowRight className="text-primary h-6 w-6 self-start" />
                </div>
              </div>

              <FormDropdown
                title={t('kgstudio.asset.edit-order-modal.change-status-to')}
                name="status"
                control={form.control}
                options={isEditingUnpaid ? [{ label: 'Unpaid', value: 'unpaid' }] : [{ label: 'Paid', value: 'paid' }]}
                defaultValue={isEditingUnpaid ? 'unpaid' : 'paid'}
                required
              />
            </div>
            <Separator />
            {!isEditingUnpaid && (
              <>
                <h3 className="text-h3 text-primary">{t('kgstudio.asset.edit-order-modal.edit-unpaid-himt')}</h3>

                <FormInput
                  name="customer_transfer_time"
                  title={t('kgstudio.asset.customer-transfer-time')}
                  control={form.control}
                  required
                  type="datetime-local"
                  data-cy="edit-payment-transfer-time-input"
                />

                <FormInput
                  name="transfer_amount"
                  title={t('kgstudio.asset.transfer-amount')}
                  control={form.control}
                  onInput={amountInputInterceptor}
                  required
                  suffix="TWD"
                />
                <FormInput
                  name="last_five_digits"
                  title={t('kgstudio.asset.order-detail.payment-details.last-five-digits')}
                  control={form.control}
                  required
                  data-cy="edit-payment-last-five-digits-input"
                />
              </>
            )}

            <FormTextarea
              name="note"
              title={t('kgstudio.asset.edit-order-modal.payment-note')}
              placeholder="Enter any notes regarding order payment information"
              control={form.control}
              className="min-h-[154px]"
              data-cy="edit-payment-note-textarea"
            />
            <div>
              <FormLabel>{t('kgstudio.asset.edit-order-modal.upload-attachments')}</FormLabel>
              <ImageUploader
                multiple
                maxNumber={10}
                value={attachments}
                onChange={setAttachments}
                maxFileSize={1024 * 1024}
                acceptType={['png', 'jpg', 'jpeg', 'webp']}
              >
                {({ imageList, onImageUpload, onImageRemove, dragProps, errors }) => {
                  return (
                    <div className="space-y-2">
                      <Label title={t('kgstudio.asset.edit-order-modal.upload-attachments')} />
                      <Button
                        type="button"
                        variant="grey-outlined"
                        icon={<Plus />}
                        size="lg"
                        onClick={onImageUpload}
                        {...dragProps}
                      />
                      <div className="flex flex-wrap items-center gap-[10px]">
                        {imageList.map((image, index) => (
                          <AttachmentCard
                            key={index}
                            imageSrc={image.dataURL ?? ''}
                            imageAlt={image.file?.name}
                            file={image.file}
                            onImageRemove={() => onImageRemove(index)}
                          />
                        ))}
                      </div>

                      <InputHint>{t('kgstudio.asset.edit-order-modal.max-file-upload-info')}</InputHint>
                      {errors?.acceptType && (
                        <InputError>{t('kgstudio.asset.edit-order-modal.accepted-file-types')}</InputError>
                      )}
                      {errors?.maxFileSize && (
                        <InputError>{t('kgstudio.asset.edit-order-modal.file-size-error')}</InputError>
                      )}
                      {errors?.maxNumber && (
                        <InputError>{t('kgstudio.asset.edit-order-modal.max-files-error')}</InputError>
                      )}
                    </div>
                  );
                }}
              </ImageUploader>
            </div>
            {Number(transferAmount) !== Number(details.amount) && (
              <ReminderBlock
                variant="warning"
                title={t('kgstudio.asset.edit-order-modal.payment-amount-mismatch')}
                description={t('kgstudio.asset.edit-order-modal.cancel-transaction-instruction')}
              />
            )}
            <Modal.Footer className="flex items-center justify-between">
              <Button
                variant="grey"
                onClick={() => props.onOpenChange(false)}
                size="lg"
                disabled={confirmPaymentLoading}
              >
                {t('kgstudio.common.cancel')}
              </Button>
              <Button
                variant="primary"
                size="lg"
                className="min-w-[200px]"
                loading={updateButtonLoading}
                disabled={!isEditingUnpaid && updateButtonDisabled}
                data-cy="payment-update-button"
              >
                {t('kgstudio.common.update')}
              </Button>
            </Modal.Footer>
          </form>
        </Form>
      </Modal.Content>
    </Modal>
  );
};

export { EditPaymentDetailModel };
