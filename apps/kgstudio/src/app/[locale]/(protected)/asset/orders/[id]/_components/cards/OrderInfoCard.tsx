import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import { CSSProperties, ComponentProps } from 'react';

import { DataList, RiskScanPanel } from '@/app/[locale]/(protected)/asset/_components';
import { TokenIcon } from '@/app/_common/components';
import { formatDate } from '@/app/_common/lib/utils';
import { OrderDetails } from '@/app/_common/services/asset-pro-order/model';
import { Accordion, Badge, Card, ImageBox, Separator } from '@kryptogo/2b';
import { SectionHeader } from '@kryptogo/2b/server';
import { formatCurrency, getChainFullName, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';

interface OrderInfoCardProps {
  orderDetails: OrderDetails;
}

const OrderInfoCard = ({ orderDetails }: OrderInfoCardProps) => {
  const t = useTranslations();

  interface ExtendedDataListProps extends ComponentProps<typeof DataList> {
    span?: number;
  }
  const getOrderInfoData = (orderDetails: OrderDetails): ExtendedDataListProps[] => [
    {
      title: t('kgstudio.asset.order-detail.order-information.tx-id'),
      'data-cy': 'order-info-transaction-id',
      value: (
        <Badge variant={'grey'} size={'md'}>
          {`OD-${orderDetails.order_id}`}
        </Badge>
      ),
    },
    {
      title: t('kgstudio.asset.order-detail.order-information.order-time'),
      'data-cy': 'order-info-order-time',
      value: formatDate(orderDetails.create_time),
    },
    {
      title: 'Purchase',
      'data-cy': 'order-info-purchase',
      value: (
        <div className="gap-item-small flex flex-col">
          <div className="gap-item-small text-h2 text-primary flex flex-wrap items-center font-bold">
            <TokenIcon
              token={{
                logoUrl: orderDetails.purchase.logo_url,
                name: orderDetails.purchase.name,
              }}
              chain={orderDetails.purchase.chain_id}
            />
            <span>{formatCurrency({ amount: orderDetails.purchase.amount })}</span>
            <span>{orderDetails.purchase.base_currency}</span>
            <span>{`(${getChainFullName(orderDetails.purchase.chain_id)})`}</span>
          </div>
          <span className="text-secondary text-small">{`≈ ${formatCurrency({
            amount: orderDetails.purchase.usd_amount,
            decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
            roundingMode: 'ceil',
            fmt: { suffix: ' USD' },
          })}`}</span>
        </div>
      ),
    },
    {
      title: t('kgstudio.asset.order-detail.order-information.total-price'),
      'data-cy': 'order-info-total-price',
      value: (
        <div className="gap-item-small flex flex-col">
          <div className="gap-item-small text-h2 text-primary flex items-center font-bold">
            <ImageBox
              imageSrc={orderDetails.total_price.logo_url}
              imageAlt={orderDetails.total_price.quote_currency}
              size="24"
            />
            <span>
              {formatCurrency({
                amount: orderDetails.total_price.amount,
                decimals: DECIMAL_DISPLAY_MODE.ORDER_TWD,
                roundingMode: 'ceil',
                fmt: { prefix: 'NT$' },
              })}
            </span>
          </div>
          <span className="text-secondary text-small">{`≈ ${formatCurrency({
            amount: orderDetails.total_price.usd_amount,
            decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
            roundingMode: 'ceil',
            fmt: { suffix: ' USD' },
          })}`}</span>
        </div>
      ),
    },
    {
      title: t('kgstudio.asset.order-detail.order-information.customer'),
      'data-cy': 'order-info-customer',
      value: (
        <RiskScanPanel
          customer={{
            displayName: orderDetails.customer.name,
            email: orderDetails.customer.email ?? undefined,
            phone: orderDetails.customer.phone ?? undefined,
            wallet: {
              chain: orderDetails.purchase.chain_id,
              address: orderDetails.customer.wallet_address,
            },
            kycStatus: orderDetails.customer.kyc_status,
          }}
          t={t}
        />
      ),
      span: 2,
    },
  ];

  return (
    <Card className="gap-list-medium flex flex-col" data-cy="order-info-card">
      <SectionHeader>
        <SectionHeader.Title title={t('kgstudio.asset.order-detail.order-information.title')} />
      </SectionHeader>
      <div className="grid grid-cols-2 gap-6">
        {getOrderInfoData(orderDetails).map((data) => (
          <DataList
            key={data.title}
            className="col-[span_var(--data-col-span)]"
            style={
              {
                '--data-col-span': data.span || 1,
              } as CSSProperties
            }
            data-cy={`order-info-${data.title.replaceAll(' ', '-').toLowerCase()}`}
            {...data}
          />
        ))}
      </div>
      <Accordion type="single" collapsible>
        <Accordion.Item value="888">
          <Accordion.Trigger>{t('kgstudio.asset.order-detail.order-information.payment-details')}</Accordion.Trigger>
          <Accordion.Content>
            <div className="text-body-2 gap-list-small grid grid-cols-4">
              <div className="text-secondary col-span-4 grid grid-cols-[subgrid]">
                <span>{t('kgstudio.asset.order-detail.order-information.product')}</span>
                <span>{t('kgstudio.asset.order-detail.order-information.qty')}</span>
                <span>{t('kgstudio.asset.products.price')}</span>
                <span>{t('kgstudio.asset.order-detail.order-information.total-price')}</span>
              </div>
              <Separator className="col-span-4" />
              <div className="text-primary col-span-4 grid grid-cols-[subgrid]">
                <span>{orderDetails.purchase.name}</span>
                <span>{formatCurrency({ amount: orderDetails.purchase.amount })}</span>
                <span>
                  {formatCurrency({
                    amount: orderDetails.purchase.price,
                    decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
                    fmt: { prefix: 'NT$' },
                  })}
                </span>
                <span>
                  {formatCurrency({
                    amount: BigNumber(orderDetails.purchase.amount).times(orderDetails.purchase.price),
                    decimals: DECIMAL_DISPLAY_MODE.ORDER_TWD,
                    roundingMode: 'ceil',
                    fmt: { prefix: 'NT$' },
                  })}
                </span>
              </div>
              <div className="text-primary col-span-4 grid grid-cols-[subgrid]">
                <span>{`${t('kgstudio.asset.products.handling-fee')}: ${
                  orderDetails.total_price.handling_fee_percentage
                }%`}</span>
                <span>1</span>
                <span>
                  {formatCurrency({
                    amount: orderDetails.total_price.handling_fee,
                    decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
                    fmt: { prefix: 'NT$' },
                  })}
                </span>
                <span>
                  {formatCurrency({
                    amount: orderDetails.total_price.handling_fee,
                    decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
                    fmt: { prefix: 'NT$' },
                  })}
                </span>
              </div>
              <Separator className="col-span-4" />
              <div className="text-primary col-span-4 grid grid-cols-[subgrid]">
                <span />
                <span />
                <span className="font-bold">{t('kgstudio.common.total')}</span>
                <span className="font-bold">
                  {formatCurrency({
                    amount: orderDetails.total_price.amount,
                    decimals: DECIMAL_DISPLAY_MODE.ORDER_TWD,
                    roundingMode: 'ceil',
                    fmt: { prefix: 'NT$' },
                  })}
                </span>
              </div>
            </div>
          </Accordion.Content>
        </Accordion.Item>
      </Accordion>
    </Card>
  );
};

export { OrderInfoCard };
