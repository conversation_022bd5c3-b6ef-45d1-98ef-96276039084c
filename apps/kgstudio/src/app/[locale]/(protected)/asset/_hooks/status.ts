import { useEffect, useState } from 'react';

import { TxStatus, getTxStatus } from '@/app/_common/lib/etherscan';
import { getTxStatus as getTronTxStatus } from '@/app/_common/lib/trongrid';
import { AssetProChainId, AssetProEvmChainId, AssetProNonEvmChainId } from '@/app/_common/services/asset-pro/model';
import { EvmChainIdList } from '@/app/_common/types/web3';

export const useTransactionStatus = (chain: AssetProChainId, txHash: string) => {
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    setLoading(true);
    setSuccess(false);

    const getTxSuccessFunc =
      EvmChainIdList.indexOf(chain) > -1
        ? () => getEvmTxSuccess(chain as AssetProEvmChainId, txHash)
        : () => getTronTxSuccess(chain as AssetProNonEvmChainId, txHash);

    getTxSuccess(getTxSuccessFunc).then((success) => {
      setLoading(false);
      setSuccess(success);
    });
  }, [chain, txHash]);

  return {
    loading,
    success,
  };
};

const getTxSuccess = async (getTxStatusFunc: () => Promise<TxStatus>): Promise<boolean> => {
  // check tx status every 5 seconds
  const interval = 5000;
  const maxRetry = 100;
  let retry = 0;
  while (retry < maxRetry) {
    const status = await getTxStatusFunc();
    if (status === 'success') {
      return true;
    } else if (status === 'failed') {
      return false;
    }
    retry++;
    await new Promise((r) => setTimeout(r, interval));
  }
  return false;
};

const getEvmTxSuccess = (chainId: AssetProEvmChainId, txHash: string): Promise<TxStatus> => {
  return getTxStatus(chainId, txHash);
};

const getTronTxSuccess = (chainId: AssetProNonEvmChainId, txHash: string): Promise<TxStatus> => {
  return getTronTxStatus(chainId, txHash);
};
