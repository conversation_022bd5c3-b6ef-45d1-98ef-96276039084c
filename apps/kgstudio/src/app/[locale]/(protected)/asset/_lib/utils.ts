import BigNumber from 'bignumber.js';
import * as A from 'fp-ts/Array';
import * as O from 'fp-ts/Option';
import * as R from 'fp-ts/Record';
import { pipe } from 'fp-ts/lib/function';

import { SEVERE_KEYWORDS } from '../_constants';

export const roundDownTo = (amount: string, precision: number) => {
  if (amount.endsWith('.')) return amount;
  if (amount === '') return 0;

  if (amount.includes('.') && amount.endsWith('0')) {
    const arr = amount.split('.');

    if (arr[1].length <= precision) return amount;

    return arr[0] + '.' + arr[1].slice(0, precision);
  }

  const bigNumber = new BigNumber(amount);
  if ((bigNumber.dp() as number) > precision) return bigNumber.toFixed(precision, BigNumber.ROUND_DOWN);
  else return bigNumber.toFixed(bigNumber.dp() as number);
};

export const amountInputInterceptor = (e: React.FormEvent<HTMLInputElement>, decimals = 8) => {
  e.currentTarget.value = e.currentTarget.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');

  e.currentTarget.value = roundDownTo(e.currentTarget.value, decimals).toString();
};

// For KYA Risk chart
export const preprocessSignalsProfile = (signalProfile: Record<string, number>): Record<string, number> =>
  pipe(
    signalProfile,
    R.toArray,
    A.map(([name, value]) => {
      let result = value;

      SEVERE_KEYWORDS.forEach((keyword) => {
        if (name.toLowerCase().includes(keyword)) {
          result *= 10;
        }
      });

      return [name, result];
    }),
    Object.fromEntries,
  );

export const calculateScore = (signals: Record<string, number> | null, signalsProfile: Record<string, number>) =>
  pipe(
    signals,
    O.fromNullable,
    O.map(R.toArray),
    O.map(
      A.reduce(0, (acc, [name, value]) => {
        const processedSignalsProfile = preprocessSignalsProfile(signalsProfile);
        const profileValue = processedSignalsProfile[name];
        return acc + value * profileValue;
      }),
    ),
    O.getOrElseW(() => null),
  );
