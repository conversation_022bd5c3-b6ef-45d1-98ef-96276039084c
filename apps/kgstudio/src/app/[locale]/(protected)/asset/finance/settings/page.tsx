'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { usePageHeader } from '@/app/_common/hooks';
import { apiAssetProExtendHooks } from '@/app/_common/services';
import { ProfitRate } from '@/app/_common/services/asset-pro-extend/model';
import { useOrganizationStore } from '@/app/_common/store';
import { Card, DataTable } from '@kryptogo/2b';
import { TableOptions, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';

import { ProfitSettingTableColumn, ProfitSettingModal } from './_components';

type SelectedService = {
  service: ProfitRate['service'];
  profitRate: number;
  minRate: number;
  maxRate: number;
};

export default function FinanceSettings() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.treasury.profit-rate-setting'), backLink: '/asset/finance' });

  const [orgId] = useOrganizationStore((state) => [state.orgId]);
  const [openProfitRateModal, setOpenProfitRateModal] = useState(false);
  const [selectedService, setSelectedService] = useState<SelectedService>({
    service: 'buy',
    profitRate: 0,
    minRate: 0,
    maxRate: 20,
  });

  const [profitRateTableOption, setProfitRateTableOption] = useState<TableOptions<ProfitRate>>({
    data: [],
    columns: [],
    manualSorting: false,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });
  const profitRateTable = useReactTable(profitRateTableOption);

  const {
    data: profitRateData,
    error: profitRateError,
    isLoading: profitRateLoading,
  } = apiAssetProExtendHooks.useGetProfitRates(
    {
      params: {
        org_id: orgId ?? -1,
      },
    },
    {
      enabled: !!orgId,
    },
  );

  const handleOpenProfitRateModal = (
    service: ProfitRate['service'],
    profitRate: number,
    minRate: number,
    maxRate: number,
  ) => {
    setSelectedService({
      service,
      profitRate,
      minRate,
      maxRate,
    });
    setOpenProfitRateModal(true);
  };

  useEffect(() => {
    if (!profitRateData) return;
    setProfitRateTableOption((prev) => ({
      ...prev,
      data: profitRateData.data,
      columns: ProfitSettingTableColumn({
        t,
        actionFn: handleOpenProfitRateModal,
      }),
    }));
  }, [profitRateData, t]);

  useEffect(() => {
    if (profitRateError) {
      toast.error(t('kgstudio.common.error'));
    }
  }, [profitRateError, t]);

  return (
    <>
      <Card className="!p-0">
        <DataTable
          table={profitRateTable}
          isLoading={profitRateLoading}
          dataLength={profitRateData?.data.length || 0}
        />
      </Card>
      <ProfitSettingModal
        open={openProfitRateModal}
        onOpenChange={setOpenProfitRateModal}
        service={selectedService.service}
        profitRate={selectedService.profitRate}
        minRate={selectedService.minRate}
        maxRate={selectedService.maxRate}
      />
    </>
  );
}
