import BigNumber from 'bignumber.js';
import { Pencil } from 'lucide-react';
import { match } from 'ts-pattern';

import { ProfitRate } from '@/app/_common/services/asset-pro-extend/model';
import { Button } from '@kryptogo/2b';
import { ColumnDef } from '@tanstack/react-table';

const PROFIT_RATE_MIN_MAX = new Map<ProfitRate['service'], { min: number; max: number }>([
  ['buy', { min: 0, max: 10 }],
  ['swap_gas', { min: 0, max: 20 }],
  ['swap_defi', { min: 0, max: 3 }],
  ['send_with_fee', { min: 0, max: 30 }],
  ['send_gasless', { min: 0, max: 20 }],
  ['send_batch', { min: 0, max: 0 }],
  ['bridge', { min: 0, max: 2 }],
]);

interface ProfitSettingTableColumnArgs {
  t: any;
  actionFn: (service: ProfitRate['service'], profitRate: number, minRate: number, maxRate: number) => void;
}

const ProfitSettingTableColumn = ({ t, actionFn }: ProfitSettingTableColumnArgs): ColumnDef<ProfitRate>[] => [
  {
    accessorKey: 'service',
    size: 180,
    meta: {
      sortable: true,
    },
    header: () => <div className="pl-2">{t('kgstudio.treasury.profit-service')}</div>,
    cell: ({ row }) => {
      const { service } = row.original;

      const type = match(service)
        .with('buy', () => t('kgstudio.treasury.profit-service-buy'))
        .with('swap_gas', () => t('kgstudio.treasury.profit-service-swap-cefi'))
        .with('swap_defi', () => t('kgstudio.treasury.profit-service-swap-defi'))
        .with('send_with_fee', () => t('kgstudio.treasury.profit-service-send'))
        .with('send_gasless', () => t('kgstudio.treasury.profit-service-send-gasless'))
        .with('send_batch', () => t('kgstudio.treasury.profit-service-send-batch'))
        .with('bridge', () => t('kgstudio.treasury.profit-service-bridge'))
        .exhaustive();

      return <p className="text-primary text-body-2-bold pl-2">{type}</p>;
    },
  },
  {
    accessorFn: (row) => row.service,
    id: 'profit-description',
    size: 300,
    header: t('kgstudio.treasury.profit-description'),
    cell: ({ row }) => {
      const { service } = row.original;
      const [desc1, desc2] = match(service)
        .with('buy', () => [
          t('kgstudio.treasury.profit-service-buy-desc1'),
          t('kgstudio.treasury.profit-service-buy-desc2'),
        ])
        .with('swap_gas', () => [
          t('kgstudio.treasury.profit-service-swap-cefi-desc1'),
          t('kgstudio.treasury.profit-service-swap-cefi-desc2'),
        ])
        .with('swap_defi', () => [
          t('kgstudio.treasury.profit-service-swap-defi-desc1'),
          t('kgstudio.treasury.profit-service-swap-defi-desc2'),
        ])
        .with('send_with_fee', () => [
          t('kgstudio.treasury.profit-service-send-desc1'),
          t('kgstudio.treasury.profit-service-send-desc2'),
        ])
        .with('send_gasless', () => [
          t('kgstudio.treasury.profit-service-send-gasless-desc1'),
          t('kgstudio.treasury.profit-service-send-gasless-desc2'),
        ])
        .with('send_batch', () => [t('kgstudio.treasury.profit-service-send-batch-desc1'), null])
        .with('bridge', () => [
          t('kgstudio.treasury.profit-service-bridge-desc1'),
          t('kgstudio.treasury.profit-service-bridge-desc2'),
        ])
        .exhaustive();

      return (
        <div className="flex flex-col gap-4">
          <p className="text-primary text-small whitespace-pre-line">{desc1}</p>
          {desc2 && <p className="text-primary text-small whitespace-pre-line">{desc2}</p>}
        </div>
      );
    },
  },
  {
    accessorKey: 'profit_rate',
    size: 120,
    meta: {
      sortable: true,
    },
    header: () => t('kgstudio.treasury.profit-margin-rate'),
    sortUndefined: 'last',
    cell: ({ row }) => {
      const { profit_rate, service } = row.original;
      return (
        <p className="text-primary text-body-2-bold">
          {service === 'send_batch' ? '-' : `${BigNumber(profit_rate).multipliedBy(100).toString()} %`}
        </p>
      );
    },
  },
  {
    accessorKey: 'action',
    header: () => <p className="pr-6 text-center">{t('common.action')}</p>,
    size: 100,
    cell: ({ row }) => {
      const { service, profit_rate } = row.original;
      if (service === 'send_batch') return null;

      return (
        <div className="flex justify-center">
          <Button
            size="md"
            variant="grey"
            data-cy="edit-profit-rate-btn"
            icon={<Pencil className="stroke-secondary" strokeWidth={3} size={16} />}
            onClick={() =>
              actionFn(
                service,
                profit_rate,
                PROFIT_RATE_MIN_MAX.get(service)?.min ?? 0,
                PROFIT_RATE_MIN_MAX.get(service)?.max ?? 0,
              )
            }
          >
            {t('kgstudio.common.edit')}
          </Button>
        </div>
      );
    },
  },
];

export { ProfitSettingTableColumn };
