'use client';

import { endOfDay, endOfToday, getTime, isAfter, isEqual, startOfDay, startOfToday, subDays } from 'date-fns';
import { decodeJwt } from 'jose';
import { Loader, RotateCw, Settings2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { toast } from 'sonner';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { FilterItem, FormFilterGroup } from '@/app/_common/components/form';
import { usePageHeader, usePermissions } from '@/app/_common/hooks';
import { apiOrganizationExtendHooks } from '@/app/_common/services';
import { GrafanaLogin } from '@/app/_common/services/user360';
import { useOrganizationStore } from '@/app/_common/store';
import { env } from '@/env.mjs';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form } from '@kryptogo/2b';

const GRAFANA_DATA_URL = `${env.NEXT_PUBLIC_GRAFANA_BASE_URL}/2fmxtrnt91sq5h/finance`;

const FilterSchema = z.object({
  date: z.enum(['all', 'last_7_days', 'last_14_days', 'last_30_days', 'custom']),
  date_from: z.coerce
    .date()
    .optional()
    .transform((date) => (!!date ? Math.floor(date.getTime() / 1000) : undefined)),
  date_to: z.coerce
    .date()
    .optional()
    .transform((date) => (!!date ? Math.floor(date.getTime() / 1000) : undefined)),
});
type DataFilter = z.infer<typeof FilterSchema>;

// TODO: This page is duplicated with user360 asset-pro data page for V1, refactor to reuse the same page after requirements are clear
//TODO: use cookie to memorize grafana login data to prevent login every time
const Finance = () => {
  const t = useTranslations();
  const router = useRouter();
  usePageHeader({ title: t('kgstudio.common.revenue') });

  const [hasEditLiquidityPermission] = usePermissions(['asset_pro_liquidity', 'edit']);
  const [orgId] = useOrganizationStore((state) => [state.orgId]);
  const {
    mutate: loginToGrafana,
    data: grafanaData,
    status: loginToGrafanaStatus,
  } = apiOrganizationExtendHooks.useLoginToGrafana(
    {
      params: {
        org_id: orgId ?? -1,
      },
    },
    {
      onError: (error) => {
        console.error(error);

        toast.error(t('kgstudio.data.asset-pro.error'));
      },
    },
  );

  const grafanaFromData = grafanaData?.data?.configs?.from;

  const form = useForm<DataFilter>({
    values: {
      date: 'all',
      date_from: grafanaFromData ? grafanaFromData * 1000 : startOfToday().getTime(),
      date_to: endOfToday().getTime(),
    },
    resolver: zodResolver(FilterSchema),
  });

  const [date, fromTime, toTime] = useWatch({
    control: form.control,
    name: ['date', 'date_from', 'date_to'],
  });

  const fromTimestamp = getTime(!!fromTime ? startOfDay(fromTime) : startOfToday());
  const toTimestamp = getTime(!!toTime ? endOfDay(toTime) : endOfToday());

  const filterItems: FilterItem<DataFilter>[] = useMemo(
    () => [
      {
        name: 'date',
        subject: t('kgstudio.data.asset-pro.date.title'),
        type: 'select',
        hideOptionAll: true,
        options: [
          { label: t('kgstudio.data.asset-pro.date.all'), value: 'all' },
          { label: t('kgstudio.data.asset-pro.date.last-7-days'), value: 'last_7_days' },
          { label: t('kgstudio.data.asset-pro.date.last-14-days'), value: 'last_14_days' },
          { label: t('kgstudio.data.asset-pro.date.last-30-days'), value: 'last_30_days' },
          { label: t('kgstudio.data.time-option-custom'), value: 'custom', disabled: true },
        ],
      },
      {
        names: ['date_from', 'date_to'],
        subject: t('kgstudio.data.asset-pro.date.title'),
        type: 'date-range',
        disabledUnchecked: true,
      },
    ],
    [t],
  );

  const generateGrafanaUrl = useCallback(
    (grafanaData: GrafanaLogin, kgOrgId: number, fromTimestamp: number, toTimestamp: number) => {
      const url = new URL(GRAFANA_DATA_URL);
      const searchParams = new URLSearchParams({
        orgId: '1',
        'var-organization_id': kgOrgId.toString(),
        auth_token: grafanaData.auth_token,
        kiosk: grafanaData.configs.kiosk ?? 'true',
        from: fromTimestamp.toString(),
        to: toTimestamp.toString(),
      });

      url.search = searchParams.toString();
      return url.toString();
    },
    [],
  );

  useEffect(() => {
    if (!grafanaData) return loginToGrafana(undefined);

    const decodedJwt = decodeJwt(grafanaData.data.auth_token);
    const expTimestampInMs = (decodedJwt.exp as number) * 1_000;
    const currentTimestampInMs = new Date().getTime();
    const remainingTimeInMs = expTimestampInMs - currentTimestampInMs;

    const refreshToken = setTimeout(() => {
      loginToGrafana(undefined);
    }, remainingTimeInMs);

    return () => {
      clearTimeout(refreshToken);
    };
  }, [grafanaData, loginToGrafana]);

  useEffect(() => {
    if (date === 'custom') return;
    form.setValue('date_to', endOfToday().getTime());

    match(date)
      .with('last_7_days', () => form.setValue('date_from', subDays(startOfToday(), 6).getTime()))
      .with('last_14_days', () => form.setValue('date_from', subDays(startOfToday(), 13).getTime()))
      .with('last_30_days', () => form.setValue('date_from', subDays(startOfToday(), 29).getTime()))
      .with('all', () =>
        form.setValue('date_from', grafanaFromData ? grafanaFromData * 1000 : startOfToday().getTime()),
      )
      .otherwise(() => null);
  }, [date, form, grafanaFromData]);

  useEffect(() => {
    if (!toTimestamp || !fromTimestamp) return;

    if (!isEqual(toTimestamp, endOfToday().getTime())) {
      form.setValue('date', 'custom');
    } else if (isEqual(fromTimestamp, subDays(startOfToday(), 6).getTime())) {
      form.setValue('date', 'last_7_days');
    } else if (isEqual(fromTimestamp, subDays(startOfToday(), 13).getTime())) {
      form.setValue('date', 'last_14_days');
    } else if (isEqual(fromTimestamp, subDays(startOfToday(), 29).getTime())) {
      form.setValue('date', 'last_30_days');
    } else if (!!grafanaFromData && !isAfter(fromTimestamp, grafanaFromData * 1000)) {
      form.setValue('date', 'all');
    } else {
      form.setValue('date', 'custom');
    }
  }, [fromTimestamp, toTimestamp, grafanaFromData, form]);

  if (loginToGrafanaStatus === 'loading' || loginToGrafanaStatus === 'idle') {
    return (
      <div className="flex h-[calc(100vh-88px)] w-full items-center justify-center gap-2">
        <Loader className="stroke-brand-primary-dark animate-spin" />
        <span className="text-body text-primary">{t('kgstudio.asset.finance.loading')}</span>
      </div>
    );
  }

  if (loginToGrafanaStatus === 'error') {
    return (
      <div
        className="flex h-[calc(100vh-88px)] w-full cursor-pointer flex-col items-center justify-center gap-2"
        onClick={() => loginToGrafana(undefined)}
      >
        <RotateCw className="stroke-error" size={24} />
        <span className="text-body text-error">{t('kgstudio.data.asset-pro.retry')}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Form {...form}>
          <FormFilterGroup control={form.control} items={filterItems} data-cy="finance-filter-group" />
        </Form>
        {hasEditLiquidityPermission && (
          <Button
            size="md"
            variant="primary"
            icon={<Settings2 />}
            onClick={() => router.push('/asset/finance/settings')}
          >
            {t('kgstudio.treasury.profit-margin-setting')}
          </Button>
        )}
      </div>
      {!!fromTimestamp && !!toTimestamp && (
        <iframe
          src={generateGrafanaUrl(grafanaData.data, orgId as number, fromTimestamp, toTimestamp)}
          title="KryptoGO Finance Data"
          allowFullScreen={true}
          className="h-[calc(100vh-88px)] w-full"
        ></iframe>
      )}
    </div>
  );
};

export default Finance;
