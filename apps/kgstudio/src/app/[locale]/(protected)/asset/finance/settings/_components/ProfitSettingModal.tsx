import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { toast } from 'sonner';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { amountInputInterceptor } from '@/app/[locale]/(protected)/asset/_lib/utils';
import { FormInput } from '@/app/_common/components/form';
import { isApiError } from '@/app/_common/lib/api';
import { apiAssetProExtendHooks } from '@/app/_common/services';
import { ProfitRateSchema, ProfitRate } from '@/app/_common/services/asset-pro-extend/model';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal, ConfirmationModal, ReminderBlock } from '@kryptogo/2b';
import { formatCurrency, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';

interface ProfitSettingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  service: ProfitRate['service'];
  profitRate: number;
  minRate: number;
  maxRate: number;
}

const ProfitSettingModal = ({ service, profitRate, minRate, maxRate, ...props }: ProfitSettingModalProps) => {
  const t = useTranslations();
  const orgId = useOrganizationStore((state) => state.orgId);
  const profitRatePercent = BigNumber(profitRate).multipliedBy(100).toString();
  const [confirmEditModalOpen, setConfirmEditModalOpen] = useState(false);

  const EditProfitSchema = z.object({
    service: ProfitRateSchema.shape.service,
    profit_rate: z.string().refine((value) => Number(value) >= minRate && Number(value) <= maxRate, {
      message: t('kgstudio.error.out-of-range'),
    }),
  });
  type EditProfit = z.infer<typeof EditProfitSchema>;

  const form = useForm<EditProfit>({
    values: {
      service,
      profit_rate: profitRatePercent,
    },
    resolver: zodResolver(EditProfitSchema),
    mode: 'onChange',
  });

  const [watchedProfitRate] = useWatch({ control: form.control, name: ['profit_rate'] });
  const watchedProfitRateNumber = BigNumber(watchedProfitRate).dividedBy(100).toNumber();

  const [serviceType, hint1, hint2, info1, info2] = match(service)
    .with('buy', () => [
      t('kgstudio.treasury.profit-service-buy'),
      t('kgstudio.treasury.profit-service-buy-hint1'),
      null,
      t('kgstudio.treasury.profit-service-buy-info1'),
      t('kgstudio.treasury.profit-service-buy-info2', {
        profit_rate: watchedProfitRate,
        profit_usd: formatCurrency({
          amount: 100 / (1 + 0.07) - 100 / (1 + 0.07 + watchedProfitRateNumber),
          decimals: DECIMAL_DISPLAY_MODE.FIAT,
        }),
      }),
    ])
    .with('swap_gas', () => [
      t('kgstudio.treasury.profit-service-swap-cefi'),
      t('kgstudio.treasury.profit-service-swap-cefi-hint1'),
      t('kgstudio.treasury.profit-service-swap-cefi-hint2'),
      t('kgstudio.treasury.profit-service-swap-cefi-info1'),
      t('kgstudio.treasury.profit-service-swap-cefi-info2', {
        profit_rate: watchedProfitRate,
        profit_usd: formatCurrency({
          amount: BigNumber(100 - 5).minus(BigNumber(100 - 5).times(1 - watchedProfitRateNumber)),
          decimals: DECIMAL_DISPLAY_MODE.FIAT,
        }),
      }),
    ])
    .with('swap_defi', () => [
      t('kgstudio.treasury.profit-service-swap-defi'),
      t('kgstudio.treasury.profit-service-swap-defi-hint1'),
      t('kgstudio.treasury.profit-service-swap-defi-hint2'),
      t('kgstudio.treasury.profit-service-swap-defi-info1'),
      t('kgstudio.treasury.profit-service-swap-defi-info2', {
        profit_rate: watchedProfitRate,
        profit_usd: formatCurrency({
          amount: 100 - 100 * (1 - watchedProfitRateNumber),
          decimals: DECIMAL_DISPLAY_MODE.FIAT,
        }),
      }),
    ])
    .with('send_with_fee', () => [
      t('kgstudio.treasury.profit-service-send'),
      t('kgstudio.treasury.profit-service-send-hint1'),
      t('kgstudio.treasury.profit-service-send-hint2'),
      t('kgstudio.treasury.profit-service-send-info1'),
      t('kgstudio.treasury.profit-service-send-info2', {
        profit_rate: watchedProfitRate,
        profit_trx: formatCurrency({
          amount: BigNumber(3 * watchedProfitRateNumber).toNumber(),
          decimals: DECIMAL_DISPLAY_MODE.TOKEN,
        }),
        profit_usd: formatCurrency({
          amount: 3 * watchedProfitRateNumber * 0.15,
          decimals: DECIMAL_DISPLAY_MODE.FIAT,
        }),
      }),
    ])
    .with('send_gasless', () => [
      t('kgstudio.treasury.profit-service-send-gasless'),
      t('kgstudio.treasury.profit-service-send-gasless-hint1'),
      t('kgstudio.treasury.profit-service-send-hint2'),
      t('kgstudio.treasury.profit-service-send-gasless-info1'),
      t('kgstudio.treasury.profit-service-send-gasless-info2', {
        profit_rate: watchedProfitRate,
        profit_trx: formatCurrency({
          amount: BigNumber(10 * watchedProfitRateNumber).toNumber(),
          decimals: DECIMAL_DISPLAY_MODE.TOKEN,
        }),
        profit_usd: formatCurrency({
          amount: 10 * watchedProfitRateNumber * 0.15,
          decimals: DECIMAL_DISPLAY_MODE.FIAT,
        }),
      }),
    ])
    .with('send_batch', () => [t('kgstudio.treasury.profit-service-send-batch'), null, null, null])
    .with('bridge', () => [
      t('kgstudio.treasury.profit-service-bridge'),
      t('kgstudio.treasury.profit-service-bridge-info1'),
      t('kgstudio.treasury.profit-service-bridge-info2'),
      t('kgstudio.treasury.profit-service-bridge-hint1'),
    ])
    .exhaustive();

  const { mutate: editProfitRate, isLoading: editProfitRateLoading } = apiAssetProExtendHooks.useEditProfitRate(
    {
      params: {
        org_id: orgId ?? -1,
      },
    },
    {
      meta: {
        awaitInvalidates: [apiAssetProExtendHooks.getKeyByAlias('getProfitRates')],
      },
      onSuccess: () => {
        setConfirmEditModalOpen(false);
        props.onOpenChange(false);
        toast.success(t('kgstudio.treasury.profit-rate-update-success'));
      },
      onError: (error) => {
        if (isApiError(error) && error.status === 400) {
          toast.error(t('kgstudio.error.out-of-range'));
        } else {
          toast.error(t('kgstudio.common.error'));
        }
      },
    },
  );

  const onSubmit = () => {
    const formData = form.getValues();

    editProfitRate({
      service: formData.service,
      profit_rate: BigNumber(formData.profit_rate).dividedBy(100).toNumber(),
    });
  };

  useEffect(() => {
    if (!props.open) {
      form.reset({
        service,
        profit_rate: profitRatePercent,
      });
    }
  }, [form, profitRatePercent, props.open, service]);

  return (
    <>
      <Modal {...props}>
        <Modal.Content className="max-h-[90%]">
          <Modal.Header>
            <Modal.Title>{t('kgstudio.treasury.profit-rate-edit')}</Modal.Title>
          </Modal.Header>
          <Form {...form}>
            <form onSubmit={() => setConfirmEditModalOpen(true)} className="flex flex-col gap-4">
              <div className="space-y-2">
                <h2 className="text-h2 text-primary">{serviceType}</h2>
                <p className="text-body-2 text-secondary">{`${t('kgstudio.treasury.profit-current-rate')}: ${profitRatePercent}%`}</p>
              </div>
              <FormInput
                name="profit_rate"
                title={t('kgstudio.treasury.profit-rate-edit-title')}
                data-cy="profit-rate-input"
                control={form.control}
                required
                onInput={(e) => amountInputInterceptor(e, 2)}
                hint={
                  <div className="flex flex-col gap-2">
                    <p>{hint1}</p>
                    <p>
                      {t('kgstudio.treasury.liquidity-settings-min-max', {
                        min: minRate,
                        max: maxRate,
                      })}
                    </p>
                    {hint2 && <p className="font-bold">{hint2}</p>}
                  </div>
                }
              />
              <ReminderBlock
                variant="info"
                title={
                  <div className="flex flex-col gap-2">
                    <p>{info1}</p>
                    <p>{info2}</p>
                  </div>
                }
              />
              <Modal.Footer className="mt-14 flex justify-between">
                <Button type="button" variant="grey" onClick={() => props.onOpenChange(false)}>
                  {t('common.cancel')}
                </Button>
                <Button
                  type="button"
                  className="md:w-[152px]"
                  data-cy="update-profit-rate-btn"
                  loading={editProfitRateLoading}
                  disabled={!form.formState.isValid}
                  onClick={() => setConfirmEditModalOpen(true)}
                >
                  {t('common.update')}
                </Button>
              </Modal.Footer>
            </form>
          </Form>
        </Modal.Content>
      </Modal>
      <ConfirmationModal
        open={confirmEditModalOpen}
        onOpenChange={setConfirmEditModalOpen}
        title={t('kgstudio.treasury.profit-rate-update-text')}
        desc={t('kgstudio.treasury.liquidity-update-confirm.message')}
        cancelState={{
          text: t('common.cancel'),
        }}
        confirmState={{
          text: t('kgstudio.treasury.liquidity-update-confirm.btn'),
          variant: 'primary',
        }}
        onConfirm={onSubmit}
      />
    </>
  );
};

export { ProfitSettingModal };
