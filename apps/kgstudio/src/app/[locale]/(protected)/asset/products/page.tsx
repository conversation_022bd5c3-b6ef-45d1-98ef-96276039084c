'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { usePageHeader } from '@/app/_common/hooks';
import { apiAssetProHooks } from '@/app/_common/services';
import { Product, Token } from '@/app/_common/services/asset-pro/model';

import { ProductModal } from './_components';
import { ProductNotReadyModal } from './_components/ProductNotReadyModal';
import ProductsTable from './_components/ProductsTable';

export default function Products() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.common.products') });
  const [modalOpen, setModalOpen] = useState(false);
  const [notReadyModalOpen, setNotReadyModalOpen] = useState(false);
  const [editData, setEditData] = useState<Product>();
  const [currToken, setCurrToken] = useState<Token | undefined>(undefined);

  const { data: tokenListData } = apiAssetProHooks.useGetTokenList();

  useEffect(() => {
    if (tokenListData && editData) {
      const token = tokenListData.data.find(
        ({ chain_id, symbol }) => chain_id === editData.chain_id && symbol === editData.base_currency,
      );
      setCurrToken(token);
    }
  }, [tokenListData, editData]);

  return (
    <div className="space-y-6">
      <ProductsTable
        type="published"
        setEditData={setEditData}
        editData={editData}
        setModalOpen={setModalOpen}
        setNotReadyModalOpen={setNotReadyModalOpen}
      />
      <ProductsTable
        type="unpublished"
        setEditData={setEditData}
        editData={editData}
        setModalOpen={setModalOpen}
        setNotReadyModalOpen={setNotReadyModalOpen}
      />

      {editData && currToken && (
        <ProductModal open={modalOpen} onOpenChange={setModalOpen} data={editData} token={currToken} />
      )}
      <ProductNotReadyModal
        open={notReadyModalOpen}
        onOpenChange={setNotReadyModalOpen}
        setEditModalOpen={setModalOpen}
      />
    </div>
  );
}
