import { format } from 'date-fns';
import { Pencil } from 'lucide-react';
import { match } from 'ts-pattern';

import { FormattedMessage, Image } from '@/app/_common/components';
import { cn } from '@/app/_common/lib/utils';
import { Product } from '@/app/_common/services/asset-pro/model';
import { Button, Switch } from '@kryptogo/2b';
import { formatCurrency, getChainFullName, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';
import { ColumnDef } from '@tanstack/react-table';

const TableColumnDesktop = (
  t: any,
  setModalOpen: (value: boolean) => void,
  setEditData: (value: Product) => void,
  onPublish: (value: Product) => void,
): ColumnDef<Product>[] => [
  {
    accessorKey: 'type',
    header: t('kgstudio.common.type'),
    size: 120,
    cell: ({ row }) => {
      const { type } = row.original;
      const productType = match(type)
        .with('buy_crypto', () => <FormattedMessage id="kgstudio.asset.products.product-type-buy-crypto" />)
        .exhaustive();

      return <p className="text-primary text-body-2">{productType}</p>;
    },
  },
  {
    accessorKey: 'image',
    header: t('kgstudio.common.image'),
    size: 64,
    cell: ({ row }) => {
      const { image } = row.original;

      return (
        <div className="relative h-12 w-12 overflow-hidden rounded-[5px]">
          {image ? <Image src={image} fill alt="product_img" /> : <div className="bg-disabled h-full w-full" />}
        </div>
      );
    },
  },
  {
    accessorKey: 'name',
    header: t('kgstudio.asset.products.product-name'),
    cell: ({ row }) => {
      const { name } = row.original;

      return <p className="text-primary text-body-2">{name}</p>;
    },
  },
  {
    accessorKey: 'price',
    header: t('kgstudio.asset.products.price'),
    size: 120,
    meta: {
      sortable: true,
    },
    cell: ({ row }) => {
      const { price, quote_currency } = row.original;

      return (
        <div className="text-primary text-body-2">
          <p>{price === null ? 'N/A' : `$${Number(price)?.toLocaleString()}`}</p>
          <p>{quote_currency}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'stock',
    header: t('kgstudio.asset.products.available'),
    size: 160,
    meta: {
      sortable: true,
    },
    cell: ({ row }) => {
      const { stock, base_currency, chain_id, logo_url } = row.original;
      const chain = getChainFullName(chain_id);

      if (stock === null) return <p className="text-primary text-body-2">N/A</p>;

      return (
        <>
          <p className="text-primary text-body-2">{formatCurrency({ amount: stock })}</p>
          <div className="flex items-center gap-1">
            <Image src={logo_url} width={16} height={16} alt={base_currency} />
            <span className="text-primary text-body-2">{`${base_currency}(${chain})`}</span>
          </div>
        </>
      );
    },
  },
  {
    accessorKey: 'fee',
    size: 160,
    header: t('kgstudio.asset.products.fee'),
    cell: ({ row }) => {
      const { fee_type, proportional_fee_percentage, proportional_minimum_fee, quote_currency } = row.original;

      return (
        <div className="text-primary text-body-2">
          {fee_type === 'no_fee' ? (
            'N/A'
          ) : (
            <>
              <p>
                {formatCurrency({
                  amount: proportional_fee_percentage ?? 0,
                  decimals: DECIMAL_DISPLAY_MODE.PERCENT,
                  fmt: {
                    suffix: ' %',
                  },
                })}
              </p>
              <p>
                <FormattedMessage id="kgstudio.common.minimum" />:{' '}
                {proportional_minimum_fee
                  ? formatCurrency({
                      amount: proportional_minimum_fee,
                      decimals: DECIMAL_DISPLAY_MODE.PRICE,
                      fmt: {
                        prefix: '$',
                      },
                    })
                  : 'N/A'}
              </p>
              <p>{quote_currency}</p>
            </>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'Updated',
    header: t('kgstudio.asset.products.updated'),
    meta: {
      sortable: true,
    },
    cell: ({ row }) => {
      const {
        updated_at,
        operator: { name },
      } = row.original;

      return (
        <>
          <p className="text-primary text-body-2">{format(new Date(updated_at), 'yyyy/MM/dd HH:mm:ss')}</p>
          <p className="text-secondary text-small">{name || 'N/A'}</p>
        </>
      );
    },
  },
  {
    id: 'action',
    header: () => (
      <div className="flex justify-between">
        <p>{t('kgstudio.asset.products.publish')}</p>
        <p>{t('common.action')}</p>
      </div>
    ),
    size: 160,
    cell: ({ row }) => {
      const { is_published } = row.original;

      return (
        <div className="flex items-center justify-between">
          <div className="flex gap-1" onClick={(e) => e.stopPropagation()}>
            <Switch
              checked={is_published}
              className="peer"
              onClick={() => {
                onPublish(row.original);
              }}
            />
            <p
              className={cn(
                'text-body-2 peer-data-[state=checked]:text-success peer-data-[state=unchecked]:text-error',
              )}
            >
              {is_published ? 'On' : 'Off'}
            </p>
          </div>
          <Button
            data-cy="edit-transfer-btn"
            className="size-9"
            icon={<Pencil size="24" strokeWidth="2" />}
            variant="grey"
            size="sm"
            onClick={() => {
              setModalOpen(true);
              setEditData(row.original);
            }}
          ></Button>
        </div>
      );
    },
  },
];

export { TableColumnDesktop };
