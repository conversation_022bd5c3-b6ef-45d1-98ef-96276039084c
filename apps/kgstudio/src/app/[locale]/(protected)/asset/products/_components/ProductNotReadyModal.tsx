import { useTranslations } from 'next-intl';
import Image from 'next/image';

import alertCircle from '@/2b/icons/AlertCircle.svg';
import { Button, Modal } from '@kryptogo/2b';

export interface ProductNotReadyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  setEditModalOpen: (open: boolean) => void;
}

const ProductNotReadyModal = (props: ProductNotReadyModalProps) => {
  const t = useTranslations();
  const { open, onOpenChange, setEditModalOpen } = props;

  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <Modal.Content
        noClose
        className="grid h-[400px] max-w-[600px] grid-rows-[1fr_auto] gap-6 overflow-auto"
        data-cy="product-not-ready-modal"
      >
        <div className="flex flex-col items-center gap-2 pt-[60px]">
          <Image src={alertCircle} alt="alert" className="h-9 w-9" />
          <h2 className="txt-h2 text-primary text-center">
            {t('kgstudio.asset.products.not-ready-for-publish-title')}
          </h2>
          <p className="txt-body-2 text-secondary text-center">
            {t('kgstudio.asset.products.not-ready-for-publish-description')}
          </p>
        </div>

        <Modal.Footer className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            <Button type="button" variant="grey" onClick={() => onOpenChange(false)}>
              {t('kgstudio.common.cancel')}
            </Button>

            <Button
              type="button"
              onClick={() => {
                onOpenChange(false);
                setEditModalOpen(true);
              }}
              data-cy="product-edit-button"
            >
              {t('kgstudio.asset.products.edit-product')}
            </Button>
          </div>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { ProductNotReadyModal };
