'use client';

import { omit } from 'lodash-es';
import { Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { match } from 'ts-pattern';

import { showToast } from '@/2b/toast';
import { useDebounce, useDeviceSize, useUpdateEffect } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiAssetProHooks } from '@/app/_common/services';
import {
  AssetProChainId,
  EditProductRequest,
  EditProductRequestSchema,
  Product,
} from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { Card, DataTable, Input, useDataTable } from '@kryptogo/2b';
import { getChainFullName } from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';
import { TableOptions, getCoreRowModel, useReactTable } from '@tanstack/react-table';

import { TableColumnDesktop } from './TableColumnDesktop';

interface ProductsTableProps {
  type: 'published' | 'unpublished';
  editData: Product | undefined;
  setEditData: (data: Product) => void;
  setModalOpen: (open: boolean) => void;
  setNotReadyModalOpen: (open: boolean) => void;
}

const ProductsTable = (props: ProductsTableProps) => {
  const { type, setModalOpen, editData, setEditData, setNotReadyModalOpen } = props;
  const isPublishedTable = type === 'published';
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);
  const t = useTranslations();
  const orgId = useOrganizationStore((state) => state.orgId);
  const { deviceSize } = useDeviceSize();
  const queryClient = useQueryClient();
  const [tableOption, setTableOption] = useState<TableOptions<Product>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    meta: {
      fixedColumn: ['action'],
    },
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size, page_sort } = useDataTable(table);

  const {
    data: productsData,
    isLoading: productsLoading,
    error: productsError,
  } = apiAssetProHooks.useGetProductsList(
    {
      params: { org_id: Number(orgId) },
      queries: {
        is_published: isPublishedTable,
        page_size,
        page_number,
        q: debouncedQuery.trim() === '' ? undefined : debouncedQuery,
        page_sort: page_sort === '' ? undefined : page_sort,
      },
    },
    {
      enabled: !!orgId,
      refetchInterval: 18_000,
    },
  );

  const getQueryKey = (isPublished: boolean) =>
    apiAssetProHooks.getKeyByAlias('getProductsList', {
      params: { org_id: orgId ?? -1 },
      queries: {
        is_published: isPublished,
        page_size,
        page_number,
        page_sort: page_sort === '' ? undefined : page_sort,
        q: debouncedQuery.trim() === '' ? undefined : debouncedQuery,
      },
    });
  const publishedQueryKey = getQueryKey(true);
  const unpublishedQueryKey = getQueryKey(false);

  const { mutate: editProduct } = apiAssetProHooks.useEditProduct(
    {
      params: { org_id: Number(orgId), product_id: Number(editData?.product_id) },
    },
    {
      onMutate: async () => {
        // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
        await queryClient.cancelQueries({ queryKey: publishedQueryKey });
        await queryClient.cancelQueries({ queryKey: unpublishedQueryKey });

        // Snapshot the previous value
        const previousPublishedProducts = queryClient.getQueryData(publishedQueryKey);
        const previousUnpublishedProducts = queryClient.getQueryData(unpublishedQueryKey);

        ['published', 'unpublished'].map((type) => {
          queryClient.setQueryData(type === 'published' ? publishedQueryKey : unpublishedQueryKey, (old: any) => {
            const isAddingToTable =
              (!editData?.is_published && type === 'published') || (editData?.is_published && type === 'unpublished');
            const newData = {
              code: 0,
              data: isAddingToTable
                ? [{ ...editData, is_published: !editData?.is_published }, ...(old?.data ? old.data : [])]
                : old?.data?.filter((d: Product) => d?.product_id !== editData?.product_id),
              paging: {
                ...old?.paging,
                total_count: old?.paging.total_count + (isAddingToTable ? 1 : -1),
              },
            };

            return { ...newData };
          });
        });

        return { previousPublishedProducts, previousUnpublishedProducts };
      },
      onSuccess: () => {
        [publishedQueryKey, unpublishedQueryKey].forEach((key) => {
          queryClient.refetchQueries({
            queryKey: key,
          });
        });

        showToast(
          `${t('kgstudio.asset.products.update-success-toast', {
            baseCurrency: editData?.base_currency || '',
            quoteCurrency: editData?.quote_currency || '',
            chain: getChainFullName(editData?.chain_id as AssetProChainId) || '',
          })}`,
          'success',
        );
      },
      // If the mutation fails,
      // use the context returned from onMutate to roll back
      onError: (err, newProducts, context: any) => {
        queryClient.setQueryData(publishedQueryKey, context.previousPublishedProducts);
        queryClient.setQueryData(unpublishedQueryKey, context.previousUnpublishedProducts);

        if (isApiError(err)) {
          showToast(
            t('kgstudio.asset.products.update-failure-toast', {
              baseCurrency: editData?.base_currency || '',
              quoteCurrency: editData?.quote_currency || '',
              chain: getChainFullName(editData?.chain_id as AssetProChainId) || '',
              code: err.code,
            }),
            'error',
          );
        }
      },
      // Always refetch after error or success:
      onSettled: () => {
        [publishedQueryKey, unpublishedQueryKey].forEach((key) => {
          queryClient.invalidateQueries({ queryKey: key });
        });
      },
    },
  );

  const validateProduct = (product: Product) => {
    // NOTE: FE validation - user can only publish product with display inventory(stock) > 0
    const { success: isProductReadyForPublish } = EditProductRequestSchema.refine(
      (data) => Number(data.stock) > 0,
    ).safeParse(product);
    return isProductReadyForPublish;
  };

  const onPublish = (product: Product) => {
    match(validateProduct(product))
      .with(true, () => {
        const originalData = omit(product, ['is_published', 'created_at', 'updated_at', 'operator']);
        setEditData(product);
        const reqData = { ...originalData, is_published: !product.is_published };
        editProduct(reqData as EditProductRequest);
      })
      .with(false, () => {
        setEditData(product);
        setNotReadyModalOpen(true);
      })
      .exhaustive();
  };

  const MemoDesktopColumn = useMemo(() => TableColumnDesktop(t, setModalOpen, setEditData, onPublish), [t]);

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: productsData?.data || [],
      columns: MemoDesktopColumn,
    }));
  }, [MemoDesktopColumn, deviceSize, productsData]);

  useUpdateEffect(() => {
    if (productsError) {
      console.error(productsError);

      if (isApiError(productsError)) {
        showToast(t('kgstudio.common.error'), 'error');
      }
    }
  }, [productsError, t]);

  return (
    <Card className="!p-0">
      <div className="flex items-center justify-between p-4 md:p-6">
        <h2 className="text-h2 text-primary">
          {isPublishedTable
            ? t('kgstudio.asset.products.status-published')
            : t('kgstudio.asset.products.status-unpublished')}
        </h2>
        <Input
          className="w-full md:!w-[231px]"
          placeholder="Search..."
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
          value={query}
          suffix={<Search />}
        />
      </div>
      <DataTable
        className="border-y border-[var(--border-primary)] md:border-none"
        table={table}
        isLoading={productsLoading}
        dataLength={productsData?.paging.total_count || 0}
        onRowClick={(rowData) => {
          setModalOpen(true);
          setEditData(rowData);
        }}
      />
    </Card>
  );
};

export default ProductsTable;
