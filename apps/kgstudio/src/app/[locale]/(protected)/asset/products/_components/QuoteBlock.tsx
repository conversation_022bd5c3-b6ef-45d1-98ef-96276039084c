import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import { useWatch } from 'react-hook-form';
import { match } from 'ts-pattern';

import { AssetProChainId } from '@/app/_common/services/asset-pro/model';
import { ReminderBlock } from '@kryptogo/2b';
import { formatCurrency, getChainFullName } from '@kryptogo/utils';

import { QUOTE_BLOCK_EXAMPLE_BUDGET } from '../_constant';

const QuoteBlock = ({ chainId }: { chainId: AssetProChainId }) => {
  const t = useTranslations();
  const feeType = useWatch({ name: 'fee_type' });
  const porportionalFeePercentage = useWatch({ name: 'proportional_fee_percentage' });
  const porportionalMinimumFee = useWatch({ name: 'proportional_minimum_fee' });
  const currentPrice = useWatch({ name: 'price' });
  const quoteCurrency = useWatch({ name: 'quote_currency' });
  const baseCurrency = useWatch({ name: 'base_currency' });

  const chain = chainId && getChainFullName(chainId);

  if (!currentPrice) return null;

  return match(feeType)
    .with('fee_included', () => {
      const quoteDenominator = BigNumber(porportionalFeePercentage || '0')
        .div(100)
        .plus(1);
      const minFee = BigNumber(porportionalMinimumFee || '0');
      const ifChargeMinFee = BigNumber(QUOTE_BLOCK_EXAMPLE_BUDGET).isLessThan(
        BigNumber(QUOTE_BLOCK_EXAMPLE_BUDGET).div(quoteDenominator).plus(minFee),
      );

      const receivingAmountWithMinFee = formatCurrency({
        amount: BigNumber(QUOTE_BLOCK_EXAMPLE_BUDGET).minus(minFee).div(BigNumber(currentPrice)),
      });
      const receivingAmount = formatCurrency({
        amount: BigNumber(QUOTE_BLOCK_EXAMPLE_BUDGET).div(BigNumber(quoteDenominator)).div(BigNumber(currentPrice)),
      });

      const displayData = {
        title: ifChargeMinFee
          ? t('kgstudio.asset.products.fee-included-reminder-with-min-fee-title')
          : t('kgstudio.asset.products.fee-included-reminder-title'),
        description: ifChargeMinFee
          ? t('kgstudio.asset.products.fee-included-reminder-with-min-fee-description', {
              quoteCurrency,
              minFee: `${minFee}`,
              quoteAmount: `${receivingAmountWithMinFee}`,
              currentPrice,
              baseCurrency,
              chain: chain || '',
            })
          : t('kgstudio.asset.products.fee-included-reminder-description', {
              quoteCurrency,
              quoteDenominator: `${quoteDenominator}`,
              currentPrice,
              quoteAmount: `${receivingAmount}`,
              baseCurrency,
              chain: chain || '',
            }),
      };

      return (
        <ReminderBlock
          variant={'info'}
          title={displayData.title}
          description={displayData.description}
          className={'w-full'}
        />
      );
    })
    .with('no_fee', () => {
      const quoteAmount = formatCurrency({
        amount: new BigNumber(QUOTE_BLOCK_EXAMPLE_BUDGET).div(new BigNumber(currentPrice)),
      });
      return (
        <ReminderBlock
          variant={'info'}
          title={t('kgstudio.asset.products.fee-free-reminder-title')}
          description={t('kgstudio.asset.products.fee-free-reminder-description', {
            quoteCurrency,
            currentPrice,
            quoteAmount: `${quoteAmount}`,
            baseCurrency,
            chain: chain || '',
          })}
          className={'w-full'}
        />
      );
    })
    .exhaustive();
};

export { QuoteBlock };
