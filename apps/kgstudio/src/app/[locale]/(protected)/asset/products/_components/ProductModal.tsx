import { ArrowR<PERSON>, ExternalLink, RotateCcw, Undo2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { amountInputInterceptor } from '@/app/[locale]/(protected)/asset/_lib/utils';
import { Image, LoadingModal, TokenIcon } from '@/app/_common/components';
import {
  FormDropdown,
  FormInput,
  FormMediaUploader,
  FormRadioGroup,
  FormSwitch,
  ImageSchema,
} from '@/app/_common/components/form';
import { Media, useGcpFileUpload, useTokenBalance, useUpdateEffect } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiAssetProHooks } from '@/app/_common/services';
import { EditProductRequest, Product, Token } from '@/app/_common/services/asset-pro/model';
import { useOrganizationStore } from '@/app/_common/store';
import taiwanFlagIcon from '@/assets/flag/icon-taiwan.png';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, FormLabel, Modal, Separator, ConfirmationModal } from '@kryptogo/2b';
import { getExplorerUrl, truncateTxhashOrAddress, getChainFullName, getChainIcon } from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';

import { QuoteBlock } from '../_components';
import { DEAFULT_FEE_PERCENTAGE, DEAFULT_MIN_FEE } from '../_constant';

type ProductModal = Omit<Product, 'organization_id' | 'created_at' | 'updated_at'>;

export interface ProductModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  data: ProductModal;
  token: Token;
}
export const TimestampSchema = z.object({ test: z.string() });

export const EditProductFormSchema = (t: any) =>
  z
    .object({
      type: z.literal('buy_crypto'),
      name: z.string().nonempty({ message: t('kgstudio.asset.products.name-required') }),
      is_published: z.boolean(),
      image: z.array(ImageSchema).nonempty({ message: t('kgstudio.image.upload-required') }),
      base_currency: z.union([z.literal('USDC'), z.literal('USDT')]),
      quote_currency: z.literal('TWD'),
      price: z
        .string()
        .nonempty({ message: t('kgstudio.asset.products.price-required') })
        .refine((val) => Number(val) > 0, { message: t('kgstudio.asset.products.price-required') }),
      order_limits_from: z
        .string()
        .nonempty({ message: t('kgstudio.asset.products.limit-from-required') })
        .refine((val) => Number(val) > 0, { message: t('kgstudio.asset.products.limit-from-required') }),
      order_limits_to: z
        .string()
        .nonempty({ message: t('kgstudio.asset.products.limit-to-required') })
        .refine((val) => Number(val) > 0, { message: t('kgstudio.asset.products.limit-to-required') }),
      fee_type: z.union([z.literal('fee_included'), z.literal('no_fee')]),
      proportional_fee_percentage: z.string(),
      proportional_minimum_fee: z.string() || '',
      stock: z.string().nullable(),
    })
    .refine((data) => Number(data.stock) > 0, {
      message: t('kgstudio.asset.products.inventory-greater-than-zero'),
      path: ['stock'],
    })
    .refine((data) => Number(data.stock) < 1000000000000, {
      message: t('kgstudio.asset.products.inventory-less'),
      path: ['stock'],
    })
    .refine((data) => Number(data.order_limits_to) > Number(data.order_limits_from), {
      message: t('kgstudio.asset.products.limit-validation'),
      path: ['order_limits_to'],
    })
    .refine(
      (data) => {
        if (data.fee_type === 'fee_included') {
          return !!data.proportional_fee_percentage;
        }
        return true;
      },
      {
        message: t('kgstudio.asset.products.limit-validation'),
        path: ['proportional_fee_percentage'],
      },
    );

type EditProductForm = z.infer<ReturnType<typeof EditProductFormSchema>>;

const ProductModal = ({ open, onOpenChange, data, token }: ProductModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  const orgId = useOrganizationStore((state) => state.orgId);
  const [cancelEditModalOpen, setCancelEditModalOpen] = useState(false);
  const chain = getChainFullName(data.chain_id);

  const form = useForm<EditProductForm>({
    defaultValues: {
      type: 'buy_crypto',
      name: '',
      is_published: true,
      image: [],
      base_currency: undefined,
      quote_currency: 'TWD',
      price: '',
      order_limits_from: '',
      order_limits_to: '',
      fee_type: undefined,
      proportional_fee_percentage: '',
      proportional_minimum_fee: '',
      stock: '',
    },
    values: {
      type: data.type,
      name: data.name,
      is_published: data.is_published,
      image: [{ dataURL: data.image }],
      base_currency: data.base_currency,
      quote_currency: data.quote_currency,
      price: data.price || '',
      order_limits_from: data.order_limits_from || '',
      order_limits_to: data.order_limits_to || '',
      fee_type: data.fee_type,
      proportional_fee_percentage: data.proportional_fee_percentage || '',
      proportional_minimum_fee: data.proportional_minimum_fee || '' || '',
      stock: data.stock || '',
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    resolver: zodResolver(EditProductFormSchema(t)),
  });

  const baseCurrency = data.base_currency;
  const quoteCurrency = data.quote_currency;

  const proportionalFeePercentage = useWatch({ control: form.control, name: 'proportional_fee_percentage' });
  const proportionalMinimumFee = useWatch({ control: form.control, name: 'proportional_minimum_fee' });
  const feeType = form.watch('fee_type');

  const isResetDefaultDisable =
    proportionalFeePercentage === DEAFULT_FEE_PERCENTAGE && proportionalMinimumFee === DEAFULT_MIN_FEE;

  const { formattedTokenBalance } = useTokenBalance({
    token: {
      chainId: token.chain_id,
      contractAddress: token.contract_address,
      symbol: token.symbol,
      decimals: token.decimals,
    },
  });

  const {
    mutate: editProduct,
    isLoading: editProductLoading,
    error: editProductError,
  } = apiAssetProHooks.useEditProduct(
    {
      params: { org_id: Number(orgId), product_id: Number(data.product_id) },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries({
          queryKey: apiAssetProHooks.getKeyByAlias('getProductsList'),
        });
        showToast(
          `${t('kgstudio.asset.products.update-success-toast', { baseCurrency, quoteCurrency, chain: chain || '' })}`,
          'success',
        );
        onOpenChange(false);
      },
    },
  );
  const {
    isError: fileUrlsError,
    isLoading: fileUrlsLoading,
    isIdle: fileUrlsIdle,
    progress: fileUrlsProgress,
    uploadAllFiles,
  } = useGcpFileUpload();

  const onSubmit = async (formData: EditProductForm) => {
    const formFields = [
      'type',
      'base_currency',
      'quote_currency',
      'name',
      'is_published',
      'price',
      'order_limits_from',
      'order_limits_to',
      'fee_type',
      'proportional_fee_percentage',
      'proportional_minimum_fee',
      'stock',
    ] as (keyof Omit<EditProductForm, 'chain_id' | 'product_id' | 'organization_id' | 'logo_url'>)[];

    const reqData = Object.fromEntries(formFields.map((val: (typeof formFields)[number]) => [val, formData[val]]));

    const files = formData.image;
    const localFiles = files.filter((data) => !!data.file);
    const filesWithKey =
      localFiles.length === 0
        ? null
        : {
            image: localFiles as Media[],
          };

    const fileUrls = !!filesWithKey ? await uploadAllFiles(filesWithKey, 'asset_pro') : null;

    fileUrls
      ? Object.keys(fileUrls).forEach((key) =>
          Object.assign(reqData, {
            [key]: fileUrls[key][0],
          }),
        )
      : Object.assign(reqData, {
          image: data.image,
        });

    if (formData.fee_type === 'no_fee') {
      Object.assign(reqData, {
        proportional_fee_percentage: null,
        proportional_minimum_fee: null,
      });
    }
    if (reqData.proportional_minimum_fee === '') {
      Object.assign(reqData, {
        proportional_minimum_fee: null,
      });
    }
    editProduct({
      ...reqData,
      chain_id: data.chain_id,
      product_id: Number(data.product_id),
      organization_id: Number(orgId),
      logo_url: data.logo_url,
    } as EditProductRequest);
  };

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  useUpdateEffect(() => {
    if (editProductError) {
      console.error(editProductError);

      if (isApiError(editProductError)) {
        showToast(
          t('kgstudio.asset.products.update-failure-toast', {
            baseCurrency,
            quoteCurrency,
            chain: chain || '',
            code: editProductError.code,
          }),
          'error',
        );
      }
    }
  }, [editProductError, t]);

  useUpdateEffect(() => {
    if (feeType === 'no_fee') {
      form.setValue('proportional_fee_percentage', '');
      form.setValue('proportional_minimum_fee', '');
    }
  }, [feeType]);

  return (
    <>
      <Modal open={open} onOpenChange={onOpenChange}>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Modal.Content className="max-h-[80%]" noClose scrollable data-cy="product-modal">
              <Modal.Header>
                <Modal.Title className="flex flex-col items-start gap-1">
                  {t('kgstudio.asset.products.edit-product')}
                </Modal.Title>
              </Modal.Header>
              <div className="grid h-full grid-cols-1 gap-6">
                <FormDropdown
                  disabled
                  title={t('kgstudio.asset.products.product-type')}
                  name="type"
                  control={form.control}
                  options={[{ label: t('kgstudio.asset.products.product-type-buy-crypto'), value: 'buy_crypto' }]}
                  required
                  className="w-full"
                />
                <div className="flex gap-6">
                  <FormMediaUploader
                    name="image"
                    title={t('kgstudio.common.image')}
                    previewInZone
                    dropZoneRatio="1/1"
                    dropZoneWidth={160}
                    control={form.control}
                    required
                    dropZoneDesc={
                      <div className="flex flex-col gap-1">
                        <span className="text-caption text-secondary">{t('kgstudio.asset.products.image-size')}</span>
                        <Button
                          variant="text"
                          size="sm"
                          className="text-highlight"
                          icon={<Undo2 strokeWidth={3} className="!stroke-[var(--text-highlight)]" />}
                          onClick={() => form.resetField('image')}
                        >
                          {t('kgstudio.asset.products.reset-image')}
                        </Button>
                      </div>
                    }
                  />
                  <div className="flex w-full flex-col gap-4">
                    <div className="space-y-2">
                      <FormLabel required>{t('kgstudio.asset.products.trading-pair')}</FormLabel>
                      <div className="flex w-full items-center gap-2 first:*:grow">
                        <FormDropdown
                          disabled
                          name="base_currency"
                          control={form.control}
                          options={[
                            {
                              label: (
                                <span className="flex items-center gap-2">
                                  <span className="relative h-5 w-5 shrink-0">
                                    <Image src={data.logo_url} alt={data.chain_id} fill className="rounded-full" />
                                    <div className="absolute bottom-[-2px] right-0 h-1/2 w-1/2 overflow-hidden rounded-full border-[2px] border-white">
                                      {
                                        <Image
                                          src={getChainIcon(data.chain_id) ?? ''}
                                          alt={data.chain_id}
                                          fill
                                          className="object-cover"
                                        />
                                      }
                                    </div>
                                  </span>
                                  USDT
                                </span>
                              ),
                              value: 'USDT',
                            },
                            {
                              label: (
                                <span className="flex items-center gap-2">
                                  <span className="relative h-5 w-5 shrink-0">
                                    <Image src={data.logo_url} alt={data.chain_id} fill className="rounded-full" />
                                    <div className="absolute bottom-[-2px] right-0 h-1/2 w-1/2 overflow-hidden rounded-full border-[2px] border-white">
                                      {
                                        <Image
                                          src={getChainIcon(data.chain_id) ?? ''}
                                          alt={data.chain_id}
                                          fill
                                          className="object-cover"
                                        />
                                      }
                                    </div>
                                  </span>
                                  USDC
                                </span>
                              ),
                              value: 'USDC',
                            },
                          ]}
                          required
                          className="w-full"
                        />
                        <ArrowRight size={24} />
                        <FormDropdown
                          disabled
                          name="quote_currency"
                          control={form.control}
                          options={[
                            {
                              label: (
                                <span className="flex items-center gap-2">
                                  <span className="relative h-5 w-5 overflow-hidden rounded-full">
                                    <Image src={taiwanFlagIcon} alt="flag" fill className="object-cover" />
                                  </span>
                                  {quoteCurrency}
                                </span>
                              ),
                              value: 'TWD',
                            },
                          ]}
                          required
                          className="max-w-[302px]"
                        />
                      </div>
                    </div>
                    <div className="space-y-4 rounded-xl border p-4">
                      <div className="flex items-center gap-3">
                        <TokenIcon
                          size="40"
                          token={{
                            logoUrl: token.logo_url,
                            name: token.name,
                          }}
                          chain={token.chain_id}
                        />
                        <div>
                          <p className="text-body-2-bold text-primary">{token.name}</p>
                          <span className="text-small text-secondary">
                            {token.symbol} ({token.chain_name})
                          </span>
                        </div>
                      </div>
                      <Separator />
                      <div className="flex gap-2">
                        <div className="text-caption text-secondary space-y-1">
                          <p>{t('kgstudio.treasury.quantity')}</p>
                          <p>{t('kgstudio.treasury.price')}</p>
                          <p>{t('kgstudio.treasury.contract-address')}</p>
                        </div>
                        <div className="text-caption text-primary space-y-1">
                          <p>
                            {formattedTokenBalance} {token.symbol}
                          </p>
                          <p>${data.price}</p>
                          <div className="flex items-center gap-3">
                            <p>{truncateTxhashOrAddress(token.contract_address)}</p>
                            <Link
                              href={getExplorerUrl('address', token.chain_id, token.contract_address) ?? '#'}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <ExternalLink size={16} className="text-secondary" />
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <FormInput
                  title={t('kgstudio.asset.products.product-name')}
                  name="name"
                  control={form.control}
                  required
                  data-cy="product-modal-name-input"
                />

                <FormInput
                  title={t('kgstudio.asset.products.price')}
                  name="price"
                  control={form.control}
                  suffix="TWD"
                  required
                  onInput={(e) => amountInputInterceptor(e, 4)}
                  data-cy="product-modal-price-input"
                />

                <div className="flex flex-col gap-2">
                  <FormLabel required>{t('kgstudio.asset.products.order-limit')}</FormLabel>
                  <div className="grid grid-cols-[1fr_10px_1fr] items-start gap-2">
                    <FormInput
                      name="order_limits_from"
                      control={form.control}
                      suffix="TWD"
                      required
                      onInput={(e) => amountInputInterceptor(e, 4)}
                      data-cy="product-modal-limits-from-input"
                    />
                    <span className="mt-3">~</span>
                    <FormInput
                      name="order_limits_to"
                      control={form.control}
                      suffix="TWD"
                      required
                      onInput={(e) => amountInputInterceptor(e, 4)}
                      data-cy="product-modal-limits-to-input"
                    />
                  </div>
                </div>
                <FormInput
                  title={t('kgstudio.asset.products.stock')}
                  name="stock"
                  control={form.control}
                  required
                  onInput={(e) => amountInputInterceptor(e, 6)}
                  data-cy="product-modal-stock-input"
                  hint={
                    <>
                      {t('kgstudio.asset.products.stock-hint', {
                        tokenAmount: `${formattedTokenBalance} ${token.symbol}`,
                      })}{' '}
                      <Link href="/asset/treasury" target="_blank" className="text-brand-primary underline">
                        {t('kgstudio.common.top-up')}
                      </Link>
                    </>
                  }
                />

                <div className="space-y-list-medium">
                  <div>
                    <FormRadioGroup
                      title={t('kgstudio.asset.products.handling-fee')}
                      name="fee_type"
                      control={form.control}
                      items={[
                        {
                          label: t('kgstudio.asset.products.handling-fee-no'),
                          value: 'no_fee',
                        },
                        {
                          label: t('kgstudio.asset.products.handling-fee-yes'),
                          value: 'fee_included',
                        },
                      ]}
                      required
                      data-cy="product-modal-fee-type-radio"
                    />
                    {feeType === 'fee_included' && (
                      <div className="grid h-full grid-cols-[8px_1fr]">
                        <Separator orientation="vertical" className="m-2" />
                        <div className="pl-card-large py-card-medium space-y-list-medium">
                          <FormInput
                            title={t('kgstudio.asset.products.handling-fee-proportional')}
                            name="proportional_fee_percentage"
                            control={form.control}
                            suffix="%"
                            hint={t('kgstudio.asset.products.handling-fee-hint')}
                            required
                            data-cy="product-modal-fee-percentage-input"
                            onInput={(e) => amountInputInterceptor(e, 2)}
                          />
                          <FormInput
                            title={t('kgstudio.asset.products.minimum-fee')}
                            name="proportional_minimum_fee"
                            control={form.control}
                            suffix="TWD"
                            onInput={(e) => amountInputInterceptor(e, 4)}
                            data-cy="product-modal-min-fee-input"
                          />
                          <Button
                            disabled={isResetDefaultDisable}
                            variant="secondary"
                            onClick={() => {
                              form.setValue('proportional_fee_percentage', DEAFULT_FEE_PERCENTAGE);
                              form.setValue('proportional_minimum_fee', DEAFULT_MIN_FEE);
                            }}
                            icon={<Undo2 className="group-disabled:stroke-white" />}
                            data-cy="product-modal-reset-button"
                          >
                            {t('kgstudio.asset.products.reset-to-default')}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  <QuoteBlock chainId={data.chain_id} />

                  <Separator />
                  <p className="text-body-bold text-primary">{t('kgstudio.asset.products.reset-all-title')}</p>
                  <Button variant="danger" size="md" onClick={() => form.reset()} icon={<RotateCcw />}>
                    {t('kgstudio.asset.products.reset-all')}
                  </Button>
                </div>
              </div>

              <Modal.Footer className="flex justify-between">
                <Button
                  variant={'grey'}
                  size="md"
                  onClick={() => {
                    setCancelEditModalOpen(true);
                  }}
                  data-cy="product-modal-cancel-button"
                >
                  {t('kgstudio.common.cancel')}
                </Button>
                <div className="flex items-center gap-10">
                  <div className="flex items-center gap-2">
                    <p className="text-primary text-body-bold">{t('kgstudio.asset.products.publish')}</p>
                    <FormSwitch name="is_published" control={form.control} data-cy="product-modal-publish-input" />
                  </div>
                  <Button
                    onClick={form.handleSubmit(onSubmit)}
                    data-cy="product-modal-update-button"
                    size="md"
                    className="w-[150px]"
                    disabled={editProductLoading || !form.formState.isValid}
                  >
                    {t('kgstudio.common.update')}
                  </Button>
                </div>
              </Modal.Footer>
            </Modal.Content>
          </form>
        </Form>
      </Modal>
      <LoadingModal open={editProductLoading} />
      <ConfirmationModal
        displayIcon
        open={cancelEditModalOpen}
        onOpenChange={setCancelEditModalOpen}
        title={t('kgstudio.asset.products.cancel-modal-hint')}
        cancelState={{
          text: t('kgstudio.asset.products.cancel-modal-stay'),
        }}
        confirmState={{
          text: t('kgstudio.asset.products.cancel-modal-cancel'),
        }}
        onConfirm={() => {
          setCancelEditModalOpen(false);
          onOpenChange(false);
        }}
      />
    </>
  );
};

export { ProductModal };
