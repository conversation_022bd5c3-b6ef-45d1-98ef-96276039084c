'use client';

import { Info, Pencil, Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import { showToast } from '@/2b/toast';
import { RoleIdentifierBadge } from '@/app/[locale]/(protected)/setting/team/_components/RoleIdentifierBadge';
import { useDebounce, usePageHeader, usePermissions } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiOrganizationHooks } from '@/app/_common/services';
import { AssetProOperators } from '@/app/_common/services/organization/model';
import { useAuthStore } from '@/app/_common/store';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { Avatar, Card, DataTable, Input, Tooltip, useDataTable } from '@kryptogo/2b';
import { formatCurrency, DECIMAL_DISPLAY_MODE } from '@kryptogo/utils';
import { ColumnDef, TableOptions, getCoreRowModel, useReactTable } from '@tanstack/react-table';

import { EditTransferModal, EditTransferModalProps } from './_components';

export default function Operators() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.operators.title') });
  const searchParams = useSearchParams();
  const userInfo = useAuthStore((state) => state.userInfo);
  const orgId = useOrganizationStore((state) => state.orgId);
  const [hasEditPermission] = usePermissions(['asset_pro_operator', 'edit']);

  const [modalData, setModalData] = useState<Omit<EditTransferModalProps, 'onClose'>>({
    data: {
      uid: '',
      name: '',
      member_id: '',
      daily_transfer_limit: null,
      transfer_approval_threshold: 0,
    },
    open: false,
  });
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);

  const [tableOption, setTableOption] = useState<TableOptions<AssetProOperators['data'][number]>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size, page_sort } = useDataTable(table);

  const {
    data: operators,
    isLoading: operatorsLoading,
    refetch,
  } = apiOrganizationHooks.useGetAssetProOperators(
    {
      params: { org_id: Number(orgId) },
      queries: { page_number, page_size, page_sort, q: debouncedQuery },
    },
    {
      enabled: !!orgId,
      onError: (error) => {
        console.error('error', error);

        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  const columnAction: ColumnDef<AssetProOperators['data'][number]>[] = useMemo(
    () => [
      {
        id: 'actions',
        size: 70,
        header: t('common.action'),
        cell: ({ row }) => {
          const { uid, name, member_id, daily_transfer_limit, transfer_approval_threshold } = row.original;
          return (
            <div className="flex items-center justify-center">
              <Pencil
                data-cy={'edit-transfer-btn'}
                className="text-placeholder h-4 w-4 cursor-pointer"
                onClick={() => {
                  setModalData({
                    data: {
                      uid,
                      name,
                      member_id,
                      daily_transfer_limit,
                      transfer_approval_threshold,
                    },
                    open: true,
                  });
                }}
              />
            </div>
          );
        },
      },
    ],
    [t],
  );

  const columns: ColumnDef<AssetProOperators['data'][number]>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        size: 200,
        header: t('common.name'),
        cell: ({ row }) => {
          const { name, member_id } = row.original;
          return (
            <div className="flex items-center gap-3">
              <Avatar imageSrc={undefined} displayName={name} size="36" imageAlt="member_avatar" />
              <div>
                <p className="text-primary text-body-2-bold">{name}</p>
                {!!member_id && <span className="text-secondary text-small">{member_id}</span>}
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'email',
        header: t('common.email'),
        cell: ({ row }) => row.getValue('email'),
      },
      {
        accessorKey: 'roles',
        header: t('kgstudio.common.roles'),
        size: 200,
        cell: ({ row }) => {
          const { roles } = row.original;
          return (
            <div className="flex flex-wrap gap-2">
              {roles.map((role) => {
                return <RoleIdentifierBadge role={role !== 'owner' ? `asset_pro:${role}` : role} key={role} />;
              })}
            </div>
          );
        },
      },
      {
        accessorKey: 'daily_transfer_limit',
        meta: {
          sortable: true,
        },
        header: () => {
          return (
            <div className="flex w-[95%] items-center gap-2">
              <p className="text-body-2">{t('kgstudio.operators.daily-transfer-limit')}</p>
              <Tooltip>
                <Tooltip.Trigger>
                  <Info className="text-surface-primary h-3 w-3" fill="var(--text-placeholder)" />
                </Tooltip.Trigger>
                <Tooltip.Content
                  side="bottom"
                  className="text-small bg-surface-primary border-none p-2 shadow-[0px_4px_12px_rgba(0,0,0,0.1)]"
                >
                  <span className="text-primary text-small">{t('kgstudio.operators.transfer-limit-desc')}</span>
                  <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
                </Tooltip.Content>
              </Tooltip>
            </div>
          );
        },
        cell: ({ row }) => {
          const { daily_transfer_limit } = row.original;
          return daily_transfer_limit !== null ? (
            <p className="text-primary text-body-2-bold">
              {formatCurrency({
                amount: daily_transfer_limit,
                decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
                fmt: { prefix: '$' },
              })}
            </p>
          ) : (
            <p className="text-disabled text-body-2-bold">N/A</p>
          );
        },
      },
      {
        accessorKey: 'transfer_approval_threshold',
        header: () => (
          <div className="flex w-[95%] items-center gap-2">
            <p className="text-body-2">{t('kgstudio.operators.threshold-amount')}</p>
            <Tooltip>
              <Tooltip.Trigger>
                <Info className="text-surface-primary h-3 w-3" fill="var(--text-placeholder)" />
              </Tooltip.Trigger>
              <Tooltip.Content
                side="bottom"
                className="text-small bg-surface-primary border-none p-2 shadow-[0px_4px_12px_rgba(0,0,0,0.1)]"
              >
                <span className="text-primary text-small">
                  {t('kgstudio.operators.transfer-approval-threshold-desc')}
                </span>
                <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
              </Tooltip.Content>
            </Tooltip>
          </div>
        ),
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { transfer_approval_threshold } = row.original;
          return transfer_approval_threshold !== null ? (
            <p className="text-primary text-body-2-bold">
              {formatCurrency({
                amount: transfer_approval_threshold,
                decimals: DECIMAL_DISPLAY_MODE.ORDER_USD,
                fmt: { prefix: '$' },
              })}
            </p>
          ) : (
            <p className="text-disabled text-body-2-bold">N/A</p>
          );
        },
      },
      ...(hasEditPermission ? columnAction : []),
    ],
    [columnAction, hasEditPermission, t],
  );

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: operators?.data || [],
      columns,
    }));
  }, [columns, operators]);

  useEffect(() => {
    if (searchParams.get('edit') && userInfo) {
      const {
        uid,
        name,
        member_id,
        asset_pro: { daily_transfer_limit, transfer_approval_threshold },
      } = userInfo;
      setModalData({
        data: {
          uid,
          name,
          member_id,
          daily_transfer_limit,
          transfer_approval_threshold,
        },
        open: true,
      });
    }
  }, [searchParams, userInfo]);

  return (
    <>
      <EditTransferModal
        open={modalData.open}
        data={modalData.data}
        onClose={(success) => {
          if (success) refetch();
          setModalData({ ...modalData, open: false });
        }}
      />
      <main className="flex flex-col gap-6">
        <Card className="!p-0">
          <div className="flex flex-col justify-between gap-4 p-4 md:flex-row md:items-center md:gap-0 md:p-6">
            <h3 className="text-h3 text-primary font-bold" data-cy={'operator-members'}>
              AssetPro Operators ({operators?.paging?.total_count})
            </h3>
            <Input
              className="h-[45px] w-full md:w-[231px]"
              type="text"
              placeholder={t('kgstudio.operators.placeholder')}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
              value={query}
              suffix={<Search />}
            />
          </div>
          <DataTable
            table={table}
            isLoading={operatorsLoading}
            dataLength={operators?.paging.total_count || 0}
            onRowClick={
              !hasEditPermission
                ? undefined
                : (rowData) => {
                    const { uid, name, member_id, daily_transfer_limit, transfer_approval_threshold } = rowData;
                    setModalData({
                      data: {
                        uid,
                        name,
                        member_id,
                        daily_transfer_limit,
                        transfer_approval_threshold,
                      },
                      open: true,
                    });
                  }
            }
          />
        </Card>
      </main>
    </>
  );
}
