import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { amountInputInterceptor } from '@/app/[locale]/(protected)/asset/_lib/utils';
import { FormInput } from '@/app/_common/components/form';
import { isApiError } from '@/app/_common/lib/api';
import { apiOrganizationHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { Avatar, Button, Form, Modal } from '@kryptogo/2b';

export interface EditTransferModalProps {
  open: boolean;
  onClose: (success?: boolean) => void;
  data: {
    uid: string;
    name: string;
    member_id: string;
    daily_transfer_limit: number | null;
    transfer_approval_threshold: number;
  };
}

const EditTransferSchema = (t: any) =>
  z
    .object({
      daily_transfer_limit: z
        .string()
        .min(1)
        .refine((val) => Number(val) > 0),
      transfer_approval_threshold: z.string(),
    })
    .refine(
      (schema) => {
        return Number(schema.daily_transfer_limit) >= Number(schema.transfer_approval_threshold);
      },
      { message: t('kgstudio.operators.transfer-limit-error'), path: ['transfer_approval_threshold'] },
    );
export type EditTransfer = z.infer<ReturnType<typeof EditTransferSchema>>;

const EditTransferModal = ({ data, open, onClose }: EditTransferModalProps) => {
  const t = useTranslations();
  const orgId = useOrganizationStore((state) => state.orgId);

  const { mutate: editOperator, isLoading: editOperatorLoading } = apiOrganizationHooks.useEditOperatorTransfer(
    { params: { org_id: Number(orgId), uid: data.uid } },
    {
      onSuccess: () => {
        onClose(true);
        showToast(t('kgstudio.common.save-changes'), 'success');
      },
      onError: (error) => {
        console.error(error);

        if (isApiError(error)) {
          if (error.status === 400) {
            showToast(t('kgstudio.operators.transfer-limit-error'), 'error');
          } else {
            showToast(t('kgstudio.common.error'), 'error');
          }
        }
      },
      meta: {
        awaitInvalidates: ['getCurrentUserInfo'],
      },
    },
  );

  const form = useForm<EditTransfer>({
    values: {
      daily_transfer_limit: String(data.daily_transfer_limit ?? ''),
      transfer_approval_threshold: String(data.transfer_approval_threshold ?? ''),
    },
    mode: 'onChange',
    resolver: zodResolver(EditTransferSchema(t)),
  });

  const onSubmit = () => {
    const { daily_transfer_limit, transfer_approval_threshold } = form.getValues();

    editOperator({
      daily_transfer_limit: Number(daily_transfer_limit),
      transfer_approval_threshold: Number(transfer_approval_threshold),
    });
  };

  useEffect(() => {
    if (!open) form.reset();
  }, [form, open]);

  return (
    <Modal
      data-cy={'edit-transfer-limit-modal'}
      open={open}
      onOpenChange={(open: boolean) => {
        if (!open) onClose();
      }}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Modal.Content className="scrollbar-hide max-h-[80%] overflow-auto" data-cy="edit-transfer-modal-content">
            <Modal.Header>
              <Modal.Title>{t('kgstudio.operators.edit-operator')}</Modal.Title>
            </Modal.Header>
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Avatar imageSrc={undefined} displayName={data.name} size="36" />
                <div>
                  <p className="text-primary text-body-2-bold">{data.name}</p>
                  {!!data.member_id && <span className="text-secondary text-small">{data.member_id}</span>}
                </div>
              </div>
              <div className="relative flex flex-col gap-2">
                <FormInput
                  title={t('kgstudio.operators.daily-transfer-limit')}
                  placeholder="e.g. 50000"
                  name="daily_transfer_limit"
                  control={form.control}
                  required
                  withoutMessage={true}
                  onInput={(e) => amountInputInterceptor(e, 2)}
                  data-cy="daily-transfer-limit-input"
                  hint={t('kgstudio.operators.transfer-limit-desc')}
                  suffix="USD"
                />
              </div>
              <div className="relative flex flex-col gap-2">
                <FormInput
                  title={t('kgstudio.operators.transfer-approval-threshold')}
                  placeholder="e.g. 10000"
                  name="transfer_approval_threshold"
                  control={form.control}
                  onInput={(e) => amountInputInterceptor(e, 2)}
                  data-cy="transfer-approval-threshold-input"
                  hint={t('kgstudio.operators.approval-threshold-desc')}
                  suffix="USD"
                />
              </div>
            </div>
            <Modal.Footer>
              <div className="flex w-full items-center justify-between">
                <Button type="button" variant="grey" onClick={() => onClose()}>
                  {t('kgstudio.common.cancel')}
                </Button>
                <Button
                  className="w-[200px]"
                  onClick={form.handleSubmit(onSubmit)}
                  data-cy="edit-transfer-limit-confirm"
                  loading={editOperatorLoading}
                  disabled={!form.formState.isValid}
                >
                  {t('common.update')}
                </Button>
              </div>
            </Modal.Footer>
          </Modal.Content>
        </form>
      </Form>
    </Modal>
  );
};

export { EditTransferModal };
