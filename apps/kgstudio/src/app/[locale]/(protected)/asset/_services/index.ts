import { omit } from 'lodash-es';

import axios from '@/app/_common/lib/axios/instances/internal';
import { createError } from '@/app/_common/lib/error';
import { UseMutationOptionType, UseQueryOptionType } from '@/app/_common/lib/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';

import { TokenTransferRequest, TransferHistoriesResponse, TransferTokenResponse, UsersResponse } from '../_types';

export type getOrganizationCustomerQuery = { id: string; phone_number: string } | { id: string; email: string };

const transferTokenError = createError('TransferTokenError', 'Insufficient balance');
type TransferTokenError = ReturnType<typeof transferTokenError>;

const transferToken = async (data: TokenTransferRequest): Promise<TransferTokenResponse> => {
  try {
    return (
      await axios.post<TransferTokenResponse>(
        `/studio/organization/${data.org_id}/asset_pro/transfer`,
        omit(data, ['org_id']),
      )
    ).data;
  } catch (error) {
    throw transferTokenError(error);
  }
};

export const useTransferToken = (
  options?: UseMutationOptionType<TokenTransferRequest, TransferTokenResponse, TransferTokenError>,
) => useMutation(transferToken, options);

const getUsers = (params: string) => async (): Promise<UsersResponse> =>
  (await axios.get<UsersResponse>(`/studio/permissions?${params}`)).data;

export const useUsers = (params: string, options?: UseQueryOptionType<UsersResponse>) =>
  useQuery(['users', params], getUsers(params), options);

// only enable KG organization for now. Should be changed after permission system is ready
const getTransferHistories = (params: string) => async (): Promise<TransferHistoriesResponse> =>
  (await axios.get<TransferHistoriesResponse>(`/studio/organization/1/asset_pro/transfer/histories?${params}`)).data;
export const useTransferHistories = (params: string, options?: UseQueryOptionType<TransferHistoriesResponse>) =>
  useQuery(['histories', params], getTransferHistories(params), options);
