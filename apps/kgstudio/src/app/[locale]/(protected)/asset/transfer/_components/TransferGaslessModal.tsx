'use client';

import { useTranslations } from 'next-intl';

import { <PERSON>dal, Button, RadioGroup, type RadioGroupItem } from '@kryptogo/2b';

interface GaslessOption extends RadioGroupItem {
  sendAmount: string;
  receiveAmount: string;
  receiveToken: string;
  disabled?: boolean;
}

interface TransferGaslessModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  options: GaslessOption[];
  selected: 'deducted' | 'full';
  onSelect: (value: 'deducted' | 'full') => void;
  gasFee: string;
  onConfirm: () => void;
}

const TransferGaslessModal = ({
  open,
  onOpenChange,
  options,
  selected,
  onSelect,
  gasFee,
  onConfirm,
}: TransferGaslessModalProps) => {
  const t = useTranslations();

  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>{t('kgstudio.send.gasless-modal-title')}</Modal.Title>
        </Modal.Header>
        <div className="text-body text-secondary mb-4">{t('kgstudio.send.gasless-modal-desc', { gasFee })} ETH</div>
        <RadioGroup value={selected} onValueChange={onSelect} variant="vertical" className="my-4 gap-6">
          {options.map((opt) => (
            <RadioGroup.Item
              key={opt.value}
              disabled={opt.disabled}
              className={opt.disabled ? 'bg-disabled' : ''}
              item={{
                label: (
                  <div className="flex flex-col">
                    <span className="text-h3 font-bold">{opt.label}</span>
                    <span className="text-body font-normal">
                      {t('kgstudio.send.gasless-modal-deducted-desc-1')}{' '}
                      <span className="text-body-bold">
                        {opt.sendAmount} {opt.receiveToken}
                      </span>
                    </span>
                    <span className="text-body font-normal">
                      {t('kgstudio.send.gasless-modal-deducted-desc-2')}{' '}
                      <span className="text-body-bold">
                        {opt.receiveAmount} {opt.receiveToken}
                      </span>
                    </span>
                  </div>
                ),
                value: opt.value,
                desc: opt.desc,
              }}
              variant="card"
              id={opt.value}
            />
          ))}
        </RadioGroup>
        <Modal.Footer className="mt-6 flex justify-end">
          <Button size="lg" className="w-[120px]" onClick={onConfirm}>
            {t('common.confirm')}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export { TransferGaslessModal };
