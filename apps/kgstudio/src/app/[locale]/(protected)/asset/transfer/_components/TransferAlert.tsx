/* eslint-disable react/display-name */
import BigNumber from 'bignumber.js';
import { useLocale, useTranslations } from 'next-intl';
import React, { PropsWithChildren, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { P, match } from 'ts-pattern';

import { usePermissions } from '@/app/_common/hooks';
import { readOnlyArrayIncludes } from '@/app/_common/lib/utils';
import { UserInfo } from '@/app/_common/services/organization/model';
import { ChecklistBlock } from '@kryptogo/2b';
import { ASSETPRO_SUPPORTED_TRON_CHAINS, formatCurrency } from '@kryptogo/utils';

import { EditTransferModal } from '../../operators/_components';
import TopupModal from '../../treasury/_component/TopupModal';

const GAS_BUFFER = 1.5;

export interface TransferAlertProps {
  userInfo?: UserInfo | null;
  transferData?: {
    nativeTokenSymbol?: string;
    selectedTokenLabel?: string;
  };
  checkData?: {
    tokenBalanceLoading?: boolean;
    formattedShortfallAmount?: string | undefined;
    isBalanceEnough?: boolean;
    nativeShortfallAmount?: BigNumber | undefined;
    nativeTokenBalanceLoading?: boolean;
    formattedNativeTokenShortfallAmount?: string | undefined;
    isFeeEnough?: boolean;
  };

  isAboveApprovalThreshold?: boolean;
}
const TransferAlertContext = React.createContext<TransferAlertProps | null>(null);
const useTransferAlertContext = () => {
  const context = React.useContext(TransferAlertContext);

  if (!context) {
    throw new Error('TransferAlert compound components cannot be rendered outside the TrasferAlert component');
  }

  return context;
};

const TransferAlert = ({
  children,
  userInfo,
  transferData,
  checkData,
  isAboveApprovalThreshold,
}: PropsWithChildren<TransferAlertProps>) => {
  return (
    <div className="space-y-2">
      <TransferAlertContext.Provider
        value={{
          userInfo,
          transferData,
          checkData,
          isAboveApprovalThreshold,
        }}
      >
        {children}
      </TransferAlertContext.Provider>
    </div>
  );
};

const TokenGasBalanceCheck = () => {
  const t = useTranslations();
  const [qrCodeModal, setQrCodeModal] = useState({
    open: false,
    chainType: 'evm',
  });

  const [hasEditOperatorPermission, hasReadAssetPoolPermission] = usePermissions(
    ['asset_pro_operator', 'edit'],
    ['asset_pool', 'read'],
  );
  const { transferData, checkData, userInfo } = useTransferAlertContext();

  const { getValues } = useFormContext();
  const [amount, blockchain, token] = getValues(['amount', 'blockchain', 'token']);
  const isNativeTokenSelected = token === '';
  const isTronLikeChain = readOnlyArrayIncludes(ASSETPRO_SUPPORTED_TRON_CHAINS, blockchain);

  const nativeShortfallAmountBN = checkData?.nativeShortfallAmount ?? 0;
  const bufferedNativeShortfallAmount = formatCurrency({
    amount: isNativeTokenSelected ? nativeShortfallAmountBN : BigNumber(nativeShortfallAmountBN).times(GAS_BUFFER),
  });

  const handleModalOpen = (chainType: 'tron' | 'evm') => {
    setQrCodeModal({ open: true, chainType });
  };

  if (
    (checkData?.isBalanceEnough && checkData?.isFeeEnough) ||
    checkData?.nativeTokenBalanceLoading ||
    checkData?.tokenBalanceLoading ||
    amount === ''
  ) {
    return null;
  }

  return (
    <>
      {hasReadAssetPoolPermission && <TopupModal qrCodeModal={qrCodeModal} setQrCodeModal={setQrCodeModal} />}

      {match([userInfo?.asset_pro.daily_transfer_limit, checkData?.isFeeEnough, checkData?.isBalanceEnough])
        .with([P.number, false, false], () => (
          <ChecklistBlock
            variant="error"
            title={t('kgstudio.asset.token-and-gas-insufficient')}
            description={
              <div>
                {t('kgstudio.asset.amount-invalid-description-1')}{' '}
                {!isNativeTokenSelected && (
                  <>
                    <span className="font-bold">
                      {checkData?.formattedShortfallAmount ?? '?'} {transferData?.selectedTokenLabel}
                    </span>{' '}
                    {t('kgstudio.asset.amount-invalid-description-2')}{' '}
                  </>
                )}
                <span className="font-bold">
                  {bufferedNativeShortfallAmount} {transferData?.nativeTokenSymbol}
                </span>{' '}
                {t('kgstudio.asset.amount-invalid-description-3')}
              </div>
            }
            size="sm"
            {...(hasReadAssetPoolPermission && {
              buttonText: t('kgstudio.asset.deposit-now'),
              onButtonClick: () => handleModalOpen(isTronLikeChain ? 'tron' : 'evm'),
            })}
            data-cy="token-gas-balance-check"
          />
        ))
        .with([P.number, false, true], () => (
          <ChecklistBlock
            variant="error"
            title={t('kgstudio.asset.gas-insufficient')}
            description={
              <div>
                {t('kgstudio.asset.amount-invalid-description-1')}{' '}
                <span className="font-bold">
                  {bufferedNativeShortfallAmount} {transferData?.nativeTokenSymbol}
                </span>{' '}
                {t('kgstudio.asset.amount-invalid-description-3')}
              </div>
            }
            size="sm"
            {...(hasEditOperatorPermission && {
              buttonText: t('kgstudio.asset.deposit-now'),
              onButtonClick: () => handleModalOpen(isTronLikeChain ? 'tron' : 'evm'),
            })}
            data-cy="token-gas-balance-check"
          />
        ))
        .with([P.number, true, false], () => (
          <ChecklistBlock
            variant="error"
            title={t('kgstudio.asset.token-insufficient')}
            description={
              <div>
                {t('kgstudio.asset.amount-invalid-description-1')}{' '}
                <span className="font-bold">
                  {isNativeTokenSelected
                    ? `${bufferedNativeShortfallAmount ?? '?'} ${transferData?.nativeTokenSymbol}`
                    : `${checkData?.formattedShortfallAmount ?? '?'} ${transferData?.selectedTokenLabel}`}
                </span>{' '}
                {t('kgstudio.asset.amount-invalid-description-3')}
              </div>
            }
            size="sm"
            {...(hasEditOperatorPermission && {
              buttonText: t('kgstudio.asset.deposit-now'),
              onButtonClick: () => handleModalOpen(isTronLikeChain ? 'tron' : 'evm'),
            })}
            data-cy="token-gas-balance-check"
          />
        ))
        .otherwise(() => null)}
    </>
  );
};

const LimitCheck = ({ tokenPriceUSD }: { tokenPriceUSD?: BigNumber }) => {
  const t = useTranslations();
  const locale = useLocale();
  const [operatorModalOpen, setOperatorModalOpen] = useState(false);

  const { userInfo } = useTransferAlertContext();
  const { getValues } = useFormContext();
  const amount = getValues('amount');

  const remainLimit = userInfo?.asset_pro.remain_limit?.toLocaleString(locale);
  const dailyTransferLimit = userInfo?.asset_pro.daily_transfer_limit?.toLocaleString(locale);
  const [hasEditOperatorPermission] = usePermissions(['asset_pro_operator', 'edit']);

  const isRemainingLimitInsufficient =
    remainLimit === '0' ||
    BigNumber(amount)
      .multipliedBy(tokenPriceUSD ?? 1)
      .isGreaterThan(BigNumber(userInfo?.asset_pro?.remain_limit ?? 0));

  const memoizedResult = useMemo(
    () =>
      match([remainLimit, dailyTransferLimit, amount, isRemainingLimitInsufficient])
        .with([undefined, undefined, P._, P._], () => (
          <ChecklistBlock
            variant="checking"
            description={t('kgstudio.asset.remain-limit-checking')}
            size="sm"
            data-cy="limit-check"
          />
        ))
        .with([P._, P._, '', P._], () => (
          <ChecklistBlock
            variant="default"
            description={t('kgstudio.asset.remain-limit-info', {
              remainLimit: remainLimit || '',
              dailyTransferLimit: dailyTransferLimit || '',
            })}
            size="sm"
            data-cy="limit-check"
          />
        ))
        .with(['0', P._, '', true], () => (
          <ChecklistBlock
            variant="error"
            title={t('kgstudio.asset.remain-limit-info', {
              remainLimit: remainLimit || '',
              dailyTransferLimit: dailyTransferLimit || '',
            })}
            description={t('kgstudio.asset.remain-limit-info-invalid')}
            size="sm"
            {...(hasEditOperatorPermission && {
              buttonText: t('kgstudio.asset.edit-now'),
              onButtonClick: () => setOperatorModalOpen(true),
            })}
            data-cy="limit-check"
          />
        ))
        .with([P._, P._, P.not(''), false], () => (
          <ChecklistBlock
            variant="success"
            description={t('kgstudio.asset.remain-limit-info', {
              remainLimit: remainLimit || '',
              dailyTransferLimit: dailyTransferLimit || '',
            })}
            size="sm"
            data-cy="limit-check"
          />
        ))
        .with([P._, P._, P.not(''), true], () => (
          <ChecklistBlock
            variant="error"
            title={t('kgstudio.asset.remain-limit-info', {
              remainLimit: remainLimit || '',
              dailyTransferLimit: dailyTransferLimit || '',
            })}
            description={t('kgstudio.asset.remain-limit-invalid-hint')}
            size="sm"
            {...(hasEditOperatorPermission && {
              buttonText: t('kgstudio.asset.edit-now'),
              onButtonClick: () => setOperatorModalOpen(true),
            })}
            data-cy="limit-check"
          />
        ))
        .otherwise(() => null),
    [amount, dailyTransferLimit, hasEditOperatorPermission, isRemainingLimitInsufficient, remainLimit, t],
  );

  return (
    <>
      {hasEditOperatorPermission && (
        <EditTransferModal
          open={operatorModalOpen}
          data={{
            uid: userInfo?.uid || '',
            name: userInfo?.name || '',
            member_id: userInfo?.member_id || '',
            daily_transfer_limit: userInfo?.asset_pro?.daily_transfer_limit || null,
            transfer_approval_threshold: userInfo?.asset_pro?.transfer_approval_threshold || 0,
          }}
          onClose={() => {
            setOperatorModalOpen(false);
          }}
        />
      )}
      {memoizedResult}
    </>
  );
};

const ApprovalThresholdCheck = () => {
  const t = useTranslations();
  const [operatorModalOpen, setOperatorModalOpen] = useState(false);
  const { trigger } = useFormContext();

  const { isAboveApprovalThreshold, userInfo } = useTransferAlertContext();
  const [hasEditOperatorPermission] = usePermissions(['asset_pro_operator', 'edit']);

  const transferApprovalThreshold = userInfo?.asset_pro.transfer_approval_threshold;
  const memoizedResult = useMemo(
    () =>
      match([transferApprovalThreshold, isAboveApprovalThreshold])
        .with([undefined, P._], () => null)
        .with([P._, true], () => (
          <ChecklistBlock
            variant="warning"
            title={t('kgstudio.asset.threshold-info', { transferApprovalThreshold: transferApprovalThreshold || 0 })}
            description={t('kgstudio.asset.note-attachments-required')}
            {...(hasEditOperatorPermission && {
              buttonText: t('kgstudio.asset.edit-now'),

              onButtonClick: () => setOperatorModalOpen(true),
            })}
            size="sm"
            data-cy="approval-threshold-check"
          />
        ))
        .otherwise(() => null),
    [hasEditOperatorPermission, isAboveApprovalThreshold, t, transferApprovalThreshold],
  );

  return (
    <>
      {hasEditOperatorPermission && isAboveApprovalThreshold && (
        <EditTransferModal
          open={operatorModalOpen}
          data={{
            uid: userInfo?.uid || '',
            name: userInfo?.name || '',
            member_id: userInfo?.member_id || '',
            daily_transfer_limit: userInfo?.asset_pro?.daily_transfer_limit || null,
            transfer_approval_threshold: userInfo?.asset_pro?.transfer_approval_threshold || 0,
          }}
          onClose={(success) => {
            if (success) {
              trigger(['note', 'attachments']);
            }
            setOperatorModalOpen(false);
          }}
        />
      )}
      {memoizedResult}
    </>
  );
};

const TransferAlertNamespace = Object.assign(TransferAlert, {
  TokenGasBalanceCheck,
  LimitCheck,
  ApprovalThresholdCheck,
});

export { TransferAlertNamespace as TransferAlert };
