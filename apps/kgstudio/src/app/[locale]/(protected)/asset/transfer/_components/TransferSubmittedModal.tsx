'use client';

import { CheckCircle2, XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { match, P } from 'ts-pattern';

import { cn } from '@/app/_common/lib/utils';
import { TransferToken } from '@/app/_common/services/asset-pro/model';
import { Button, Modal } from '@kryptogo/2b';

interface TransferSubmittedModalProps {
  transferTokenState: {
    data?: TransferToken;
    error?: any;
  };
  approvalRequired: boolean;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const TransferSubmittedModal = ({
  transferTokenState,
  approvalRequired,
  open,
  onOpenChange,
}: TransferSubmittedModalProps) => {
  const router = useRouter();
  const t = useTranslations();
  const { data, error } = transferTokenState;

  const unExpetedError = !!error && error.code !== 4015;

  const MODAL_INFO = {
    icon: !!data ? CheckCircle2 : XCircle,
    title: match([!!data, approvalRequired])
      .with([true, true], () => t('kgstudio.send.transaction-submit-success-title'))
      .with([true, false], () => t('kgstudio.send.transaction-success-title'))
      .with([false, P.any], () => t('kgstudio.send.transaction-error-title'))
      .exhaustive(),
    description: match([!!data, approvalRequired])
      .with([true, true], () => t('kgstudio.send.transaction-submit-success-desc'))
      .with([true, false], () => t('kgstudio.send.transaction-success-desc'))
      .with([false, P.any], () => t('kgstudio.send.transaction-error-desc'))
      .exhaustive(),
  };

  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <Modal.Content className="w-modal-medium">
        <div className="gap-item-medium flex size-full flex-col items-center justify-center">
          <MODAL_INFO.icon
            className={cn('stroke-surface-primary size-[36px]', {
              'fill-success': !!data,
              'fill-error': !data,
            })}
          />
          {unExpetedError ? (
            <h2 className="text-primary text-h2" data-cy="transfer-submitted-failed-text">
              {t('kgstudio.send.tx-failed')}
            </h2>
          ) : (
            <div className="gap-item-medium flex flex-col items-center" data-cy="transfer-submitted-text">
              <h2 className="text-primary text-h2">{MODAL_INFO.title}</h2>
              <p className="text-secondary text-body text-center">{MODAL_INFO.description}</p>
            </div>
          )}
          <Modal.Footer className="mt-14 flex items-center justify-center gap-3">
            <Button
              variant="grey"
              size="lg"
              onClick={() => {
                onOpenChange(false);
              }}
            >
              {t('kgstudio.common.close')}
            </Button>
            {!!data && (
              <Button
                variant="secondary"
                size="lg"
                onClick={() => {
                  router.push(`/asset/transactions/${data.id}`);
                }}
              >
                {t('kgstudio.nft.go-to-project')}
              </Button>
            )}
          </Modal.Footer>
        </div>
      </Modal.Content>
    </Modal>
  );
};

export { TransferSubmittedModal };
