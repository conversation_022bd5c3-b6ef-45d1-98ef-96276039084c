import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { ImageListType } from 'react-images-uploading';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import {
  useAddressValidation,
  useEmailValidation,
  useNativeTokenBalanceAndFee,
  usePhoneNumberValidation,
  useTokenBalance,
  useTokenSelection,
} from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { countries } from '@/app/_common/lib/countries';
import { apiAssetProHooks } from '@/app/_common/services';
import { AssetProChainIdSchema, type AssetProChainId } from '@/app/_common/services/asset-pro/model';
import { ValidationResult } from '@/app/_common/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { ChainId } from '@kryptogo/utils';

const ARB_USDT_ADDRESS = '0xFd086bC7CD5C481DCC9C85ebE478A1C0b69FCbb9';

interface UseTransferProps {
  transferApprovalThreshold: number;
  attachments: ImageListType;
}

export function useTransfer({ transferApprovalThreshold, attachments }: UseTransferProps) {
  const t = useTranslations();

  const TransferFormSchema = z
    .object({
      amount: z.string().refine((amount) => {
        if (amount === '') return true;
        if (Number(amount) <= 0) return false;
        if (token === '') {
          return !nativeTokenBalanceLoading && isFeeEnough;
        } else {
          return !tokenBalanceLoading && !nativeTokenBalanceLoading && isBalanceEnough && isFeeEnough;
        }
      }),
      blockchain: AssetProChainIdSchema,
      token: z.string(),
      sendType: z.enum(['email', 'phone', 'address']),
      countryCode: z.string().min(1, {
        message: t('kgstudio.validation.required'),
      }),
      phone: z.string(),
      email: z.string(),
      address: z.string(),
      note: z.string().optional(),
      attachments: z.array(z.string()).optional(),
    })
    .superRefine((data, ctx) => {
      if (data.sendType === 'email') {
        validateField(data.email, validateEmail, ctx, ['email']);
      }
      if (data.sendType === 'phone') {
        validateField(data.phone, validatePhone, ctx, ['phone']);
      }
      if (data.sendType === 'address') {
        validateField(data.address, validateAddress, ctx, ['address']);
      }
      if (Number(data.amount) > Number(transferApprovalThreshold)) {
        if (!data.note) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('kgstudio.send.note-required'),
            path: ['note'],
          });
        }
        if (!attachments?.length) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t('kgstudio.send.attachment-required'),
            path: ['attachments'],
          });
        }
      }
    });

  const form = useForm({
    defaultValues: {
      amount: '',
      blockchain: 'arb',
      token: ARB_USDT_ADDRESS,
      sendType: 'email',
      countryCode: countries[0].phoneCode,
      phone: '',
      email: '',
      address: '',
      note: '',
      attachments: [],
    },
    values: {
      amount: '',
      countryCode: countries[0].phoneCode,
      phone: '',
      email: '',
      address: '',
      note: '',
      attachments: [],
      blockchain: 'arb',
      token: ARB_USDT_ADDRESS,
    },
    mode: 'all',
    resolver: zodResolver(TransferFormSchema),
  });

  const [amount, blockchain, token, countryCode, sendType] = useWatch({
    control: form.control,
    name: ['amount', 'blockchain', 'token', 'countryCode', 'sendType'],
  });

  useEffect(() => {
    form.trigger('amount');
  }, [amount, blockchain, token, form]);

  const validateEmail = useEmailValidation();
  const validatePhone = usePhoneNumberValidation(countryCode ?? '');
  const validateAddress = useAddressValidation(blockchain as ChainId);
  const validateField = (value: string, validateFn: (value: string) => ValidationResult, ctx: any, path: string[]) => {
    const result = validateFn(value);
    if (!result.isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: result.errorMessage,
        path,
      });
    }
  };

  const { data: tokenListData, error: tokenListError } = apiAssetProHooks.useGetTokenList(
    {},
    {
      onError: (error) => {
        console.error(error);
        if (isApiError(error)) {
          showToast(t('kgstudio.common.error'), 'error');
        }
      },
    },
  );

  const { blockchainOptions, tokenOptions, selectedBlockchain, selectedToken } = useTokenSelection(
    tokenListData,
    blockchain as ChainId,
    token,
    (c) => form.setValue('blockchain', c as ChainId),
    (t) => form.setValue('token', t),
  );

  const {
    tokenBalance,
    formattedTokenBalance,
    isBalanceEnough,
    isLoading: tokenBalanceLoading,
    formattedShortfallAmount,
  } = useTokenBalance({
    token: {
      chainId: blockchain as AssetProChainId,
      contractAddress: token,
      symbol: selectedToken.symbol,
      decimals:
        tokenListData?.data?.find(
          (t) => t.chain_id === selectedBlockchain.value && t.contract_address === selectedToken.value,
        )?.decimals ?? 18,
    },
    txTokenAmount: amount,
  });

  const {
    nativeTokenBalance,
    formattedNativeTokenBalance,
    isFeeEnough,
    gasFee,
    formattedFee,
    nativeTokenSymbol,
    isLoading: nativeTokenBalanceLoading,
    nativeShortfallAmount,
  } = useNativeTokenBalanceAndFee({
    chainId: blockchain as AssetProChainId,
    contractAddress: token,
    tokenBalance,
    isGasless: ['base', 'optimism', 'arb'].includes(blockchain),
  });

  const formDisabled = tokenListError !== null || !tokenListData;

  useEffect(() => {
    match(sendType)
      .with('email', () => {
        form.resetField('phone');
        form.resetField('address');
      })
      .with('phone', () => {
        form.resetField('email');
        form.resetField('address');
      })
      .with('address', () => {
        form.resetField('phone');
        form.resetField('address');
      });
  }, [form, sendType]);

  return {
    form,
    formDisabled,
    blockchainOptions,
    tokenOptions,
    selectedToken,
    selectedBlockchain,
    formattedTokenBalance,
    formattedShortfallAmount,
    nativeTokenBalance,
    formattedNativeTokenBalance,
    isFeeEnough,
    gasFee,
    formattedFee,
    nativeTokenSymbol,
    tokenBalanceLoading,
    nativeTokenBalanceLoading,
    nativeShortfallAmount,
    isBalanceEnough,
  };
}
