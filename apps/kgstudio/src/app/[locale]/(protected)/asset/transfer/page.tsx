'use client';

import BigNumber from 'bignumber.js';
import { find } from 'lodash-es';
import { ArrowUpRightFromSquare, Copy, ExternalLink, Plus, Scan } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import React, { useCallback, useEffect, useState } from 'react';
import ImageUploader, { ImageListType } from 'react-images-uploading';
import { P, match } from 'ts-pattern';

import { showToast } from '@/2b/toast';
import { AttachmentCard, ImageViewer } from '@/app/[locale]/(protected)/asset/_components';
import { LoadingModal } from '@/app/_common/components';
import { KycStatusBadge } from '@/app/_common/components/badge';
import { FormDropdown, FormInput, FormInputError, FormRadioGroup, FormTextarea } from '@/app/_common/components/form';
import { Media, useDeviceSize, useGcpFileUpload, usePageHeader, useTokenPrice } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { countries } from '@/app/_common/lib/countries';
import { handleCopyAddress } from '@/app/_common/lib/utils';
import { apiAssetProHooks, apiOrganizationHooks, proxyHooks } from '@/app/_common/services';
import { ProxyKyaResponse } from '@/app/_common/services/proxy/model';
import { useAuthStore, useOrganizationStore } from '@/app/_common/store';
import { useRouter } from '@/i18n/navigation';
import {
  Avatar,
  Button,
  Card,
  Checklist,
  ChecklistBlock,
  Form,
  FormDescription,
  FormLabel,
  InputError,
  Label,
  Modal,
  ReminderBlock,
  Separator,
  Spinner,
  Tooltip,
} from '@kryptogo/2b';
import {
  ChainId,
  DECIMAL_DISPLAY_MODE,
  formatCurrency,
  getChainFullName,
  getChainIcon,
  getCrystalCurrency,
  getExplorerUrl,
  truncateTxhashOrAddress,
} from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';

import { KyaRiskReminderAndModal, KycStatusNotification } from '../_components';
import { amountInputInterceptor } from '../_lib/utils';
import { KycStatus } from '../_types';
import { TransferAlert, TransferGaslessModal, TransferSubmittedModal } from './_components';
import { useTransfer } from './_hooks/useTransfer';

const GAS_BUFFER = 1.5;

export default function Transfer() {
  const t = useTranslations();
  const router = useRouter();
  const queryClient = useQueryClient();
  usePageHeader({ title: t('kgstudio.common.transfer') });

  const orgId = useOrganizationStore((state) => state.orgId);
  const userInfo = useAuthStore((state) => state.userInfo);
  const transferApprovalThreshold = userInfo?.asset_pro.transfer_approval_threshold ?? 0;
  const { deviceSize } = useDeviceSize();
  const [isClient, setIsClient] = useState(false);
  const [attachments, setAttachments] = useState<ImageListType>(() => []);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [openSubmittedModal, setOpenSubmittedModal] = useState(false);
  const [isGaslessModalOpen, setIsGaslessModalOpen] = useState(false);
  const [gaslessOption, setGaslessOption] = useState<'deducted' | 'full'>('deducted');

  const {
    form,
    formDisabled,
    blockchainOptions,
    tokenOptions,
    selectedToken,
    selectedBlockchain,
    formattedTokenBalance,
    formattedShortfallAmount,
    nativeTokenBalance,
    formattedNativeTokenBalance,
    isFeeEnough,
    gasFee,
    formattedFee,
    tokenBalanceLoading,
    nativeTokenSymbol,
    nativeTokenBalanceLoading,
    nativeShortfallAmount,
    isBalanceEnough,
  } = useTransfer({
    transferApprovalThreshold: transferApprovalThreshold || 0,
    attachments,
  });

  const { amount, blockchain, token, sendType, countryCode, phone, email, address } = form.getValues();

  const isNativeTokenSelected = token === '';

  const { tokenPriceUSD } = useTokenPrice(token, blockchain);
  const { tokenPriceUSD: gasTokenPriceUSD } = useTokenPrice('', 'arb');

  const requiredNoteAndAttachment = BigNumber(amount)
    .multipliedBy(tokenPriceUSD ?? 1)
    .isGreaterThan(BigNumber(transferApprovalThreshold ?? 0));

  const { isError: fileUrlsError, isLoading: fileUrlsLoading, uploadAllFiles } = useGcpFileUpload();

  const {
    refetch: getCustomerData,
    data: customerData,
    isFetching: customerFetching,
    error: customerError,
    remove: removeCustomer,
  } = apiAssetProHooks.useGetOrganizationCustomer(
    {
      params: { org_id: orgId ?? -1 },
      queries: { ...(sendType == 'email' ? { email } : { phone_number: `+${countryCode}${phone}` }) },
    },
    {
      enabled: false,
      retry: false,
    },
  );

  const userNotFound = isApiError(customerError) && customerError?.code === 2100;
  const customerNotFound = isApiError(customerError) && customerError?.code === 7016;

  const {
    mutate: getKyaProfile,
    data: kyaProfileData,
    error: kyaProfileError,
    isLoading: kyaProfileLoading,
    reset: resetKyaProfile,
  } = proxyHooks.useProxy();

  // When user sendType is "phone" or "email", match the selected transfer blockchain with user's wallet address.
  const selectedChainWalletAddress =
    customerData && find(customerData.data?.wallets, { chain_id: blockchain })?.address;
  const customerAddress = sendType === 'address' ? address : selectedChainWalletAddress;

  const performRiskScan = (sendType: 'email' | 'phone' | 'address') =>
    match(sendType)
      .with('email', 'phone', () => {
        getCustomerData();
      })
      .with('address', () => {
        getKyaProfile({
          body: {
            address: address || (customerAddress as string),
            currency:
              getCrystalCurrency(blockchain as ChainId) === 'optimism'
                ? 'op'
                : (getCrystalCurrency(blockchain as ChainId) as any),
          },
        });
      })
      .exhaustive();

  const resetRiskScan = useCallback(() => {
    removeCustomer();
    resetKyaProfile();
  }, [removeCustomer, resetKyaProfile]);

  const {
    mutate: transferToken,
    data: transferTokenResp,
    isLoading: transferTokenLoading,
    error: transferTokenError,
  } = apiAssetProHooks.useTransferToken(
    { params: { org_id: Number(orgId) }, retry: (_, error) => isApiError(error) && error.status !== 500 },
    {
      meta: {
        invalidates: ['getCurrentUserInfo'],
      },
    },
  );

  const getKyaButtonEnabled = () => {
    const inValid = match(form.getValues('sendType'))
      .with('email', () => !!form.formState.errors.email || form.getValues('email') === '')
      .with('phone', () => !!form.formState.errors.phone || form.getValues('phone') === '')
      .with('address', () => !!form.formState.errors.address || form.getValues('address') === '')
      .otherwise(() => false);

    return inValid || !!customerData || !!kyaProfileData;
  };

  useEffect(() => {
    queryClient.invalidateQueries({
      queryKey: apiOrganizationHooks.getKeyByAlias('getCurrentUserInfo'),
    });
  }, [queryClient]);

  useEffect(() => {
    resetRiskScan();
  }, [sendType, resetRiskScan]);

  useEffect(() => {
    if (!transferTokenResp) return;

    setIsDialogOpen(false);
    setOpenSubmittedModal(true);
  }, [transferTokenResp]);

  useEffect(() => {
    if (!customerError) return;

    console.error(customerError);
    if (isApiError(customerError) && customerError.code !== 2100 && customerError.code !== 7016) {
      showToast(t('kgstudio.common.error'), 'error');
    }
  }, [customerError, t]);

  useEffect(() => {
    if (!kyaProfileError) return;

    console.error('kyaProfileError', kyaProfileError);
    showToast(t('kgstudio.asset.kya-status.error'), 'error');
  }, [kyaProfileError, t]);

  useEffect(() => {
    if (!transferTokenError) return;
    setIsDialogOpen(false);
    setOpenSubmittedModal(true);

    console.error(transferTokenError);
    if (isApiError(transferTokenError) && [1013, 4015, 6003].includes(transferTokenError.code)) {
      showToast(t('kgstudio.error.insufficient-balance'), 'error');
    } else {
      showToast(t('kgstudio.error.try-again'), 'error');
    }
  }, [t, transferTokenError]);

  const getAmountInputInfo = (isNativeToken: boolean) => {
    const isLoading = isNativeToken ? nativeTokenBalanceLoading : tokenBalanceLoading;
    const balance = isNativeToken ? formattedNativeTokenBalance : formattedTokenBalance;
    const isTokenBalanceEnough = isNativeTokenSelected
      ? nativeTokenBalance?.minus(amount).isGreaterThan(0)
      : isBalanceEnough;

    return match([isLoading, amount, isTokenBalanceEnough, balance])
      .with([true, P._, P._, P._], () => (
        <Checklist variant="checking" description={t('kgstudio.asset.balance-checking')} reverse size="sm" />
      ))
      .with([false, P._, true, P._], () => (
        <Checklist
          variant="success"
          description={t('kgstudio.asset.balance-value', { formattedTokenBalance: balance || '' })}
          reverse
          size="sm"
        />
      ))
      .with([false, P._, false, '0'], () => (
        <Checklist
          variant="error"
          description={t('kgstudio.asset.balance-value', { formattedTokenBalance: balance || '' })}
          reverse
          size="sm"
        />
      ))
      .with([false, '', false, P._], () => (
        <p className="text-caption text-secondary">
          {t('kgstudio.asset.balance-value', { formattedTokenBalance: balance || '' })}
        </p>
      ))
      .with([false, P._, false, P._], () => (
        <Checklist
          variant="error"
          description={t('kgstudio.asset.balance-value', { formattedTokenBalance: balance || '' })}
          reverse
          size="sm"
        />
      ))
      .otherwise(() => null);
  };

  const getTransferAlertInfo = () => {
    const nativeTokenShortfallAmount = isNativeTokenSelected
      ? BigNumber(gasFee ?? 0)
          .times(GAS_BUFFER)
          .plus(amount)
          .isGreaterThan(nativeTokenBalance ?? 0)
        ? BigNumber(gasFee ?? 0)
            .times(GAS_BUFFER)
            .plus(amount)
            .minus(nativeTokenBalance ?? 0)
        : BigNumber(0)
      : nativeShortfallAmount;

    const isTokenBalanceEnough = isNativeTokenSelected
      ? nativeTokenBalance
          ?.minus(amount)
          .minus(BigNumber(gasFee ?? 1).times(GAS_BUFFER))
          .isGreaterThan(0)
      : isBalanceEnough;

    return {
      nativeShortfallAmount: nativeTokenShortfallAmount,
      isFeeEnough,
      tokenBalanceLoading: isNativeTokenSelected ? false : tokenBalanceLoading,
      nativeTokenBalanceLoading: isNativeTokenSelected ? nativeTokenBalanceLoading : false,
      isBalanceEnough: isTokenBalanceEnough,
      formattedShortfallAmount: isNativeTokenSelected
        ? formatCurrency({ amount: nativeShortfallAmount?.plus(amount) ?? 0 })
        : formattedShortfallAmount,
    };
  };

  const onConfirmClick = async () => {
    if (Object.keys(form.formState.errors).length > 0) return;

    if (orgId && customerAddress) {
      const noteAndAttachments: Record<string, string | string[]> = {};
      const files = attachments.filter((attachment) => attachment.file);

      if (files.length) {
        const fileUrls = await uploadAllFiles({ attachments: files as Media[] }, 'asset_pro');
        if (!fileUrls) return;
        noteAndAttachments['attachments'] = fileUrls.attachments;
      }
      if (form.getValues('note') !== '') {
        noteAndAttachments['note'] = form.getValues('note') as string;
      }

      transferToken({
        chain_id: blockchain as any,
        contract_address: token,
        amount,
        wallet_address: customerAddress,
        ...(sendType !== 'address' && {
          ...(sendType === 'email'
            ? { email }
            : {
                phone: `+${countryCode}${phone}`,
              }),
          display_name: customerData?.data?.display_name,
          kyc_status: customerData?.data?.kyc_status,
        }),
        ...noteAndAttachments,
      });
    }
  };

  useEffect(() => {
    if (fileUrlsError) {
      showToast(t('kgstudio.error.upload-attachment'), 'error');
    }
  }, [fileUrlsError, t]);

  useEffect(() => {
    form.trigger('attachments');
  }, [attachments, form]);

  useEffect(() => {
    if (tokenBalanceLoading || nativeTokenBalanceLoading) return;
    form.trigger('amount');
  }, [blockchain, form, nativeTokenBalanceLoading, tokenBalanceLoading]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isChecking = isNativeTokenSelected ? nativeTokenBalanceLoading : tokenBalanceLoading;
  const isTransferAmountInvalid = form.formState.errors.amount || amount === '';
  const isRecipientRequiredInvalid = !customerAddress;
  const isAdditionalInfoRequiredInvalid =
    requiredNoteAndAttachment && (form.getValues('note') === '' || attachments.length === 0);
  const cantSend = isChecking || !form.formState.isValid || isRecipientRequiredInvalid || amount === '';
  const isGaslessAlertVisible =
    BigNumber(form.getValues('amount') ?? 0).isGreaterThan(0) && ['base', 'optimism', 'arb'].includes(blockchain);

  if (!isClient) return null;

  return (
    <div className="flex flex-col gap-6">
      <Form {...form}>
        <div className="flex flex-col gap-10">
          <div className="flex flex-col gap-6">
            <Card className="section-padding space-y-6">
              {/* Select Blockchain and Token */}
              <div className="grid w-full grid-cols-2 gap-6">
                <FormDropdown
                  title={t('common.blockchain')}
                  data-cy="send-input-blockchain"
                  name="blockchain"
                  control={form.control}
                  options={blockchainOptions}
                  required
                  disabled={formDisabled}
                />

                <FormDropdown
                  className="flex-auto [&>button]:flex-auto"
                  data-cy="send-input-token"
                  title={t('common.token')}
                  control={form.control}
                  name="token"
                  options={tokenOptions}
                  required
                  disabled={formDisabled}
                />
              </div>
              {/* Input Amount */}
              <FormInput
                data-cy="send-input-amount"
                control={form.control}
                name="amount"
                placeholder="0"
                title={t('common.amount')}
                info={getAmountInputInfo(isNativeTokenSelected)}
                onInput={amountInputInterceptor}
                suffix={
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => {
                      form.setValue('amount', formattedTokenBalance ?? '0');
                    }}
                  >
                    {t('kgstudio.send.max')}
                  </Button>
                }
                required
                withoutMessage
                hint={
                  <div className="flex justify-between">
                    <span>
                      {tokenPriceUSD &&
                        `≈ $${formatCurrency({
                          amount: BigNumber(form.getValues('amount') || 0).multipliedBy(tokenPriceUSD),
                          decimals: DECIMAL_DISPLAY_MODE.FIAT,
                        })} USD`}
                    </span>
                    <Tooltip>
                      <Tooltip.Trigger>
                        {match([nativeTokenBalance, isFeeEnough])
                          .with([undefined, P._], () => (
                            <Checklist
                              variant="checking"
                              description={t('kgstudio.asset.estimated-gas-checking')}
                              reverse
                              size="sm"
                            />
                          ))
                          .otherwise(() => (
                            <Checklist
                              variant={isFeeEnough ? 'success' : 'error'}
                              description={`${t('kgstudio.asset.estimated-gas')} ${formattedFee} ${nativeTokenSymbol}`}
                              reverse
                              size="sm"
                            />
                          ))}
                      </Tooltip.Trigger>
                      <Tooltip.Content side="bottom" className="bg-surface-primary border-none px-3 py-2">
                        <span className="text-primary text-caption">
                          {t('kgstudio.asset.balance')} {formattedNativeTokenBalance} {nativeTokenSymbol}
                        </span>
                        <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
                      </Tooltip.Content>
                    </Tooltip>
                  </div>
                }
              />

              {isGaslessAlertVisible && (
                <ChecklistBlock
                  variant="warning"
                  title={t('kgstudio.send.gasless-alert-title')}
                  description={t('kgstudio.send.gasless-alert-desc', {
                    mainTokenName: nativeTokenSymbol,
                    chainName: getChainFullName(blockchain as ChainId) || '',
                    sourceToken: selectedToken?.symbol,
                    estimatedGasUsd: formatCurrency({
                      amount: BigNumber(formattedFee ?? 0).multipliedBy(gasTokenPriceUSD ?? 0),
                      decimals: DECIMAL_DISPLAY_MODE.FIAT,
                    }),
                    estimatedGas: formattedFee || '',
                  })}
                  // buttonText={t('kgstudio.send.gasless-alert-button')}
                  // onButtonClick={() => setIsGaslessModalOpen(true)}
                  size="sm"
                />
              )}
              <TransferGaslessModal
                open={isGaslessModalOpen}
                onOpenChange={setIsGaslessModalOpen}
                options={[
                  {
                    label: t('kgstudio.send.gasless-modal-deducted-title'),
                    value: 'deducted',
                    sendAmount: form.getValues('amount'),
                    receiveAmount: BigNumber(form.getValues('amount'))
                      .minus(BigNumber(formattedFee ?? 0).multipliedBy(gasTokenPriceUSD ?? 0))
                      .toString(),
                    receiveToken: selectedToken?.symbol,
                  },
                  {
                    label: t('kgstudio.send.gasless-modal-full-title'),
                    value: 'full',
                    sendAmount: BigNumber(form.getValues('amount'))
                      .plus(BigNumber(formattedFee ?? 0).multipliedBy(gasTokenPriceUSD ?? 0))
                      .toString(),
                    receiveAmount: form.getValues('amount'),
                    receiveToken: selectedToken?.symbol,
                    disabled: BigNumber(form.getValues('amount')).isGreaterThanOrEqualTo(formattedTokenBalance ?? 0),
                  },
                ]}
                selected={gaslessOption}
                onSelect={setGaslessOption}
                gasFee={formattedFee ?? ''}
                onConfirm={() => {
                  if (gaslessOption === 'full') {
                    form.setValue(
                      'amount',
                      BigNumber(form.getValues('amount'))
                        .plus(BigNumber(formattedFee ?? 0).multipliedBy(gasTokenPriceUSD ?? 0))
                        .toString(),
                    );
                  }
                  setIsGaslessModalOpen(false);
                }}
              />
              <TransferAlert
                userInfo={userInfo}
                transferData={{
                  nativeTokenSymbol,
                  selectedTokenLabel: selectedToken?.symbol,
                }}
                checkData={getTransferAlertInfo()}
                isAboveApprovalThreshold={requiredNoteAndAttachment}
              >
                <TransferAlert.TokenGasBalanceCheck />
                <TransferAlert.LimitCheck tokenPriceUSD={tokenPriceUSD} />
                {/* <TransferAlert.ApprovalThresholdCheck /> */}
              </TransferAlert>
            </Card>
            <Card className="section-padding list-spacing-medium flex flex-col gap-2">
              {/* Select email or phone number */}
              <div className="relative flex w-full flex-col gap-6">
                <FormRadioGroup
                  variant="horizontal"
                  title={t('kgstudio.send.send-to')}
                  desc={t('kgstudio.validation.phone-or-email-required')}
                  name="sendType"
                  control={form.control}
                  items={[
                    {
                      label: t('kgstudio.send.by-email'),
                      value: 'email',
                    },
                    {
                      label: t('kgstudio.send.by-phone-number'),
                      value: 'phone',
                    },
                    {
                      label: t('common.wallet-address'),
                      value: 'address',
                    },
                  ]}
                  required
                  disabled={formDisabled}
                  data-cy="send-type-radio"
                />
                <div className="grid w-full grid-cols-1 items-start gap-2 lg:!grid-cols-[1fr_auto] lg:!gap-4">
                  {/* Input Email */}
                  {sendType == 'email' && (
                    <FormInput
                      data-cy="send-input-email"
                      control={form.control}
                      name="email"
                      type="email"
                      placeholder={t('common.enter-email')}
                      onFocus={resetRiskScan}
                      disabled={formDisabled}
                    />
                  )}

                  {/* Input Phone number */}
                  {sendType == 'phone' && (
                    <div className="grid items-start gap-2 md:grid-cols-[auto_1fr]">
                      <FormDropdown
                        name="countryCode"
                        control={form.control}
                        options={countries.map((country) => {
                          return { value: country.phoneCode, label: `${country.label} (+${country.phoneCode})` };
                        })}
                        defaultValue={countries[0].phoneCode}
                      />
                      <FormInput
                        data-cy="send-input-phone"
                        control={form.control}
                        name="phone"
                        placeholder={t('common.enter-phone-number')}
                        onFocus={resetRiskScan}
                        onInput={(e) => {
                          // only numeric input
                          e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');
                        }}
                        disabled={formDisabled}
                      />
                    </div>
                  )}
                  {/* Input Wallet Address */}
                  {sendType == 'address' && (
                    <FormInput
                      data-cy="send-input-address"
                      control={form.control}
                      name="address"
                      onFocus={resetRiskScan}
                      disabled={formDisabled}
                    />
                  )}

                  <Button
                    icon={<Scan />}
                    loading={kyaProfileLoading || customerFetching}
                    disabled={getKyaButtonEnabled()}
                    onClick={() => performRiskScan(sendType as 'phone' | 'email' | 'address')}
                    data-cy="risk-scan-button"
                  >
                    {t('kgstudio.asset.kyc-status.scan')}
                  </Button>
                </div>
                {userNotFound && <FormInputError error={t('kgstudio.error.cant-find-user')} className="mt-2" />}
                {customerNotFound && <FormInputError error={t('kgstudio.error.cant-find-customer')} className="mt-2" />}

                {!!customerData && !selectedChainWalletAddress && (
                  <FormInputError
                    className="mt-2"
                    error={t('kgstudio.asset.mismatch-error', { selectedBlockchain: selectedBlockchain.symbol })}
                  />
                )}
              </div>

              {customerFetching && (
                <div className="flex flex-row items-center gap-2">
                  <Spinner className="h-3 w-3 animate-spin" />
                  <Label className="text-sm font-normal text-[#172133]">{t('kgstudio.asset.checking-kyc')}...</Label>
                </div>
              )}
              {!!customerData && (
                <div className="space-y-3">
                  <div className="flex flex-col gap-2">
                    <FormLabel>{t('common.please-check')}</FormLabel>
                    <FormDescription>{t('kgstudio.asset.kyc-status.subtitle')}</FormDescription>
                  </div>
                  <div className="space-y-2">
                    <div className="grid grid-cols-1 gap-3">
                      <div className="border-primary w-full space-y-3 rounded-lg border p-3">
                        <div className="grid grid-cols-[40px_1fr] items-center gap-3">
                          <Avatar
                            imageSrc={customerData?.data?.avatar_url}
                            displayName={customerData?.data?.display_name}
                            size="36"
                          />
                          <div className="flex justify-between">
                            <div className="flex flex-col justify-center">
                              <p className="text-body-2 font-bold">{customerData?.data?.display_name}</p>
                              <div>
                                <KycStatusBadge status={customerData?.data.kyc_status as KycStatus} />
                              </div>
                            </div>
                            <Link
                              href={`/user360/audience/${customerData?.data?.uid}`}
                              target="_blank"
                              className="text-brand-primary text-body-2-bold flex items-center gap-1"
                            >
                              <ExternalLink className="stroke-[var(--brand-primary)]" size={16} />
                              {t('kgstudio.common.profile')}
                            </Link>
                          </div>
                        </div>
                        <Separator />
                        <div className="text-small text-secondary min-gap-8 grid grid-cols-1 gap-6 md:grid-cols-3">
                          <div className="flex flex-col items-start gap-1">
                            <p>{t('common.wallet-address')}</p>
                            <p>{sendType === 'email' ? t('common.email') : t('common.phone-number')}</p>
                          </div>

                          <div className="flex flex-col gap-1 overflow-hidden">
                            <p className="truncate">
                              <a
                                href={getExplorerUrl('address', blockchain as ChainId, customerAddress ?? '') ?? '#'}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="underline"
                              >
                                {customerAddress}
                              </a>
                            </p>
                            <p className="truncate">{(phone && `+${countryCode}${phone}`) || email}</p>
                          </div>
                          <Button
                            className="w-fit"
                            icon={<Scan />}
                            loading={kyaProfileLoading || customerFetching}
                            onClick={() => performRiskScan('address')}
                          >
                            {t('kgstudio.asset.kyc-status.scan-address')}
                          </Button>
                        </div>
                      </div>
                      <KycStatusNotification status={customerData?.data?.kyc_status as KycStatus} />
                    </div>
                  </div>
                </div>
              )}

              {kyaProfileLoading && (
                <div className="flex flex-row items-center gap-2">
                  <Spinner className="h-3 w-3 animate-spin" />
                  <Label className="text-sm font-normal text-[#172133]">{t('kgstudio.asset.checking-kya')}...</Label>
                </div>
              )}
              {!!kyaProfileData && !!customerAddress && (
                <KyaRiskReminderAndModal
                  address={customerAddress}
                  riskScore={(kyaProfileData as ProxyKyaResponse).data.riskscore}
                  signals={(kyaProfileData as ProxyKyaResponse).data.signals}
                  signalsProfile={(kyaProfileData as ProxyKyaResponse).data.riskscore_profile?.signals || null}
                />
              )}
            </Card>
            <Card className="section-padding list-spacing-medium flex flex-col gap-2">
              <div data-cy="transaction-note">
                <FormTextarea
                  name="note"
                  required={requiredNoteAndAttachment}
                  title={
                    requiredNoteAndAttachment
                      ? t('kgstudio.send.transaction-note')
                      : `${t('kgstudio.send.transaction-note')} (${t('kgstudio.common.optional')})`
                  }
                  control={form.control}
                  className="min-h-[86px]"
                />
                <span className="text-caption text-secondary">{t('kgstudio.send.note-placeholder')}</span>
              </div>
              <div className="space-y-2" data-cy="transaction-attachment">
                <p className="text-body-bold text-primary space-x-2" data-cy="transaction-attachment-label">
                  <span>{t('kgstudio.send.transaction-attachment')}</span>
                  {requiredNoteAndAttachment ? (
                    <span className="text-brand-primary">*</span>
                  ) : (
                    <span>({t('kgstudio.common.optional')})</span>
                  )}
                </p>
                <ImageUploader
                  multiple
                  maxNumber={10}
                  value={attachments}
                  onChange={setAttachments}
                  maxFileSize={1024 * 1024 * 10}
                  acceptType={['png', 'jpg', 'jpeg', 'webp']}
                >
                  {({ imageList, onImageUpload, onImageRemove, dragProps, errors }) => {
                    return (
                      <div className="space-y-2">
                        <Button
                          variant="secondary"
                          size="sm"
                          icon={<Plus className="!stroke-brand-primary-dark" size={16} strokeWidth={3} />}
                          onClick={onImageUpload}
                          loading={fileUrlsLoading}
                          {...dragProps}
                        >
                          {t('kgstudio.send.add-attachment')}
                        </Button>
                        <div className="flex flex-wrap items-center gap-[10px]">
                          {imageList.map((image, index) => (
                            <AttachmentCard
                              key={index}
                              imageSrc={image.dataURL ?? ''}
                              imageAlt={image.file?.name}
                              file={image.file}
                              onImageRemove={() => onImageRemove(index)}
                            />
                          ))}
                        </div>
                        {errors && (
                          <InputError>
                            {match(errors)
                              .with({ maxNumber: true }, () => t('kgstudio.asset.edit-order-modal.max-files-error'))
                              .with({ acceptType: true }, () =>
                                t('kgstudio.asset.edit-order-modal.accepted-file-types'),
                              )
                              .with({ maxFileSize: true }, () => t('kgstudio.asset.edit-order-modal.file-size-error'))
                              .otherwise(() => '')}
                          </InputError>
                        )}
                        {form.formState.errors.attachments && (
                          <InputError>{form.formState.errors.attachments.message}</InputError>
                        )}
                      </div>
                    );
                  }}
                </ImageUploader>
              </div>
              {/* TODO: Need to update FormTextarea and ImageUploader error display first */}
              {/* {isAdditionalInfoRequiredInvalid && (
                <ChecklistBlock
                  variant="warning"
                  description="Enter the transaction note and upload necessary attachments for approval."
                  size="sm"
                />
              )} */}
            </Card>

            {(isTransferAmountInvalid || isRecipientRequiredInvalid || isAdditionalInfoRequiredInvalid) && (
              <ChecklistBlock
                variant="error"
                title={t('kgstudio.asset.transfer-validation')}
                description={
                  <ul className="list-disc">
                    {isTransferAmountInvalid && <li>{t('kgstudio.asset.transfer-validation-amount-invalid')}</li>}
                    {isRecipientRequiredInvalid && <li>{t('kgstudio.asset.transfer-validation-recipient-invalid')}</li>}
                    {isAdditionalInfoRequiredInvalid && <li>{t('kgstudio.asset.transfer-validation-info-invalid')}</li>}
                  </ul>
                }
                size="sm"
              />
            )}
          </div>

          {/* Send Button and dialog */}
          <Modal open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <div className="flex justify-center">
              <Modal.Trigger
                asChild
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.preventDefault();
                  if (cantSend) {
                    return;
                  }
                  form.trigger().then((res) => res && setIsDialogOpen(true));
                }}
              >
                <Button
                  className="w-full md:w-[200px]"
                  variant={cantSend ? 'grey' : 'primary'}
                  disabled={!!cantSend}
                  data-cy="send-submit-button"
                >
                  {t('common.send')}
                </Button>
              </Modal.Trigger>
            </div>

            <Modal.Content className="md:w-modal-medium max-h-[80%] w-[90vw]" data-cy="send-confirm-dialog" scrollable>
              <Modal.Header>
                <Modal.Title>{t('kgstudio.asset.check-tx')}</Modal.Title>
              </Modal.Header>
              <div className="list-spacing-medium flex flex-col pb-2">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
                  <div className="flex flex-col gap-1">
                    <p className="text-body-2 text-secondary">{t('common.send-token')}</p>
                    <div className="flex flex-row items-center gap-2">
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img src={selectedToken.logoUrl} alt="token_img" className="h-6 w-6 object-contain" />
                      <p className="text-h3 md:text-h2 text-primary">
                        {`${formatCurrency({ amount: amount })} ${selectedToken.symbol}`}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col gap-1">
                    <p className="text-body-2 text-secondary">{t('common.blockchain')}</p>
                    <div className="flex flex-row items-center gap-2">
                      <div className="relative h-6 w-6 overflow-hidden rounded-full">
                        {<Image src={getChainIcon(blockchain) ?? ''} alt={blockchain} fill className="object-cover" />}
                      </div>
                      <p className="text-h3 md:text-h2 text-primary">{selectedBlockchain.symbol}</p>
                    </div>
                  </div>
                  <div className="col-span-2 flex flex-col gap-1">
                    <p className="text-body-2 text-secondary">{t('kgstudio.common.recipient')}</p>
                    <div
                      className="border-primary w-full space-y-3 rounded-xl border p-4"
                      data-cy="transaction-dialog-recipient"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex flex-col justify-center gap-1">
                          <p className="text-body-2-bold text-primary">{customerData?.data?.display_name}</p>
                          <div>
                            {customerData?.data?.kyc_status && (
                              <KycStatusBadge status={customerData.data.kyc_status as KycStatus} />
                            )}
                          </div>
                        </div>
                        {customerData?.data?.uid && (
                          <Button
                            variant="grey"
                            size="sm"
                            icon={<ArrowUpRightFromSquare size={12} />}
                            onClick={() => router.push(`/user360/audience/${customerData?.data?.uid}`)}
                          >
                            {t('kgstudio.send.view-profile')}
                          </Button>
                        )}
                      </div>
                      <Separator />
                      <div className="text-small text-secondary space-y-2">
                        <div className="flex items-center gap-6">
                          <p className="basis-[90px]">{t('common.wallet-address')}</p>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="text"
                              className="p-0"
                              onClick={() => {
                                window.open(
                                  getExplorerUrl('address', blockchain as ChainId, customerAddress ?? '') ?? '#',
                                  '_blank',
                                );
                              }}
                            >
                              <p className="text-primary text-small truncate underline">
                                {deviceSize === 'sm' ? truncateTxhashOrAddress(customerAddress ?? '') : customerAddress}
                              </p>
                            </Button>
                            <Copy
                              className="cursor-pointer stroke-[var(--text-primary)]"
                              size={12}
                              onClick={() => handleCopyAddress(customerAddress as string, t)}
                            />
                          </div>
                        </div>
                        <div className="flex items-center gap-6">
                          <p className="basis-[90px]">
                            {sendType === 'email' ? t('common.email') : t('common.phone-number')}
                          </p>
                          <p className="text-primary truncate">{(phone && `+${countryCode}${phone}`) || email}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  {form.getValues('note') && (
                    <div className="col-span-2 flex flex-col gap-2" data-cy="transaction-dialog-note">
                      <p className="text-body-2 text-secondary">{t('kgstudio.send.review-note')}</p>
                      <p className="text-body-2 text-primary">{form.getValues('note')}</p>
                    </div>
                  )}
                  {!!attachments.length && (
                    <div className="col-span-2 flex flex-col gap-2" data-cy="transaction-dialog-attachment">
                      <p className="text-body-2 text-secondary">{t('kgstudio.send.transaction-attachment')}</p>
                      <div className="flex flex-wrap gap-3">
                        {attachments.map((attachment, index) => (
                          <ImageViewer key={index} imageUrl={attachment.dataURL as string} />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                {requiredNoteAndAttachment ? (
                  <ReminderBlock
                    variant="warning"
                    title={t('kgstudio.send.send-confirm-submit-title-alert')}
                    description={t('kgstudio.send.send-confirm-submit-desc-alert')}
                  />
                ) : (
                  <ReminderBlock variant="warning" description={t('kgstudio.send.send-confirm-alert')} />
                )}
              </div>
              <Modal.Footer className="flex flex-col gap-2 md:flex-row md:justify-between">
                <Button variant="grey" onClick={() => setIsDialogOpen(false)} className="md:w-[100px]">
                  {t('common.cancel')}
                </Button>
                <Button
                  onClick={form.handleSubmit(onConfirmClick)}
                  className="rounded-xl md:w-[200px]"
                  data-cy="send-confirm-dialog-confirm"
                  loading={fileUrlsLoading || transferTokenLoading}
                >
                  {requiredNoteAndAttachment ? t('kgstudio.send.submit-request') : t('common.confirm')}
                </Button>
              </Modal.Footer>
            </Modal.Content>
          </Modal>
          <LoadingModal open={!!transferTokenLoading} />
          <TransferSubmittedModal
            open={openSubmittedModal}
            onOpenChange={setOpenSubmittedModal}
            transferTokenState={{
              data: transferTokenResp?.data,
              error: transferTokenError,
            }}
            approvalRequired={requiredNoteAndAttachment}
          />
        </div>
      </Form>
    </div>
  );
}
