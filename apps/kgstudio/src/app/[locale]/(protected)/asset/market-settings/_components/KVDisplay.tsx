'use client';

import { Info } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ComponentPropsWithoutRef } from 'react';

import { useMaxWidth } from '@/app/_common/hooks';
import { cn } from '@/app/_common/lib/utils';
import { Skeleton, Tooltip, TooltipProvider } from '@kryptogo/2b';

type Datum = {
  key: string;
  tooltip?: string;
  value: string | React.ReactNode;
};

interface IKVDisplayProps extends ComponentPropsWithoutRef<'div'> {
  data: Datum[];
}

const KVDisplay = ({ data, className, ...props }: IKVDisplayProps) => {
  const t = useTranslations();
  const { containerId, itemId } = useMaxWidth();

  const fieldNameDisplay = (data: Datum) => {
    const tooltip = data.tooltip;

    return (
      <div id={itemId} className="flex items-center gap-[6px]">
        <p className="text-secondary">{data.key}</p>
        {tooltip && (
          <TooltipProvider>
            <Tooltip>
              <Tooltip.Trigger>
                <Info className="h-4 w-4 stroke-[var(--text-secondary)]" />
              </Tooltip.Trigger>
              <Tooltip.Content side="bottom">
                <span>{tooltip}</span>
                <Tooltip.Arrow />
              </Tooltip.Content>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    );
  };

  const fieldValueDisplay = (data: Datum) => {
    const nilValue = t('kgstudio.common.unset');

    return typeof data.value === 'string' ? (
      <p className="text-primary whitespace-pre-wrap">{data.value}</p>
    ) : data.value === null ? (
      <p className="text-error">{nilValue}</p>
    ) : data.value === undefined ? (
      <Skeleton className="h-6 w-[200px]" />
    ) : (
      data.value
    );
  };
  return (
    <div id={containerId} className={cn('flex flex-col justify-center gap-6', className)} {...props}>
      {data.map((datum, index) => (
        <div key={index} className="text-body flex items-start gap-4">
          {fieldNameDisplay(datum)}
          {fieldValueDisplay(datum)}
        </div>
      ))}
    </div>
  );
};

export { KVDisplay };
