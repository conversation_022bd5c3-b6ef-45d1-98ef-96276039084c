'use client';

import * as E from 'fp-ts/Either';
import { pipe } from 'fp-ts/lib/function';
import { Copy } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

import { usePageHeader } from '@/app/_common/hooks';
import { handleCopyAddress } from '@/app/_common/lib/utils';
import { apiAssetProHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { Button, Card, Separator } from '@kryptogo/2b';

import { KVDisplay } from './_components/KVDisplay';

const MarketSettingsPage = () => {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.common.market-settings') });
  const orgId = useOrganizationStore((state) => state.orgId);

  const { data: marketSettings } = apiAssetProHooks.useGetMarketSettings(
    {
      params: { org_id: Number(orgId) },
    },
    {
      enabled: !!orgId,
    },
  );

  //FIXME: implement loading state
  if (!marketSettings) return 'loading';

  return (
    <div className="space-y-[42px]">
      <main className="flex flex-col gap-6">
        <Card className="section-padding space-y-6">
          <div className="item-gap-medium flex flex-col justify-center">
            <h2 className="text-h2 text-primary font-bold">{t('kgstudio.asset.market-profile.section-title')}</h2>
            <Separator />
          </div>

          <KVDisplay
            data={[
              {
                key: t('kgstudio.asset.market-profile.url'),
                tooltip: t('kgstudio.asset.market-profile.url-tooltip'),
                value: (
                  <div className="flex items-center gap-3">
                    <p className="text-primary break-all">{marketSettings.data?.market_url}</p>
                    <button onClick={() => handleCopyAddress(marketSettings.data?.market_url, t)}>
                      <Copy className="h-4 w-4 stroke-[var(--text-secondary)]" />
                    </button>
                    <Button size="sm" onClick={() => window.open(marketSettings.data?.market_url, '_blank')}>
                      {t('kgstudio.asset.market-profile.store-link')}
                    </Button>
                  </div>
                ),
              },
              {
                key: t('kgstudio.asset.market-profile.title'),
                tooltip: t('kgstudio.asset.market-profile.title-tooltip'),
                value: marketSettings.data?.title,
              },
              {
                key: t('kgstudio.asset.market-profile.logo'),
                value: marketSettings.data?.logo && (
                  <div className="relative h-[45px] w-[220px] self-start">
                    <Image
                      src={marketSettings.data.logo}
                      alt="Market logo"
                      fill
                      className="object-contain object-left"
                    />
                  </div>
                ),
              },
              {
                key: t('kgstudio.asset.market-profile.phone'),
                value: marketSettings.data?.phone,
              },
              {
                key: t('kgstudio.asset.market-profile.email'),
                value: marketSettings.data?.email,
              },
              {
                key: t('kgstudio.asset.market-profile.line-id'),
                value: marketSettings.data?.line_id,
              },
              {
                key: t('kgstudio.asset.market-profile.intro'),
                tooltip: t('kgstudio.asset.market-profile.intro-tooltip'),
                value: marketSettings.data?.introduction,
              },
            ]}
          />
        </Card>
        <Card className="section-padding space-y-6">
          <div className="item-gap-medium flex flex-col justify-center">
            <h2 className="text-h2 text-primary font-bold">{t('kgstudio.asset.order-settings.section-title')}</h2>
            <Separator />
          </div>
          <KVDisplay
            data={[
              {
                key: t('kgstudio.asset.order-settings.payment-terms'),
                tooltip: t('kgstudio.asset.order-settings.payment-terms-tooltip'),
                value: formatDuration(marketSettings.data?.payment_expiration_sec),
              },
            ]}
          />
        </Card>
        <Card className="section-padding space-y-6">
          <div className="item-gap-medium flex flex-col justify-center">
            <h2 className="text-h2 text-primary font-bold">{t('kgstudio.asset.payment-info.section-title')}</h2>
            <Separator />
          </div>
          <KVDisplay
            data={[
              {
                key: t('kgstudio.asset.payment-info.payment-method'),
                value: t('kgstudio.asset.payment-info.bank-transfer'),
              },
              {
                key: t('kgstudio.asset.payment-info.currency'),
                value: marketSettings.data?.payment_currency,
              },
              {
                key: t('kgstudio.asset.payment-info.bank-name'),
                value: marketSettings.data?.bank_name,
              },
              {
                key: t('kgstudio.asset.payment-info.branch-name'),
                value: marketSettings.data?.branch_name,
              },
              {
                key: t('kgstudio.asset.payment-info.account-number'),
                value: marketSettings.data?.bank_account,
              },
              {
                key: t('kgstudio.asset.payment-info.account-holder-name'),
                value: marketSettings.data?.bank_account_holder_name,
              },
            ]}
          />
        </Card>
      </main>
    </div>
  );
};

export default MarketSettingsPage;

// Constants for time conversions
const SECONDS_PER_MINUTE = 60;
const SECONDS_PER_HOUR = 3600;

type Duration = {
  hours: number;
  minutes: number;
};

function toDuration(seconds: number): Duration {
  return {
    hours: Math.floor(seconds / SECONDS_PER_HOUR),
    minutes: Math.floor((seconds % SECONDS_PER_HOUR) / SECONDS_PER_MINUTE),
  };
}

function formatDuration(seconds: number): string {
  return pipe(
    toDuration(seconds),
    E.fromPredicate(
      (duration) => duration.hours > 0 && duration.minutes === 0,
      (duration) => ({ ...duration, minutes: duration.minutes + duration.hours * 60 }),
    ),
    E.fold(
      (duration) => `${duration.minutes} Minutes`,
      (duration) => `${duration.hours} Hours`,
    ),
  );
}
