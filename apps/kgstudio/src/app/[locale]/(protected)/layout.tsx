'use client';

import { useTranslations } from 'next-intl';
import { notFound } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { toast } from 'sonner';

import { Footer, Loading, Navbar, Sidebar } from '@/app/_common/components';
import { useDeviceSize, useIsClient, usePermissions } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { allRoutes, filterModulesRoutes } from '@/app/_common/lib/route';
import { cn } from '@/app/_common/lib/utils';
import { apiAssetProHooks, apiAssetProOrderHooks, apiOrganizationHooks, apiUser360Hooks } from '@/app/_common/services';
import { OrgInfo, UserInfo } from '@/app/_common/services/organization/model';
import { useAuthStore } from '@/app/_common/store/useAuthStore';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { usePathname } from '@/i18n/navigation';
import * as Sentry from '@sentry/nextjs';

export default function ProtectedLayout({ children }: { children: React.ReactNode }) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);
  const { isClient } = useIsClient();
  const { deviceSize } = useDeviceSize();
  const pathname = usePathname();
  const [hasReadOrderPermission, hasReadTransactionPermission, hasReadCompPermission] = usePermissions(
    ['asset_pro_order', 'read'],
    ['transaction', 'read'],
    ['user_360_statistics_compliance', 'read'],
  );

  const [orgId, setOrgId, setOrgInfo] = useOrganizationStore((state) => [
    state.orgId,
    state.setOrgId,
    state.setOrgInfo,
  ]);
  const [isLoggedIn, setUserInfo, logout] = useAuthStore((state) => [
    state.isLoggedIn,
    state.setUserInfo,
    state.logout,
  ]);

  const { isLoading: isOrgsLoading } = apiOrganizationHooks.useGetOrganizations(
    {},
    {
      onSuccess: (response) => {
        const org = response.data[0];
        if (org) {
          orgId ?? setOrgId(org.id);
        } else {
          console.error('No organizations found in the response');
        }
      },
      onError: (error) => {
        console.error('Failed to get organizations:', error);
        if (isApiError(error)) {
          toast.error(t('kgstudio.common.fetch-org-failed'));
        }
      },
      refetchOnWindowFocus: true,
      staleTime: 0,
    },
  );

  const { data: userInfo, isLoading: isUserInfoLoading } = apiOrganizationHooks.useGetCurrentUserInfo(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
      retry: (_, error) => {
        if (isApiError(error) && ((error.status === 404 && error.code === 7014) || error.status === 403)) {
          toast.error(t('kgstudio.common.user-not-found'));
          logout();
          return false;
        }
        console.error('Error fetching user info:', error);
        return true;
      },
      refetchOnWindowFocus: true,
      staleTime: 0,
    },
  );
  useSyncUserInfoToStore(userInfo?.data ?? null);

  const { data: orgInfo, isLoading: isOrgInfoLoading } = apiOrganizationHooks.useGetOrganizationInfo(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId,
      onSuccess: (response) => {
        setOrgInfo(response.data);
      },
      onError: (error) => {
        console.error('Failed to get organization info:', error);
        if (isApiError(error)) {
          toast.error(t('kgstudio.common.fetch-org-info-failed'));
        }
      },
      refetchOnWindowFocus: true,
      staleTime: 0,
    },
  );
  useSyncOrgInfoToStore(orgInfo?.data ?? null);

  const { data: pendingOrderCouont, error: pendingOrderCouontError } = apiAssetProOrderHooks.useGetPendingOrderCount(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId && !!hasReadOrderPermission,
      refetchOnWindowFocus: true,
      staleTime: 60_000,
    },
  );

  const { data: complianceStat, error: complianceStatError } = apiUser360Hooks.useGetComplianceStat(
    {
      params: { org_id: orgId ?? -1 },
    },
    {
      enabled: !!orgId && !!hasReadCompPermission,
      refetchOnWindowFocus: true,
      staleTime: 60_000,
    },
  );

  const { data: pendingTransactionCount, error: pendingTransactionCountError } =
    apiAssetProHooks.useGetPendingTxHistoryCount(
      {
        params: { org_id: Number(orgId) },
      },
      { enabled: !!orgId && !!hasReadTransactionPermission, refetchOnWindowFocus: true, staleTime: 60_000 },
    );

  // TODO: fix type and optimize conditional logic for clarity and efficiency
  const checkPathAgainstRoutes = useCallback((pathname: string, routes: any) => {
    for (const route of routes) {
      if (route.items) {
        const isPathValid = checkPathAgainstRoutes(pathname, route.items);
        if (isPathValid) return true;
      }
      if (route.regex && route.regex.test(pathname)) return true;
      if (route.path === pathname) return true;
    }
    return false;
  }, []);

  //
  const routes = filterModulesRoutes(
    allRoutes,
    userInfo?.data.modules,
    {
      '/asset/orders': pendingOrderCouont?.data.count ?? 0,
      '/asset/transactions': pendingTransactionCount?.data.count ?? 0,
      '/compliance/review': complianceStat?.data.kyc.pending_tasks.value ?? 0,
    },
    userInfo?.data.permissions,
  );

  const isLoading = isUserInfoLoading || isOrgInfoLoading || isOrgsLoading;

  useHotkeys('ctrl+s', () => setOpen((prev) => !prev), [open]);

  useEffect(() => {
    if (!isLoggedIn) {
      window.location.reload();
    }
  }, [isLoggedIn]);

  useEffect(() => {
    if (!routes.length) return;

    const canVisit = checkPathAgainstRoutes(pathname, routes);
    if (!canVisit) notFound();
  }, [checkPathAgainstRoutes, pathname, routes]);

  useEffect(() => {
    setOpen(deviceSize === 'lg');
  }, [deviceSize, pathname]);

  if (isLoading) return <Loading />;

  return (
    <div className="h-full w-full lg:p-8">
      <>
        <div
          className={cn('font-dm h-full p-6 lg:p-0 lg:pl-8', {
            'lg:ml-[260px]': open,
          })}
        >
          <div className="space-y-6">
            <Navbar />
            <main>{children}</main>
            <Footer />
          </div>
        </div>
        {isClient && routes?.length !== 0 && (
          <Sidebar routes={routes} open={open} setOpen={setOpen} data-cy="sidebar" />
        )}
      </>
    </div>
  );
}

const useSyncUserInfoToStore = (userInfo: UserInfo | null) => {
  const [setUserInfo] = useAuthStore((state) => [state.setUserInfo]);

  useEffect(() => {
    if (!userInfo) return;

    setUserInfo(userInfo);
    Sentry.setUser({
      id: userInfo.uid,
      username: userInfo.name,
      email: userInfo.email,
      phone: userInfo.phone,
      member_id: userInfo.member_id,
    });
  }, [userInfo, setUserInfo]);
};

const useSyncOrgInfoToStore = (orgInfo: OrgInfo | null) => {
  const [setOrgInfo] = useOrganizationStore((state) => [state.setOrgInfo]);

  useEffect(() => {
    if (!orgInfo) return;

    setOrgInfo(orgInfo);
  }, [orgInfo, setOrgInfo]);
};
