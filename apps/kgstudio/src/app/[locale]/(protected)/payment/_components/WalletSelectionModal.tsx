'use client';

import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useEffect, useState } from 'react';
import { useAccount, useConnect } from 'wagmi';

import { showToast } from '@/2b/toast';
import { cn } from '@/app/_common/lib/utils';
import { Modal, Button } from '@kryptogo/2b';

interface WalletSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const WalletSelectionModal: React.FC<WalletSelectionModalProps> = ({ isOpen, onClose }) => {
  const t = useTranslations();
  const { connect, connectors, error, isLoading, pendingConnector } = useConnect();
  const { isConnected } = useAccount();
  const [connectingId, setConnectingId] = useState<string | null>(null);
  const [lastErrorMessage, setLastErrorMessage] = useState<string | null>(null);

  // Close modal when wallet is connected
  useEffect(() => {
    if (isConnected) {
      onClose();
      showToast(t('kgstudio.payment.wallet-connected'), 'success');
      setConnectingId(null);
      setLastErrorMessage(null);
    }
  }, [isConnected, onClose, t]);

  // Show error toast when connection fails
  useEffect(() => {
    if (error && error.message && error.message !== lastErrorMessage) {
      showToast(error.message || t('kgstudio.payment.wallet-connection-failed'), 'error');
      setConnectingId(null);
      setLastErrorMessage(error.message);
    }
  }, [error?.message, lastErrorMessage, t]);

  // Reset state when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setConnectingId(null);
      setLastErrorMessage(null);
    }
  }, [isOpen]);

  const handleConnectorClick = (connector: any) => {
    setConnectingId(connector.id);
    connect({ connector });
  };

  const getConnectorIcon = (connectorId: string) => {
    switch (connectorId) {
      case 'metaMask':
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-500">
            <span className="text-sm font-bold text-white">M</span>
          </div>
        );
      case 'injected':
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500">
            <span className="text-sm font-bold text-white">W</span>
          </div>
        );
      default:
        return (
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-500">
            <span className="text-sm font-bold text-white">{connectorId.charAt(0).toUpperCase()}</span>
          </div>
        );
    }
  };

  const getConnectorName = (connector: any) => {
    switch (connector.id) {
      case 'metaMask':
        return 'MetaMask';
      case 'injected':
        return connector.name || 'Browser Wallet';
      default:
        return connector.name || connector.id;
    }
  };

  const getConnectorDescription = (connectorId: string) => {
    switch (connectorId) {
      case 'metaMask':
        return t('kgstudio.payment.metamask-description');
      case 'injected':
        return t('kgstudio.payment.browser-wallet-description');
      default:
        return t('kgstudio.payment.wallet-description');
    }
  };

  useEffect(() => {
    console.log(connectors);
  }, [connectors]);

  return (
    <Modal open={isOpen} onOpenChange={onClose}>
      <Modal.Content className="max-w-md">
        <Modal.Header>
          <div className="flex items-center justify-between">
            <Modal.Title>{t('kgstudio.payment.connect-wallet')}</Modal.Title>
            <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0">
              <X size={16} />
            </Button>
          </div>
        </Modal.Header>

        <div className="space-y-3">
          <p className="text-sm text-gray-600">{t('kgstudio.payment.select-wallet-description')}</p>

          <div className="space-y-2">
            {connectors.map((connector) => {
              const isConnecting = connectingId === connector.id && isLoading;
              const isPending = pendingConnector?.id === connector.id;

              return (
                <button
                  key={connector.id}
                  onClick={() => handleConnectorClick(connector)}
                  disabled={!connector.ready || isConnecting}
                  className={cn(
                    'flex w-full items-center gap-3 rounded-lg border border-gray-200 p-4 transition-colors hover:border-gray-300 hover:bg-gray-50',
                    {
                      'cursor-not-allowed opacity-50': !connector.ready,
                      'border-blue-500 bg-blue-50': isConnecting || isPending,
                    },
                  )}
                >
                  {getConnectorIcon(connector.id)}
                  <div className="flex-1 text-left">
                    <div className="font-medium text-gray-900">{getConnectorName(connector)}</div>
                    <div className="text-sm text-gray-500">{getConnectorDescription(connector.id)}</div>
                  </div>
                  {isConnecting && (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
                      <span className="text-sm text-blue-600">{t('kgstudio.payment.connecting')}</span>
                    </div>
                  )}
                  {!connector.ready && (
                    <span className="text-xs text-gray-400">{t('kgstudio.payment.not-available')}</span>
                  )}
                </button>
              );
            })}
          </div>

          {error && (
            <div className="rounded-lg border border-red-200 bg-red-50 p-3">
              <p className="text-sm text-red-600">{error.message}</p>
            </div>
          )}

          <div className="text-center text-xs text-gray-500">{t('kgstudio.payment.wallet-security-notice')}</div>
        </div>
      </Modal.Content>
    </Modal>
  );
};
