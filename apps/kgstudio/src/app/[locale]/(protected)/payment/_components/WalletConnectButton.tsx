'use client';

import { Wallet } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React from 'react';
import { useAccount, useDisconnect } from 'wagmi';

import { showToast } from '@/2b/toast';
import { cn } from '@/app/_common/lib/utils';
import { Button, DropdownMenu } from '@kryptogo/2b';
import { truncateTxhashOrAddress } from '@kryptogo/utils';

interface WalletConnectButtonProps {
  onConnectClick: () => void;
  className?: string;
}

export const WalletConnectButton: React.FC<WalletConnectButtonProps> = ({ onConnectClick, className }) => {
  const t = useTranslations();
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();

  const handleDisconnect = () => {
    disconnect();
    showToast(t('kgstudio.payment.wallet-disconnected'), 'success');
  };

  const handleCopyAddress = () => {
    if (address) {
      navigator.clipboard.writeText(address);
      showToast(t('kgstudio.payment.address-copied'), 'success');
    }
  };

  if (isConnected && address) {
    return (
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <Button variant="secondary" size="md" className={cn('flex items-center gap-2', className)}>
            <Wallet size={16} />
            <span className="hidden sm:block">{truncateTxhashOrAddress(address)}</span>
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end" className="w-48">
          <DropdownMenu.Item onClick={handleCopyAddress} className="flex items-center gap-2">
            <Wallet size={14} />
            {t('kgstudio.payment.copy-address')}
          </DropdownMenu.Item>
          <DropdownMenu.Separator />
          <DropdownMenu.Item onClick={handleDisconnect} className="flex items-center gap-2 text-red-600">
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              />
            </svg>
            {t('kgstudio.payment.disconnect-wallet')}
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu>
    );
  }

  return (
    <Button variant="primary" size="md" onClick={onConnectClick} className={cn('flex items-center gap-2', className)}>
      <Wallet size={16} />
      <span className="hidden sm:block">{t('kgstudio.payment.connect-wallet')}</span>
    </Button>
  );
};
