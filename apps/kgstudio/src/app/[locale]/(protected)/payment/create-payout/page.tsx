'use client';

import { ArrowLeft } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormDropdown, FormInput, FormMediaUploader, FormTextarea } from '@/app/_common/components/form';
import { usePageHeader } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { apiPaymentHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Form } from '@kryptogo/2b';

// Expanded schema for the create payout form based on Figma design
const CreatePayoutSchema = z
  .object({
    // Recipient Details
    recipient_name: z.string().min(1, 'Recipient name is required'),
    recipient_email: z.string().email('Valid email is required'),

    // Your Details
    organization_name: z.string().min(1, 'Organization name is required'),
    organization_email: z.string().email('Valid email is required'),

    // Payout Details
    payout_id: z.string().optional(), // Auto-generated
    payout_due_date: z.string().min(1, 'Due date is required'),
    description: z.string().min(1, 'Description is required'),
    attachment: z.any().optional(), // File upload

    // Payout Method
    network: z.enum(['arbitrum', 'base', 'optimism'], {
      required_error: 'Network is required',
    }),
    token: z.enum(['USDC', 'USDT'], {
      required_error: 'Token is required',
    }),
    recipient_wallet: z.string().min(1, 'Recipient wallet address is required'),
    payout_amount: z.string().min(1, 'Payout amount is required'),
    currency: z.enum(['USD', 'TWD']).default('USD'),

    // Legacy fields for API compatibility
    pricing_mode: z.enum(['fiat', 'crypto']).default('crypto'),
    pay_token: z.enum(['USDC', 'USDT']).default('USDC'),
    payout_target_address: z.string(),
  })
  .refine((data) => {
    // Sync recipient_wallet with payout_target_address for API compatibility
    data.payout_target_address = data.recipient_wallet;
    data.pay_token = data.token;
    return true;
  });

type CreatePayoutFormValues = z.infer<typeof CreatePayoutSchema>;

const CreatePayoutPage = () => {
  const t = useTranslations();
  const router = useRouter();
  const { orgId } = useOrganizationStore();
  const [isLoading, setIsLoading] = useState(false);
  usePageHeader({ title: t('kgstudio.payment.create-payout'), backLink: '/payment/payout-list' });

  const form = useForm<CreatePayoutFormValues>({
    resolver: zodResolver(CreatePayoutSchema),
    defaultValues: {
      recipient_name: '',
      recipient_email: '',
      organization_name: '',
      organization_email: '',
      payout_id: `IV${Math.random().toString(36).substring(2, 11).toUpperCase()}`, // Auto-generate
      payout_due_date: '',
      description: '',
      network: 'arbitrum',
      token: 'USDC',
      recipient_wallet: '',
      payout_amount: '',
      currency: 'USD',
      pricing_mode: 'crypto',
      pay_token: 'USDC',
      payout_target_address: '',
    },
    mode: 'onChange',
  });

  const watchedValues = form.watch();
  const payoutAmount = parseFloat(watchedValues.payout_amount || '0');
  const handlingFee = payoutAmount * 0.05; // 5% handling fee
  const totalAmount = payoutAmount + handlingFee;

  const { mutate: createPaymentIntent } = apiPaymentHooks.useCreatePaymentIntent(
    {
      headers: {
        'X-Client-ID': 'kryptogo-xyz',
      },
    },
    {
      onSuccess: () => {
        showToast(t('kgstudio.payment.create-intent-success'), 'success');
        router.push('/payment/payout-list');
      },
      onError: (error) => {
        if (isApiError(error)) {
          showToast(error.message || t('kgstudio.payment.create-intent-error'), 'error');
        } else {
          showToast(t('kgstudio.payment.create-intent-error'), 'error');
        }
      },
    },
  );

  const onSubmit = async (data: CreatePayoutFormValues) => {
    if (!orgId) {
      showToast(t('kgstudio.payment.organization-required'), 'error');
      return;
    }

    setIsLoading(true);
    try {
      // Token addresses for different chains
      const tokenAddresses = {
        USDC: '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913', // Base USDC
        USDT: '0xfde4C96c8593536E31F229EA8f37b2ADa2699bb2', // Base USDT
      };

      // Map network to chain_id
      const chainIdMap = {
        arbitrum: 'arb' as const,
        base: 'base' as const,
        optimism: 'optimism' as const,
      };

      // Map the form data to the API request format
      const requestData = {
        chain_id: chainIdMap[data.network] || 'arb',
        token_address: tokenAddresses[data.token],
        amount: data.payout_amount,
        pricing_mode: data.pricing_mode,
        pay_token: data.token,
        payout_target_address: data.recipient_wallet,
        group_key: data.payout_id,
      };

      createPaymentIntent(requestData);
    } catch (error) {
      console.error('Error creating payout:', error);
      showToast(t('kgstudio.payment.create-intent-error'), 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <div className="mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <Card className="p-8">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Recipient Details Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Recipient Details</h2>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormInput
                    control={form.control}
                    name="recipient_name"
                    title="Recipient Name"
                    placeholder="Organization Name"
                    required
                  />
                  <FormInput
                    control={form.control}
                    name="recipient_email"
                    title="Contact Email"
                    placeholder="<EMAIL>"
                    type="email"
                    required
                  />
                </div>
              </div>

              {/* Your Details Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Your Details</h2>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormInput
                    control={form.control}
                    name="organization_name"
                    title="Your Organization Name"
                    placeholder="Organization Name"
                    required
                  />
                  <FormInput
                    control={form.control}
                    name="organization_email"
                    title="Your Organization Contact Email"
                    placeholder="<EMAIL>"
                    type="email"
                    required
                  />
                </div>
              </div>

              {/* Payout Details Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Payout Details</h2>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormInput
                    control={form.control}
                    name="payout_id"
                    title="Payout ID"
                    placeholder="Auto-generated"
                    disabled
                    required
                  />
                  <FormInput
                    control={form.control}
                    name="payout_due_date"
                    title="Payout Due Date"
                    placeholder="YYYY/MM/DD"
                    type="date"
                    required
                  />
                </div>
                <FormTextarea
                  control={form.control}
                  name="description"
                  title="Description"
                  placeholder="Enter invoice ID or any other descriptions about this payout"
                  className="min-h-[120px]"
                  required
                />
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">Attachment</label>
                  <FormMediaUploader
                    control={form.control}
                    name="attachment"
                    title=""
                    dropZoneDesc="Upload invoice or any attachment here"
                    acceptType={['pdf', 'png', 'jpg', 'jpeg', 'webp']}
                    maxFileSize={10 * 1024 * 1024} // 10MB
                    dropZoneRatio="16/9"
                    dropZoneWidth={400}
                  />
                </div>
              </div>

              {/* Payout Method Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Payout Method</h2>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormDropdown
                    control={form.control}
                    name="network"
                    title="Network"
                    options={[
                      { label: 'Arbitrum', value: 'arbitrum' },
                      { label: 'Base', value: 'base' },
                      { label: 'Optimism', value: 'optimism' },
                    ]}
                    required
                  />
                  <FormDropdown
                    control={form.control}
                    name="token"
                    title="Token"
                    options={[
                      { label: 'USDC', value: 'USDC' },
                      { label: 'USDT', value: 'USDT' },
                    ]}
                    required
                  />
                </div>
                <FormInput
                  control={form.control}
                  name="recipient_wallet"
                  title="Recipient Receiving Wallet"
                  placeholder="0xDaF09F6E6cEa6E08f4c7C32D4f71b54bdA02913"
                  required
                />
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormInput
                    control={form.control}
                    name="payout_amount"
                    title="Payout Amount"
                    placeholder="2000"
                    type="number"
                    step="0.01"
                    required
                  />
                  <FormDropdown
                    control={form.control}
                    name="currency"
                    title="Currency"
                    options={[
                      { label: 'USD', value: 'USD' },
                      { label: 'TWD', value: 'TWD' },
                    ]}
                    required
                  />
                </div>
              </div>

              {/* Payout Summary Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Payout Summary</h2>
                <div className="space-y-4 rounded-lg bg-gray-50 p-6">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Payout Amount</span>
                    <span className="font-medium">
                      {payoutAmount.toFixed(2)} {watchedValues.currency}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">KryptoGO Handling Fee (5%)</span>
                    <span className="font-medium">
                      {handlingFee.toFixed(2)} {watchedValues.currency}
                    </span>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold">Total Amount</span>
                      <span className="text-lg font-semibold">
                        {totalAmount.toFixed(2)} {watchedValues.currency}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-between border-t border-gray-200 pt-6">
                <Button type="button" variant="secondary" onClick={handleCancel} disabled={isLoading}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  loading={isLoading}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Create Payout
                </Button>
              </div>
            </form>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default CreatePayoutPage;
