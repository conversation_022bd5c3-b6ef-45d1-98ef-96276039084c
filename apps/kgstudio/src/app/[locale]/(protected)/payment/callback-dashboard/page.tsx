'use client';

import { Play, RotateCcw, Search, Filter, Info, ChevronDown, ChevronUp, TestTube } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import qs from 'qs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import JsonView from 'react18-json-view';
import 'react18-json-view/src/style.css';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FilterItem, FormFilterGroup, FormInput } from '@/app/_common/components/form';
import { useDebounce, usePageHeader } from '@/app/_common/hooks';
import { ApiResponse, ApiResponseWithPaging, isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { cn, removeEmptyArray, removeEmptyString } from '@/app/_common/lib/utils';
import { zodFilterSchema } from '@/app/_common/lib/zod';
import { useOrganizationStore } from '@/app/_common/store';
import { usePathname, useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, DataTable, Form, Input, Modal, Tooltip, useDataTable, Badge, Textarea } from '@kryptogo/2b';
import { ColumnDef, getCoreRowModel, TableOptions, useReactTable } from '@tanstack/react-table';

// Callback log types based on backend API
interface CallbackLog {
  id: string;
  payment_intent_id: string | null;
  client_id: string | null;
  org_id: number | null;
  url: string;
  type: 'payment' | 'test'; // Backend enum: payment, test
  status: 'success' | 'failed'; // Backend enum: success, failed
  status_code: number | null;
  callback_payload: string;
  error: string | null;
  duration: number; // Duration in milliseconds
  created_at: string;
}

// Filter schema based on backend types
const CallbackFilterSchema = z
  .object({
    payment_intent_id: z.string().optional(),
    type: z.array(z.enum(['payment', 'test'])).optional(), // Backend enum: payment, test
    status: z.array(z.enum(['success', 'failed'])).optional(), // Backend enum: success, failed
    from_date: z.string().optional(),
    to_date: z.string().optional(),
  })
  .strip();
type CallbackFilter = z.infer<typeof CallbackFilterSchema>;

// Test callback schema based on backend TestCallbackRequest
const TestCallbackSchema = z.object({
  url: z.string().url('Please enter a valid URL'),
  payload: z.record(z.any()).default({}), // map[string]any in backend
  client_id: z.string().optional(), // Set from header in backend
  sign_payload: z.boolean().default(false),
});
type TestCallbackData = z.infer<typeof TestCallbackSchema>;

// Status badge component
const StatusBadge = ({ status }: { status: CallbackLog['status'] }) => {
  const variant = {
    success: 'green',
    failed: 'red',
  }[status] as 'green' | 'red';

  return <Badge variant={variant}>{status}</Badge>;
};

// Type badge component - no longer needed but keeping for filter compatibility
const TypeBadge = ({ type }: { type: CallbackLog['type'] }) => {
  const variant = {
    payment: 'blue',
    test: 'grey',
  }[type] as 'blue' | 'grey';

  return <Badge variant={variant}>{type}</Badge>;
};

// Payment Status Badge component for parsing payment status from callback payload
const PaymentStatusBadge = ({ callbackPayload }: { callbackPayload: string }) => {
  try {
    const payload = JSON.parse(callbackPayload);
    const paymentStatus = payload.status as string;
    if (!paymentStatus) return <span className="text-gray-400">-</span>;

    const variantMap: Record<string, 'green' | 'yellow' | 'red' | 'grey' | 'blue'> = {
      completed: 'green',
      pending: 'yellow',
      failed: 'red',
      expired: 'grey',
      refunded: 'blue',
      cancelled: 'grey',
    };

    const variant = variantMap[paymentStatus] || 'grey';

    return <Badge variant={variant}>{paymentStatus}</Badge>;
  } catch {
    return <span className="text-gray-400">-</span>;
  }
};

const CallbackDashboard = () => {
  const pathname = usePathname();
  const router = useRouter();
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.payment.callback-dashboard') });
  const params = useSearchParams();
  const parsedParams = zodFilterSchema(CallbackFilterSchema).parse(qs.parse(decodeURIComponent(params.toString())));
  const orgId = useOrganizationStore((state) => state.orgId);

  const [oauthClients, setOAuthClients] = useState<Array<{ id: string; name: string }>>([]);
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [callbackLogs, setCallbackLogs] = useState<CallbackLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedLog, setSelectedLog] = useState<CallbackLog | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showTestModal, setShowTestModal] = useState(false);
  const [isTestingCallback, setIsTestingCallback] = useState(false);
  const [showOptionalFields, setShowOptionalFields] = useState(false);

  const form = useForm<CallbackFilter>({
    defaultValues: {
      payment_intent_id: undefined,
      type: undefined,
      status: undefined,
      from_date: undefined,
      to_date: undefined,
    },
    values: {
      ...parsedParams,
    },
    mode: 'onChange',
    resolver: zodResolver(CallbackFilterSchema),
  });

  const testForm = useForm<TestCallbackData>({
    defaultValues: {
      url: '',
      payload: {
        payment_intent_id: 'pi_example123',
        client_id: '',
        fiat_amount: '100.00',
        fiat_currency: 'USD',
        crypto_amount: '0.1',
        status: 'completed',
        pricing_mode: 'fixed',
      },
      client_id: '', // Will be auto-filled from selected client
      sign_payload: false,
    },
    resolver: zodResolver(TestCallbackSchema),
  });

  const isFilterSelected = useMemo(() => {
    return Object.values(removeEmptyString(removeEmptyArray(form.watch()))).some((v) => v !== undefined);
  }, [form]);

  // Filter items for the form - based on backend types
  const filterItems: FilterItem<CallbackFilter>[] = [
    {
      name: 'type',
      subject: t('kgstudio.payment.type'),
      type: 'checkbox',
      options: [
        { value: 'payment', label: t('kgstudio.payment.type-payment') },
        { value: 'test', label: t('kgstudio.payment.type-test') },
      ],
    },
    {
      name: 'status',
      subject: t('kgstudio.payment.callback-result'),
      type: 'checkbox',
      options: [
        { value: 'success', label: t('kgstudio.payment.result-success') },
        { value: 'failed', label: t('kgstudio.payment.result-failed') },
      ],
    },
    {
      names: ['from_date', 'to_date'],
      subject: t('kgstudio.payment.date-range'),
      type: 'date-range',
    },
  ];

  // Table columns - memoized to prevent re-renders
  const columns: ColumnDef<CallbackLog>[] = useMemo(
    () => [
      {
        accessorKey: 'id',
        header: 'ID',
        size: 120,
        cell: ({ row }) => (
          <div className="font-mono text-sm">
            {row.original.type === 'test' && (
              <div className="mb-1 flex items-center gap-1">
                <TestTube size={12} className="text-blue-600" />
                <span className="text-xs font-medium text-blue-600">{t('kgstudio.payment.test')}</span>
              </div>
            )}
            {row.original.id.slice(0, 8)}...
          </div>
        ),
      },
      {
        accessorKey: 'payment_intent_id',
        header: t('kgstudio.payment.intent-id'),
        size: 150,
        cell: ({ row }) => (
          <div className="font-mono text-sm">
            {row.original.payment_intent_id ? `${row.original.payment_intent_id.slice(0, 8)}...` : '-'}
          </div>
        ),
      },
      {
        accessorKey: 'client_id',
        header: t('kgstudio.payment.client-id'),
        size: 150,
        cell: ({ row }) => (
          <div className="font-mono text-sm">
            {row.original.client_id ? `${row.original.client_id.slice(0, 8)}...` : '-'}
          </div>
        ),
      },
      {
        accessorKey: 'payment_status',
        header: t('kgstudio.payment.payment-status'),
        size: 120,
        cell: ({ row }) => <PaymentStatusBadge callbackPayload={row.original.callback_payload} />,
      },
      {
        accessorKey: 'status',
        header: t('kgstudio.payment.callback-result'),
        size: 100,
        cell: ({ row }) => <StatusBadge status={row.original.status} />,
      },
      {
        accessorKey: 'url',
        header: t('kgstudio.payment.webhook-url'),
        size: 200,
        cell: ({ row }) => (
          <Tooltip>
            <Tooltip.Trigger asChild>
              <div className="max-w-[180px] truncate">{row.original.url}</div>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>{row.original.url}</p>
            </Tooltip.Content>
          </Tooltip>
        ),
      },
      {
        accessorKey: 'status_code',
        header: t('kgstudio.payment.http-code'),
        size: 100,
        cell: ({ row }) => (
          <div
            className={cn(
              'font-mono text-sm',
              row.original.status_code && row.original.status_code >= 200 && row.original.status_code < 300
                ? 'text-green-600'
                : row.original.status_code
                  ? 'text-red-600'
                  : 'text-gray-400',
            )}
          >
            {row.original.status_code || '-'}
          </div>
        ),
      },
      {
        accessorKey: 'duration',
        header: t('kgstudio.payment.duration'),
        size: 100,
        cell: ({ row }) => <div className="text-sm">{row.original.duration}ms</div>,
      },
      {
        accessorKey: 'created_at',
        header: t('kgstudio.payment.timestamp'),
        size: 150,
        cell: ({ row }) => <div className="text-sm">{new Date(row.original.created_at).toLocaleString()}</div>,
      },
      {
        id: 'actions',
        header: '',
        size: 60,
        cell: ({ row }) => (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedLog(row.original);
              setShowDetailModal(true);
            }}
            className="h-8 w-8 p-0"
          >
            <Info size={16} className="text-gray-500" />
          </Button>
        ),
      },
    ],
    [t],
  );

  // Initialize table with proper configuration
  const table = useReactTable<CallbackLog>({
    data: callbackLogs || [],
    columns: columns || [],
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: false, // Disable row selection to avoid getSelectedRowModel issues
    meta: {
      rowHover: true,
      getRowClassName: (row: CallbackLog) => {
        // Add dashed border and subtle background for test callbacks to indicate they're test data
        return row.type === 'test' ? 'bg-blue-50 border-2 border-dashed border-blue-300' : '';
      },
    },
  });

  // Custom hook to safely extract table state without row selection
  const tableState = useMemo(() => {
    if (!table) return { page_number: 1, page_size: 10 };
    const state = table.getState();
    return {
      page_number: state.pagination.pageIndex + 1,
      page_size: state.pagination.pageSize,
    };
  }, [table]);
  const { page_number, page_size } = tableState;

  // Fetch OAuth clients
  useEffect(() => {
    const fetchOAuthClients = async () => {
      if (!orgId) {
        return;
      }

      try {
        const response = await internalAxiosInstance.get<
          ApiResponse<
            Array<{
              client_id: string;
              client_name: string;
            }>
          >
        >(`/studio/organization/${orgId}/oauth_clients`);
        const clients = response.data.data.map((client: { client_id: string; client_name: string }) => ({
          id: client.client_id,
          name: client.client_name,
        }));
        setOAuthClients(clients);
      } catch (error) {
        if (isApiError(error)) {
          showToast(t('kgstudio.payment.error-loading-oauth-clients'), 'error');
        }
      }
    };

    fetchOAuthClients();
  }, [orgId, t]);

  // Auto-fill client_id in test form when selectedClientId changes
  useEffect(() => {
    if (selectedClientId) {
      testForm.setValue('client_id', selectedClientId);
      testForm.setValue('payload.client_id', selectedClientId);
    }
  }, [selectedClientId, testForm]);

  // Fetch callback logs
  const fetchCallbackLogs = useCallback(async () => {
    if (!orgId) return;

    setIsLoading(true);
    try {
      const filterValues = form.getValues();
      const queryParams = new URLSearchParams();

      // Add client_id as query parameter if selected
      if (selectedClientId) {
        queryParams.set('client_id', selectedClientId);
      }

      if (filterValues.payment_intent_id) {
        queryParams.set('payment_intent_id', filterValues.payment_intent_id);
      }
      if (filterValues.type?.length) {
        filterValues.type.forEach((type) => queryParams.append('type', type));
      }
      if (filterValues.status?.length) {
        filterValues.status.forEach((status) => queryParams.append('status', status));
      }
      if (filterValues.from_date) {
        queryParams.set('from_date', filterValues.from_date);
      }
      if (filterValues.to_date) {
        queryParams.set('to_date', filterValues.to_date);
      }

      queryParams.set('page_number', page_number.toString());
      queryParams.set('page_size', page_size.toString());

      const apiUrl = `/studio/organization/${orgId}/payment/callbacks?${queryParams.toString()}`;

      const response = await internalAxiosInstance.get<ApiResponseWithPaging<CallbackLog[]>>(apiUrl);

      setCallbackLogs(response.data.data || []);
      setTotalCount(response.data.paging?.total_count || 0);
    } catch (error) {
      if (isApiError(error)) {
        showToast(error.message || t('kgstudio.payment.error-loading-callbacks'), 'error');
      } else {
        showToast(t('kgstudio.payment.error-loading-callbacks'), 'error');
      }
    } finally {
      setIsLoading(false);
    }
  }, [page_number, page_size, selectedClientId, orgId, form, t]);

  // Test callback
  const testCallback = async (data: TestCallbackData) => {
    if (!orgId) {
      showToast(t('kgstudio.payment.organization-required'), 'error');
      return;
    }

    setIsTestingCallback(true);
    try {
      const payload = {
        ...data.payload,
        timestamp: Math.floor(Date.now() / 1000),
        signature: 'test_signature_' + Math.random().toString(36).substr(2, 9),
      };

      await internalAxiosInstance.post<ApiResponse<CallbackLog>>(
        `/studio/organization/${orgId}/payment/callbacks/test`,
        {
          url: data.url,
          payload,
          client_id: data.client_id,
          sign_payload: data.sign_payload,
        },
      );

      showToast(t('kgstudio.payment.test-callback-sent'), 'success');
      setShowTestModal(false);
      testForm.reset();
      fetchCallbackLogs();
    } catch (error) {
      if (isApiError(error)) {
        showToast(error.message || t('kgstudio.payment.test-callback-error'), 'error');
      } else {
        showToast(t('kgstudio.payment.test-callback-error'), 'error');
      }
    } finally {
      setIsTestingCallback(false);
    }
  };

  // Fetch data on component mount and filter changes
  useEffect(() => {
    fetchCallbackLogs();
  }, [fetchCallbackLogs]);

  // Watch form changes and update URL
  useEffect(() => {
    const subscription = form.watch((value) => {
      const queryString = qs.stringify(removeEmptyString(removeEmptyArray(value)), {
        skipNulls: true,
        encode: false,
      });
      router.push(`${pathname}?${queryString}`, { scroll: false });

      // Reset to first page when filters change
      if (table) {
        table.setPageIndex(0);
      }

      // Fetch data when filters change
      if (orgId) {
        const filterValues = form.getValues();
        const queryParams = new URLSearchParams();

        // Add client_id as query parameter if selected
        if (selectedClientId) {
          queryParams.set('client_id', selectedClientId);
        }

        if (filterValues.payment_intent_id) {
          queryParams.set('payment_intent_id', filterValues.payment_intent_id);
        }
        if (filterValues.type?.length) {
          filterValues.type.forEach((type) => queryParams.append('type', type));
        }
        if (filterValues.status?.length) {
          filterValues.status.forEach((status) => queryParams.append('status', status));
        }
        if (filterValues.from_date) {
          queryParams.set('from_date', filterValues.from_date);
        }
        if (filterValues.to_date) {
          queryParams.set('to_date', filterValues.to_date);
        }

        queryParams.set('page_number', '1');
        queryParams.set('page_size', page_size.toString());

        setIsLoading(true);
        internalAxiosInstance
          .get<ApiResponseWithPaging<CallbackLog[]>>(
            `/studio/organization/${orgId}/payment/callbacks?${queryParams.toString()}`,
          )
          .then((response) => {
            setCallbackLogs(response.data.data || []);
            setTotalCount(response.data.paging?.total_count || 0);
          })
          .catch((error) => {
            if (isApiError(error)) {
              showToast(error.message || t('kgstudio.payment.error-loading-callbacks'), 'error');
            } else {
              showToast(t('kgstudio.payment.error-loading-callbacks'), 'error');
            }
          })
          .finally(() => {
            setIsLoading(false);
          });
      }
    });
    return () => subscription.unsubscribe();
  }, [pathname, router, table, form, selectedClientId, orgId, page_size, t]);

  // Separate effect for initial data fetch and pagination changes
  useEffect(() => {
    if (orgId) {
      fetchCallbackLogs();
    }
  }, [orgId, selectedClientId, page_number, fetchCallbackLogs]);

  return (
    <>
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <select
              value={selectedClientId}
              onChange={(e) => setSelectedClientId(e.target.value)}
              className="rounded border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">{t('kgstudio.payment.all-clients')}</option>
              {oauthClients.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.name}
                </option>
              ))}
            </select>
            <Button variant="primary" icon={<Play size={16} />} onClick={() => setShowTestModal(true)}>
              {t('kgstudio.payment.test-callback')}
            </Button>
            <Button
              variant="secondary"
              icon={<RotateCcw size={16} />}
              onClick={() => fetchCallbackLogs()}
              disabled={isLoading}
            >
              {t('common.refresh')}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Form {...form}>
          <div className="flex flex-wrap items-center gap-3">
            <FormFilterGroup
              control={form.control}
              items={filterItems}
              {...(isFilterSelected && {
                onClearFilter: () => {
                  params.toString() ? router.push(pathname) : form.reset();
                },
              })}
            />
            <div className="ml-auto">
              <FormInput
                control={form.control}
                name="payment_intent_id"
                placeholder={t('kgstudio.payment.search-intent')}
                className="w-64"
                withoutMessage
              />
            </div>
          </div>
        </Form>

        {/* Table */}
        <Card className="overflow-x-auto !p-0">
          <DataTable
            table={table}
            isLoading={isLoading}
            dataLength={totalCount}
            onRowClick={(row) => {
              setSelectedLog(row);
              setShowDetailModal(true);
            }}
          />
        </Card>
      </div>

      {/* Callback Detail Modal - Balanced width */}
      <Modal open={showDetailModal} onOpenChange={setShowDetailModal}>
        <Modal.Content className="max-h-[90vh] w-[50vw] max-w-[50vw] overflow-hidden">
          <Modal.Header className="border-b px-8 py-6">
            <Modal.Title className="flex items-center gap-3">
              <Info size={24} className="text-blue-600" />
              <span className="text-xl font-semibold">{t('kgstudio.payment.callback-details')}</span>
            </Modal.Title>
          </Modal.Header>
          {selectedLog && (
            <div className="flex h-[80vh]">
              {/* Left Panel - Event Details */}
              <div className="w-2/5 overflow-y-auto border-r bg-gray-50 p-6">
                <h3 className="mb-6 text-lg font-semibold text-gray-900">{t('kgstudio.payment.event-details')}</h3>

                <div className="space-y-6">
                  {/* Callback ID */}
                  <div>
                    <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                      {t('kgstudio.payment.callback-id')}
                    </label>
                    <div className="rounded-lg border border-gray-200 bg-white p-3 font-mono text-sm shadow-sm">
                      <div className="break-all">{selectedLog.id}</div>
                    </div>
                  </div>

                  {/* Intent ID */}
                  {selectedLog.payment_intent_id && (
                    <div>
                      <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                        {t('kgstudio.payment.intent-id')}
                      </label>
                      <div className="rounded-lg border border-gray-200 bg-white p-3 font-mono text-sm shadow-sm">
                        <div className="break-all">{selectedLog.payment_intent_id}</div>
                      </div>
                    </div>
                  )}

                  {/* Client ID */}
                  {selectedLog.client_id && (
                    <div>
                      <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                        {t('kgstudio.payment.client-id')}
                      </label>
                      <div className="rounded-lg border border-gray-200 bg-white p-3 font-mono text-sm shadow-sm">
                        <div className="break-all">{selectedLog.client_id}</div>
                      </div>
                    </div>
                  )}

                  {/* Callback Type and Status - Side by side */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                        {t('kgstudio.payment.type')}
                      </label>
                      <div className="flex">
                        <TypeBadge type={selectedLog.type} />
                      </div>
                    </div>

                    <div>
                      <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                        {t('kgstudio.payment.callback-result')}
                      </label>
                      <div className="flex">
                        <StatusBadge status={selectedLog.status} />
                      </div>
                    </div>
                  </div>

                  {/* HTTP Status Code and Duration - Side by side */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                        {t('kgstudio.payment.http-code')}
                      </label>
                      <div
                        className={cn(
                          'rounded-lg border bg-white p-3 text-center font-mono text-lg font-bold shadow-sm',
                          selectedLog.status_code && selectedLog.status_code >= 200 && selectedLog.status_code < 300
                            ? 'border-green-200 bg-green-50 text-green-700'
                            : selectedLog.status_code
                              ? 'border-red-200 bg-red-50 text-red-700'
                              : 'border-gray-200 text-gray-400',
                        )}
                      >
                        {selectedLog.status_code || 'N/A'}
                      </div>
                    </div>

                    <div>
                      <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                        {t('kgstudio.payment.duration')}
                      </label>
                      <div className="rounded-lg border border-gray-200 bg-white p-3 text-center font-mono text-lg font-semibold shadow-sm">
                        {selectedLog.duration}ms
                      </div>
                    </div>
                  </div>

                  {/* URL */}
                  <div>
                    <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                      {t('kgstudio.payment.webhook-url')}
                    </label>
                    <div className="rounded-lg border border-gray-200 bg-white p-3 font-mono text-sm shadow-sm">
                      <div className="break-all">{selectedLog.url}</div>
                    </div>
                  </div>

                  {/* Timestamp */}
                  <div>
                    <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-gray-600">
                      {t('kgstudio.payment.timestamp')}
                    </label>
                    <div className="rounded-lg border border-gray-200 bg-white p-3 text-sm shadow-sm">
                      {new Date(selectedLog.created_at).toLocaleString()}
                    </div>
                  </div>

                  {/* Error */}
                  {selectedLog.error && (
                    <div>
                      <label className="mb-2 block text-sm font-semibold uppercase tracking-wider text-red-600">
                        {t('common.error')}
                      </label>
                      <div className="rounded-lg border border-red-200 bg-red-50 p-3 text-sm text-red-700 shadow-sm">
                        <div className="break-words">{selectedLog.error}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Panel - Payload */}
              <div className="w-3/5 overflow-hidden p-6">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">{t('kgstudio.payment.callback-payload')}</h3>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(selectedLog.callback_payload);
                      showToast(t('common.copied'), 'success');
                    }}
                    className="flex items-center gap-2"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                    <span>{t('common.copy')}</span>
                  </Button>
                </div>
                <div
                  className="h-full max-h-[calc(80vh-6rem)] overflow-auto rounded-lg border border-gray-300 bg-gray-900 p-4 shadow-lg"
                  style={{ height: 'calc(100% - 5rem)' }}
                >
                  {(() => {
                    try {
                      return (
                        <JsonView
                          src={JSON.parse(selectedLog.callback_payload)}
                          theme="vscode"
                          displaySize="collapsed"
                          collapsed={2}
                          style={{
                            fontSize: '13px',
                            lineHeight: '1.5',
                            fontFamily:
                              'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                            backgroundColor: 'transparent',
                            overflow: 'visible',
                          }}
                        />
                      );
                    } catch {
                      return (
                        <pre className="whitespace-pre-wrap break-words text-sm leading-relaxed text-green-400">
                          {selectedLog.callback_payload}
                        </pre>
                      );
                    }
                  })()}
                </div>
              </div>
            </div>
          )}
        </Modal.Content>
      </Modal>

      {/* Test Callback Modal */}
      <Modal open={showTestModal} onOpenChange={setShowTestModal}>
        <Modal.Content className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <Modal.Header>
            <Modal.Title>{t('kgstudio.payment.test-callback')}</Modal.Title>
          </Modal.Header>
          <Form {...testForm}>
            <form onSubmit={testForm.handleSubmit(testCallback)} className="space-y-4">
              {/* Callback URL */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  {t('kgstudio.payment.callback-url')}
                </label>
                <FormInput
                  control={testForm.control}
                  name="url"
                  placeholder="https://your-domain.com/webhook/callback"
                  required
                  withoutMessage
                />
              </div>

              {/* Payload Fields */}
              <div className="space-y-6">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="text-lg font-semibold text-gray-800">{t('kgstudio.payment.callback-payload')}</h3>
                  <p className="mt-1 text-sm text-gray-600">
                    Configure the payload data that will be sent to your webhook
                  </p>
                </div>

                {/* Required Fields Section */}
                <div className="ml-6 space-y-4">
                  <div className="border-l-4 border-red-400 pl-4">
                    <h4 className="flex items-center gap-2 text-base font-semibold text-gray-700">
                      <span className="inline-block h-2 w-2 rounded-full bg-red-400"></span>
                      {t('kgstudio.payment.required-fields')}
                    </h4>
                    <p className="mt-1 text-sm text-gray-600">These fields are mandatory for the callback payload</p>
                  </div>

                  <div className="ml-6 space-y-4 rounded-lg border border-red-200 bg-red-50 p-6">
                    <div className="grid grid-cols-2 gap-4">
                      {/* Payment Intent ID */}
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          {t('kgstudio.payment.payment-intent-id')}
                        </label>
                        <FormInput
                          control={testForm.control}
                          name="payload.payment_intent_id"
                          placeholder="pi_example123"
                          required
                          withoutMessage
                        />
                      </div>

                      {/* Client ID */}
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          {t('kgstudio.payment.client-id')}
                        </label>
                        <FormInput
                          control={testForm.control}
                          name="client_id"
                          placeholder="client_123"
                          required
                          withoutMessage
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {/* Fiat Amount */}
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          {t('kgstudio.payment.fiat-amount')}
                        </label>
                        <FormInput
                          control={testForm.control}
                          name="payload.fiat_amount"
                          placeholder="100.00"
                          required
                          withoutMessage
                        />
                      </div>

                      {/* Fiat Currency */}
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          {t('kgstudio.payment.fiat-currency')}
                        </label>
                        <FormInput
                          control={testForm.control}
                          name="payload.fiat_currency"
                          placeholder="USD"
                          required
                          withoutMessage
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {/* Crypto Amount */}
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          {t('kgstudio.payment.crypto-amount')}
                        </label>
                        <FormInput
                          control={testForm.control}
                          name="payload.crypto_amount"
                          placeholder="0.1"
                          required
                          withoutMessage
                        />
                      </div>

                      {/* Status */}
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          {t('kgstudio.payment.status')}
                        </label>
                        <select
                          {...testForm.register('payload.status')}
                          className="w-full rounded border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
                        >
                          <option value="pending">{t('kgstudio.payment.status-pending')}</option>
                          <option value="completed">{t('kgstudio.payment.status-completed')}</option>
                          <option value="failed">{t('kgstudio.payment.status-failed')}</option>
                          <option value="expired">{t('kgstudio.payment.status-expired')}</option>
                          <option value="refunded">{t('kgstudio.payment.status-refunded')}</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="mb-2 block text-sm font-medium text-gray-700">
                        {t('kgstudio.payment.pricing-mode')}
                      </label>
                      <select
                        {...testForm.register('payload.pricing_mode')}
                        className="w-1/2 rounded border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
                      >
                        <option value="fixed">{t('kgstudio.payment.pricing-mode-fixed')}</option>
                        <option value="dynamic">{t('kgstudio.payment.pricing-mode-dynamic')}</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Optional Fields Section - Collapsible */}
                <div className="ml-6 space-y-4">
                  <div className="border-l-4 border-gray-400 pl-4">
                    <h4 className="flex items-center gap-2 text-base font-semibold text-gray-700">
                      <span className="inline-block h-2 w-2 rounded-full bg-gray-400"></span>
                      {t('kgstudio.payment.optional-fields')}
                    </h4>
                    <p className="mt-1 text-sm text-gray-600">Additional fields that can be included in the payload</p>
                  </div>

                  <div className="ml-6">
                    <button
                      type="button"
                      onClick={() => setShowOptionalFields(!showOptionalFields)}
                      className="flex w-full items-center justify-between rounded-lg border border-gray-300 bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100"
                    >
                      <span className="flex items-center gap-2">
                        <ChevronDown
                          size={16}
                          className={cn('transform transition-transform', showOptionalFields && 'rotate-180')}
                        />
                        {showOptionalFields ? 'Hide Optional Fields' : 'Show Optional Fields'}
                      </span>
                      <Badge variant="grey">{showOptionalFields ? 'Expanded' : 'Collapsed'}</Badge>
                    </button>

                    {showOptionalFields && (
                      <div className="mt-4 space-y-4 rounded-lg border border-gray-300 bg-gray-50 p-6">
                        <div className="grid grid-cols-2 gap-4">
                          {/* Payment Chain ID */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.payment-chain-id')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.payment_chain_id"
                              placeholder="1"
                              withoutMessage
                            />
                          </div>

                          {/* Symbol */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.symbol')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.symbol"
                              placeholder="ETH"
                              withoutMessage
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          {/* Payment TX Hash */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.payment-tx-hash')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.payment_tx_hash"
                              placeholder="0x..."
                              withoutMessage
                            />
                          </div>

                          {/* Payer Address */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.payer-address')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.payer_address"
                              placeholder="0x..."
                              withoutMessage
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          {/* Received Amount */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.received-amount')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.received_amount"
                              placeholder="0.1"
                              withoutMessage
                            />
                          </div>

                          {/* Crypto Price */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.crypto-price')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.crypto_price"
                              placeholder="2000.00"
                              withoutMessage
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          {/* Group Key */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.group-key')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.group_key"
                              placeholder="group_123"
                              withoutMessage
                            />
                          </div>

                          {/* KG Deep Link */}
                          <div>
                            <label className="mb-2 block text-sm font-medium text-gray-700">
                              {t('kgstudio.payment.kg-deep-link')}
                            </label>
                            <FormInput
                              control={testForm.control}
                              name="payload.kg_deep_link"
                              placeholder="kryptogo://..."
                              withoutMessage
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Sign Payload */}
              <div className="flex items-center space-x-2">
                <input type="checkbox" {...testForm.register('sign_payload')} id="sign_payload" />
                <label htmlFor="sign_payload" className="text-sm">
                  {t('kgstudio.payment.sign-payload')}
                </label>
              </div>

              <Modal.Footer className="flex justify-between">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setShowTestModal(false)}
                  disabled={isTestingCallback}
                >
                  {t('common.cancel')}
                </Button>
                <Button type="submit" variant="primary" disabled={isTestingCallback}>
                  {isTestingCallback ? t('kgstudio.payment.sending') : t('kgstudio.payment.send-test')}
                </Button>
              </Modal.Footer>
            </form>
          </Form>
        </Modal.Content>
      </Modal>
    </>
  );
};

export default CallbackDashboard;
