'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormInput } from '@/app/_common/components/form';
import { FormMediaUploader } from '@/app/_common/components/form/FormMediaUploader';
import { Media, useGcpFileUpload, usePageHeader } from '@/app/_common/hooks';
import { useGoogleAds } from '@/app/_common/hooks/useGoogleAds';
import { ApiResponse, isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { apiPaymentItemHooks } from '@/app/_common/services';
import { useAuthStore, useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON>, Card, DataTable, FormLabel, <PERSON>dal, Spinner, Toolt<PERSON> } from '@kryptogo/2b';
import { ColumnDef, getCoreRowModel, useReactTable } from '@tanstack/react-table';

interface OrderDataField {
  field_name: string;
  field_label: string;
  required: boolean;
  field_type: 'text' | 'number' | 'boolean' | 'textarea' | 'tel' | 'url' | 'email' | 'date' | 'time';
  default_value?: string | number | boolean | null;
}

interface ImageFile {
  dataURL: string;
  file?: {
    name: string;
    size: number;
    type: string;
  };
}

interface OAuthClientResponse {
  client_id: string;
  client_name: string;
  client_domain: string;
  client_type: string;
  main_logo: string;
  square_logo?: string;
  created_at?: number;
  app_store_link?: string;
  google_play_link?: string;
}

// Custom API client using the internal axios instance
const api = {
  get: <T,>(url: string) => internalAxiosInstance.get<ApiResponse<T>>(url).then((response) => response.data),
};

const formSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().optional(),
  price: z
    .string()
    .min(1, { message: 'Price is required' })
    .refine(
      (val) => {
        const num = Number(val);
        return !isNaN(num) && num >= 0.01 && num <= 10000000;
      },
      { message: 'Price must be between 0.01 and 10,000,000' },
    ),
  currency: z.string().min(1, { message: 'Currency is required' }),
  image: z
    .array(
      z.object({
        dataURL: z.string(),
        file: z
          .object({
            name: z.string(),
            size: z.number(),
            type: z.string(),
          })
          .optional(),
      }),
    )
    .optional(),
  successUrl: z.string().url().optional().or(z.literal('')),
  errorUrl: z.string().url().optional().or(z.literal('')),
  callbackUrl: z.string().url().optional().or(z.literal('')),
  successMessage: z.string().optional(),
  merchantEmail: z.string().email().optional(),
  accentColor: z.string().optional(),
  client_id: z.string().min(1, { message: 'Client ID is required' }),
  pay_token: z.string().optional(),
  chain_id: z.string().optional(),
});

export default function ProductListPage() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.payment.payment-item-list') });
  const [open, setOpen] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [openPaymentModal, setOpenPaymentModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [selectedPaymentItem, setSelectedPaymentItem] = useState<any>(null);
  const [orderDataFields, setOrderDataFields] = useState<OrderDataField[]>([]);
  const [showOrderDataForm, setShowOrderDataForm] = useState(false);
  const [currentOrderDataField, setCurrentOrderDataField] = useState<OrderDataField>({
    field_name: '',
    field_label: '',
    required: false,
    field_type: 'text',
    default_value: null,
  });
  const [oauthClients, setOAuthClients] = useState<OAuthClientResponse[]>([]);
  const orderDataFormRef = useRef<HTMLDivElement>(null);

  const orgId = useOrganizationStore((state) => state.orgId);
  const email = useAuthStore((state) => state.userInfo?.email);

  // File upload hook
  const { isLoading: fileUrlsLoading, uploadAllFiles, reset: fileUrlsReset } = useGcpFileUpload();

  // Fetch OAuth clients
  useEffect(() => {
    const fetchOAuthClients = async () => {
      if (!orgId) return;

      try {
        const response = await api.get<OAuthClientResponse[]>(`/studio/organization/${orgId}/oauth_clients`);
        setOAuthClients(response.data);
      } catch (error) {
        if (isApiError(error)) {
          showToast(t('Error loading OAuth clients'), 'error');
        }
      }
    };

    fetchOAuthClients();
  }, [orgId, t]);

  // Get payment items from API
  const {
    data: paymentItemsResponse,
    isLoading: isLoadingItems,
    refetch,
  } = apiPaymentItemHooks.useGetPaymentItems(
    {
      params: {
        org_id: orgId as number,
      },
      queries: {
        page_size: 10,
        page: 1,
        client_id: '', // Required parameter from API
      },
    },
    {
      enabled: !!orgId,
      onError: (error) => {
        if (isApiError(error)) {
          showToast(t('Error loading payment items'), 'error');
        }
      },
    },
  );

  const methods = useForm({
    defaultValues: {
      name: '',
      description: '',
      price: '',
      currency: 'TWD',
      image: [] as ImageFile[],
      successUrl: '',
      errorUrl: '',
      callbackUrl: '',
      successMessage: '',
      merchantEmail: email || '',
      accentColor: '#000000',
      client_id: '',
      pay_token: '',
      chain_id: '',
    },
    resolver: zodResolver(formSchema),
  });
  const { handleSubmit, reset, setValue, watch } = methods;

  // Watch the accentColor value to keep both inputs in sync
  const accentColor = watch('accentColor');

  const { trackConversion } = useGoogleAds();

  const handleClickOpen = (item?: any) => {
    trackConversion({
      sendTo: 'AW-17057468787/wRisCKS6_scaEPOi0cU_',
    });

    if (item) {
      // Edit mode
      setIsEditMode(true);
      setSelectedItem(item);
      setValue('name', item.name);
      setValue('description', item.description || '');
      setValue('price', item.price);
      setValue('currency', item.currency);
      setValue('successUrl', item.success_url || '');
      setValue('errorUrl', item.error_url || '');
      setValue('callbackUrl', item.callback_url || '');
      setValue('successMessage', item.success_message || '');
      setValue('client_id', item.client_id || '');
      setValue('pay_token', item.pay_token || '');
      setValue('chain_id', item.chain_id || '');
      // Set config fields if they exist
      if (item.config) {
        setValue('merchantEmail', item.config.merchantEmail || '');
        setValue('accentColor', item.config.accentColor || '#000000');
      }
      setOrderDataFields(item.order_data_fields || []);

      // Set image if exists
      if (item.image) {
        const imageFile: ImageFile = {
          dataURL: item.image,
          file: undefined,
        };
        setValue('image', [imageFile]);
      }
    } else {
      // Create mode
      setIsEditMode(false);
      setSelectedItem(null);
      // Set default merchant email
      setValue('merchantEmail', email || '');
      // Add default email field to orderDataFields
      setOrderDataFields([
        {
          field_name: 'email',
          field_label: 'Email',
          required: true,
          field_type: 'email',
          default_value: null,
        },
      ]);
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setOrderDataFields([]);
    reset();
    fileUrlsReset();
    setIsEditMode(false);
    setSelectedItem(null);
  };

  const handleOpenDeleteModal = (item: any) => {
    setSelectedItem(item);
    setOpenDelete(true);
  };

  const handleCloseDeleteModal = () => {
    setOpenDelete(false);
    setSelectedItem(null);
  };

  // Create payment item mutation
  const { mutate: createPaymentItem, isLoading: isCreating } = apiPaymentItemHooks.useCreatePaymentItem(
    {
      params: {
        org_id: orgId as number,
      },
    },
    {
      onSuccess: () => {
        refetch();
        showToast(t('kgstudio.payment.create-item-success'), 'success');
        handleClose();
      },
      onError: (error) => {
        console.error('Error creating payment item:', error);
        showToast(t('kgstudio.payment.create-item-error'), 'error');
      },
    },
  );

  // Update payment item mutation
  const { mutate: updatePaymentItem, isLoading: isUpdating } = apiPaymentItemHooks.useUpdatePaymentItem(
    {
      params: {
        org_id: orgId as number,
        id: selectedItem?.id || '',
      },
    },
    {
      onSuccess: () => {
        refetch();
        showToast(t('kgstudio.payment.update-item-success'), 'success');
        handleClose();
      },
      onError: (error) => {
        console.error('Error updating payment item:', error);
        showToast(t('kgstudio.payment.update-item-error'), 'error');
      },
    },
  );

  // Delete payment item mutation
  const { mutate: deletePaymentItem, isLoading: isDeleting } = apiPaymentItemHooks.useDeletePaymentItem(
    {
      params: {
        org_id: orgId as number,
        item_id: selectedItem?.id || '',
      },
    },
    {
      onSuccess: () => {
        console.log('deletePaymentItem success');
        refetch();
        showToast(t('kgstudio.payment.delete-item-success'), 'success');
        handleCloseDeleteModal();
      },
      onError: (error) => {
        console.error('Error deleting payment item:', error);
        showToast(t('kgstudio.payment.delete-item-error'), 'error');
      },
    },
  );

  const onSubmit = async (data: any) => {
    if (!orgId) {
      showToast(t('kgstudio.common.organization-id-required'), 'error');
      return;
    }

    let imageUrl = isEditMode ? selectedItem.image || '' : '';

    // Upload image if provided
    if (data.image && data.image.length > 0 && data.image[0].file) {
      const localFiles = data.image.filter((img: any) => !!img.file);

      if (localFiles.length > 0) {
        const filesWithKey = {
          image: localFiles as Media[],
        };

        const fileUrls = await uploadAllFiles(filesWithKey, 'payment-items');
        if (fileUrls) {
          imageUrl = fileUrls.image[0];
        }
      }
    }

    const paymentItemData = {
      name: data.name,
      description: data.description || '',
      price: data.price,
      currency: data.currency,
      image: imageUrl,
      success_url: data.successUrl,
      error_url: data.errorUrl,
      callback_url: data.callbackUrl,
      success_message: data.successMessage,
      order_data_fields: orderDataFields,
      client_id: data.client_id || '',
      pay_token: data.pay_token || '',
      chain_id: data.chain_id || '',
      config: {
        merchantEmail: data.merchantEmail,
        accentColor: data.accentColor,
      },
    };

    if (isEditMode) {
      // Update existing payment item
      updatePaymentItem(paymentItemData);
    } else {
      // Create new payment item
      trackConversion({
        sendTo: 'AW-17057468787/LTWTCNCWid4aEPOi0cU_',
      });
      createPaymentItem(paymentItemData);
    }
  };

  const onDelete = async () => {
    if (!orgId || !selectedItem) {
      showToast(t('kgstudio.payment.invalid-payment-item'), 'error');
      return;
    }

    deletePaymentItem(undefined);
  };

  const handleAddOrderDataField = () => {
    setShowOrderDataForm(true);
    setTimeout(() => {
      if (orderDataFormRef.current) {
        orderDataFormRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  const handleOrderDataFieldChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      setCurrentOrderDataField({
        ...currentOrderDataField,
        [name]: (e.target as HTMLInputElement).checked,
      });
    } else {
      setCurrentOrderDataField({
        ...currentOrderDataField,
        [name]: value,
      });
    }
  };

  const handleSaveOrderDataField = () => {
    // 驗證必填欄位
    if (!currentOrderDataField.field_name.trim()) {
      showToast(t('kgstudio.payment.field-name-required'), 'error');
      return;
    }
    if (!currentOrderDataField.field_label.trim()) {
      showToast(t('kgstudio.payment.field-label-required'), 'error');
      return;
    }

    // 檢查欄位名稱是否重複
    const isDuplicate = orderDataFields.some((field) => field.field_name === currentOrderDataField.field_name);
    if (isDuplicate) {
      showToast(t('kgstudio.payment.field-name-duplicate'), 'error');
      return;
    }

    setOrderDataFields([...orderDataFields, currentOrderDataField]);
    setCurrentOrderDataField({
      field_name: '',
      field_label: '',
      required: false,
      field_type: 'text',
      default_value: null,
    });
    setShowOrderDataForm(false);
  };

  const handleDeleteOrderDataField = (index: number) => {
    const updatedFields = [...orderDataFields];
    updatedFields.splice(index, 1);
    setOrderDataFields(updatedFields);
  };

  const getPaymentUrl = (itemId: string) => {
    const isDev =
      process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT === 'local' ||
      process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT === 'development';
    return `https://pay.kryptogo.com/invoice/${itemId}${isDev ? '?isDev=true' : ''}`;
  };

  // Function to handle payment URL
  const handlePaymentUrl = (itemId: string, copyOnly = false) => {
    const paymentUrl = getPaymentUrl(itemId);

    if (copyOnly) {
      navigator.clipboard
        .writeText(paymentUrl)
        .then(() => showToast(t('kgstudio.common.successfully-copied'), 'success'))
        .catch(() => showToast(t('kgstore.common.error'), 'error'));
    } else {
      window.open(paymentUrl, '_blank');
    }
  };

  // Function to open payment modal
  const handleOpenPaymentModal = (item: any) => {
    setSelectedPaymentItem(item);
    setOpenPaymentModal(true);
  };

  // Function to close payment modal
  const handleClosePaymentModal = () => {
    setOpenPaymentModal(false);
    setSelectedPaymentItem(null);
  };

  // Function to generate payment button code
  const generatePaymentButtonCode = (item: any) => {
    const paymentUrl = getPaymentUrl(item.id);

    return `<a href="${paymentUrl}" target="_blank" style="display: inline-flex; align-items: center; background-color: #1b2559; color: white; padding: 12px 20px; text-decoration: none; border-radius: 8px; font-family: Arial, sans-serif; font-weight: 600; border: none; cursor: pointer; transition: background-color 0.2s ease; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.backgroundColor='#2a3a6b'" onmouseout="this.style.backgroundColor='#1b2559'">
  <span style="font-size: 14px; font-weight: 600;">Buy with</span>
  <img src="https://www.kryptogo.com/docs/img/kg_logo.svg" alt="KryptoGo" style="height: 20px; width: auto; margin-left: 8px; margin-right: 4px;">
  <span style="font-size: 14px; font-weight: 600;">Pay</span>
</a>`;
  };

  // Function to copy payment button code
  const handleCopyPaymentButton = () => {
    if (!selectedPaymentItem) return;

    const buttonCode = generatePaymentButtonCode(selectedPaymentItem);

    navigator.clipboard
      .writeText(buttonCode)
      .then(() => showToast(t('kgstudio.common.successfully-copied'), 'success'))
      .catch(() => showToast(t('kgstore.common.error'), 'error'));
  };

  // Table setup
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'name',
      header: t('kgstudio.payment.product-name'),
      cell: ({ row }) => <div className="text-primary text-body-2-bold">{row.original.name}</div>,
    },
    {
      accessorKey: 'description',
      header: t('kgstudio.payment.product-description'),
      cell: ({ row }) => <div className="text-primary text-body-2">{row.original.description || '-'}</div>,
    },
    {
      accessorKey: 'price',
      header: t('kgstudio.payment.product-price'),
      cell: ({ row }) => (
        <div className="text-primary text-body-2-bold">
          {row.original.price} {row.original.currency}
        </div>
      ),
    },
    {
      accessorKey: 'image',
      header: t('kgstudio.payment.product-image'),
      cell: ({ row }) => (
        <div className="h-16 w-16 overflow-hidden rounded-md border border-gray-200">
          {row.original.image ? (
            <img src={row.original.image} alt={row.original.name} className="h-full w-full object-cover" />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gray-100 text-xs text-gray-400">
              {t('common.no-image')}
            </div>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'client_id',
      header: t('kgstudio.setting.user.client-id'),
      cell: ({ row }) => {
        const { client_id } = row.original;
        const clientName = oauthClients.find((client) => client.client_id === client_id)?.client_name || '-';

        return client_id ? (
          <Tooltip>
            <Tooltip.Trigger>
              <div className="text-primary text-body-2">{clientName}</div>
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p className="text-primary text-body-2">{client_id}</p>
            </Tooltip.Content>
          </Tooltip>
        ) : (
          <div className="text-primary text-body-2">-</div>
        );
      },
    },
    {
      accessorKey: 'pay_token',
      header: t('kgstudio.payment.pay-token'),
      cell: ({ row }) => <div className="text-primary text-body-2">{row.original.pay_token || '-'}</div>,
    },
    {
      accessorKey: 'chain_id',
      header: t('kgstudio.payment.chain-id'),
      cell: ({ row }) => <div className="text-primary text-body-2">{row.original.chain_id || '-'}</div>,
    },
    {
      accessorKey: 'order_data_fields',
      header: t('kgstudio.payment.order-data-fields'),
      cell: ({ row }) => (
        <div className="text-primary text-body-2">
          {row.original.order_data_fields?.length > 0 ? (
            <div className="max-h-20 overflow-y-auto">
              {row.original.order_data_fields.map((field: OrderDataField, index: number) => (
                <div key={index} className="text-xs">
                  {field.field_label}
                </div>
              ))}
            </div>
          ) : (
            <span>-</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) => <div className="text-primary text-body-2">{row.original.id}</div>,
    },
    {
      accessorKey: 'created_at',
      header: t('common.created-at'),
      cell: ({ row }) => (
        <div className="text-primary text-body-2">{new Date(row.original.created_at).toLocaleDateString()}</div>
      ),
    },
    {
      id: 'actions',
      size: 250,
      header: t('common.actions'),

      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button variant="grey" size="sm" onClick={() => handleClickOpen(row.original)}>
            {t('common.edit')}
          </Button>
          <Button variant="danger" size="sm" onClick={() => handleOpenDeleteModal(row.original)}>
            {t('common.delete')}
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleOpenPaymentModal(row.original)}
            title={t('kgstudio.payment.view-payment-page')}
          >
            {t('kgstudio.payment.payment-link')}
          </Button>
        </div>
      ),
    },
  ];

  const tableOptions = {
    data: paymentItemsResponse?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    meta: {
      fixedColumn: ['actions'],
    },
  };

  const table = useReactTable(tableOptions);

  const isLoading = isLoadingItems || isCreating || isUpdating || isDeleting || fileUrlsLoading;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="primary" onClick={() => handleClickOpen()}>
          {t('kgstudio.payment.create-product')}
        </Button>
      </div>

      {/* Create/Edit Modal */}
      <Modal open={open} onOpenChange={setOpen}>
        <Modal.Content className="max-h-[90%] max-w-[600px]" scrollable noClose>
          <Modal.Header>
            <Modal.Title>
              {isEditMode ? t('kgstudio.payment.edit-product-title') : t('kgstudio.payment.create-product-title')}
            </Modal.Title>
          </Modal.Header>

          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-10">
              <Spinner className="animate-spin" />
              <p className="text-secondary mt-4 text-center">
                {fileUrlsLoading ? t('kgstudio.common.uploading') : t('kgstudio.common.processing')}
              </p>
            </div>
          ) : (
            <FormProvider {...methods}>
              <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
                <FormInput
                  title={t('kgstudio.payment.product-name')}
                  placeholder={t('kgstudio.payment.product-name-placeholder')}
                  name="name"
                  control={methods.control}
                  required
                />
                <FormInput
                  title={t('kgstudio.payment.product-description')}
                  placeholder={t('kgstudio.payment.product-description-placeholder')}
                  name="description"
                  control={methods.control}
                />
                <FormInput
                  title={t('kgstudio.payment.product-price')}
                  placeholder={t('kgstudio.payment.product-price-placeholder')}
                  name="price"
                  control={methods.control}
                  required
                  type="number"
                  min="0.01"
                  max="10000000"
                  step="0.01"
                />
                <div className="flex flex-row justify-between">
                  <FormLabel>{t('kgstudio.payment.product-currency')}</FormLabel>
                  <select {...methods.register('currency', { required: true })}>
                    <option value="TWD">TWD</option>
                    <option value="USD">USD</option>
                  </select>
                </div>

                {/* Pay Token Select */}
                <div className="flex flex-col gap-2">
                  <FormLabel>{t('kgstudio.payment.pay-token')}</FormLabel>
                  <select {...methods.register('pay_token')} className="w-full rounded border p-2">
                    <option value="">-- {t('common.any-token')} --</option>
                    <option value="USDC">USDC</option>
                    <option value="USDT">USDT</option>
                  </select>
                  <div className="text-xs text-gray-500">{t('kgstudio.payment.pay-token-desc')}</div>
                </div>

                {/* Chain ID Select */}
                <div className="flex flex-col gap-2">
                  <FormLabel>{t('kgstudio.payment.chain-id')}</FormLabel>
                  <select {...methods.register('chain_id')} className="w-full rounded border p-2">
                    <option value="">-- {t('common.any-chain')} --</option>
                    <option value="arb">Arbitrum</option>
                    <option value="base">Base</option>
                    <option value="optimism">Optimism</option>
                  </select>
                  <div className="text-xs text-gray-500">{t('kgstudio.payment.chain-id-desc')}</div>
                </div>

                {/* Client ID Select */}
                <div className="flex flex-col gap-2">
                  <FormLabel required>{t('kgstudio.setting.user.client-id')} </FormLabel>
                  <select {...methods.register('client_id', { required: true })} className="w-full rounded border p-2">
                    <option value="">-- {t('common.select')} --</option>
                    {oauthClients?.map((client) => (
                      <option key={client.client_id} value={client.client_id}>
                        {client.client_name} ({client.client_id})
                      </option>
                    ))}
                  </select>
                  <div className="text-xs text-gray-500">{t('kgstudio.setting.user.client-id-desc')}</div>
                  {methods.formState.errors.client_id && (
                    <p className="mt-1 text-sm text-red-500">{t('kgstudio.setting.user.client-id-required')}</p>
                  )}
                </div>

                <FormMediaUploader
                  title={t('kgstudio.payment.product-image')}
                  name="image"
                  control={methods.control}
                  dropZoneDesc={t('kgstudio.payment.upload-image-desc')}
                  acceptType={['png', 'jpg', 'jpeg', 'webp']}
                  maxFileSize={10 * 1024 * 1024}
                  displayError={true}
                  previewInZone
                  dropZoneRatio="1/1"
                  dropZoneWidth={120}
                  data-cy="product-image-uploader"
                />

                <FormInput
                  title={t('kgstudio.payment.success-url')}
                  placeholder={t('kgstudio.payment.success-url-placeholder')}
                  control={methods.control}
                  name="successUrl"
                />
                <div className="-mt-4 ml-1 text-xs text-gray-500">{t('kgstudio.payment.url-hint')}</div>

                <FormInput
                  title={t('kgstudio.payment.error-url')}
                  placeholder={t('kgstudio.payment.error-url-placeholder')}
                  control={methods.control}
                  name="errorUrl"
                />
                <div className="-mt-4 ml-1 text-xs text-gray-500">{t('kgstudio.payment.url-hint')}</div>

                <FormInput
                  title={t('kgstudio.payment.callback-url')}
                  placeholder={t('kgstudio.payment.callback-url-placeholder')}
                  control={methods.control}
                  name="callbackUrl"
                />

                <div className="flex flex-col gap-2">
                  <FormLabel>{t('kgstudio.payment.success-message')}</FormLabel>
                  <textarea
                    {...methods.register('successMessage')}
                    rows={4}
                    className="w-full rounded border border-gray-300 p-3 focus:border-blue-500 focus:outline-none"
                    placeholder={t('kgstudio.payment.success-message-placeholder')}
                  />
                  <div className="text-xs text-gray-500">{t('kgstudio.payment.success-message-desc')}</div>
                </div>

                <div className="mb-4 border-t pt-4">
                  <h3 className="mb-1 text-lg font-medium">{t('kgstudio.payment.merchant-settings')}</h3>
                  <p className="mb-4 text-sm text-gray-500">{t('kgstudio.payment.merchant-settings-desc')}</p>

                  <FormInput
                    title={t('kgstudio.payment.merchant-email')}
                    placeholder={'<EMAIL>'}
                    control={methods.control}
                    name="merchantEmail"
                  />

                  <div className="mt-4">
                    <FormLabel>{t('kgstudio.payment.accent-color')}</FormLabel>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        value={accentColor}
                        onChange={(e) => setValue('accentColor', e.target.value)}
                        className="h-10 w-10 cursor-pointer"
                      />
                      <input
                        type="text"
                        value={accentColor}
                        onChange={(e) => setValue('accentColor', e.target.value)}
                        className="w-32 rounded border p-2"
                      />
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-lg font-medium">{t('kgstudio.payment.order-data-fields')}</h3>
                    <Button variant="outline" onClick={handleAddOrderDataField} type="button" className="text-sm">
                      {t('kgstudio.payment.add-field')}
                    </Button>
                  </div>

                  {orderDataFields.length > 0 && (
                    <div className="mb-4 space-y-3">
                      {orderDataFields.map((field, index) => (
                        <div key={index} className="flex items-center justify-between rounded bg-gray-50 p-3">
                          <div>
                            <p className="font-medium">
                              {field.field_label} ({field.field_name})
                            </p>
                            <p className="text-sm text-gray-500">
                              {field.field_type}{' '}
                              {field.required
                                ? `• ${t('kgstudio.payment.required-field')}`
                                : `• ${t('kgstudio.payment.optional-field')}`}
                            </p>
                          </div>
                          {field.field_name !== 'email' && (
                            <Button
                              variant="grey"
                              onClick={() => handleDeleteOrderDataField(index)}
                              type="button"
                              className="text-sm"
                            >
                              {t('common.delete')}
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {showOrderDataForm && (
                    <div className="mb-4 rounded border p-4" ref={orderDataFormRef}>
                      <h4 className="mb-3 font-medium">{t('kgstudio.payment.new-field')}</h4>
                      <div className="space-y-4">
                        <div>
                          <FormLabel required>{t('kgstudio.payment.field-name')}</FormLabel>
                          <input
                            name="field_name"
                            value={currentOrderDataField.field_name}
                            onChange={handleOrderDataFieldChange}
                            className={`w-full rounded border p-2 ${
                              !currentOrderDataField.field_name.trim() ? 'border-red-300' : 'border-gray-300'
                            }`}
                            placeholder="e.g. email, phone_num, address"
                            required
                          />
                          {!currentOrderDataField.field_name.trim() && (
                            <p className="mt-1 text-sm text-red-500">{t('kgstudio.payment.field-name-required')}</p>
                          )}
                        </div>

                        <div>
                          <FormLabel required>{t('kgstudio.payment.field-label')}</FormLabel>
                          <input
                            name="field_label"
                            value={currentOrderDataField.field_label}
                            onChange={handleOrderDataFieldChange}
                            className={`w-full rounded border p-2 ${
                              !currentOrderDataField.field_label.trim() ? 'border-red-300' : 'border-gray-300'
                            }`}
                            placeholder="e.g. Email Address, Phone Number"
                            required
                          />
                          {!currentOrderDataField.field_label.trim() && (
                            <p className="mt-1 text-sm text-red-500">{t('kgstudio.payment.field-label-required')}</p>
                          )}
                        </div>

                        <div>
                          <FormLabel required>{t('kgstudio.payment.field-type')}</FormLabel>
                          <select
                            name="field_type"
                            value={currentOrderDataField.field_type}
                            onChange={handleOrderDataFieldChange}
                            className="w-full rounded border border-gray-300 p-2"
                            required
                          >
                            <option value="text">Text</option>
                            <option value="number">Number</option>
                            <option value="boolean">Boolean</option>
                            <option value="textarea">Textarea</option>
                            <option value="tel">Telephone</option>
                            <option value="url">URL</option>
                            <option value="email">Email</option>
                            <option value="date">Date</option>
                            <option value="time">Time</option>
                          </select>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="required"
                            name="required"
                            checked={currentOrderDataField.required}
                            onChange={handleOrderDataFieldChange}
                            className="mr-2"
                          />
                          <FormLabel htmlFor="required">{t('kgstudio.payment.required-field')}</FormLabel>
                        </div>

                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="grey"
                            onClick={() => setShowOrderDataForm(false)}
                            type="button"
                            className="text-sm"
                          >
                            {t('common.cancel')}
                          </Button>
                          <Button
                            variant="primary"
                            onClick={handleSaveOrderDataField}
                            type="button"
                            className="text-sm"
                            disabled={
                              !currentOrderDataField.field_name.trim() || !currentOrderDataField.field_label.trim()
                            }
                          >
                            {t('common.add')}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </form>
            </FormProvider>
          )}

          <Modal.Footer className="mt-6 flex justify-between">
            <Button onClick={handleClose} variant="grey" disabled={isLoading}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleSubmit(onSubmit)} variant="primary" disabled={isLoading}>
              {isEditMode ? t('common.update') : t('common.save')}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal open={openDelete} onOpenChange={setOpenDelete}>
        <Modal.Content className="max-w-[400px]">
          <Modal.Header>
            <Modal.Title>{t('kgstudio.payment.delete-item-title')}</Modal.Title>
          </Modal.Header>
          <div className="py-4">
            <p>{t('kgstudio.payment.delete-item-confirmation')}</p>
          </div>
          <Modal.Footer className="flex justify-between">
            <Button onClick={handleCloseDeleteModal} variant="grey" disabled={isDeleting}>
              {t('common.cancel')}
            </Button>
            <Button onClick={onDelete} variant="danger" disabled={isDeleting}>
              {isDeleting ? <Spinner className="h-4 w-4 animate-spin" /> : t('common.delete')}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>

      {/* Payment Options Modal */}
      <Modal open={openPaymentModal} onOpenChange={setOpenPaymentModal}>
        <Modal.Content className="max-w-[900px]">
          <div className="space-y-4 py-4">
            {selectedPaymentItem && (
              <>
                <div className="mb-8 flex flex-row gap-2">
                  <img
                    src={selectedPaymentItem.image}
                    alt={selectedPaymentItem.name}
                    className="h-16 w-16 rounded-xl"
                  />
                  <div className="flex flex-col">
                    <div className="text-2xl font-bold">{selectedPaymentItem.name}</div>
                    <div className="text-gray-600">
                      {selectedPaymentItem.price} {selectedPaymentItem.currency}
                    </div>
                  </div>
                </div>

                <div className="flex w-full flex-row justify-between">
                  <Button
                    variant="primary"
                    className="text-base"
                    onClick={() => {
                      handlePaymentUrl(selectedPaymentItem.id, true);
                      handleClosePaymentModal();
                    }}
                  >
                    📋 {t('kgstudio.payment.copy-link')}
                  </Button>

                  <Button
                    variant="secondary"
                    className="text-base"
                    onClick={() => {
                      handlePaymentUrl(selectedPaymentItem.id, false);
                      handleClosePaymentModal();
                    }}
                  >
                    🌐 {t('kgstudio.payment.open-page')}
                  </Button>

                  <Button
                    variant="outline"
                    className="text-base"
                    onClick={() => {
                      handleCopyPaymentButton();
                      handleClosePaymentModal();
                    }}
                  >
                    💳 {t('kgstudio.payment.copy-button')}
                  </Button>
                </div>

                <div className="mt-4 rounded-lg bg-gray-50 p-3 text-sm text-gray-600">
                  <p className="mb-1 font-medium">{t('kgstudio.payment.button-preview')}</p>
                  <div
                    className="mt-2"
                    dangerouslySetInnerHTML={{
                      __html: generatePaymentButtonCode(selectedPaymentItem),
                    }}
                  />
                </div>
              </>
            )}
          </div>
        </Modal.Content>
      </Modal>

      {/* Product List Table */}
      <Card className="!p-0">
        <DataTable
          table={table}
          dataLength={paymentItemsResponse?.paging?.total_count || 0}
          isLoading={isLoadingItems}
        />
      </Card>
    </div>
  );
}
