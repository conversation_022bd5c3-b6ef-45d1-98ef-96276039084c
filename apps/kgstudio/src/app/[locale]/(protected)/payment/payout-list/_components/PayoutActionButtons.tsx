'use client';

import { MoreVertical } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useAccount, useConnect } from 'wagmi';

import { showToast } from '@/2b/toast';
import { Button, DropdownMenu } from '@kryptogo/2b';

import { useSendStableCoin } from '../../_hooks/useSendStableCoins';

interface PayoutActionButtonsProps {
  paymentIntentId: string;
  cryptoAmount: string;
  paymentAddress: string;
  tokenContractAddress: `0x${string}`;
  openConnectModal?: () => void;
  onCopyPaymentId?: (paymentIntentId: string) => void;
}

export const PayoutActionButtons = ({
  paymentIntentId,
  openConnectModal,
  onCopyPaymentId,
  cryptoAmount,
  paymentAddress,
  tokenContractAddress,
}: PayoutActionButtonsProps) => {
  const t = useTranslations();
  const [clickApprove, setClickApprove] = useState(false);
  const { isConnected } = useAccount();

  const { writeAsync, reset, prepareError, prepareStatus, writeStatus } = useSendStableCoin({
    amount: cryptoAmount as `${number}`,
    recipient: paymentAddress as `0x${string}`,
    tokenContractAddress,
    enabled: clickApprove,
  });

  useEffect(() => {
    if (!prepareError && clickApprove && writeAsync && prepareStatus === 'success' && writeStatus === 'idle') {
      writeAsync().catch(reset).finally(setClickApprove.bind(this, false));
    }
  }, [prepareStatus, writeStatus, clickApprove, writeAsync, prepareError]);

  const handleApprove = (e: React.MouseEvent) => {
    e.stopPropagation();
    setClickApprove(true);
    !isConnected && openConnectModal?.();
  };

  const handleCopyPaymentId = () => {
    if (onCopyPaymentId) {
      onCopyPaymentId(paymentIntentId);
    } else {
      // Default behavior - copy to clipboard
      navigator.clipboard.writeText(paymentIntentId);
      showToast('Payment ID copied to clipboard', 'success');
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Button variant="primary" size="sm" className="text-white" loading={clickApprove} onClick={handleApprove}>
        {t('kgstudio.payment.approve')}
      </Button>

      <div className="relative">
        <DropdownMenu>
          <DropdownMenu.Trigger asChild className="flex">
            <Button className="h-9 w-9 rounded-lg p-0" variant="grey" icon={<MoreVertical size={20} />} />
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="end">
            <DropdownMenu.Item onClick={handleCopyPaymentId}>Copy payment ID</DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu>
      </div>
    </div>
  );
};
