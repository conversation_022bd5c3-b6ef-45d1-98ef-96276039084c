'use client';

import { Co<PERSON>, ExternalLink } from 'lucide-react';
import moment from 'moment';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import qs from 'qs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import JsonView from 'react18-json-view';
import 'react18-json-view/src/style.css';
import { useForm } from 'react-hook-form';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FilterItem, FormFilterGroup } from '@/app/_common/components/form';
import { usePageHeader } from '@/app/_common/hooks';
import { ApiResponse, isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { cn, handleCopyAddress, removeEmptyArray, removeEmptyString } from '@/app/_common/lib/utils';
import { zodFilterSchema } from '@/app/_common/lib/zod';
import { apiPaymentHooks } from '@/app/_common/services';
import { PaymentChainIdSchema, PaymentStatusSchema, type PaymentIntent } from '@/app/_common/services/payment/model';
import { useOrganizationStore } from '@/app/_common/store';
import { usePathname, useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Avatar, Button, Card, DataTable, Form, useDataTable, Modal, Checkbox, TokenIcon, Badge } from '@kryptogo/2b';
import { getChainFullName } from '@kryptogo/utils';
import { formatCurrency, getExplorerUrl, truncateTxhashOrAddress } from '@kryptogo/utils';
import { ColumnDef, getCoreRowModel, TableOptions, useReactTable, VisibilityState } from '@tanstack/react-table';

import usdcImage from '../_asset/usdc.png';
import usdtImage from '../_asset/usdt.png';
import { WalletConnectButton } from '../_components/WalletConnectButton';
import { WalletSelectionModal } from '../_components/WalletSelectionModal';
import RefundModal from '../payment-list/_components/RefundModal';
import { PayoutActionButtons } from './_components/PayoutActionButtons';

const PayoutFilterSchema = z
  .object({
    status: z.array(PaymentStatusSchema).optional(),
    chain_id: PaymentChainIdSchema.optional(),
    client_id: z.string().optional(),
    group_key: z.string().optional(),
    collapsed_order_data: z.array(z.string()),
    tab_status: z.string().optional(), // Add tab filter for status tabs
  })
  .strip();
type PayoutFilter = z.infer<typeof PayoutFilterSchema>;

// Custom API client using the internal axios instance
const api = {
  get: <T,>(url: string) => internalAxiosInstance.get<ApiResponse<T>>(url).then((response) => response.data),
  post: <T,>(url: string, data: any) =>
    internalAxiosInstance.post<ApiResponse<T>>(url, data).then((response) => response.data),
  delete: <T,>(url: string) => internalAxiosInstance.delete<ApiResponse<T>>(url).then((response) => response.data),
  put: <T,>(url: string, data: any) =>
    internalAxiosInstance.put<ApiResponse<T>>(url, data).then((response) => response.data),
};

interface OAuthClientResponse {
  client_id: string;
  client_name: string;
  client_domain: string;
  client_type: string;
  main_logo: string;
  square_logo?: string;
  created_at?: number;
  app_store_link?: string;
  google_play_link?: string;
}

// Add this new component for displaying JSON data
const JsonViewModal = ({ isOpen, onClose, data, t }: { isOpen: boolean; onClose: () => void; data: any; t: any }) => {
  if (!isOpen) return null;

  return (
    <Modal open={isOpen} onOpenChange={onClose}>
      <Modal.Content className="max-w-4xl">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.payment.order-data')}</Modal.Title>
        </Modal.Header>
        <div className="max-h-[60vh] overflow-y-auto">
          <JsonView src={data} />
        </div>
      </Modal.Content>
    </Modal>
  );
};

const STORAGE_KEY = 'payout_columns_visibility';

// Save column visibility to localStorage
const saveColumnVisibility = (visibility: VisibilityState) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(visibility));
  } catch (e) {
    console.error('Failed to save column visibility settings', e);
  }
};

// Load column visibility from localStorage
const loadColumnVisibility = (): VisibilityState | null => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    return saved ? JSON.parse(saved) : null;
  } catch (e) {
    console.error('Failed to load column visibility settings', e);
    return null;
  }
};

const PayoutList = () => {
  const pathname = usePathname();
  const router = useRouter();
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.payment.payout-list') });
  const params = useSearchParams();
  const parsedParams = zodFilterSchema(PayoutFilterSchema).parse(
    qs.parse(decodeURIComponent(params.toString()), { comma: true }),
  );

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(() => {
    // Try to load saved settings, fallback to defaults based on Figma design
    return (
      loadColumnVisibility() || {
        batch_id: true,
        payout_id: true,
        recipient: true,
        symbol: true,
        crypto_amount: true,
        status: true,
        payment_deadline: true,
        creator_info: true,
        reviewer_info: true,
        payout_wallet: false,
        handling_fee: false,
        attachment: false,
        description: false,
        actions: true,
      }
    );
  });

  const [tableOption, setTableOption] = useState<TableOptions<PaymentIntent>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
    state: {
      columnVisibility,
    },
    onColumnVisibilityChange: setColumnVisibility,
    meta: {
      fixedColumn: ['fixed-actions'], // Pin actions column to the right using @kryptogo/2b approach
    },
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size } = useDataTable(table);

  const form = useForm<PayoutFilter>({
    defaultValues: {
      status: undefined,
      chain_id: undefined,
      client_id: undefined,
      group_key: 'payout_no_1',
      collapsed_order_data: [],
      tab_status: 'all', // Default to 'all' tab
    },
    values: {
      ...parsedParams,
    },
    mode: 'onChange',
    resolver: zodResolver(PayoutFilterSchema),
  });

  const formClientId = form.watch('client_id');
  const orgId = useOrganizationStore((state) => state.orgId);
  const isFilterSelected = useMemo(() => {
    return Object.values(removeEmptyString(removeEmptyArray(form.watch()))).some((v) => v !== undefined);
  }, [form]);

  const [oauthClients, setOAuthClients] = useState<Array<{ id: string; name: string }>>([]);
  const [selectedPaymentIntent, setSelectedPaymentIntent] = useState<{
    id: string;
    amount: string;
  } | null>(null);
  const [jsonModalData, setJsonModalData] = useState<{ isOpen: boolean; data: any }>({
    isOpen: false,
    data: null,
  });

  const [showWalletModal, setShowWalletModal] = useState(false);

  // Create ref for click outside detection

  useEffect(() => {
    const fetchOAuthClients = async () => {
      if (!orgId) return;

      try {
        const response = await api.get<OAuthClientResponse[]>(`/studio/organization/${orgId}/oauth_clients`);
        setOAuthClients(response.data.map((client) => ({ id: client.client_id, name: client.client_name })));
      } catch (error) {
        if (isApiError(error)) {
          showToast(t('kgstudio.payment.error-loading-oauth-clients'), 'error');
        }
      }
    };

    fetchOAuthClients();
  }, [orgId]);

  const {
    data: paymentIntents,
    isLoading: paymentIntentsLoading,
    refetch: refetchPaymentIntents,
  } = apiPaymentHooks.useGetPaymentIntents(
    {
      params: {
        org_id: orgId as number,
      },
      queries: {
        page_size: page_size,
        page_number: page_number,
        client_id: formClientId ? formClientId : undefined,
        status: form.watch('status'),
        chain_id: form.watch('chain_id'),
        group_key: form.watch('group_key'),
      },
      paramsSerializer: (params) => {
        return qs.stringify(params, { indices: false });
      },
    },
    {
      onError: (error) => {
        console.error('error', error);

        if (isApiError(error)) {
          showToast(t('kgstudio.payment.error-loading-payout-data'), 'error');
        }
      },
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchInterval: 30_000,
    },
  );

  const filterItems = useMemo<FilterItem<PayoutFilter>[]>(() => {
    return [
      {
        name: 'group_key',
        subject: t('kgstudio.payment.group-key'),
        type: 'input',
        placeholder: t('kgstudio.payment.group-key-search-placeholder'),
      },
      {
        name: 'chain_id',
        subject: t('kgstudio.treasury.network'),
        type: 'select',
        options: [
          { label: getChainFullName('arb')!, value: 'arb' },
          { label: getChainFullName('base')!, value: 'base' },
          { label: getChainFullName('optimism')!, value: 'optimism' },
        ],
      },
    ];
  }, [t, oauthClients]);

  // Desktop (full) columns layout - Updated to match Figma design
  const getColumns = useMemo(
    (): ColumnDef<PaymentIntent>[] => [
      {
        accessorKey: 'batch-select',
        header: (<Checkbox className="mx-2" />) as any,
        size: 40,
        cell: () => {
          return <Checkbox className="mx-2" />;
        },
      },
      {
        accessorKey: 'payout_id',
        header: t('kgstudio.payment.payout-id'),
        size: 120,
        cell: ({ row }) => {
          const { payment_intent_id } = row.original;
          return <div className="text-primary text-body-2">{payment_intent_id || '-'}</div>;
        },
      },
      {
        accessorKey: 'recipient',
        header: t('kgstudio.payment.recipient'),
        size: 200,
        cell: ({ row }) => {
          const { client_id, payment_address, payment_chain_id } = row.original;
          const clientName = oauthClients.find((client) => client.id === client_id)?.name || 'Client Ltd.';

          return (
            <div className="flex flex-col gap-1">
              <div className="text-primary text-body-2-bold">{clientName}</div>
              <div className="text-secondary text-small">{client_id}@mail.com</div>
              <div className="flex items-center gap-1">
                <div className="text-small underline" onClick={(e) => e.stopPropagation()}>
                  {truncateTxhashOrAddress(payment_address)}
                </div>
                <Copy
                  className="cursor-pointer stroke-[var(--text-primary)]"
                  size={12}
                  onClick={() => handleCopyAddress(payment_address, t)}
                />
                <Link
                  href={getExplorerUrl('address', payment_chain_id, payment_address) ?? ''}
                  target="_blank"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ExternalLink className="cursor-pointer stroke-[var(--text-primary)]" size={12} />
                </Link>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'symbol',
        header: t('kgstudio.payment.send-token'),
        size: 150,
        cell: ({ row }) => {
          const { symbol, payment_chain_id } = row.original;
          return (
            <div className="flex items-center gap-3">
              <TokenIcon
                className="overflow-hidden"
                token={{
                  logoUrl: symbol === 'USDT' ? usdtImage.src : usdcImage.src,
                  name: symbol,
                }}
                chain={payment_chain_id}
                size="40"
              />
              <div>
                <p className="text-primary text-body-2-bold">{symbol}</p>
                <p className="text-secondary text-small">{getChainFullName(payment_chain_id)}</p>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'crypto_amount',
        header: t('common.amount'),
        size: 140,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { crypto_amount, fiat_amount, fiat_currency } = row.original;

          return (
            <div>
              <p className="text-primary text-body-2-bold">{formatCurrency({ amount: crypto_amount })}</p>
              {fiat_currency && (
                <p className="text-secondary text-small">
                  ≈ {fiat_amount} {fiat_currency}
                </p>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: t('common.status.text'),
        size: 140,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { status } = row.original;
          const statusConfig = match(status)
            .with('pending', () => ({
              text: t('kgstudio.payment.awaiting-approval'),
              variant: 'yellow' as const,
            }))
            .with('success', () => ({
              text: t('kgstudio.payment.send-success'),
              variant: 'green' as const,
            }))
            .with('expired', () => ({
              text: t('kgstudio.common.expired'),
              variant: 'grey' as const,
            }))
            .with('insufficient_not_refunded', () => ({
              text: t('kgstudio.payment.send-failed'),
              variant: 'red' as const,
            }))
            .with('insufficient_refunded', () => ({
              text: t('kgstudio.payment.request-rejected'),
              variant: 'red' as const,
            }))
            .exhaustive();

          return <Badge variant={statusConfig.variant}>{statusConfig.text}</Badge>;
        },
      },
      {
        accessorKey: 'payment_deadline',
        header: t('kgstudio.payment.due-date'),
        meta: {
          sortable: true,
        },
        size: 120,
        cell: ({ row }) => {
          const { payment_deadline } = row.original;
          return (
            <div>
              <p className="text-primary text-body-2">{moment(payment_deadline * 1000).format('YYYY/MM/DD')}</p>
              <p className="text-secondary text-small">{moment(payment_deadline * 1000).format('HH:mm')}</p>
            </div>
          );
        },
      },
      {
        accessorKey: 'creator_info',
        header: t('kgstudio.payment.creator-creation-date'),
        size: 200,
        meta: {
          sortable: true,
        },
        cell: () => {
          const creatorName = 'Kordan'; // This would typically come from API data
          const isOwner = true; // This would typically come from API data

          return (
            <div className="grid grid-flow-col grid-cols-[auto,1fr] grid-rows-2 gap-x-3">
              <Avatar className="row-span-2" imageSrc={undefined} displayName={creatorName} size="36" />
              <span className={cn('text-primary', { 'row-span-2 my-auto': !isOwner })}>{creatorName}</span>
              {isOwner && <span className="text-secondary text-small">{t('kgstudio.team.role.owner')}</span>}
            </div>
          );
        },
      },
      {
        accessorKey: 'reviewer_info',
        header: t('kgstudio.payment.reviewer-review-date'),
        size: 200,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { status } = row.original;

          // Only show reviewer info for reviewed items
          if (status === 'pending') {
            return (
              <div className="grid grid-flow-col grid-cols-[auto,1fr] grid-rows-2 gap-x-3">
                <Avatar className="row-span-2" imageSrc={undefined} displayName="-" size="36" />
                <span className="text-secondary text-small row-span-2 my-auto">-</span>
              </div>
            );
          }

          const reviewerName = 'Arthur'; // This would typically come from API data
          const isTeamMember = true; // This would typically come from API data

          return (
            <div className="grid grid-flow-col grid-cols-[auto,1fr] grid-rows-2 gap-x-3">
              <Avatar className="row-span-2" imageSrc={undefined} displayName={reviewerName} size="36" />
              <span className={cn('text-primary', { 'row-span-2 my-auto': !isTeamMember })}>{reviewerName}</span>
              {isTeamMember && <span className="text-secondary text-small">Team M.</span>}
            </div>
          );
        },
      },
      {
        accessorKey: 'payout_wallet',
        header: t('kgstudio.payment.payout-wallet'),
        size: 150,
        cell: ({ row }) => {
          const { payment_tx_hash, payment_chain_id } = row.original;

          return !payment_tx_hash ? (
            <span className="text-secondary text-small">-</span>
          ) : (
            <Link
              className="text-brand-primary text-body-2"
              href={getExplorerUrl('tx', payment_chain_id, payment_tx_hash) ?? ''}
              target="_blank"
              rel="noopener noreferrer"
              onClick={(e) => e.stopPropagation()}
            >
              {truncateTxhashOrAddress(payment_tx_hash)}
            </Link>
          );
        },
      },
      {
        accessorKey: 'handling_fee',
        header: t('kgstudio.payment.handling-fee'),
        size: 140,
        meta: {
          sortable: true,
        },
        cell: ({ row }) => {
          const { crypto_amount } = row.original;
          // Calculate a mock handling fee (typically would come from API)
          const feeAmount = (parseFloat(crypto_amount) * 0.001).toFixed(6);
          const feeInFiat = (parseFloat(crypto_amount) * 0.001 * 30000).toFixed(0); // Mock rate

          return (
            <div>
              <p className="text-primary text-body-2-bold">{feeAmount}</p>
              <p className="text-secondary text-small">≈ {feeInFiat} TWD</p>
            </div>
          );
        },
      },
      {
        accessorKey: 'attachment',
        header: t('kgstudio.payment.attachment'),
        size: 120,
        cell: ({ row }) => {
          const { order_data } = row.original;

          if (!order_data) {
            return <span className="text-secondary text-small">-</span>;
          }

          return (
            <div className="flex items-center gap-2">
              <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                />
              </svg>
              <span className="text-secondary text-small">invoice.pdf</span>
            </div>
          );
        },
      },
      {
        accessorKey: 'description',
        header: t('kgstudio.payment.description'),
        size: 150,
        cell: ({ row }) => {
          const { group_key } = row.original;
          return <div className="text-primary text-body-2">{group_key || '-'}</div>;
        },
      },
      {
        accessorKey: 'fixed-actions',
        header: t('common.actions'),
        size: 180,
        meta: {
          pin: true,
        },
        cell: ({ row }) => {
          const { payment_intent_id, crypto_amount, payment_address, token_address } = row.original;
          // if (!payout_target_address) {
          //   return null;
          // }
          return (
            <PayoutActionButtons
              paymentIntentId={payment_intent_id}
              cryptoAmount={crypto_amount}
              paymentAddress={payment_address}
              tokenContractAddress={token_address as `0x${string}`}
              openConnectModal={() => {
                setShowWalletModal(true);
              }}
            />
          );
        },
      },
    ],
    [t, oauthClients],
  ); // Dependencies for useMemo

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: paymentIntents?.data || [],
      columns: getColumns,
      state: {
        ...prev.state,
        columnVisibility: columnVisibility,
      },
      meta: {
        hideHeader: false,
        rowHover: true,
        fixedColumn: ['fixed-actions'],
      },
    }));
  }, [paymentIntents, columnVisibility, getColumns]);

  // Update localStorage when column visibility changes
  useEffect(() => {
    saveColumnVisibility(columnVisibility);
  }, [columnVisibility]);

  const closeWalletSelect = useCallback(() => setShowWalletModal(false), []);

  return (
    <>
      {/* Status Tabs Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'all', label: t('kgstudio.payment.all'), count: null },
              { id: 'pending', label: t('kgstudio.payment.awaiting-approval'), count: null },
              { id: 'rejected', label: t('kgstudio.payment.request-rejected'), count: null },
              { id: 'send_failed', label: t('kgstudio.payment.send-failed'), count: null },
              { id: 'expired', label: t('kgstudio.common.expired'), count: null },
              { id: 'success', label: t('kgstudio.payment.send-success'), count: null },
            ].map((tab) => {
              const isActive = form.watch('tab_status') === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => {
                    form.setValue('tab_status', tab.id);
                    // Update URL with new tab status
                    const currentParams = new URLSearchParams(params.toString());
                    currentParams.set('tab_status', tab.id);
                    router.push(`${pathname}?${currentParams.toString()}`);
                  }}
                  className={cn(
                    'whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium',
                    isActive
                      ? 'border-yellow-500 text-gray-900'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                  )}
                >
                  {tab.label}
                  {tab.count && (
                    <span
                      className={cn(
                        'ml-2 rounded-full px-2.5 py-0.5 text-xs',
                        isActive ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-500',
                      )}
                    >
                      {tab.count}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      <Card className="overflow-x-auto !p-0">
        <div className="w-full overflow-x-auto">
          <Form {...form}>
            <div className="m-4 flex flex-wrap items-center gap-3">
              <FormFilterGroup
                control={form.control}
                items={filterItems}
                data-cy="payout-filter-group"
                {...(isFilterSelected && {
                  onClearFilter: () => {
                    params.toString() ? router.push(pathname) : form.reset();
                  },
                })}
              />

              {/* Action buttons section */}
              <div className="ml-auto flex items-center gap-3">
                <WalletConnectButton onConnectClick={() => setShowWalletModal(true)} />

                <Button
                  variant="secondary"
                  size="md"
                  className=""
                  onClick={() => {
                    router.push('/payment/create-payout');
                  }}
                >
                  <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  {t('kgstudio.payment.create-payment-intent')}
                </Button>

                {/* <Button
                  variant="grey-outlined"
                  size="md"
                  className="border-gray-300"
                  onClick={() => {
                    // Handle upload CSV functionality
                    showToast('Upload CSV functionality to be implemented', 'info');
                  }}
                >
                  <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                    />
                  </svg>
                  {t('kgstudio.payment.upload-csv')}
                </Button> */}
              </div>
            </div>
          </Form>

          <DataTable
            table={table}
            isLoading={paymentIntentsLoading}
            dataLength={paymentIntents?.paging?.total_count || 0}
          />
        </div>
      </Card>

      {jsonModalData.isOpen && (
        <JsonViewModal
          isOpen={jsonModalData.isOpen}
          onClose={() => setJsonModalData({ isOpen: false, data: null })}
          data={jsonModalData.data}
          t={t}
        />
      )}

      {selectedPaymentIntent && (
        <RefundModal
          isOpen={!!selectedPaymentIntent}
          onClose={() => setSelectedPaymentIntent(null)}
          paymentIntentId={selectedPaymentIntent.id}
          orgId={orgId as number}
          defaultAmount={selectedPaymentIntent.amount}
          onSuccess={() => {
            // Refetch the payout data list
            refetchPaymentIntents();
          }}
        />
      )}

      <WalletSelectionModal isOpen={showWalletModal} onClose={closeWalletSelect} />
    </>
  );
};

export default PayoutList;
