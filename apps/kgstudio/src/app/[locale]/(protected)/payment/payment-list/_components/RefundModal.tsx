import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { ControllerRenderProps } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { isApiError } from '@/app/_common/lib/api';
import internalAxiosInstance from '@/app/_common/lib/axios/instances/internal';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Input, Modal } from '@kryptogo/2b';

const RefundModalSchema = (maxAmount: string) =>
  z.object({
    refund_crypto_amount: z
      .string()
      .min(1, 'Amount is required')
      .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
        message: 'Amount must be greater than 0',
      })
      .refine((val) => !isNaN(Number(val)) && Number(val) <= Number(maxAmount), {
        message: `Amount must be less than or equal to maximum amount (${maxAmount})`,
      }),
    to: z.string().min(1, 'Address is required'),
  });

type RefundFormValues = z.infer<ReturnType<typeof RefundModalSchema>>;

type RefundModalProps = {
  isOpen: boolean;
  onClose: () => void;
  paymentIntentId: string;
  orgId: number;
  defaultAmount: string;
  receivedAmount?: string;
  onSuccess: () => void;
};

const RefundModal = ({ isOpen, onClose, paymentIntentId, orgId, defaultAmount, onSuccess }: RefundModalProps) => {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<RefundFormValues>({
    resolver: zodResolver(RefundModalSchema(defaultAmount)),
    defaultValues: {
      refund_crypto_amount: defaultAmount,
      to: '',
    },
  });

  const onSubmit = async (data: RefundFormValues) => {
    setIsLoading(true);
    try {
      await internalAxiosInstance.post(`/studio/organization/${orgId}/payment/intents/${paymentIntentId}/refund`, data);
      showToast(t('kgstudio.payment.refund.success'), 'success');
      onSuccess();
      onClose();
    } catch (error) {
      if (isApiError(error)) {
        showToast(error.message || t('kgstudio.payment.refund.error'), 'error');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Modal open={isOpen} onOpenChange={onClose}>
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>{t('kgstudio.payment.refund.title')}</Modal.Title>
        </Modal.Header>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label className="mb-2 block font-medium" htmlFor="refund_crypto_amount">
              {t('kgstudio.payment.refund.amount.text')}
              <span className="ml-1 text-sm text-gray-500">(Max: {defaultAmount})</span>
            </label>
            <Input
              {...form.register('refund_crypto_amount')}
              id="refund_crypto_amount"
              type="text"
              placeholder={t('kgstudio.payment.refund.amount.placeholder')}
            />
            {form.formState.errors.refund_crypto_amount && (
              <p className="mt-1 text-sm text-red-500">{form.formState.errors.refund_crypto_amount.message}</p>
            )}
          </div>
          <div>
            <label className="mb-2 block font-medium" htmlFor="to">
              {t('kgstudio.payment.refund.address.text')}
            </label>
            <Input
              {...form.register('to')}
              id="to"
              type="text"
              placeholder={t('kgstudio.payment.refund.address.placeholder')}
            />
            {form.formState.errors.to && (
              <p className="mt-1 text-sm text-red-500">{form.formState.errors.to.message}</p>
            )}
          </div>
          <Modal.Footer className="flex justify-end gap-2">
            <Button variant="secondary" onClick={onClose} type="button">
              {t('kgstudio.payment.refund.cancel')}
            </Button>
            <Button type="submit" loading={isLoading}>
              {t('kgstudio.payment.refund.confirm')}
            </Button>
          </Modal.Footer>
        </form>
      </Modal.Content>
    </Modal>
  );
};

export default RefundModal;
