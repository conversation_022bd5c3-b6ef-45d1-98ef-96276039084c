'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormDropdown, FormInput } from '@/app/_common/components/form';
import { isApiError } from '@/app/_common/lib/api';
import { apiPaymentHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, Modal } from '@kryptogo/2b';

// Schema for the create payment intent form based on the user requirements
const CreatePaymentIntentSchema = z
  .object({
    pricing_mode: z.enum(['fiat', 'crypto'], {
      required_error: 'Pricing mode is required',
    }),
    amount: z.string().min(1, 'Amount is required'),
    currency: z.enum(['TWD', 'USD']).optional(),
    pay_token: z.enum(['USDC', 'USDT'], {
      required_error: 'Pay token is required',
    }),
    group_key: z.string().optional(),
    payout_target_address: z.string(),
  })
  .refine(
    (data) => {
      // If pricing mode is fiat, currency is required
      if (data.pricing_mode === 'fiat' && !data.currency) {
        return false;
      }
      return true;
    },
    {
      message: 'Currency is required when pricing mode is fiat',
      path: ['currency'],
    },
  );

type CreatePaymentIntentFormValues = z.infer<typeof CreatePaymentIntentSchema>;

interface CreatePaymentIntentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CreatePaymentIntentModal = ({ isOpen, onClose, onSuccess }: CreatePaymentIntentModalProps) => {
  const t = useTranslations();
  const { orgId } = useOrganizationStore();
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<CreatePaymentIntentFormValues>({
    resolver: zodResolver(CreatePaymentIntentSchema),
    defaultValues: {
      pricing_mode: 'crypto',
      amount: '',
      currency: 'TWD',
      pay_token: 'USDC',
      group_key: '',
      payout_target_address: '',
    },
    mode: 'onChange',
  });

  const pricingMode = form.watch('pricing_mode');

  const { mutate: createPaymentIntent } = apiPaymentHooks.useCreatePaymentIntent(
    {
      headers: {
        'X-Client-ID': 'kryptogo-xyz',
      },
    },
    {
      onSuccess: () => {
        showToast(t('kgstudio.payment.create-intent-success'), 'success');
        onSuccess?.();
        onClose();
        form.reset();
      },
      onError: (error) => {
        if (isApiError(error)) {
          showToast(error.message || t('kgstudio.payment.create-intent-error'), 'error');
        } else {
          showToast(t('kgstudio.payment.create-intent-error'), 'error');
        }
      },
    },
  );

  const onSubmit = async (data: CreatePaymentIntentFormValues) => {
    if (!orgId) {
      showToast(t('kgstudio.payment.organization-required'), 'error');
      return;
    }

    setIsLoading(true);
    try {
      // Token addresses for Base chain (you can expand this for other chains)
      const tokenAddresses = {
        USDC: '0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913', // Base USDC
        USDT: '0xfde4C96c8593536E31F229EA8f37b2ADa2699bb2', // Base USDT
      };

      // Map the form data to the API request format
      const requestData = {
        chain_id: 'arb' as const, // Default to base chain
        token_address: tokenAddresses[data.pay_token],
        amount: data.amount,
        pricing_mode: data.pricing_mode,
        ...(data.pricing_mode === 'fiat' && {
          fiat_currency: data.currency || 'TWD',
        }),
        pay_token: data.pay_token,
        payout_target_address: data.payout_target_address,
        group_key: 'payout_no_1', //data.group_key || undefined,
      };
      createPaymentIntent(requestData);
    } catch (error) {
      console.error('Error creating payment intent:', error);
      showToast(t('kgstudio.payment.create-intent-error'), 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    form.reset();
  };

  if (!isOpen) return null;

  return (
    <Modal open={isOpen} onOpenChange={handleClose}>
      <Modal.Content className="max-w-2xl">
        <Modal.Header>
          <Modal.Title>{t('kgstudio.payment.create-payment-intent')}</Modal.Title>
        </Modal.Header>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="text-lg font-semibold text-gray-800">{t('kgstudio.payment.payment-intent-details')}</h3>
                <p className="mt-1 text-sm text-gray-600">{t('kgstudio.payment.configure-payment-intent-details')}</p>
              </div>

              <div className="space-y-4">
                {/* Required Fields Section */}
                <div className="space-y-4 rounded-lg border border-red-200 bg-red-50 p-6">
                  <div className="border-l-4 border-red-400 pl-4">
                    <h4 className="flex items-center gap-2 text-base font-semibold text-gray-700">
                      <span className="inline-block h-2 w-2 rounded-full bg-red-400"></span>
                      {t('kgstudio.payment.required-fields')}
                    </h4>
                    <p className="mt-1 text-sm text-gray-600">{t('kgstudio.payment.required-fields-description')}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* Pricing Mode */}
                    <FormDropdown
                      control={form.control}
                      name="pricing_mode"
                      title={t('kgstudio.payment.pricing-mode')}
                      options={[
                        { label: t('kgstudio.payment.pricing-mode-fiat'), value: 'fiat' },
                        { label: t('kgstudio.payment.pricing-mode-crypto'), value: 'crypto' },
                      ]}
                      required
                    />

                    {/* Pay Token */}
                    <FormDropdown
                      control={form.control}
                      name="pay_token"
                      title={t('kgstudio.payment.pay-token')}
                      options={[
                        { label: 'USDC', value: 'USDC' },
                        { label: 'USDT', value: 'USDT' },
                      ]}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* Amount */}
                    <FormInput
                      control={form.control}
                      name="amount"
                      title={t('kgstudio.payment.amount')}
                      placeholder={
                        pricingMode === 'fiat'
                          ? t('kgstudio.payment.amount-fiat-placeholder')
                          : t('kgstudio.payment.amount-crypto-placeholder')
                      }
                      required
                      withoutMessage
                    />

                    {/* Currency - only show for fiat mode */}
                    {pricingMode === 'fiat' && (
                      <FormDropdown
                        control={form.control}
                        name="currency"
                        title={t('kgstudio.payment.fiat-currency')}
                        options={[
                          { label: 'TWD', value: 'TWD' },
                          { label: 'USD', value: 'USD' },
                        ]}
                        required
                      />
                    )}
                    <FormInput
                      control={form.control}
                      name="payout_target_address"
                      title={t('kgstudio.payment.payout_target_address')}
                      placeholder="0x..."
                      required
                      withoutMessage
                    />
                  </div>
                </div>
              </div>
            </div>

            <Modal.Footer className="flex justify-between">
              <Button type="button" variant="secondary" onClick={handleClose} disabled={isLoading}>
                {t('common.cancel')}
              </Button>
              <Button type="submit" variant="primary" loading={isLoading}>
                {t('kgstudio.payment.create-payment-intent')}
              </Button>
            </Modal.Footer>
          </form>
        </Form>
      </Modal.Content>
    </Modal>
  );
};

export default CreatePaymentIntentModal;
