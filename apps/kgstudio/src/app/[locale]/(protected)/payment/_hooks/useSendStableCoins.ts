import { useEffect } from 'react';
import { parseUnits } from 'viem';
import { useContractWrite, usePrepareContractWrite, useAccount, useSwitchNetwork, useNetwork } from 'wagmi';

import { ERC20ABI } from '@kryptogo/utils';

export const useSendStableCoin = ({
  recipient,
  amount,
  tokenContractAddress,
  enabled,
}: {
  recipient: `0x${string}`;
  amount: `${number}`;
  tokenContractAddress: `0x${string}`;
  enabled: boolean;
}) => {
  const { isConnected } = useAccount();
  const { chain } = useNetwork();
  const { switchNetwork } = useSwitchNetwork();

  const ARBITRUM_CHAIN_ID = 42161;

  // Auto-switch to Arbitrum network if connected but on wrong network
  useEffect(() => {
    if (isConnected && chain?.id !== ARBITRUM_CHAIN_ID && switchNetwork) {
      switchNetwork(+ARBITRUM_CHAIN_ID);
    }
  }, [isConnected, chain?.id, switchNetwork]);

  const {
    config,
    error: prepareError,
    status: prepareStatus,
  } = usePrepareContractWrite({
    address: tokenContractAddress,
    abi: ERC20ABI,
    functionName: 'transfer',
    args: [recipient, parseUnits(amount, 6)],
    enabled,
    chainId: ARBITRUM_CHAIN_ID, // Arbitrum chainId
  });

  const {
    data,
    isLoading: isWriteLoading,
    isSuccess,
    writeAsync,
    reset,
    error: writeError,
    status: writeStatus,
  } = useContractWrite(config);
  return {
    writeAsync,
    reset,
    data,
    isWriteLoading,
    isSuccess,
    writeError,
    prepareError,
    isConnected,
    prepareStatus,
    writeStatus,
  };
};
