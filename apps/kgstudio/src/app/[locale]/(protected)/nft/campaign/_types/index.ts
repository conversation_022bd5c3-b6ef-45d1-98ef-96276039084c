import { getTime } from 'date-fns';
import { z } from 'zod';

import { ApiResponseWithPaging } from '@/app/_common/lib/api';

import { getDateTimeDisplay } from '../_utils';

export type NFTCollectionStatus = 'draft' | 'publish';
export type NFTCollectionPublishStatus = 'success' | 'failed' | 'pending';
export type NFTCollectionContractType = 'ERC721' | 'ERC1155';

export interface NFTCollection {
  contract_address: string;
  collection_name: string;
  total_supply: number;
  max_supply: number;
  start_time: number;
  end_time: number;
  mint_page: boolean;
  publish_status: NFTCollectionPublishStatus;
  status: NFTCollectionStatus;
  event_id: string;
  project_id: number;
  collection_image_url: string;
}
export type NFTCollectionsResponse = ApiResponseWithPaging<NFTCollection[]>;

export interface NFTCollectionDetail extends NFTCollection {
  project_id: number;
  collection_image_url: string;
  symbol_name: string;
  collection_description: string;
  banner_image_url: string;
  contract_schema_name: NFTCollectionContractType;
  title: string;
  subtitle: string;
  favicon_image_url: string;
  msg_content: string;
  contract_address: string;
  event_id: string;
  publish_status: NFTCollectionPublishStatus;
  status: NFTCollectionStatus;
}

export interface UpdateCollectionRequest {
  project_id?: number;
  status?: string;
  collection_image_url?: string;
  collection_name?: string;
  symbol_name?: string;
  collection_description?: string;
  banner_image_url?: string;
  contract_schema_name?: NFTCollectionContractType;
  max_supply?: number;
  start_time?: number;
  end_time?: number;
  title?: string;
  subtitle?: string;
  favicon_image_url?: string;
  msg_content?: string;
}

export type NFTCollectionDetailResponse = {
  code: 0;
  data: NFTCollectionDetail;
};

const ContractTypeSchema = z.enum(['ERC721', 'ERC1155']);
const ReceivedMethodSchema = z.enum(['phone', 'email', 'address']);
const MintTimeTypeSchema = z.enum(['instant', 'customized']);
const ImageSchema = z.object({
  dataURL: z.string(),
  file: z
    .object({
      name: z.string(),
      size: z.number(),
      type: z.string(),
    })
    .optional(),
});

export type ImageType = z.infer<typeof ImageSchema>;

export const FormSchema = (t: any) =>
  z
    .object({
      collection_name: z
        .string()
        .min(1, { message: t('kgstudio.nft.validation.collection-name-min') })
        .max(30, {
          message: t('kgstudio.nft.validation.collection-name-max'),
        })
        .refine((value) => /^[A-Za-z0-9\s]*$/.test(value), {
          message: t('kgstudio.nft.validation.only-english-and-whitespace'),
        }),
      symbol_name: z.string().min(1, { message: t('kgstudio.nft.validation.enter-collection-abbreviation') }),
      collection_description: z.string().min(1, {
        message: t('kgstudio.nft.validation.collection-desc'),
      }),
      contract_schema_name: ContractTypeSchema,
      max_supply: z.string().min(1, { message: t('kgstudio.nft.validation.enter-total-supply') }),
      mintTimeStartType: MintTimeTypeSchema,
      mintTimeStart: z.date(),
      mintTimeEndType: MintTimeTypeSchema,
      mintTimeEnd: z.date(),
      received_method: z.array(ReceivedMethodSchema),
      mintPageTitle: z.string().min(1, { message: t('kgstudio.page.input-page-title') }),
      mintPageSubTitle: z.string().min(1, { message: t('kgstudio.page.input-page-subtitle') }),
      msg_content: z.string().min(1, { message: t('kgstudio.message.input-message-content') }),
      mintTimeStartTime: z.string().min(1, { message: t('kgstudio.time.input-time') }),
      mintTimeEndTime: z.string().min(1, { message: t('kgstudio.time.input-time') }),
      collection_image_url: z.array(ImageSchema).nonempty({ message: t('kgstudio.image.upload-required') }),
      banner_image_url: z.array(ImageSchema).nonempty({ message: t('kgstudio.image.upload-required') }),
      favicon_image_url: z.array(ImageSchema).nonempty({ message: t('kgstudio.image.upload-required') }),
    })
    .refine(
      (data) => {
        return (
          getTime(new Date(getDateTimeDisplay(data.mintTimeStart, data.mintTimeStartTime))) <
          getTime(new Date(getDateTimeDisplay(data.mintTimeEnd, data.mintTimeEndTime)))
        );
      },
      {
        message: t('kgstudio.time.end-after-start'),
        path: ['mintTimeEnd'],
      },
    );

export type CollectionFormValues = z.infer<ReturnType<typeof FormSchema>>;

export type PageStatus = 'pending' | 'draft' | 'failed' | 'published';
