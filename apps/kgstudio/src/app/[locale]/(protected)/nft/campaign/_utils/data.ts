import { addDays, endOfDay, fromUnixTime, getTime, startOfDay } from 'date-fns';
import { pipe } from 'fp-ts/lib/function';

import { removeNullish } from '@/app/_common/lib/utils';

import { getDateTimeDisplay } from '.';
import { MINT_DEADLINE } from '../_constant';
import { CollectionFormValues, NFTCollectionDetail, NFTCollectionStatus, UpdateCollectionRequest } from '../_types';

export const transformCollectionFormValuesToBackend = (
  collectionFormValues: CollectionFormValues,
  status: NFTCollectionStatus,
  id?: string,
): UpdateCollectionRequest => {
  const reqData = {
    status,
    collection_name: collectionFormValues.collection_name,
    symbol_name: collectionFormValues.symbol_name,
    collection_description: collectionFormValues.collection_description,
    contract_schema_name: collectionFormValues.contract_schema_name,
    ...(collectionFormValues.max_supply && { max_supply: Number(collectionFormValues.max_supply) }),
    start_time:
      collectionFormValues.mintTimeStartType === 'instant'
        ? Number((getTime(new Date()) / 1000).toFixed(0))
        : Number(
            getTime(
              new Date(getDateTimeDisplay(collectionFormValues.mintTimeStart, collectionFormValues.mintTimeStartTime)),
            ) / 1000,
          ),
    end_time:
      collectionFormValues.mintTimeEndType === 'instant'
        ? Number((getTime(new Date(MINT_DEADLINE)) / 1000).toFixed(0))
        : Number(
            (
              getTime(
                new Date(getDateTimeDisplay(collectionFormValues.mintTimeEnd, collectionFormValues.mintTimeEndTime)),
              ) / 1000
            ).toFixed(0),
          ),
    title: collectionFormValues.mintPageTitle,
    subtitle: collectionFormValues.mintPageSubTitle,
    msg_content: collectionFormValues.msg_content,
    ...(id && { project_id: Number(id) }),
  };

  return reqData;
};

export const transformBackendToCollectionFormValues = (
  detailData: NFTCollectionDetail,
  isEditing: boolean,
): Partial<CollectionFormValues> => {
  const formData = {
    collection_image_url: [{ dataURL: detailData?.collection_image_url }],
    banner_image_url: [{ dataURL: detailData?.banner_image_url }],
    favicon_image_url: [{ dataURL: detailData?.favicon_image_url }],

    collection_name: detailData?.collection_name as string,
    symbol_name: detailData?.symbol_name,
    collection_description: detailData?.collection_description,
    contract_schema_name: detailData?.contract_schema_name,
    max_supply: detailData?.max_supply === 0 ? '' : detailData?.max_supply?.toString(),

    // note: 特別處理
    mintTimeStartType: 'customized' as 'customized' | 'instant',
    mintTimeStart:
      isEditing && detailData?.start_time
        ? fromUnixTime(Math.floor(detailData?.start_time))
        : startOfDay(addDays(new Date(), 1)),
    mintTimeEndType: 'customized' as 'customized' | 'instant',
    mintTimeEnd:
      isEditing && detailData?.end_time
        ? fromUnixTime(Math.floor(detailData?.end_time))
        : endOfDay(addDays(new Date(), 1)),
    mintTimeStartTime: '00:00',
    mintTimeEndTime: '23:59',

    received_method: ['phone'] as ('phone' | 'email' | 'address')[],
    mintPageTitle: detailData?.title ?? '免費領取 NFT',
    mintPageSubTitle: detailData?.subtitle ?? detailData?.collection_name,
    msg_content: detailData?.msg_content,
  };

  return pipe(formData, removeNullish) as Partial<CollectionFormValues>;
};
