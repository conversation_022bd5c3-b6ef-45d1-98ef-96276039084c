import { format } from 'date-fns';
import { truncate } from 'lodash-es';

import { FormattedMessage } from '@/app/_common/components';
import { Badge, ReminderBlock } from '@kryptogo/2b';

import { NFTCollectionPublishStatus, NFTCollectionStatus, PageStatus } from '../_types';

export const truncateMintPageUrl = (url: string) =>
  truncate(url, {
    length: 26,
    omission: '...',
  });

export const getGMTTimezone = (timezoneOffset: number) => {
  const timezoneOffsetHours = timezoneOffset / 60;
  const sign = timezoneOffsetHours >= 0 ? '-' : '+';
  return `${sign}${Math.abs(timezoneOffsetHours)}`;
};

export const getDateTimeDisplay = (date: Date, time: string) => {
  const gmt = getGMTTimezone(date.getTimezoneOffset());
  return `${format(date, `yyyy/MM/dd`)} ${time} (GMT${gmt})`;
};
export const getStepTitle = (step: string) => {
  switch (step) {
    case 'collection':
      return <FormattedMessage id="kgstudio.nft.step.collection" />;
    case 'mint':
      return <FormattedMessage id="kgstudio.nft.step.mint" />;
    case 'check':
      return <FormattedMessage id="kgstudio.nft.step.check" />;

    default:
      break;
  }
};

export const getNotificationDisplay = (pageStatus: PageStatus) => {
  switch (pageStatus) {
    case 'pending':
      return (
        <ReminderBlock variant="warning" description={<FormattedMessage id="kgstudio.nft.notification.pending" />} />
      );
    case 'draft':
      return (
        <ReminderBlock variant="warning" description={<FormattedMessage id="kgstudio.nft.notification.draft" />} />
      );
    case 'failed':
      return (
        <ReminderBlock variant="warning" description={<FormattedMessage id="kgstudio.nft.notification.failed" />} />
      );
    case 'published':
      return (
        <ReminderBlock variant="success" description={<FormattedMessage id="kgstudio.nft.notification.published" />} />
      );

    default:
      break;
  }
};

export const getContractTypeBadgeDisplay = (status: string) => {
  switch (status) {
    case 'ERC721':
      return <Badge variant="grey">ERC-721</Badge>;
    case 'ERC1155':
      return <Badge variant="grey">ERC-1155</Badge>;

    default:
      break;
  }
};

export const getPublishStatusBadgeDisplay = (pageStatus: PageStatus) => {
  switch (pageStatus) {
    case 'pending':
      return (
        <Badge variant="yellow">
          <FormattedMessage id="kgstudio.nft.status.pending" />
        </Badge>
      );

    case 'failed':
      return (
        <Badge variant="red">
          <FormattedMessage id="kgstudio.nft.status.failed" />
        </Badge>
      );
    case 'draft':
      return (
        <Badge variant="grey">
          <FormattedMessage id="kgstudio.nft.status.draft" />
        </Badge>
      );
    case 'published':
      return (
        <Badge variant="green">
          <FormattedMessage id="kgstudio.nft.status.published" />
        </Badge>
      );

    default:
      break;
  }
};

export const getCollectionStatusBadgeDisplay = (pageStatus: PageStatus) => {
  switch (pageStatus) {
    case 'draft':
      return (
        <Badge variant="grey">
          <FormattedMessage id="kgstudio.nft.status.draft" />
        </Badge>
      );
    case 'pending':
      return (
        <Badge variant="yellow">
          <FormattedMessage id="kgstudio.nft.status.pending" />
        </Badge>
      );
    case 'published':
      return (
        <Badge variant="green">
          <FormattedMessage id="kgstudio.nft.status.published" />
        </Badge>
      );
    case 'failed':
      return (
        <Badge variant="red">
          <FormattedMessage id="kgstudio.nft.status.failed" />
        </Badge>
      );

    default:
      break;
  }
};

export const getInfoDisplay = (pageStatus: PageStatus | null, contract_address?: string, user?: string) => {
  const data = (() => {
    switch (pageStatus) {
      case 'pending':
        return {
          contract: <FormattedMessage id="kgstudio.nft.status.pending" />,
          creator: <FormattedMessage id="kgstudio.nft.status.pending" />,
        };
      case 'draft':
      case 'failed':
        return {
          contract: <FormattedMessage id="kgstudio.nft.info.na" />,
          creator: <FormattedMessage id="kgstudio.nft.info.na" />,
        };
      case 'published':
        return { contract: contract_address, creator: user };
      default:
        break;
    }
  })();

  return (
    <div>
      <div>
        <span className="text-body-2 text-description inline-block w-[80px]">
          <FormattedMessage id="kgstudio.nft.info.contract" />
        </span>
        <span className="text-body-2-bold text-primary">{data?.contract}</span>
      </div>
      <div>
        <span className="text-body-2 text-description inline-block w-[80px]">
          <FormattedMessage id="kgstudio.nft.info.creator" />
        </span>
        <span className="text-body-2-bold text-primary">{data?.creator}</span>
      </div>
    </div>
  );
};

export const getPageStatus = (status: NFTCollectionStatus, publishStatus: NFTCollectionPublishStatus) => {
  if (status === 'draft') return 'draft';
  if (status === 'publish' && publishStatus === 'success') return 'published';
  if (status === 'publish' && publishStatus === 'failed') return 'failed';
  if (status === 'publish' && publishStatus === 'pending') return 'pending';

  return;
};
