import { formatInTimeZone } from 'date-fns-tz';

export const TimeRange = ({ startTime, endTime }: { startTime: number; endTime: number }) => {
  // Automatically detect the user's timezone
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const startDate = formatInTimeZone(startTime, timeZone, 'yyyy/MM/dd');
  const startTimezoneTime = formatInTimeZone(startTime, timeZone, 'HH:mm');
  const endDate = formatInTimeZone(endTime, timeZone, 'yyyy/MM/dd');
  const endTimeZoneTime = formatInTimeZone(endTime, timeZone, 'HH:mm');

  return (
    <div className="flex items-center gap-2">
      <div>
        <span className="text-body-2-bold">{startDate}</span>
        <br />
        <span className="text-body-2 text-secondary">{startTimezoneTime}</span>
      </div>
      ~
      <div>
        <span className="text-body-2-bold">{endDate}</span>
        <br />
        <span className="text-body-2 text-secondary">{endTimeZoneTime}</span>
      </div>
    </div>
  );
};
