import { ApiResponse } from '@/app/_common/lib/api';
import axios from '@/app/_common/lib/axios/instances/internal';
import { UseMutationOptionType, UseQueryOptionType } from '@/app/_common/lib/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';

import { NFTCollectionDetailResponse, NFTCollectionsResponse, UpdateCollectionRequest } from '../_types';

export type UpdateCollectionResponse = ApiResponse<{
  project_id: number;
}>;

export type getNftCollectionsQuery = {
  q?: string;
  page_number?: number;
  page_size?: number;
};

// collection list
const getNFTCollections = async (params: getNftCollectionsQuery, org_id: number): Promise<NFTCollectionsResponse> =>
  (
    await axios.get<NFTCollectionsResponse>(`/studio/organization/${org_id}/nft/projects`, {
      params,
    })
  ).data;
export const useCollections = (
  params: getNftCollectionsQuery,
  org_id: number,
  options?: UseQueryOptionType<NFTCollectionsResponse>,
) => useQuery(['collections', params, org_id], () => getNFTCollections(params, org_id), options);

// collection detail
const getCollectionDetail = async (id: string, org_id: number): Promise<NFTCollectionDetailResponse> =>
  (await axios.get<NFTCollectionDetailResponse>(`/studio/organization/${org_id}/nft/projects/${id}`)).data;
export const useCollectionDetail = (
  id: string,
  org_id: number,
  options?: UseQueryOptionType<NFTCollectionDetailResponse>,
) => useQuery(['collections', id, org_id], () => getCollectionDetail(id, org_id), options);

export const updateCollection = async (
  data: UpdateCollectionRequest,
  org_id: number,
): Promise<UpdateCollectionResponse> => (await axios.put(`/studio/organization/${org_id}/nft/projects`, data)).data;
export const useUpdateCollection = (
  options?: UseMutationOptionType<UpdateCollectionRequest, UpdateCollectionResponse>,
) =>
  useMutation(
    ({
      org_id,
      ...data
    }: UpdateCollectionRequest & {
      org_id: number;
    }) => updateCollection(data, org_id),
    options,
  );
