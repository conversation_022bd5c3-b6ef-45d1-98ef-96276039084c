'use client';

import { find } from 'lodash-es';
import { ArrowDownToLine, Copy, Image as ImageIcon, Pen, TrendingUp, User2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import QRCode from 'qrcode.react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { BorderSection } from '@/app/[locale]/(protected)/nft/campaign/_components/BorderSection';
import { useKGAccount } from '@/app/[locale]/auth/login/_services/query/getKgAccount';
import { FormattedMessage } from '@/app/_common/components';
import { ChainBadge } from '@/app/_common/components/badge';
import { FormInput, FormMediaUploader } from '@/app/_common/components/form';
import { Media, useGcpFileUpload } from '@/app/_common/hooks';
import { cn, formatDate } from '@/app/_common/lib/utils';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Form, Modal, Separator, Skeleton, StatusCard } from '@kryptogo/2b';
import { DECIMAL_DISPLAY_MODE, formatCurrency } from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';

import { MINT_PAGE_URL } from '../_constant';
import { useCollectionDetail, useUpdateCollection } from '../_services';
import { PageStatus } from '../_types';
import {
  getCollectionStatusBadgeDisplay,
  getContractTypeBadgeDisplay,
  getInfoDisplay,
  getNotificationDisplay,
  getPublishStatusBadgeDisplay,
} from '../_utils';

const ImageSchema = z.object({
  dataURL: z.string(),
  file: z
    .object({
      name: z.string(),
      size: z.number(),
      type: z.string(),
    })
    .optional(),
});

const MintPageInfoSchema = (t: any) =>
  z.object({
    title: z.string().min(1),
    subtitle: z.string().min(1),
    favicon_image_url: z.array(ImageSchema).nonempty({ message: t('kgstudio.image.upload-required') }),
  });
type MintPageInfo = z.infer<ReturnType<typeof MintPageInfoSchema>>;

const CollectionDetail = () => {
  const t = useTranslations();
  const orgId = useOrganizationStore((state) => state.orgId);
  const [modalOpen, setModalOpen] = useState(false);
  const params = useParams();
  const id = params == null ? '' : (params.id as string);
  const { data, isLoading, error, refetch } = useCollectionDetail(`${id}`, orgId ?? -1, { enabled: !!id && !!orgId });
  const queryClient = useQueryClient();
  const { data: kgAccountResp, error: kgAccountError } = useKGAccount(orgId ?? -1);
  const kgPolygonAccountAddress = kgAccountResp && find(kgAccountResp?.data, { chain_id: 'matic' })?.address;
  const {
    mutate,
    isLoading: updateLoading,
    data: updateData,
    error: updateError,
    reset: updateCollectionReset,
  } = useUpdateCollection({
    onSuccess: (data, variables) => {
      setModalOpen(false);
      queryClient.invalidateQueries({ queryKey: ['collections', id] });
      refetch();
    },
  });
  const form = useForm<MintPageInfo>({
    resolver: zodResolver(MintPageInfoSchema(t)),
    defaultValues: {
      title: data?.data?.title,
      subtitle: data?.data?.subtitle,
      favicon_image_url: [
        {
          dataURL: data?.data?.favicon_image_url,
        },
      ],
    },
    mode: 'all',
  });

  const {
    // isError: fileUrlsError,
    // isLoading: fileUrlsLoading,
    // isSuccess: fileUrlsSuccess,
    // isIdle: fileUrlsIdle,
    uploadAllFiles,
    reset: fileUrlsReset,
  } = useGcpFileUpload();

  const downloadQR = () => {
    const canvas = document.querySelector('canvas') as HTMLCanvasElement;
    const link = document.createElement('a');
    link.href = canvas.toDataURL();
    link.download = 'imgName' + '-QR.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const pageStatus: PageStatus | null = (() => {
    const status = data?.data?.status;
    const publishStatus = data?.data?.publish_status;

    if (status === 'draft') return 'draft' as PageStatus;
    if (status === 'publish' && publishStatus === 'success') return 'published' as PageStatus;
    if (status === 'publish' && publishStatus === 'failed') return 'failed' as PageStatus;
    if (status === 'publish' && publishStatus === 'pending') return 'pending' as PageStatus;
    return null;
  })();

  const getQrCodeDisplay = (pageStatus: PageStatus | null) => {
    switch (pageStatus) {
      case 'pending':
        return (
          <div className="border-primary row-span-4 flex flex-col items-center justify-center gap-6 rounded-2xl border p-6 text-lg font-bold text-[#1B2559]">
            <FormattedMessage id="kgstudio.nft.mint-page.pending" />
          </div>
        );
      case 'draft':
      case 'failed':
        return (
          <div className="border-primary row-span-4 flex flex-col items-center justify-center gap-6 rounded-2xl border p-6 text-lg font-bold text-[#1B2559]">
            <FormattedMessage id="kgstudio.nft.mint-page.na" />
          </div>
        );
      case 'published':
        return (
          <div className="border-primary row-span-4 flex flex-col items-center justify-center gap-6 rounded-2xl border p-6 text-lg font-bold text-[#1B2559]">
            <div>
              <FormattedMessage id="kgstudio.nft.scan-to-visit" />
            </div>

            <QRCode id="QRCode-svg" value={`${MINT_PAGE_URL}/${data?.data?.event_id}`} />

            <div className="flex gap-3">
              <div
                className="bg-warning-light h-12 w-12 cursor-pointer rounded-xl p-3.5"
                data-cy="nft-qr-code-download"
                onClick={downloadQR}
              >
                <ArrowDownToLine size={20} className="text-brand-primary" />
              </div>
              <div
                className="bg-warning-light h-12 w-12 cursor-pointer rounded-xl p-3.5"
                onClick={() => navigator.clipboard.writeText(`${MINT_PAGE_URL}/${data?.data?.event_id}`)}
              >
                <Copy size={20} className="text-brand-primary" />
              </div>
              {/* v1 不會有share  */}
              {/* <div className="h-12 w-12 rounded-xl bg-[#FFF7DC] p-3.5">
                <Share2 size={20} color="#FFC211" />
              </div> */}
            </div>
            <div className="border-primary w-full break-all rounded-xl border p-3 text-base underline">
              <Link
                href={`${MINT_PAGE_URL}/${data?.data?.event_id}`}
                target="_blank"
                rel="noreferrer"
                className="text-highlight line-clamp-2 text-sm underline"
              >{`${MINT_PAGE_URL}/${data?.data?.event_id}`}</Link>
            </div>
          </div>
        );

      default:
        break;
    }
  };

  useEffect(() => {
    form.reset({
      title: data?.data?.title ?? '',
      subtitle: data?.data?.subtitle ?? '',
      favicon_image_url: [{ dataURL: data?.data?.favicon_image_url }],
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  if (error) {
    console.log('error', error);
  }
  const onSubmit = async () => {
    const reqData = {
      project_id: data?.data?.project_id as number,
      title: form.getValues('title'),
      subtitle: form.getValues('subtitle'),
      start_time: data?.data?.start_time,
      end_time: data?.data?.end_time,
    };
    const files = form.getValues('favicon_image_url');
    const localFiles = files.filter((data) => !!data.file);

    const filesWithKey =
      localFiles.length === 0
        ? null
        : {
            favicon_image_url: localFiles as Media[],
          };

    const fileUrls = !!filesWithKey ? await uploadAllFiles(filesWithKey, 'nfts') : null;

    fileUrls &&
      Object.keys(fileUrls).forEach((key) =>
        Object.assign(reqData, {
          [key]: fileUrls[key][0],
        }),
      );

    mutate({ org_id: orgId ?? -1, ...reqData });
  };

  const handleReset = () => {
    form.reset();
    fileUrlsReset();
    updateCollectionReset();
  };

  return (
    <>
      <Modal
        open={modalOpen}
        onOpenChange={(open: boolean) => {
          if (!open) {
            handleReset();
          }
          setModalOpen(open);
        }}
      >
        <Form {...form}>
          <Modal.Content>
            <Modal.Header>
              <Modal.Title>{t('kgstudio.nft.modal.edit-mint-page')}</Modal.Title>
            </Modal.Header>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-10">
              <div className="space-y-10">
                <FormInput
                  title={t('kgstudio.nft.form.title')}
                  name="title"
                  control={form.control}
                  data-cy="nft-website-title"
                  required
                />
                <FormInput
                  title={t('kgstudio.nft.form.subtitle')}
                  name="subtitle"
                  control={form.control}
                  data-cy="nft-website-subTitle"
                  required
                />
                <FormMediaUploader
                  name="favicon_image_url"
                  title={t('kgstudio.nft.form.favicon-image-title')}
                  previewInZone
                  dropZoneRatio="1/1"
                  dropZoneWidth={128}
                  control={form.control}
                  dropZoneDesc={
                    <ul className="list-inside list-disc">
                      <li>{t('kgstudio.nft.form.upload-ico-desc')}</li>
                    </ul>
                  }
                  data-cy="nft-website-favicon"
                />
              </div>
            </form>
            <Modal.Footer className="flex w-full items-center justify-end">
              <Button type="submit" className="w-[200px]" onClick={form.handleSubmit(onSubmit)}>
                {t('kgstudio.common.complete')}
              </Button>
            </Modal.Footer>
          </Modal.Content>
        </Form>
        <h1 className="text-center text-[28px] font-bold">{data?.data?.collection_name}</h1>
        <br />
        {isLoading ? (
          <Skeleton className="my-6 h-[66px] w-full" />
        ) : (
          <div className="my-6" data-cy="create-nft-detail-notification">
            {pageStatus && getNotificationDisplay(pageStatus)}
          </div>
        )}
        <Card className="overflow-hidden" data-cy="create-nft-detail-basic-info-card">
          <div className="relative h-[200px] w-full overflow-hidden">
            {isLoading ? (
              <Skeleton className="h-[200px] w-full" />
            ) : data?.data?.banner_image_url ? (
              <Image src={data?.data?.banner_image_url} alt={'banner_image_url'} fill className="object-cover" />
            ) : (
              <div className="bg-disabled h-[200px] w-full" />
            )}
          </div>
          <div className="relative top-[-40px] flex gap-10 px-10">
            {isLoading ? (
              <Skeleton className="h-[264px] w-[264px]" />
            ) : data?.data?.collection_image_url ? (
              <Image
                src={data?.data?.collection_image_url}
                width={264}
                height={264}
                alt={'collection_image_url'}
                data-cy="nft-detail-collection-image"
                // FIXME: workaround for animated remote image
                unoptimized
              />
            ) : (
              <div className="bg-disabled h-[264px] w-[264px]" />
            )}

            <div className="mt-20 flex flex-col gap-6">
              {isLoading ? (
                <Skeleton className="h-7 w-[120px]" />
              ) : (
                <div className="text-h2 text-primary">{data?.data?.collection_name}</div>
              )}
              <div className="flex gap-2.5">
                <ChainBadge chain={'polygon'} />
                {isLoading ? (
                  <Skeleton className="h-7 w-[70px]" />
                ) : (
                  getContractTypeBadgeDisplay(data?.data?.contract_schema_name as string)
                )}
                {isLoading ? <Skeleton className="h-7 w-[70px]" /> : getPublishStatusBadgeDisplay(pageStatus!)}
              </div>
              {isLoading ? (
                <>
                  <Skeleton className="h-6 w-[440px]" />
                  <Skeleton className="h-6 w-[440px]" />
                </>
              ) : (
                getInfoDisplay(pageStatus, data?.data?.contract_address, kgPolygonAccountAddress as string)
              )}
            </div>
          </div>

          <div className="flex flex-col gap-2 px-10 pb-10">
            <div className="text-body-2 text-description">{t('kgstudio.common.description')}</div>
            {isLoading ? (
              <Skeleton className="h-7 w-full" />
            ) : (
              <p className="text-lg">{data?.data?.collection_description}</p>
            )}
          </div>
        </Card>
        <br />

        {pageStatus === 'published' && (
          <div className="grid grid-cols-3 gap-5" data-cy="nft-status-cards">
            <StatusCard
              title={t('kgstudio.nft.status.claimed')}
              description={`${data?.data?.total_supply ?? 0}`}
              icon={<User2 color="#B0BBD5" />}
            />
            <StatusCard
              title={t('kgstudio.nft.status.total')}
              description={`${data?.data?.max_supply}`}
              icon={<ImageIcon color="#B0BBD5" />}
            />
            <StatusCard
              title={t('kgstudio.nft.status.claim-rate')}
              description={`${
                data?.data?.total_supply &&
                data?.data?.max_supply &&
                formatCurrency({
                  amount: (data?.data?.total_supply * 100) / data?.data?.max_supply,
                  decimals: DECIMAL_DISPLAY_MODE.PERCENT,
                })
              } %`}
              icon={<TrendingUp color="#B0BBD5" />}
            />
          </div>
        )}

        <br />
        <Card className="p-10" data-cy="create-nft-detail-mint-page-card">
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold">{t('kgstudio.nft.mint-page-title')}</h2>
            {pageStatus && getCollectionStatusBadgeDisplay(pageStatus)}
            <Modal.Trigger
              className="bg-brand-primary active:bg-brand-primary-dark text-button-lg ml-auto inline-flex h-12 items-center gap-2 rounded-xl px-6 py-[14px] !pl-4 !pr-6 font-bold text-white"
              data-cy="edit-nft-website-button"
            >
              <Pen className="h-4 w-4" />
              {t('kgstudio.nft.edit-button-text')}
            </Modal.Trigger>
          </div>

          <Separator className="mb-10 mt-6" />

          <div className="grid grid-cols-3 gap-6">
            <BorderSection title={t('kgstudio.nft.start-date-title')} desc={formatDate(data?.data?.start_time ?? 0)} />
            <BorderSection title={t('kgstudio.nft.end-date-title')} desc={formatDate(data?.data?.end_time ?? 0)} />
            {getQrCodeDisplay(pageStatus)}
            <BorderSection
              title={t('kgstudio.nft.delivery-method-title')}
              desc={t('kgstudio.nft.form.received-method-phone')}
            />
            <BorderSection
              title={t('kgstudio.nft.favicon-title')}
              desc={
                <>
                  {data?.data?.favicon_image_url && (
                    <Image alt="favicon" width="25" height="25" src={data?.data?.favicon_image_url} />
                  )}
                </>
              }
            />
            <BorderSection title={t('kgstudio.nft.title-title')} desc={data?.data?.title} className="col-span-2" />
            <BorderSection
              title={t('kgstudio.nft.subtitle-title')}
              desc={data?.data?.subtitle}
              className="col-span-2"
            />

            <div className={cn('border-primary col-span-3 flex flex-col gap-1 rounded-2xl border p-6')}>
              <h6 className="text-body-2 text-secondary">{t('kgstudio.nft.success-sms-title')}</h6>
              <span className="text-primary">{data?.data?.msg_content}</span>
            </div>
          </div>
        </Card>
      </Modal>
    </>
  );
};
export default CollectionDetail;
