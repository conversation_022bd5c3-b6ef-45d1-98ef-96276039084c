'use client';

import { MoreVertical, Pencil, Plus, Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useEffect, useMemo, useState } from 'react';

import { Image } from '@/app/_common/components';
import { useDebounce, usePageHeader, useUpdateEffect } from '@/app/_common/hooks';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { useRouter } from '@/i18n/navigation';
import { Button, Card, DataTable, Input, ProgressBar, useDataTable, DropdownMenu } from '@kryptogo/2b';
import { getExplorerUrl, truncateTxhashOrAddress } from '@kryptogo/utils';
import { ColumnDef, TableOptions, getCoreRowModel, useReactTable } from '@tanstack/react-table';

import { TimeRange } from '../_components';
import { CHAIN_ID, MINT_PAGE_URL } from '../_constant';
import { useCollections } from '../_services';
import { NFTCollectionsResponse } from '../_types';
import { getCollectionStatusBadgeDisplay, getPageStatus, truncateMintPageUrl } from '../_utils';

const Overview = () => {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.common.nft-boost') });
  const router = useRouter();
  const orgId = useOrganizationStore((state) => state.orgId);

  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);

  const [tableOption, setTableOption] = useState<TableOptions<NFTCollectionsResponse['data'][number]>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size } = useDataTable(table);

  const columns: ColumnDef<NFTCollectionsResponse['data'][number]>[] = useMemo(
    () => [
      {
        accessorKey: 'collection_image_url',
        header: t('kgstudio.nft.nft-collection'),
        size: 200,
        cell: ({ row }) => {
          const { collection_image_url, collection_name, contract_address } = row.original;
          return (
            <div className="grid grid-cols-[auto_1fr] items-center gap-2">
              <div className="relative h-[54px] w-[54px] overflow-hidden rounded-[5px]">
                {collection_image_url ? (
                  <Image src={collection_image_url} fill alt={collection_name} />
                ) : (
                  <div className="bg-disabled h-full w-full" />
                )}
              </div>
              <div className="flex w-full flex-col truncate">
                <p className="text-body-2-bold">{collection_name}</p>
                <Link
                  href={getExplorerUrl('address', CHAIN_ID, contract_address) ?? '#'}
                  target="_blank"
                  rel="noreferrer"
                >
                  <span className="text-small text-secondary underline decoration-1">
                    {contract_address && truncateTxhashOrAddress(contract_address)}
                  </span>
                </Link>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'total_supply',
        header: t('kgstudio.nft.claimed-total'),
        size: 140,
        cell: ({ row }) => {
          const { total_supply, max_supply } = row.original;
          return <ProgressBar progress={total_supply} total={max_supply} />;
        },
      },
      {
        accessorKey: 'start_time',
        header: t('kgstudio.nft.mint-time'),
        size: 200,
        cell: ({ row }) => {
          const { start_time, end_time } = row.original;
          return <TimeRange startTime={start_time * 1000} endTime={end_time * 1000} />;
        },
      },
      {
        accessorKey: 'event_id',
        header: t('kgstudio.nft.mint-page-name'),
        cell: ({ row }) => {
          const { event_id, status, publish_status } = row.original;
          const pageStatus = getPageStatus(status, publish_status);
          return pageStatus === 'published' ? (
            <Link
              href={`${MINT_PAGE_URL}/${event_id}`}
              target="_blank"
              rel="noreferrer"
              className="text-highlight text-sm underline"
            >
              {truncateMintPageUrl(`...events/${event_id}`)}
            </Link>
          ) : (
            ''
          );
        },
      },
      {
        accessorKey: 'status',
        header: t('common.status.text'),
        size: 100,
        cell: ({ row }) => {
          const { status, publish_status } = row.original;
          const pageStatus = getPageStatus(status, publish_status);
          return pageStatus && getCollectionStatusBadgeDisplay(pageStatus);
        },
      },
      {
        accessorKey: 'action',
        header: t('common.action'),
        size: 70,
        cell: ({ row }) => {
          const { status, publish_status, project_id } = row.original;
          const pageStatus = getPageStatus(status, publish_status);

          return (
            <DropdownMenu>
              <DropdownMenu.Trigger>
                <MoreVertical className="text-placeholder" size={16} />
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                {pageStatus === 'draft' && (
                  <DropdownMenu.Item
                    onClick={(e) => {
                      router.push(`/nft/campaign/${project_id}/edit`);
                      e.stopPropagation();
                    }}
                  >
                    {t('kgstudio.common.edit')}
                  </DropdownMenu.Item>
                )}
                <DropdownMenu.Item onClick={() => router.push(`/nft/campaign/${project_id}`)}>
                  {t('kgstudio.review.details')}
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu>
          );
        },
      },
    ],
    [router, t],
  );

  const { data, isLoading } = useCollections(
    {
      q: debouncedQuery.trim() === '' ? undefined : debouncedQuery,
      page_number,
      page_size,
    },
    orgId ?? -1,
    {
      enabled: !!orgId,
    },
  );

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: data?.data || [],
      columns,
    }));
  }, [columns, data]);

  useUpdateEffect(() => {
    table.resetPageIndex();
  }, [debouncedQuery]);

  return (
    <div className="space-y-5">
      <Card className="border-primary border !p-0">
        <div className="flex flex-col items-start gap-4 p-4 md:!flex-row md:!items-center md:justify-between md:!gap-0 md:p-6">
          <h2 className="text-h2 text-primary">{t('kgstudio.nft.nft-projects')}</h2>
          <div className="flex gap-4">
            <Input
              className="w-full md:!w-[231px]"
              type="text"
              placeholder={t('kgstudio.common.search')}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
              value={query}
              suffix={<Search />}
            />
            <Button
              icon={<Plus />}
              iconPosition="left"
              className="w-[100px] py-0"
              data-cy="create-nft-collection-button"
              onClick={() => router.push('/nft/campaign/create')}
            >
              {t('kgstudio.common.create')}
            </Button>
          </div>
        </div>
        <DataTable
          table={table}
          isLoading={isLoading}
          dataLength={data?.paging.total_count || 0}
          onRowClick={(rowData) => {
            router.push(`/nft/campaign/${rowData.project_id}`);
          }}
        />
      </Card>
    </div>
  );
};

export default Overview;
