import { cn } from '@/app/_common/lib/utils';

export const BorderSection = ({
  title,
  desc,
  className,
}: {
  title: string;
  desc: string | React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn('border-primary flex flex-col gap-1 rounded-2xl border p-6', className)}>
      <h6 className="text-body-2 text-secondary">{title}</h6>
      <span className="text-primary text-body-bold">{desc}</span>
    </div>
  );
};
