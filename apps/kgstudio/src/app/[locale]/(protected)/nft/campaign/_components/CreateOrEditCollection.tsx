'use client';

import BigNumber from 'bignumber.js';
import { addDays, endOfDay, fromUnixTime, isAfter, startOfDay } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { find } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useBalance, useFeeData } from 'wagmi';

import { showToast } from '@/2b/toast';
import { useKGAccount } from '@/app/[locale]/auth/login/_services/query/getKgAccount';
import { Image, LoadingModal } from '@/app/_common/components';
import {
  FormDayPicker,
  FormInput,
  FormMediaUploader,
  FormMultiSelect,
  FormRadioGroup,
  FormTextarea,
  FormTimePicker,
} from '@/app/_common/components/form';
import { useGcpFileUpload, useIsClient } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import opensea from '@/assets/opensea.png';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Card,
  Form,
  FormHint,
  FormLabel,
  Input,
  Label,
  ReminderBlock,
  Separator,
  Steps,
  Textarea,
} from '@kryptogo/2b';
import { truncateTxhashOrAddress } from '@kryptogo/utils';
import { useQueryClient } from '@tanstack/react-query';

import { CHAIN_ID_NUMBER, CREATE_COLLECTION_GAS_LIMIT, MINT_DEADLINE, MINT_NFT_GAS_LIMIT } from '../_constant';
import { useCollectionDetail, useUpdateCollection } from '../_services';
import { CollectionFormValues, FormSchema } from '../_types';
import { getDateTimeDisplay, getStepTitle } from '../_utils';
import { transformCollectionFormValuesToBackend } from '../_utils/data';

const getSteps = (
  t: any,
): {
  stepNumber: number;
  label: string;
  value: string;
  desc?: React.ReactNode;
  notification?: boolean;
  fields?: (keyof CollectionFormValues)[];
}[] => [
  {
    stepNumber: 1,
    label: 'NFT Collection',
    value: 'collection',
    desc: t('kgstudio.nft.nft-collection-chain'),
    notification: true,
    fields: [
      'collection_name',
      'symbol_name',
      'collection_description',
      'contract_schema_name',
      'max_supply',
      'collection_image_url',
      'banner_image_url',
    ],
  },
  {
    stepNumber: 2,
    label: 'Mint page',
    value: 'mint',
    fields: ['mintTimeStartType', 'mintTimeEndType', 'mintTimeStart', 'mintTimeEnd'],
  },
  {
    stepNumber: 3,
    label: 'Check',
    value: 'check',
  },
];

const CreateOrEditCollection = () => {
  const { isClient } = useIsClient();
  const router = useRouter();
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [currentStepValue, setCurrentStepValue] = useState('collection');
  const params = useParams();
  const orgId = useOrganizationStore((state) => state.orgId);
  const id = params == null ? '' : (params['id'] as string);
  const isEditing = !!id;
  const currentStep = getSteps(t).find((step) => step.value === currentStepValue);
  const [published, setPublished] = useState(false);
  const { data: kgAccountResp, error: kgAccountError } = useKGAccount(orgId ?? -1);
  const kgPolygonAccountAddress = kgAccountResp && find(kgAccountResp?.data, { chain_id: 'matic' })?.address;
  const {
    data: feeData,
    isError: feeError,
    isLoading: feeLoading,
  } = useFeeData({
    chainId: CHAIN_ID_NUMBER,
  });
  const {
    data: balanceData,
    isError: balanceError,
    isLoading: balanceLoading,
  } = useBalance({
    address: kgPolygonAccountAddress as `0x${string}` | undefined,
    enabled: !!kgPolygonAccountAddress,
    chainId: CHAIN_ID_NUMBER,
  });
  const {
    data: detailData,
    isLoading: detailLoading,
    error: detailError,
  } = useCollectionDetail(id, orgId ?? -1, { enabled: isEditing && !!orgId });
  const form = useForm<CollectionFormValues>({
    resolver: zodResolver(FormSchema(t)),
    defaultValues: {
      collection_image_url: [],
      banner_image_url: [],
      favicon_image_url: [],
      collection_name: '',
      symbol_name: '',
      collection_description: '',
      contract_schema_name: 'ERC721',
      max_supply: '',

      mintTimeStartType: 'instant',
      mintTimeStart: startOfDay(addDays(new Date(), 1)),
      mintTimeEndType: 'instant',
      mintTimeEnd: endOfDay(addDays(new Date(), 1)),
      mintTimeStartTime: '00:00',
      mintTimeEndTime: '23:59',

      received_method: ['phone'],
      mintPageTitle: t('kgstudio.nft.free-claim'),
      mintPageSubTitle: '',
      msg_content: t('kgstudio.nft.text.success-sms', {
        collectionName: '',
        appLink: 'https://www.kryptogo.com/tw/products/wallet',
      }),
    },
    mode: 'all',
  });

  const { mutate, isLoading, error, data } = useUpdateCollection({
    onSuccess: (data, variables) => {
      if (variables.status === 'draft') {
        showToast(
          t('kgstudio.nft.saved-successfully-toast', { collectionName: variables.collection_name || '' }),
          'success',
        );
        if (!id) {
          router.push(`/nft/campaign/${data?.data?.project_id}/edit`);
        }
      }
      if (variables.status === 'publish') {
        setPublished(true);
      }
      queryClient.invalidateQueries({ queryKey: ['collections'] });
    },
    onError: (error) => {
      console.error(error);
      if (isApiError(error)) {
        if (error.code === 7001) {
          showToast(t('kgstudio.nft.error.collection-name-existed'), 'error');
        } else if (error.code === 7002) {
          showToast(t('kgstudio.nft.error.project-not-found'), 'error');
        } else {
          showToast(t('kgstudio.common.error'), 'error');
        }
      }
    },
  });
  const {
    isError: fileUrlsError,
    isLoading: fileUrlsLoading,
    isIdle: fileUrlsIdle,
    progress: fileUrlsProgress,
    uploadAllFiles,
  } = useGcpFileUpload();

  const onCreate = async (status: 'draft' | 'publish') => {
    // note: The collection_name field is required when submitting the draft
    if (status === 'draft') {
      const result = await form.trigger('collection_name');
      if (!result) return;
    }

    const reqData = isEditing
      ? transformCollectionFormValuesToBackend(form.getValues(), status, id as string)
      : transformCollectionFormValuesToBackend(form.getValues(), status);

    const imageKeys = ['collection_image_url', 'banner_image_url', 'favicon_image_url'] as const;
    const filesWithKey = imageKeys.reduce((acc, key) => {
      const files = form.watch()[key];

      const localFiles = files?.filter((data) => !!data.file).map((data) => data);

      if (localFiles.length === 0) return acc;

      return {
        ...acc,
        [key]: localFiles,
      };
    }, {});
    const fileUrls = !!filesWithKey ? await uploadAllFiles(filesWithKey, 'nfts') : null;

    fileUrls &&
      Object.keys(fileUrls).forEach((key) =>
        Object.assign(reqData, {
          [key]: fileUrls[key][0],
        }),
      );

    mutate({ org_id: orgId ?? -1, ...reqData });
  };

  const onSubmit = async () => {
    await onCreate('publish');
  };

  const onSaveSubmit = async () => {
    await onCreate('draft');
  };

  const showStartTimeDateTimePicker = form.watch('mintTimeStartType') === 'customized';
  const showEndTimeDateTimePicker = form.watch('mintTimeEndType') === 'customized';
  const collectionPreviewURL = form.watch().collection_image_url?.[0]?.dataURL;
  const bannerPreviewURL = form.watch().banner_image_url?.[0]?.dataURL;
  const faviconPreviewURL = form.watch().favicon_image_url?.[0]?.dataURL;

  const gasPrice = feeData?.formatted?.gasPrice;
  const isPublished = !!(detailData?.data?.publish_status && id);
  const balance = balanceData?.formatted && new BigNumber(balanceData?.formatted).toFixed(6).toString();
  const createCollectionFee =
    gasPrice &&
    new BigNumber(gasPrice)
      .multipliedBy(CREATE_COLLECTION_GAS_LIMIT)
      .dividedBy(new BigNumber(10).pow(10))
      .toFixed(6)
      .toString();
  const mintFee =
    gasPrice &&
    new BigNumber(gasPrice).multipliedBy(MINT_NFT_GAS_LIMIT).dividedBy(new BigNumber(10).pow(10)).toFixed(6).toString();
  const isBalanceEnough = balance && createCollectionFee ? balance > createCollectionFee : false;
  const hasPrevStep = getSteps(t).some((step) => step.stepNumber === currentStep!.stepNumber - 1);
  const hasNextStep = getSteps(t).some((step) => step.stepNumber === currentStep!.stepNumber + 1);
  const handleNextStep = async () => {
    const isValid = await form.trigger(currentStep!.fields);
    if (isValid) {
      const nextStep = getSteps(t).find((step) => step.stepNumber === currentStep!.stepNumber + 1);

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      setCurrentStepValue(nextStep!.value);
    } else {
      console.log('error', form.formState.errors);
    }
  };
  const handlePrevStep = () => {
    const prevStep = getSteps(t).find((step) => step.stepNumber === currentStep!.stepNumber - 1);

    if (!prevStep) return;

    setCurrentStepValue(prevStep.value);
  };

  useEffect(() => {
    form.setValue('mintPageSubTitle', form.getValues('collection_name'));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.getValues('collection_name')]);

  useEffect(() => {
    if (!isEditing || !detailData) return;

    /* NOTE: if draft timestamp before now, set start and end time to default value: */
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const ifBeforeNow = (timestamp: number) => {
      return isAfter(timestamp, Math.floor(Date.now() / 1000)) && isEditing && timestamp;
    };
    const resetStartTime: Pick<CollectionFormValues, 'mintTimeStartType' | 'mintTimeStart' | 'mintTimeStartTime'> =
      (() => {
        const draftStartTimestamp = detailData?.data?.start_time;

        return ifBeforeNow(draftStartTimestamp)
          ? {
              mintTimeStartType: 'customized',
              mintTimeStart: fromUnixTime(Math.floor(draftStartTimestamp)),
              mintTimeStartTime: formatInTimeZone(draftStartTimestamp * 1000, timeZone, 'HH:mm'),
            }
          : {
              mintTimeStartType: 'instant',
              mintTimeStart: startOfDay(addDays(new Date(), 1)),
              mintTimeStartTime: '00:00',
            };
      })();

    const resetEndTime: Pick<CollectionFormValues, 'mintTimeEndType' | 'mintTimeEnd' | 'mintTimeEndTime'> = (() => {
      const draftEndTimestamp = detailData?.data?.end_time;

      return ifBeforeNow(draftEndTimestamp)
        ? {
            mintTimeEndType: 'customized',
            mintTimeEnd: fromUnixTime(Math.floor(draftEndTimestamp)),
            mintTimeEndTime: formatInTimeZone(draftEndTimestamp * 1000, timeZone, 'HH:mm'),
          }
        : {
            mintTimeEndType: 'instant',
            mintTimeEnd: endOfDay(addDays(new Date(), 1)),
            mintTimeEndTime: '23:59',
          };
    })();

    form.reset({
      ...(detailData?.data?.collection_image_url && {
        collection_image_url: [{ dataURL: detailData?.data?.collection_image_url }],
      }),
      ...(detailData?.data?.banner_image_url && {
        banner_image_url: [{ dataURL: detailData?.data?.banner_image_url }],
      }),
      ...(detailData?.data?.favicon_image_url && {
        favicon_image_url: [{ dataURL: detailData?.data?.favicon_image_url }],
      }),

      collection_name: detailData?.data?.collection_name,
      symbol_name: detailData?.data?.symbol_name,
      collection_description: detailData?.data?.collection_description,
      contract_schema_name: detailData?.data?.contract_schema_name,
      max_supply: detailData?.data?.max_supply === 0 ? '' : detailData?.data?.max_supply?.toString(),

      ...resetStartTime,
      ...resetEndTime,

      received_method: ['phone'],
      mintPageTitle: detailData?.data?.title ?? t('kgstudio.nft.free-claim'),
      mintPageSubTitle: detailData?.data?.subtitle ?? detailData?.data?.collection_name,
      msg_content: detailData?.data?.msg_content,
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [detailData]);

  if (!isClient) return null;
  if (!currentStep) {
    return null;
  }
  if (detailData?.data?.status === 'publish') {
    router.push('/nft/campaign/overview');
  }

  return (
    <>
      {published ? (
        <div className="flex flex-col items-center justify-center" data-cy="create-nft-status-dialog">
          <Card className="flex flex-col items-center justify-center gap-6 p-[40px] sm:p-[40px] md:mx-[100px]">
            <div className="flex flex-col items-center justify-center">
              <div className="border-brand-500 h-[60px] w-[60px] animate-spin rounded-full border-b-2" />
              <Label className="mt-8 text-center text-2xl">{t('kgstudio.nft.processing')}</Label>
            </div>

            <Label className="max-w-[600px] items-center text-center text-sm font-normal text-[#909AB6]">
              {t('kgstudio.nft.processing-description')}
            </Label>

            <div className="flex gap-6">
              <Button
                variant="secondary"
                onClick={() => {
                  router.push('/nft/campaign/overview');
                }}
                type="button"
                data-cy="create-nft-status-back-to-overview-button"
              >
                {t('kgstudio.nft.back-to-list')}
              </Button>
              <Button
                variant="secondary"
                onClick={() => {
                  router.push(`/nft/campaign/${data?.data?.project_id}`);
                }}
                type="button"
                data-cy="create-nft-status-go-to-project-detail"
              >
                {t('kgstudio.nft.go-to-project')}
              </Button>
            </div>
          </Card>
        </div>
      ) : (
        <div className="mx-auto max-w-[768px] space-y-10">
          <LoadingModal open={isLoading} />
          <Steps steps={getSteps(t)} value={currentStepValue} onValueChange={setCurrentStepValue}>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-10">
                <Steps.Display />
                <Card className="border-primary space-y-6 border p-10">
                  <div className="flex flex-col items-start gap-2">
                    <h1 className="text-h1 text-primary capitalize">{getStepTitle(currentStep.value)}</h1>
                    {currentStep?.desc && <p className="body-2 text-secondary">{currentStep.desc}</p>}
                  </div>
                  {currentStep.notification && (
                    <ReminderBlock
                      variant="warning"
                      description={
                        <div>
                          <div className="flex">
                            {t('kgstudio.nft.wallet-balance')} {balance} {t('kgstudio.nft.wallet-balance-matic')}
                          </div>
                          <li>
                            {t('kgstudio.nft.create-collection-fee', {
                              createCollectionFee: createCollectionFee || '',
                            })}
                          </li>
                          <li>{t('kgstudio.nft.mint-fee', { mintFee: mintFee || '' })}</li>
                        </div>
                      }
                    />
                  )}
                  <Separator />

                  <Steps.Content value="collection">
                    <fieldset className="space-y-6">
                      <FormMediaUploader
                        name="collection_image_url"
                        title={t('kgstudio.nft.form.nft-image')}
                        previewInZone
                        dropZoneRatio="1/1"
                        dropZoneWidth={200}
                        control={form.control}
                        required
                        dropZoneDesc={
                          <ul className="list-inside list-disc">
                            <li>{t('kgstudio.nft.image-recommended-size')}</li>
                            <li>{t('kgstudio.nft.image-file-types')}</li>
                            <li>{t('kgstudio.nft.image-collection-item')}</li>
                          </ul>
                        }
                        disabled={isPublished}
                      />
                      <FormInput
                        title={t('kgstudio.nft.form.collection-name')}
                        required
                        name="collection_name"
                        control={form.control}
                        placeholder="e.g. KryptoGO Yacht Club 2023"
                        hint={t('kgstudio.nft.form.collection-name-hint')}
                        disabled={isPublished}
                        data-cy="create-nft-input-name"
                      />

                      <FormInput
                        title={t('kgstudio.nft.form.symbol-name')}
                        required
                        name="symbol_name"
                        control={form.control}
                        placeholder="e.g. KGYC"
                        hint={t('kgstudio.nft.form.symbol-name-hint')}
                        disabled={isPublished}
                        data-cy="create-nft-input-symbol"
                      />
                      <FormTextarea
                        title={t('kgstudio.nft.form.collection-description')}
                        required
                        name="collection_description"
                        control={form.control}
                        placeholder={t('kgstudio.nft.form.placeholder.description')}
                        maxLength={1000}
                        hint={t('kgstudio.nft.form.collection-description-hint')}
                        disabled={isPublished}
                        data-cy="create-nft-textarea-description"
                      />

                      <FormMediaUploader
                        name="banner_image_url"
                        title={t('kgstudio.nft.form.nft-opensea-banner')}
                        previewInZone
                        required
                        dropZoneRatio="3/1"
                        dropZoneWidth={600}
                        control={form.control}
                        dropZoneDesc={
                          <ul className="list-inside list-disc">
                            <li>{t('kgstudio.nft.form.banner-file-types')}</li>
                            <li>{t('kgstudio.nft.form.banner-recommended-size')}</li>
                          </ul>
                        }
                        disabled={isPublished}
                      />

                      <FormRadioGroup
                        variant="horizontal"
                        title={t('kgstudio.nft.form.contract-schema-name')}
                        name="contract_schema_name"
                        control={form.control}
                        required
                        items={[
                          {
                            label: 'ERC-721',
                            value: 'ERC721',
                          },
                          {
                            label: 'ERC-1155',
                            value: 'ERC1155',
                          },
                        ]}
                        disabled={isPublished}
                        data-cy="create-nft-radio-contract-type"
                      />

                      <FormInput
                        title={t('kgstudio.nft.form.max-supply')}
                        required
                        name="max_supply"
                        control={form.control}
                        placeholder="e.g. 100"
                        type="number"
                        disabled={isPublished}
                        data-cy="create-nft-input-max-supply"
                      />
                    </fieldset>
                  </Steps.Content>
                  <Steps.Content value="mint">
                    <fieldset className="space-y-6">
                      <FormMultiSelect
                        variant="horizontal"
                        title={t('kgstudio.nft.form.received-method')}
                        name="received_method"
                        control={form.control}
                        items={[
                          {
                            id: 'phone',
                            label: t('kgstudio.nft.form.received-method-phone'),
                            disabled: true,
                          },
                          {
                            id: 'email',
                            label: t('kgstudio.nft.form.received-method-email'),
                            disabled: true,
                          },
                          {
                            id: 'address',
                            label: t('kgstudio.nft.form.received-method-address'),
                            disabled: true,
                          },
                        ]}
                      />

                      <div className="flex flex-col gap-2">
                        <FormLabel>{t('kgstudio.nft.form.max-supply-label')}</FormLabel>
                        <Input value={form.getValues('max_supply')} disabled className={'mb-2'} />
                        <FormHint>{t('kgstudio.nft.form.max-supply-hint', { mintFee: mintFee || '' })}</FormHint>
                      </div>
                      <div className="flex flex-col gap-2">
                        <FormRadioGroup
                          variant="horizontal"
                          title={t('kgstudio.nft.form.mint-time-start')}
                          name="mintTimeStartType"
                          control={form.control}
                          required
                          items={[
                            {
                              label: t('kgstudio.nft.form.mint-time-instant'),
                              value: 'instant',
                            },
                            {
                              label: t('kgstudio.nft.form.mint-time-customized'),
                              value: 'customized',
                            },
                          ]}
                        />

                        {showStartTimeDateTimePicker && (
                          <div className="grid grid-cols-2 justify-start gap-2">
                            <FormDayPicker
                              name="mintTimeStart"
                              control={form.control}
                              fromDate={new Date()}
                              toDate={new Date(MINT_DEADLINE)}
                            />
                            <FormTimePicker name="mintTimeStartTime" control={form.control} />
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col gap-2">
                        <FormRadioGroup
                          variant="horizontal"
                          title={t('kgstudio.nft.form.mint-time-end')}
                          name="mintTimeEndType"
                          control={form.control}
                          required
                          items={[
                            {
                              label: t('kgstudio.nft.form.mint-time-end-instant'),
                              value: 'instant',
                            },
                            {
                              label: t('kgstudio.nft.form.mint-time-customized'),
                              value: 'customized',
                            },
                          ]}
                        />

                        {showEndTimeDateTimePicker && (
                          <>
                            <div className="grid grid-cols-2 justify-start gap-2">
                              <FormDayPicker
                                name="mintTimeEnd"
                                control={form.control}
                                hint={t('kgstudio.nft.form.mint-time-end-hint')}
                                fromDate={new Date()}
                                toDate={new Date(MINT_DEADLINE)}
                              />
                              <FormTimePicker name="mintTimeEndTime" control={form.control} />
                            </div>
                          </>
                        )}
                      </div>
                    </fieldset>
                  </Steps.Content>
                  <Steps.Content value="check">
                    <fieldset className="space-y-6">
                      <h2 className="text-h2">{t('kgstudio.nft.preview.collection')}</h2>
                      <div className="relative h-[420px] overflow-hidden rounded-2xl border-[1px] border-[#E1E9F8]">
                        <div className="bg-surface-secondary text-description flex items-center gap-1.5 overflow-hidden p-2 text-[11px]">
                          <Image src={opensea} alt={'os_image'} width={22} height={22} />
                          OpenSea
                        </div>
                        <div className="relative h-[150px] w-full overflow-hidden">
                          {!!bannerPreviewURL && (
                            <Image src={bannerPreviewURL} alt={'banner_image_url'} fill className="object-cover" />
                          )}
                        </div>
                        <div className="relative top-[-40px] flex gap-10 px-10">
                          {!!collectionPreviewURL && (
                            <Image src={collectionPreviewURL} width={120} height={120} alt={'collection_image_url'} />
                          )}
                        </div>

                        <div className="relative flex flex-col gap-4 px-10">
                          <div>
                            <div className="text-2xl">{form.getValues('collection_name')}</div>
                            <p className="text-primary font-bold">
                              By {kgPolygonAccountAddress && truncateTxhashOrAddress(kgPolygonAccountAddress)}
                            </p>
                          </div>
                          <p className="text-body-3 text-primary">{form.getValues('collection_description')}</p>
                        </div>
                      </div>
                      <Separator className="my-6" />
                      <h2 className="text-2xl font-bold">{t('kgstudio.nft.preview.mint-page')}</h2>

                      <div className="flex gap-6">
                        <div className="flex grow flex-col gap-6">
                          <FormInput
                            title={t('kgstudio.nft.form.title')}
                            control={form.control}
                            name="mintPageTitle"
                            placeholder="e.g. KGYC"
                            required
                          />

                          <FormInput
                            title={t('kgstudio.nft.form.subtitle')}
                            control={form.control}
                            name="mintPageSubTitle"
                            placeholder="e.g. KGYC"
                            required
                          />
                          <FormMediaUploader
                            name="favicon_image_url"
                            title={t('kgstudio.nft.form.favicon')}
                            previewInZone
                            required
                            dropZoneRatio="1/1"
                            dropZoneWidth={128}
                            control={form.control}
                            dropZoneDesc={
                              <ul className="list-inside list-disc">
                                <li>jpg,png,.ico</li>
                              </ul>
                            }
                          />

                          <div className="flex flex-col gap-2">
                            <FormLabel>{t('kgstudio.nft.label.success-sms-preview')}</FormLabel>
                            <Textarea
                              className="h-[120px]"
                              value={t('kgstudio.nft.text.success-sms', {
                                collectionName: form.getValues('collection_name') || '',
                                appLink: 'https://www.kryptogo.com/tw/products/wallet',
                              })}
                              placeholder={t('kgstudio.nft.form.placeholder.description')}
                              disabled
                            />
                          </div>
                        </div>
                        <div className="border-primary h-fit w-[300px] overflow-hidden rounded-2xl border">
                          <div className="bg-surface-secondary text-description text-frame flex items-center gap-1.5 overflow-hidden p-2">
                            {!!faviconPreviewURL ? (
                              <Image src={faviconPreviewURL} alt={'favicon_image_url'} width={16} height={16} />
                            ) : (
                              <div className="bg-brand-primary inline-block h-4 w-4" />
                            )}
                            {form.getValues('collection_name')}
                          </div>
                          <div className="px-5 py-6">
                            <div className="text-primary text-center text-xl font-bold">
                              {form.getValues('mintPageTitle')}
                            </div>

                            {!!collectionPreviewURL && (
                              <Image
                                className="mx-auto my-3"
                                src={collectionPreviewURL}
                                width={172}
                                height={172}
                                alt={'mint_page_img'}
                              />
                            )}
                            <div className="flex flex-col gap-2">
                              <div className="break-all px-8 text-center font-bold">
                                {form.getValues('mintPageSubTitle')}
                              </div>

                              <div>
                                <div className="text-description text-frame text-center">
                                  {getDateTimeDisplay(form.watch('mintTimeStart'), form.watch('mintTimeStartTime'))}
                                </div>
                                <div className="text-description text-frame text-center">
                                  {getDateTimeDisplay(form.watch('mintTimeEnd'), form.watch('mintTimeEndTime'))}
                                </div>
                              </div>
                            </div>
                            <div className="divide-brand-primary mx-auto mt-[10px] grid max-w-sm grid-cols-2 divide-x divide-dotted text-center">
                              <div>
                                <div className="text-brand-primary text-frame uppercase">
                                  {t('kgstudio.nft.claimed')}
                                </div>
                                <div className="text-h1">0</div>
                              </div>
                              <div>
                                <div className="text-brand-primary text-frame uppercase">{t('kgstudio.nft.total')}</div>
                                <div className="text-h1">{form.getValues('max_supply')}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </fieldset>
                  </Steps.Content>
                </Card>
                {!hasNextStep &&
                  (isBalanceEnough ? (
                    <ReminderBlock
                      variant="warning"
                      description={
                        <div>
                          {t('kgstudio.nft.balance-and-fee', {
                            balance: balance || '',
                            createCollectionFee: createCollectionFee || '',
                          })}
                        </div>
                      }
                    />
                  ) : (
                    <ReminderBlock
                      variant="warning"
                      description={
                        <div>
                          <p>{t('kgstudio.nft.insufficient-balance')}</p>
                          <p>
                            {t('kgstudio.nft.balance-and-fee', {
                              balance: balance || '',
                              createCollectionFee: createCollectionFee || '',
                            })}
                          </p>
                          <p>
                            {t('kgstudio.nft.recharge-balance', {
                              balanceDifference:
                                createCollectionFee && balance
                                  ? new BigNumber(createCollectionFee).minus(new BigNumber(balance)).toString()
                                  : '',
                            })}
                          </p>
                        </div>
                      }
                    />
                  ))}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {hasPrevStep && (
                      <Button
                        variant="secondary"
                        onClick={handlePrevStep}
                        type="button"
                        disabled={!hasNextStep && isLoading}
                      >
                        {t('kgstudio.common.prev-step')}
                      </Button>
                    )}
                    <Button
                      variant="secondary"
                      onClick={onSaveSubmit}
                      type="button"
                      disabled={!hasNextStep && isLoading}
                      data-cy="create-nft-save-button"
                    >
                      {t('kgstudio.common.save-draft')}
                    </Button>
                  </div>
                  <Button
                    className="w-[200px]"
                    onClick={hasNextStep ? handleNextStep : form.handleSubmit(onSubmit)}
                    type="button"
                    disabled={(!hasNextStep && !isBalanceEnough) || isLoading}
                    data-cy="create-nft-confirm-button"
                  >
                    {hasNextStep ? t('kgstudio.common.next-step') : t('kgstudio.common.confirm-publish')}
                  </Button>
                </div>
              </form>
            </Form>
          </Steps>
        </div>
      )}
    </>
  );
};

export { CreateOrEditCollection };
