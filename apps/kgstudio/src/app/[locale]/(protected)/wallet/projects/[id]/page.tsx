'use client';

import { pipe } from 'fp-ts/lib/function';
import { pick } from 'lodash-es';
import { CheckCircle2, Eye, Pencil } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { QRCodeCanvas } from 'qrcode.react';
import { useState } from 'react';

import { FormattedMessage } from '@/app/_common/components';
import { ChainBadge } from '@/app/_common/components/badge';
import { useRouter } from '@/i18n/navigation';
import { Accordion, Badge, Button, Card, Modal, Separator, Tabs } from '@kryptogo/2b';

import android from '../_assets/icon-android.svg';
import chrome from '../_assets/icon-chrome.svg';
import extension from '../_assets/icon-extension.svg';
import googlePlay from '../_assets/icon-google-play.svg';
import iosGrey from '../_assets/icon-ios-grey.svg';
import ios from '../_assets/icon-ios.svg';
import placeholder from '../_assets/placeholder.svg';
import { BannerListTile, DappListTile, ProjectInfoEditor, WalletDetailLoading } from '../_components';
import { DEFAULT_FEATURES } from '../_constant';
import { useWalletProjects } from '../_services/query/getWallets';
import { Backend } from '../_types';
import { AppSettings } from '../_types/app';
import { Platform, Status } from '../_types/backend';
import {
  appSettingsKeys,
  getWalletPreviewUri,
  isEmptySettings,
  transformAppSettingsToRemoteConfig,
  transformDtoToAppSettings,
} from '../_utils';
import { WithNull } from '../_utils/type';

const WalletDetail = () => {
  const router = useRouter();
  const t = useTranslations();
  const [demoModalOpen, setDemoModalOpen] = useState(false);
  const params = useParams();
  const projectId = params == null ? '' : (params.id as string);

  const { data: wallets, error: walletsError, isLoading: walletsIsLoading } = useWalletProjects({});

  const wallet = wallets?.data.find((wallet) => wallet.project_id === Number(projectId));

  const getWalletProjectStatus = (status: Status) => {
    switch (status) {
      case 'draft':
        return (
          <Badge variant="grey">
            <FormattedMessage id="kgstudio.wallet.status.draft" />
          </Badge>
        );
      case 'in_review':
        return (
          <Badge variant="yellow">
            <FormattedMessage id="kgstudio.wallet.status.in-review" />
          </Badge>
        );
      case 'published':
        return (
          <Badge variant="green">
            <FormattedMessage id="kgstudio.wallet.status.published" />
          </Badge>
        );
    }
  };
  const getPlatformStatusDisplay = (status: Status, platforms: Platform[] | null) => {
    if (status === 'draft' || !platforms) return null;

    const statusIcon =
      status === 'published' ? (
        <CheckCircle2 className="fill-success absolute bottom-0 right-0 h-[11px] w-[11px] stroke-white" />
      ) : null;

    return (
      <div className="flex items-center gap-2">
        {platforms.includes('ios') && (
          <div className="relative">
            <Image src={iosGrey} alt="apple store" className="h-6 w-6" />
            {statusIcon}
          </div>
        )}
        {platforms.includes('android') && (
          <div className="relative">
            <Image src={android} alt="google play" className="h-6 w-6" />
            {statusIcon}
          </div>
        )}
        {platforms.includes('extension') && (
          <div className="relative">
            <Image src={chrome} alt="chrome extension store" className="h-6 w-6" />
            {statusIcon}
          </div>
        )}
      </div>
    );
  };
  const getStatusBadgeDisplay = (status: Status) => {
    switch (status) {
      case 'draft':
        return null;
      case 'in_review':
        return (
          <Badge variant="yellow" className="mr-2">
            <FormattedMessage id="kgstudio.wallet.status.in-review" />
          </Badge>
        );
      case 'published':
        return (
          <Badge variant="green" className="mr-2">
            <FormattedMessage id="kgstudio.wallet.status.published" />
          </Badge>
        );
    }
  };

  if (!wallet || walletsIsLoading) return <WalletDetailLoading />;
  if (!wallet || walletsError) return <div>Error...</div>;

  const appSettingsDto = pick(
    {
      ...wallet.config,
      ...wallet.theme,
    },
    appSettingsKeys,
  ) as unknown as WithNull<Backend.AppSettings> & WithNull<Backend.WalletTheme>;
  const appSettings = transformDtoToAppSettings(appSettingsDto);

  const { appSettings: emptyAppSettings, publishSettings: emptyPublishSettings } = isEmptySettings(wallet);
  return (
    <div className="space-y-10">
      <Card className="grid grid-cols-[auto_1fr_auto] items-center gap-10 p-10">
        <div className="relative h-[120px] w-[120px] overflow-hidden rounded-[24px]">
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img src={wallet.project_image} alt="wallet project image" className="h-full w-full object-contain" />
        </div>

        <div className="flex flex-col items-start justify-center gap-3">
          <p className="text-h2 text-primary font-bold">{wallet.project_name}</p>
          <div className="flex items-center gap-[10px]">
            {getWalletProjectStatus(wallet.status)}
            <div className="inline-flex items-center gap-2">
              {getPlatformStatusDisplay(wallet.status, wallet.config?.platforms ?? null)}
            </div>
          </div>
        </div>
        <ProjectInfoEditor
          projectInfo={{
            projectName: wallet.project_name,
            projectImage: wallet.project_image,
          }}
        />
      </Card>
      <Tabs defaultValue="app-settings" className="w-full space-y-10">
        <Tabs.List>
          <Tabs.Trigger value="app-settings">
            <FormattedMessage id="kgstudio.wallet.app-settings" />
          </Tabs.Trigger>
          <Tabs.Trigger value="publish-settings">
            <FormattedMessage id="kgstudio.wallet.publish-settings" />
          </Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="app-settings">
          <Card className="p-10">
            {emptyAppSettings && (
              <div className="flex items-center justify-between">
                <h2 className="text-h2 text-primary font-bold">
                  <FormattedMessage id="kgstudio.wallet.app-settings" />
                </h2>
                <Button
                  className="shadow-[0px_4px_24px_0px_rgba(255,194,17,0.40)]"
                  onClick={() => {
                    router.push(`/wallet/projects/${wallet.project_id}/app-settings/edit`);
                  }}
                >
                  <FormattedMessage id="kgstudio.common.start" />
                </Button>
              </div>
            )}
            {!emptyAppSettings && (
              <>
                <header className="mb-6 flex w-full items-center justify-between">
                  <p className="text-h2 text-primary font-bold">
                    <FormattedMessage id="kgstudio.wallet.app-settings" />
                  </p>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="secondary"
                      icon={<Eye className="h-5 w-5" />}
                      onClick={() => {
                        setDemoModalOpen(true);
                      }}
                    >
                      <FormattedMessage id="kgstudio.wallet.button.view-demo" />
                    </Button>
                    <Modal open={demoModalOpen} onOpenChange={setDemoModalOpen}>
                      <Modal.Content>
                        <div className="flex flex-col items-center justify-center gap-6 rounded-xl p-6">
                          <div className="text-h3 flex flex-col items-center text-center text-[#2B3674]">
                            <p>{t('kgstudio.wallet.config.scanToDemo.title1')}</p>
                            <p>{t('kgstudio.wallet.config.scanToDemo.title2')}</p>
                          </div>
                          <div>
                            <QRCodeCanvas
                              size={400}
                              level="L"
                              value={getWalletPreviewUri(
                                pipe(appSettings as AppSettings, transformAppSettingsToRemoteConfig),
                              )}
                            />
                          </div>
                          <p className="text-body-2 text-secondary">{t('kgstudio.wallet.config.scanInstruction')}</p>
                        </div>
                      </Modal.Content>
                    </Modal>

                    <Button
                      icon={<Pencil className="h-5 w-5" />}
                      disabled={wallet.status === 'in_review'}
                      {...(wallet.status !== 'in_review' && {
                        onClick: () => {
                          router.push(`/wallet/projects/${wallet.project_id}/app-settings/edit`);
                        },
                      })}
                    >
                      <FormattedMessage id="kgstudio.common.edit" />
                    </Button>
                  </div>
                </header>
                <Separator />

                {wallet.status === 'in_review' && (
                  <div className="border-focused bg-warning-light mt-10 rounded-lg border p-5 shadow-[0px_2px_8px_0px_rgba(0,0,0,0.10)]">
                    <FormattedMessage id="kgstudio.wallet.app-under-review" />
                  </div>
                )}

                <Accordion type="multiple" className="mt-10 space-y-6">
                  <Accordion.Item value="feature-settings">
                    <Accordion.Trigger>
                      <FormattedMessage id="kgstudio.wallet.feature-settings" />
                    </Accordion.Trigger>

                    <Accordion.Content>
                      <div className="space-y-6">
                        <div className="border-primary flex flex-col gap-[3px] rounded-2xl border p-6">
                          <p className="text-body-2 text-secondary">
                            <FormattedMessage id="kgstudio.wallet.supported-chains" />
                          </p>

                          <div className="flex items-center gap-[10px]">
                            {wallet.config?.all_rpcs?.map((chain) => <ChainBadge key={chain} chain={chain} />) ?? null}
                          </div>
                        </div>
                        <div className="border-primary flex flex-col gap-[3px] rounded-2xl border p-6">
                          <p className="text-body-2 text-secondary">
                            <FormattedMessage id="kgstudio.common.languages" />
                          </p>
                          <div className="flex items-center gap-[10px]">
                            {wallet.config?.supported_locales?.map((localeObj) => {
                              const locale = `${localeObj.language_code}_${localeObj.country_code}`;

                              switch (locale) {
                                case 'en_US':
                                  return (
                                    <Badge key={locale}>
                                      <FormattedMessage id="kgstudio.common.language.english" />
                                    </Badge>
                                  );
                                case 'zh_TW':
                                  return (
                                    <Badge key={locale}>
                                      <FormattedMessage id="kgstudio.common.language.traditional-chinese" />
                                    </Badge>
                                  );
                                case 'zh_CN':
                                  return (
                                    <Badge key={locale}>
                                      <FormattedMessage id="kgstudio.common.language.simplified-chinese" />
                                    </Badge>
                                  );
                                case 'ja_JP':
                                  return (
                                    <Badge key={locale}>
                                      <FormattedMessage id="kgstudio.common.language.japanese" />
                                    </Badge>
                                  );
                              }
                            }) ?? null}
                          </div>
                        </div>
                        <div className="border-primary flex flex-col gap-[3px] rounded-2xl border p-6">
                          <p className="text-body-2 text-secondary">
                            <FormattedMessage id="kgstudio.wallet.active-features" />
                          </p>
                          <div className="flex items-center gap-[10px]">
                            {wallet.config?.show_swap && (
                              <Badge>
                                <FormattedMessage id="kgstudio.wallet.feature.swap" />
                              </Badge>
                            )}
                            {wallet.config?.show_nft_sell_btn && (
                              <Badge>
                                <FormattedMessage id="kgstudio.wallet.feature.nft-sell" />
                              </Badge>
                            )}
                            {wallet.config?.show_rewards && (
                              <Badge>
                                <FormattedMessage id="kgstudio.wallet.feature.nft-rewards" />
                              </Badge>
                            )}
                            {wallet.config?.show_explore_tab && (
                              <Badge>
                                <FormattedMessage id="kgstudio.wallet.feature.explore-dapp" />
                              </Badge>
                            )}
                            {wallet.config?.show_poap && (
                              <Badge>
                                <FormattedMessage id="kgstudio.wallet.feature.show-poap" />
                              </Badge>
                            )}
                            {wallet.config?.show_kyc && (
                              <Badge>
                                <FormattedMessage id="kgstudio.wallet.feature.show-kyc" />
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </Accordion.Content>
                  </Accordion.Item>

                  {!!wallet.theme && (
                    <Accordion.Item value="Theme">
                      <Accordion.Trigger>
                        <FormattedMessage id="kgstudio.wallet.theme.title" />
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className="space-y-6">
                          <div className="border-primary flex flex-col gap-[3px] rounded-2xl border p-6">
                            <p className="text-body-2 text-secondary">
                              <FormattedMessage id="kgstudio.wallet.theme.primary-color" />
                            </p>
                            <div className="flex items-center gap-2">
                              <div className="border-primary rounded-[6px] border p-1">
                                <div
                                  className="h-5 w-5 rounded-[4px]"
                                  style={{
                                    backgroundColor: wallet.theme.primary_color ?? DEFAULT_FEATURES.primary_color,
                                  }}
                                ></div>
                              </div>
                              <p className="text-body text-primary">{wallet.theme.primary_color}</p>
                            </div>
                          </div>
                          <div className="border-primary flex flex-col gap-[3px] rounded-2xl border p-6">
                            <p className="text-body-2 text-secondary">
                              <FormattedMessage id="kgstudio.wallet.theme.secondary-color" />
                            </p>
                            <div className="flex items-center gap-2">
                              <div className="border-primary rounded-[6px] border p-1">
                                <div
                                  className="h-5 w-5 rounded-[4px]"
                                  style={{
                                    backgroundColor: wallet.theme.secondary_color ?? DEFAULT_FEATURES.secondary_color,
                                  }}
                                ></div>
                              </div>
                              <p className="text-body text-primary">{wallet.theme.secondary_color}</p>
                            </div>
                          </div>
                        </div>
                      </Accordion.Content>
                    </Accordion.Item>
                  )}

                  <Accordion.Item value="supported-links">
                    <Accordion.Trigger>
                      <FormattedMessage id="kgstudio.wallet.supported-links" />
                    </Accordion.Trigger>
                    <Accordion.Content>
                      <div className="grid grid-cols-[150px_1fr] gap-2">
                        <div className="grid grid-rows-4 gap-3">
                          <p className="text-body text-secondary">
                            <FormattedMessage id="kgstudio.wallet.support-email" />
                          </p>
                          <p className="text-body text-secondary">
                            <FormattedMessage id="kgstudio.wallet.help-center" />
                          </p>
                          <p className="text-body text-secondary">
                            <FormattedMessage id="kgstudio.wallet.terms-condition" />
                          </p>
                          <p className="text-body text-secondary">
                            <FormattedMessage id="kgstudio.wallet.privacy-policy" />
                          </p>
                        </div>
                        <div className="grid grid-rows-4 gap-3">
                          <p className="text-body text-primary underline">{wallet.config?.support_email ?? ''}</p>
                          <a href={wallet.config?.help_center_url ?? ''} className="text-body text-primary underline">
                            {wallet.config?.help_center_url ?? ''}
                          </a>
                          <a
                            href={wallet.config?.privacy_and_legal_url ?? ''}
                            className="text-body text-primary underline"
                          >
                            {wallet.config?.privacy_and_legal_url ?? ''}
                          </a>
                          <a
                            href={wallet.config?.privacy_policy_url ?? ''}
                            className="text-body text-primary underline"
                          >
                            {wallet.config?.privacy_policy_url ?? ''}
                          </a>
                        </div>
                      </div>
                    </Accordion.Content>
                  </Accordion.Item>

                  <Accordion.Item value="community-links">
                    <Accordion.Trigger>
                      <FormattedMessage id="kgstudio.common.community-links" />
                    </Accordion.Trigger>
                    <Accordion.Content>
                      <div className="grid grid-cols-[150px_1fr] gap-2">
                        <div className="grid grid-rows-3 gap-3">
                          <p className="text-body text-secondary">
                            <FormattedMessage id="kgstudio.common.twitter" />
                          </p>
                          <p className="text-body text-secondary">
                            <FormattedMessage id="kgstudio.common.discord" />
                          </p>
                          <p className="text-body text-secondary">
                            <FormattedMessage id="kgstudio.common.telegram" />
                          </p>
                        </div>
                        <div className="grid grid-rows-3 gap-3">
                          <a href={wallet.config?.twitter_url ?? ''} className="text-body text-primary underline">
                            {wallet.config?.twitter_url ?? ''}
                          </a>
                          <a href={wallet.config?.discord_url ?? ''} className="text-body text-primary underline">
                            {wallet.config?.discord_url ?? ''}
                          </a>
                          <a href={wallet.config?.telegram_url ?? ''} className="text-body text-primary underline">
                            {wallet.config?.telegram_url ?? ''}
                          </a>
                        </div>
                      </div>
                    </Accordion.Content>
                  </Accordion.Item>

                  {wallet.config?.explorer_screen_recommend_item && (
                    <Accordion.Item value="dapp-list">
                      <Accordion.Trigger>
                        <FormattedMessage id="kgstudio.wallet.customized-dapp-list" />
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <Tabs defaultValue="hot" className="w-full">
                          <Tabs.List>
                            <Tabs.Trigger value="hot">
                              <FormattedMessage id="kgstudio.wallet.hot" />
                            </Tabs.Trigger>
                            <Tabs.Trigger value="nft">
                              <FormattedMessage id="kgstudio.wallet.nft" />
                            </Tabs.Trigger>
                            <Tabs.Trigger value="dex">
                              <FormattedMessage id="kgstudio.wallet.dex" />
                            </Tabs.Trigger>
                          </Tabs.List>

                          <Tabs.Content value="hot">
                            <div className="flex flex-col gap-3">
                              {wallet.config.explorer_screen_recommend_item.map((dapp, index) => (
                                <DappListTile key={index} dapp={dapp} />
                              ))}
                            </div>
                          </Tabs.Content>
                        </Tabs>
                      </Accordion.Content>
                    </Accordion.Item>
                  )}

                  {wallet.config?.explorer_banner_item && (
                    <Accordion.Item value="banner">
                      <Accordion.Trigger>
                        <FormattedMessage id="kgstudio.wallet.explorer-banner" />
                      </Accordion.Trigger>

                      <Accordion.Content>
                        <Tabs defaultValue="en_US" className="w-full">
                          <Tabs.List>
                            <Tabs.Trigger value="en_US">
                              <FormattedMessage id="kgstudio.common.language.english" />
                            </Tabs.Trigger>
                            <Tabs.Trigger value="zh_TW">
                              <FormattedMessage id="kgstudio.common.language.traditional-chinese" />
                            </Tabs.Trigger>
                            <Tabs.Trigger value="zh-CN">
                              <FormattedMessage id="kgstudio.common.language.simplified-chinese" />
                            </Tabs.Trigger>
                            <Tabs.Trigger value="ja_JP">
                              <FormattedMessage id="kgstudio.common.language.japanese" />
                            </Tabs.Trigger>
                            <Tabs.Trigger value="vi_VN">
                              {' '}
                              <FormattedMessage id="kgstudio.wallet.language.vietnamese" />
                            </Tabs.Trigger>
                          </Tabs.List>

                          <Tabs.Content value="en_US">
                            <div className="flex flex-col gap-2">
                              {wallet.config.explorer_banner_item.map((banner, index) => (
                                <BannerListTile key={index} banner={banner} />
                              ))}
                            </div>
                          </Tabs.Content>
                        </Tabs>
                      </Accordion.Content>
                    </Accordion.Item>
                  )}
                </Accordion>
              </>
            )}
          </Card>
        </Tabs.Content>

        <Tabs.Content value="publish-settings">
          <Card className="p-10">
            {emptyPublishSettings && (
              <div className="flex items-center justify-between">
                <h2 className="text-h2 text-primary font-bold">
                  <FormattedMessage id="kgstudio.wallet.publish-settings" />
                </h2>
                <Button
                  className="shadow-[0px_4px_24px_0px_rgba(255,194,17,0.40)]"
                  onClick={() => {
                    router.push(`/wallet/projects/${wallet.project_id}/publish-settings/edit`);
                  }}
                >
                  <FormattedMessage id="kgstudio.common.start" />
                </Button>
              </div>
            )}
            {!emptyPublishSettings && (
              <>
                <header className="mb-6 flex w-full items-center justify-between">
                  <p className="text-h2 text-primary font-bold">{t('kgstudio.wallet.publish-settings')}</p>
                  <Button
                    icon={<Pencil className="h-5 w-5" />}
                    disabled={wallet.status === 'in_review'}
                    {...(wallet.status !== 'in_review' && {
                      onClick: () => {
                        router.push(`/wallet/projects/${wallet.project_id}/publish-settings/edit`);
                      },
                    })}
                  >
                    <FormattedMessage id="kgstudio.common.edit" />
                  </Button>
                </header>
                <Separator />

                {wallet.status === 'in_review' && (
                  <div className="border-focused bg-warning-light mt-10 rounded-lg border p-5 shadow-[0px_2px_8px_0px_rgba(0,0,0,0.10)]">
                    <FormattedMessage id="kgstudio.wallet.app-under-review" />
                  </div>
                )}
                <Accordion type="multiple" className="mt-10 space-y-6">
                  {(wallet.config?.app_icon || wallet.config?.splash_screen || wallet.config?.get_started_image) && (
                    <Accordion.Item value="app-images">
                      <Accordion.Trigger>
                        <FormattedMessage id="kgstudio.wallet.app-images.title" />
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className="grid grid-cols-[1fr_230px_230px] gap-6">
                          <div className="border-primary flex items-center justify-start self-start rounded-2xl border p-6">
                            <div className="flex flex-col items-start justify-center gap-[3px]">
                              <p className="text-body-2 text-secondary">
                                <FormattedMessage id="kgstudio.wallet.app-images.app-icon" />
                              </p>
                              <div className="relative h-[99px] w-[99px] overflow-hidden rounded-2xl">
                                {/* FIXME: Use Image instead, and set image origin in next configs to GCP */}
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                  src={wallet.config.app_icon ?? placeholder.src}
                                  alt="app icon"
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            </div>
                          </div>

                          <div className="space-y-3">
                            <Badge variant="yellow">
                              <FormattedMessage id="kgstudio.wallet.app-images.splash" />
                            </Badge>
                            <div className="aspect-[9/16] w-full overflow-hidden rounded-xl shadow-[0px_2.4444496631622314px_12.222247123718262px_0px_rgba(0,0,0,0.1)]">
                              {/* FIXME: Use Image instead, and set image origin in next configs to GCP */}
                              {wallet.config.splash_screen && (
                                // eslint-disable-next-line @next/next/no-img-element
                                <img
                                  src={wallet.config.splash_screen}
                                  alt="splash screen"
                                  className="h-full w-full object-cover"
                                />
                              )}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <Badge variant="yellow">
                              <FormattedMessage id="kgstudio.common.get-started" />
                            </Badge>

                            <div className="grid aspect-[9/16] w-full grid-rows-[1fr_auto] overflow-hidden rounded-xl bg-white p-5 shadow-[0px_2.4444496631622314px_12.222247123718262px_0px_rgba(0,0,0,0.1)]">
                              <div className="flex items-center justify-center">
                                {/* FIXME: Use Image instead, and set image origin in next configs to GCP */}
                                {wallet.config.get_started_image && (
                                  // eslint-disable-next-line @next/next/no-img-element
                                  <img
                                    src={wallet.config.get_started_image}
                                    alt="get started screen"
                                    className="h-[110px] w-[110px] object-cover"
                                  />
                                )}
                              </div>
                              <div
                                className="text-button-md flex w-full items-center justify-center rounded-[11px] py-[11px] font-bold text-white"
                                style={{
                                  background: wallet.theme?.primary_color ?? DEFAULT_FEATURES.primary_color,
                                }}
                              >
                                <FormattedMessage id="kgstudio.common.get-started" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </Accordion.Content>
                    </Accordion.Item>
                  )}
                  <Accordion.Item value="ios">
                    <Accordion.Trigger>
                      <div className="flex w-full items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="relative h-10 w-10">
                            <Image src={ios} alt="ios" fill className="object-contain" />
                          </div>
                          <div className="flex flex-col items-start justify-center">
                            <p className="text-body-2 text-primary font-bold">
                              <FormattedMessage id="kgstudio.wallet.ios.title" />
                            </p>
                            <p className="text-small text-secondary">
                              <FormattedMessage id="kgstudio.wallet.ios.subtitle" />
                            </p>
                          </div>
                        </div>
                        {getStatusBadgeDisplay(wallet.status)}
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content>
                      <FormattedMessage id="kgstudio.wallet.mock.content" />
                    </Accordion.Content>
                  </Accordion.Item>
                  <Accordion.Item value="googlePlay">
                    <Accordion.Trigger>
                      <div className="flex w-full items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="relative h-10 w-10">
                            <Image src={googlePlay} alt="google play store" fill className="object-contain" />
                          </div>
                          <div className="flex flex-col items-start justify-center">
                            <p className="text-body-2 text-primary font-bold">
                              <FormattedMessage id="kgstudio.wallet.google-play.title" />
                            </p>
                            <p className="text-small text-secondary">
                              <FormattedMessage id="kgstudio.wallet.google-play.subtitle" />
                            </p>
                          </div>
                        </div>
                        {getStatusBadgeDisplay(wallet.status)}
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content>
                      <FormattedMessage id="kgstudio.wallet.mock.content" />
                    </Accordion.Content>
                  </Accordion.Item>
                  <Accordion.Item value="extension">
                    <Accordion.Trigger>
                      <div className="flex w-full items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="relative h-10 w-10">
                            <Image src={extension} alt="extension" fill className="object-contain" />
                          </div>

                          <div className="flex flex-col items-start justify-center">
                            <p className="text-body-2 text-primary font-bold">
                              <FormattedMessage id="kgstudio.wallet.extension.title" />
                            </p>
                            <p className="text-small text-secondary">
                              <FormattedMessage id="kgstudio.wallet.extension.subtitle" />
                            </p>
                          </div>
                        </div>
                        {getStatusBadgeDisplay(wallet.status)}
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content>
                      <FormattedMessage id="kgstudio.wallet.mock.content" />
                    </Accordion.Content>
                  </Accordion.Item>
                </Accordion>
              </>
            )}
          </Card>
        </Tabs.Content>
      </Tabs>
    </div>
  );
};
export default WalletDetail;
