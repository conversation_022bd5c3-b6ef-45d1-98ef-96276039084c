'use client';

import { pick } from 'lodash-es';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { FormattedMessage } from '@/app/_common/components';
import { FormMediaUploader, FormMultiSelect } from '@/app/_common/components/form';
import { useGcpFileUpload } from '@/app/_common/hooks';
import { createError } from '@/app/_common/lib/error';
import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Accordion,
  Badge,
  Button,
  Card,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  Modal,
  Separator,
  Spinner,
  Steps,
} from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import extension from '../../../_assets/icon-extension.svg';
import android from '../../../_assets/icon-google-play.svg';
import ios from '../../../_assets/icon-ios.svg';
import placeholder from '../../../_assets/placeholder.svg';
import { SectionTitle } from '../../../_components';
import { DEFAULT_FEATURES } from '../../../_constant';
import { useUpdateWallet } from '../../../_services/command';
import { useWalletProjects } from '../../../_services/query';
import { Backend } from '../../../_types';
import { PublishSettingSchema, type PublishSettings } from '../../../_types/publish';
import { publishSettingsKeys, transformDtoToPublishSettings } from '../../../_utils';
import { WithNull } from '../../../_utils/type';

const steps: {
  stepNumber: number;
  label: React.ReactNode;
  value: string;
  desc?: React.ReactNode;
}[] = [
  {
    stepNumber: 1,
    label: <FormattedMessage id="kgstudio.wallet.config.app-image" />,
    value: 'appImages',
  },
  {
    stepNumber: 2,
    label: <FormattedMessage id="kgstudio.wallet.config.app-store-info" />,
    value: 'appStoreInfo',
  },
  {
    stepNumber: 3,
    label: <FormattedMessage id="kgstudio.wallet.config.check" />,
    value: 'check',
    desc: <FormattedMessage id="kgstudio.wallet.config.confirm-before-submit" />,
  },
];

const formDefaultValues = {
  app_icon: [],
  splash_screen: [],
  get_started_image: [],
  platforms: [],
  languages: [],
  app_store_info: null,
};
const publishSettingsError = createError('PublishSettingsError', 'Publish settings error');

const PublishSettings = () => {
  const [confirmPublishModal, setConfirmPublishModal] = useState(false);
  const router = useRouter();
  const params = useParams();
  const projectId = params == null ? '' : (params.id as string);
  const t = useTranslations();

  const queryClient = useQueryClient();
  const { data: wallets, error: walletsError, isLoading: walletsIsLoading } = useWalletProjects({});
  const wallet = wallets?.data.find((wallet) => wallet.project_id === Number(projectId));
  const {
    mutate: updateWalletSettings,
    error: updateWalletSettingsError,
    isSuccess: updateWalletSettingsSuccess,
    isLoading: updateWalletSettingsLoading,
    isIdle: updateWalletSettingsIdle,
    reset: updateWalletSettingsReset,
  } = useUpdateWallet({
    onSuccess: () => {
      showToast(t('kgstudio.common.project-updated'), 'success');
      queryClient.refetchQueries(['walletProjects']);
    },
  });
  const {
    isError: fileUrlsError,
    isLoading: fileUrlsLoading,
    isIdle: fileUrlsIdle,
    // progress: fileUrlsProgress,
    uploadAllFiles,
  } = useGcpFileUpload();

  const form = useForm<PublishSettings>({
    resolver: zodResolver(PublishSettingSchema(t)),
    defaultValues: formDefaultValues,
    mode: 'all',
  });
  const getStartedImage = form.watch('get_started_image');
  const appIcon = form.watch('app_icon');
  const splashScreen = form.watch('splash_screen');
  const platforms = form.watch('platforms');

  const [currentStepValue, setCurrentStepValue] = useState('appImages');
  const currentStep = steps.find((step) => step.value === currentStepValue);

  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const hasNextStep = steps.some((step) => step.stepNumber === currentStep!.stepNumber + 1);
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const hasPrevStep = steps.some((step) => step.stepNumber === currentStep!.stepNumber - 1);
  const handleNextStep = () => {
    if (!hasNextStep) return;

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const nextStep = steps.find((step) => step.stepNumber === currentStep!.stepNumber + 1);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    setCurrentStepValue(nextStep!.value);
  };

  const handlePrevStep = () => {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const prevStep = steps.find((step) => step.stepNumber === currentStep!.stepNumber - 1);

    if (!prevStep) return;

    setCurrentStepValue(prevStep.value);
  };

  const handleConfirmSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const validationResult = await form.trigger();

    if (!validationResult) showToast(t('kgstudio.data.invalid'), 'error');
    if (validationResult) {
      updateWalletSettingsReset();
      setConfirmPublishModal(true);
    }
  };

  const onSubmit = async (values: z.infer<ReturnType<typeof PublishSettingSchema>>) => {
    const imageKeys = ['app_icon', 'splash_screen', 'get_started_image'] as const;
    const filesWithKey = imageKeys.reduce((acc, key) => {
      const files = values[key];
      const localFiles = files.filter((data) => !!data.file);
      if (localFiles.length === 0) return acc;

      return {
        ...acc,
        [key]: localFiles,
      };
    }, {});

    const fileUrls = !!filesWithKey ? await uploadAllFiles(filesWithKey, 'wallets') : null;

    const baseBody = {
      project_id: Number(projectId),
      config: {
        platforms: values.platforms,
        languages: values.languages,
      },
    };
    fileUrls &&
      Object.keys(fileUrls).forEach((key) =>
        Object.assign(baseBody.config, {
          [key]: fileUrls[key][0],
        }),
      );

    values.app_store_info && Object.assign(baseBody.config, { app_store_info: values.app_store_info });

    updateWalletSettings(baseBody);
  };

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (!wallets || !projectId) return;

    const walletNotFoundError = createError('WalletNotFoundError', 'Wallet not found');
    if (!wallet) throw publishSettingsError(walletNotFoundError(`Can not find wallet: ${projectId}`));

    const publishSettingsDto = pick(wallet.config, publishSettingsKeys);
    const publishSettings = transformDtoToPublishSettings(
      publishSettingsDto as unknown as WithNull<Backend.PublishSettings>,
    );

    form.reset({
      ...formDefaultValues,
      ...publishSettings,
    });
  }, [wallet, wallets, projectId, form]);

  if (!currentStep) {
    const stepNotFoundError = createError('StepNotFoundError', 'Step not found');
    throw publishSettingsError(stepNotFoundError(`Can not find current step: ${currentStepValue}`));
  }
  if (!currentStep) return null;
  if (!wallets || walletsIsLoading) return <div>Loading...</div>;
  if (!wallets || walletsError) return <div>Error...</div>;

  return (
    <div className="mx-auto w-[768px] max-w-[768px] space-y-10">
      <Steps steps={steps} value={currentStepValue} onValueChange={setCurrentStepValue}>
        <Form {...form}>
          <div className="space-y-10">
            <Steps.Display />
            <Card className="border-primary space-y-6 border p-10">
              <div className="flex flex-col items-start gap-2">
                <h1 className="text-h1 text-primary capitalize">{currentStep.label}</h1>
                {currentStep.desc && <p className="body-2 text-secondary">{currentStep.desc}</p>}
              </div>
              <Separator />

              <Steps.Content value="appImages">
                <section className="space-y-6">
                  <SectionTitle title={t('kgstudio.data.upload-branding-assets')} />
                  <fieldset className="space-y-6">
                    <FormMediaUploader
                      title={t('kgstudio.wallet.app-images.app-icon')}
                      name="app_icon"
                      control={form.control}
                      required
                      dropZoneRatio="1/1"
                      dropZoneWidth={200}
                      dropZoneDesc={
                        <ul className="list-inside list-disc">
                          <li>{t('kgstudio.wallet.config.recommended-size')}</li>
                          <li>{t('kgstudio.wallet.config.file-type-supported')}</li>
                          <li>
                            {t('kgstudio.wallet.config.follow-guideline')}{' '}
                            <a className="text-highlight" target="_blank" rel="noreferrer noopener" href="">
                              {t('kgstudio.wallet.config.design-guideline')}
                            </a>
                          </li>
                        </ul>
                      }
                    />
                    <Separator />
                    <FormMediaUploader
                      title={t('kgstudio.wallet.config.splash-screen-title')}
                      name="splash_screen"
                      control={form.control}
                      required
                      dropZoneRatio="9/16"
                      dropZoneWidth={200}
                      dropZoneDesc={
                        <ul className="list-inside list-disc">
                          <li>{t('kgstudio.wallet.config.app-startup')}</li>
                          <li>{t('kgstudio.wallet.config.splash-recommended-size')}</li>
                          <li>{t('kgstudio.wallet.config.splash-file-type-supported')}</li>
                        </ul>
                      }
                    />

                    <Separator />
                    <FormMediaUploader
                      title={t('kgstudio.wallet.config.get-started-title')}
                      name="get_started_image"
                      control={form.control}
                      required
                      dropZoneRatio="1/1"
                      dropZoneWidth={200}
                      dropZoneDesc={
                        <ul className="list-inside list-disc">
                          <li>{t('kgstudio.wallet.config.get-started-description')}</li>
                          <li>{t('kgstudio.wallet.config.splash-recommended-size')}</li>
                          <li>{t('kgstudio.wallet.config.splash-file-type-supported')}</li>
                        </ul>
                      }
                      defaultPreview={false}
                    >
                      <div className="grid aspect-[9/16] w-[200px] grid-rows-[1fr_auto] overflow-hidden rounded-xl bg-white p-5 shadow-[0px_2.4444496631622314px_12.222247123718262px_0px_rgba(0,0,0,0.1)]">
                        <div className="flex items-center justify-center">
                          {getStartedImage[0]?.dataURL && (
                            // eslint-disable-next-line @next/next/no-img-element
                            <img
                              src={getStartedImage[0].dataURL}
                              alt="brand logo"
                              className="h-[110px] w-[110px] object-cover"
                            />
                          )}
                        </div>
                        <div className="text-button-md bg-brand-primary flex w-full items-center justify-center rounded-[11px] py-[11px] font-bold text-white">
                          {t('kgstudio.common.get-started')}
                        </div>
                      </div>
                    </FormMediaUploader>
                  </fieldset>
                </section>
              </Steps.Content>
              <Steps.Content value="appStoreInfo">
                <fieldset className="space-y-6">
                  <FormMultiSelect
                    title={t('kgstudio.wallet.config.shelf-platform-title')}
                    name="platforms"
                    control={form.control}
                    required
                    items={[
                      {
                        label: t('kgstudio.wallet.config.ios'),
                        id: 'ios',
                      },
                      {
                        label: t('kgstudio.wallet.config.android'),
                        id: 'android',
                      },
                      {
                        label: t('kgstudio.wallet.config.extension'),
                        id: 'extension',
                      },
                    ]}
                  />

                  <FormMultiSelect
                    title={t('kgstudio.data.shop-info-language')}
                    desc={t('kgstudio.data.app-store-info-language')}
                    name="languages"
                    control={form.control}
                    required
                    items={[
                      {
                        label: t('kgstudio.common.language.english'),
                        id: 'en_US',
                        desc: t('kgstudio.wallet.config.desc.english'),
                      },
                      {
                        label: t('kgstudio.common.language.traditional-chinese'),
                        id: 'zh_TW',
                        desc: t('kgstudio.wallet.config.desc.traditional-chinese'),
                      },
                      {
                        label: t('kgstudio.common.language.simplified-chinese'),
                        id: 'zh_CN',
                        desc: t('kgstudio.wallet.config.desc.simplified-chinese'),
                      },
                      {
                        label: t('kgstudio.common.language.japanese'),
                        id: 'ja_JP',
                        desc: t('kgstudio.wallet.config.desc.japanese'),
                      },
                    ]}
                  />
                  <Separator />
                  <FormField
                    control={form.control}
                    name="app_store_info"
                    render={({ field }) => (
                      <FormItem>
                        <div className="mb-6 flex flex-col gap-2">
                          <FormLabel required>{t('kgstudio.wallet.config.app-store-info')}</FormLabel>
                          <FormDescription>{t('kgstudio.wallet.config.store-display-info')}</FormDescription>
                        </div>
                        <FormControl>
                          <Accordion type="multiple" className="space-y-6">
                            {platforms?.includes('ios') && (
                              <Accordion.Item value="app-store-info">
                                <Accordion.Trigger>
                                  <div className="flex items-center gap-2">
                                    <Image src={ios} alt="ios" className="object-cover" />
                                    <div className="flex flex-col items-start justify-center">
                                      <p className="text-body-2 text-primary font-bold">
                                        {t('kgstudio.wallet.ios.title')}
                                      </p>
                                      <p className="text-small text-secondary">{t('kgstudio.wallet.ios.subtitle')}</p>
                                    </div>
                                  </div>
                                </Accordion.Trigger>
                                <Accordion.Content>{t('kgstudio.wallet.mock.content')}</Accordion.Content>
                              </Accordion.Item>
                            )}
                            {platforms?.includes('android') && (
                              <Accordion.Item value="google-play-info">
                                <Accordion.Trigger>
                                  <div className="flex items-center gap-2">
                                    <Image src={android} alt="android" className="object-cover" />
                                    <div className="flex flex-col items-start justify-center">
                                      <p className="text-body-2 text-primary font-bold">
                                        {t('kgstudio.wallet.google-play.title')}
                                      </p>
                                      <p className="text-small text-secondary">
                                        {t('kgstudio.wallet.google-play.subtitle')}
                                      </p>
                                    </div>
                                  </div>
                                </Accordion.Trigger>
                                <Accordion.Content>{t('kgstudio.wallet.mock.content')}</Accordion.Content>
                              </Accordion.Item>
                            )}
                            {platforms?.includes('extension') && (
                              <Accordion.Item value="chrome-store-info">
                                <Accordion.Trigger>
                                  <div className="flex items-center gap-2">
                                    <Image src={extension} alt="extension" className="object-cover" />
                                    <div className="flex flex-col items-start justify-center">
                                      <p className="text-body-2 text-primary font-bold">
                                        {t('kgstudio.wallet.extension.title')}
                                      </p>
                                      <p className="text-small text-secondary">
                                        {t('kgstudio.wallet.extension.subtitle')}
                                      </p>
                                    </div>
                                  </div>
                                </Accordion.Trigger>
                                <Accordion.Content>{t('kgstudio.wallet.mock.content')}</Accordion.Content>
                              </Accordion.Item>
                            )}
                          </Accordion>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </fieldset>
              </Steps.Content>
              <Steps.Content value="check">
                <Accordion type="single" collapsible className="space-y-6">
                  {(!!appIcon?.length || !!splashScreen?.length || !!getStartedImage?.length) && (
                    <Accordion.Item value="app-images">
                      <Accordion.Trigger>
                        <FormattedMessage id="kgstudio.wallet.app-images.title" />
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className="grid grid-cols-[1fr_230px_230px] gap-6">
                          <div className="space-y-3">
                            <Badge variant="yellow">
                              <FormattedMessage id="kgstudio.wallet.app-images.app-icon" />
                            </Badge>
                            <div className="relative h-[99px] w-[99px] overflow-hidden rounded-2xl">
                              {/* FIXME: Use Image instead, and set image origin in next configs to GCP */}
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              <img
                                src={appIcon?.[0]?.dataURL ?? placeholder.src}
                                alt="app icon"
                                className="h-full w-full object-cover"
                              />
                            </div>
                          </div>

                          <div className="space-y-3">
                            <Badge variant="yellow">
                              <FormattedMessage id="kgstudio.wallet.app-images.splash" />
                            </Badge>
                            <div className="aspect-[9/16] w-full overflow-hidden rounded-xl shadow-[0px_2.4444496631622314px_12.222247123718262px_0px_rgba(0,0,0,0.1)]">
                              {/* FIXME: Use Image instead, and set image origin in next configs to GCP */}
                              {!!splashScreen?.[0]?.dataURL && (
                                // eslint-disable-next-line @next/next/no-img-element
                                <img
                                  src={splashScreen[0].dataURL}
                                  alt="splash screen"
                                  className="h-full w-full object-cover"
                                />
                              )}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <Badge variant="yellow">
                              <FormattedMessage id="kgstudio.common.get-started" />
                            </Badge>

                            <div className="grid aspect-[9/16] w-full grid-rows-[1fr_auto] overflow-hidden rounded-xl bg-white p-5 shadow-[0px_2.4444496631622314px_12.222247123718262px_0px_rgba(0,0,0,0.1)]">
                              <div className="flex items-center justify-center">
                                {/* FIXME: Use Image instead, and set image origin in next configs to GCP */}
                                {!!getStartedImage?.[0]?.dataURL && (
                                  // eslint-disable-next-line @next/next/no-img-element
                                  <img
                                    src={getStartedImage[0].dataURL}
                                    alt="get started screen"
                                    className="h-[110px] w-[110px] object-cover"
                                  />
                                )}
                              </div>
                              <div
                                className="text-button-md flex w-full items-center justify-center rounded-[11px] py-[11px] font-bold text-white"
                                style={{
                                  background: wallet?.theme?.primary_color ?? DEFAULT_FEATURES.primary_color,
                                }}
                              >
                                <FormattedMessage id="kgstudio.common.get-started" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </Accordion.Content>
                    </Accordion.Item>
                  )}
                  <Accordion.Item value="ios">
                    <Accordion.Trigger>
                      <div className="flex items-center gap-2">
                        <div className="relative h-10 w-10">
                          <Image src={ios} alt="ios" fill className="object-contain" />
                        </div>

                        <div className="flex flex-col items-start justify-center">
                          <p className="text-body-2 text-primary font-bold">{t('kgstudio.wallet.ios.title')}</p>
                          <p className="text-small text-secondary">{t('kgstudio.wallet.ios.subtitle')}</p>
                        </div>
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content>{t('kgstudio.wallet.mock.content')}</Accordion.Content>
                  </Accordion.Item>
                  <Accordion.Item value="android">
                    <Accordion.Trigger>
                      <div className="flex items-center gap-2">
                        <div className="relative h-10 w-10">
                          <Image src={android} alt="android" fill className="object-contain" />
                        </div>

                        <div className="flex flex-col items-start justify-center">
                          <p className="text-body-2 text-primary font-bold">{t('kgstudio.wallet.google-play.title')}</p>
                          <p className="text-small text-secondary">{t('kgstudio.wallet.google-play.subtitle')}</p>
                        </div>
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content>{t('kgstudio.wallet.mock.content')}</Accordion.Content>
                  </Accordion.Item>
                  <Accordion.Item value="extension">
                    <Accordion.Trigger>
                      <div className="flex items-center gap-2">
                        <div className="relative h-10 w-10">
                          <Image src={extension} alt="extension" fill className="object-contain" />
                        </div>
                        <div className="flex flex-col items-start justify-center">
                          <p className="text-body-2 text-primary font-bold">{t('kgstudio.wallet.extension.title')}</p>
                          <p className="text-small text-secondary">{t('kgstudio.wallet.extension.subtitle')}</p>
                        </div>
                      </div>
                    </Accordion.Trigger>
                    <Accordion.Content>{t('kgstudio.wallet.mock.content')}</Accordion.Content>
                  </Accordion.Item>
                </Accordion>
              </Steps.Content>
            </Card>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {hasPrevStep && (
                  <Button variant="secondary" onClick={handlePrevStep} type="button">
                    {t('kgstudio.common.prev-step')}
                  </Button>
                )}
                <Button variant="secondary" type="button" onClick={() => onSubmit(form.getValues())}>
                  {t('kgstudio.common.save-draft')}
                </Button>
              </div>

              {hasNextStep ? (
                <Button type="button" onClick={handleNextStep}>
                  {t('kgstudio.common.next-step')}
                </Button>
              ) : (
                <Button type="button" onClick={handleConfirmSubmit}>
                  {t('kgstudio.common.submit')}
                </Button>
              )}
              <Modal open={confirmPublishModal} onOpenChange={setConfirmPublishModal}>
                {fileUrlsIdle && updateWalletSettingsIdle && (
                  <Modal.Content>
                    <Modal.Header type="center">
                      <Modal.Title>{t('kgstudio.wallet.config.publish-settings-confirm-title')}</Modal.Title>
                    </Modal.Header>
                    <p className="text-body-2 text-center">
                      {t('kgstudio.wallet.config.publish-settings-description')}
                    </p>
                    <Modal.Footer className="grid w-full grid-cols-2 gap-6">
                      <Modal.Close asChild>
                        <Button variant="secondary" className="w-full">
                          {t('kgstudio.wallet.config.submit-later')}
                        </Button>
                      </Modal.Close>
                      <Button className="w-full" type="submit" onClick={form.handleSubmit(onSubmit)}>
                        {t('kgstudio.wallet.config.confirm-submit')}
                      </Button>
                    </Modal.Footer>
                  </Modal.Content>
                )}
                {(fileUrlsLoading || updateWalletSettingsLoading) && (
                  <Modal.Content>
                    <Modal.Header type="center">
                      <Modal.Title>{t('kgstudio.wallet.config.submission-in-progress')}</Modal.Title>
                    </Modal.Header>
                    <div className="flex flex-col items-center gap-10 text-center">
                      <Spinner className="animate-spin" />
                      <p className="text-body-2 text-center">
                        {t('kgstudio.wallet.config.submission-in-progress-description')}
                      </p>
                    </div>
                  </Modal.Content>
                )}
                {(fileUrlsError || updateWalletSettingsError) && (
                  <Modal.Content>
                    <Modal.Header type="center">
                      <Modal.Title>{t('kgstudio.wallet.config.submission-failed')}</Modal.Title>
                    </Modal.Header>
                    <p className="text-body-2 text-center">
                      {t('kgstudio.wallet.config.submission-failed-description')}
                    </p>

                    <Modal.Footer>
                      <Modal.Close asChild>
                        <Button className="w-full">{t('kgstudio.common.close')}</Button>
                      </Modal.Close>
                    </Modal.Footer>
                  </Modal.Content>
                )}
                {updateWalletSettingsSuccess && (
                  <Modal.Content noClose>
                    <Modal.Header type="center">
                      <Modal.Title>{t('kgstudio.wallet.config.submission-successful')}</Modal.Title>
                    </Modal.Header>
                    <p className="text-body-2 text-center">{t('kgstudio.wallet.config.submission-description')}</p>
                    <Modal.Footer>
                      <Modal.Close asChild>
                        <Button
                          className="w-full"
                          onClick={() => {
                            router.push(`/wallet/projects/${projectId}`);
                          }}
                        >
                          {t('kgstudio.common.close')}
                        </Button>
                      </Modal.Close>
                    </Modal.Footer>
                  </Modal.Content>
                )}
              </Modal>
            </div>
          </div>
        </Form>
      </Steps>
    </div>
  );
};

export default PublishSettings;
