'use client';

import { useEffect } from 'react';

import { useRouter } from '@/i18n/navigation';

import { WalletDetailLoading } from '../_components';
import { useWalletProjects } from '../_services/query/getWallets';

const WalletsOverview = () => {
  const router = useRouter();
  const { data: wallets, error: walletsError } = useWalletProjects({});

  useEffect(() => {
    if (!wallets) return;

    const projectId = wallets.data[0]?.project_id;

    router.push(`/wallet/projects/${projectId}`);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [wallets]);

  if (walletsError) return <div>Error...</div>;

  return <WalletDetailLoading />;
};

export default WalletsOverview;
