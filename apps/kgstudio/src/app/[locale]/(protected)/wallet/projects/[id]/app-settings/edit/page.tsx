'use client';

import { pick } from 'lodash-es';
import { Crown, Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { QRCodeCanvas } from 'qrcode.react';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { showToast } from '@/2b/toast';
import { FormattedMessage } from '@/app/_common/components';
import { FormColorPicker, FormInput, FormMultiSelect, FormRadioGroup } from '@/app/_common/components/form';
import { useUrlHash } from '@/app/_common/hooks';
import { createError } from '@/app/_common/lib/error';
import { cn } from '@/app/_common/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Card,
  Checkbox,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  <PERSON><PERSON>,
  <PERSON>ara<PERSON>,
  Spinner,
  <PERSON>,
  Tabs,
} from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import { BannerListTile, DappListTile, SectionTitle, TokenListTile, WalletPreviewer } from '../../../_components';
import { DEFAULT_FEATURES, MOCK_BANNERS, MOCK_DAPPS } from '../../../_constant';
import { useUpdateWallet } from '../../../_services/command';
import { useWalletProjects } from '../../../_services/query';
import { Backend } from '../../../_types';
import { AppSettingSchema, type AppSettings } from '../../../_types/app';
import {
  appSettingsKeys,
  getWalletPreviewUri,
  transformAppSettingsToBackend,
  transformAppSettingsToRemoteConfig,
  transformDtoToAppSettings,
} from '../../../_utils';
import { WithNull } from '../../../_utils/type';

const steps: {
  stepNumber: number;
  label: React.ReactNode;
  value: string;
  desc?: React.ReactNode;
}[] = [
  {
    stepNumber: 1,
    label: <FormattedMessage id="kgstudio.wallet.config.steps.feature" />,
    value: 'feature',
  },
  {
    stepNumber: 2,
    label: <FormattedMessage id="kgstudio.wallet.config.steps.theme" />,
    value: 'theme',
  },
  {
    stepNumber: 3,
    label: <FormattedMessage id="kgstudio.wallet.config.steps.contact" />,
    value: 'contact',
  },
  {
    stepNumber: 4,
    label: <FormattedMessage id="kgstudio.wallet.config.steps.explorer" />,
    value: 'explorer',
    desc: <FormattedMessage id="kgstudio.wallet.config.steps.explorer-hint" />,
  },
  {
    stepNumber: 5,
    label: <FormattedMessage id="kgstudio.wallet.config.steps.check" />,
    value: 'check',
  },
];

const formDefaultValues: AppSettings = {
  default: {
    feature: false,
    dapp_list: 'false',
  },
  ...DEFAULT_FEATURES,

  help_center_url: '',
  support_email: '',
  privacy_and_legal_url: '',
  privacy_policy_url: '',
  discord_url: '',
  twitter_url: '',
  telegram_url: '',

  explorer_banner_item: [...MOCK_BANNERS],
  explorer_screen_recommend_item: [...MOCK_DAPPS],
};

const appSettingsError = createError('AppSettingsError', 'AppSettingsError');

const AppSettings = () => {
  const t = useTranslations();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const step = searchParams?.get('step') ?? '';
  const projectId = params == null ? '' : (params.id as string);
  const anchor = useUrlHash();

  const [currentStepValue, setCurrentStepValue] = useState('feature');
  const [modalOpen, setModalOpen] = useState(false);

  const queryClient = useQueryClient();
  const { data: wallets, error: walletsError, isLoading: walletsIsLoading } = useWalletProjects({});
  const {
    mutate: updateWalletSettings,
    error: updateWalletSettingsError,
    isSuccess: updateWalletSettingsSuccess,
    isLoading: updateWalletSettingsLoading,
    reset: updateWalletSettingsReset,
  } = useUpdateWallet({
    onSuccess: () => {
      showToast(t('kgstudio.common.project-updated'), 'success');
      queryClient.refetchQueries(['walletProjects']);
    },
  });

  const form = useForm<AppSettings>({
    resolver: zodResolver(AppSettingSchema(t)),
    defaultValues: formDefaultValues,
    mode: 'all',
  });
  const currentStep = steps.find((step) => step.value === currentStepValue);

  const scope = form.watch('chain_scope');
  const primaryColor = form.watch('primary_color');
  const secondaryColor = form.watch('secondary_color');
  const defaultSettings = form.watch('default');

  const featureSectionDirty = Object.keys(form.formState.dirtyFields).some((key) =>
    [
      'default',
      'chain_scope',
      'chain_ids',
      'supported_asset_types',
      'modules',
      'supported_locales',
      'custom_tokens',
      'login_methods',
    ].includes(key),
  );
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const hasNextStep = steps.some((step) => step.stepNumber === currentStep!.stepNumber + 1);
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const hasPrevStep = steps.some((step) => step.stepNumber === currentStep!.stepNumber - 1);
  const handleNextStep = () => {
    if (!hasNextStep) return;

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const nextStep = steps.find((step) => step.stepNumber === currentStep!.stepNumber + 1);

    if (!nextStep) return;
    setCurrentStepValue(nextStep.value);
  };

  const handlePrevStep = () => {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const prevStep = steps.find((step) => step.stepNumber === currentStep!.stepNumber - 1);

    if (!prevStep) return;

    setCurrentStepValue(prevStep.value);
  };

  const onSubmit = (values: AppSettings) => {
    const transformedData = transformAppSettingsToBackend(values);

    setModalOpen(true);
    updateWalletSettings({
      project_id: Number(projectId as string),
      ...transformedData,
    });
  };

  const handleSaveDraft = () => {
    const transformedData = transformAppSettingsToBackend(form.getValues());

    updateWalletSettings({
      project_id: Number(projectId as string),
      ...transformedData,
    });
  };

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (!wallets || !projectId) return;

    const wallet = wallets.data.find((wallet) => wallet.project_id === Number(projectId));

    const walletNotFoundError = createError('WalletNotFoundError', 'WalletNotFoundError');
    if (!wallet) throw appSettingsError(walletNotFoundError("Can't find wallet with id: " + projectId));

    if (wallet.config === null || wallet.theme === null) {
      return form.reset({
        ...formDefaultValues,
        default: {
          feature: true,
          dapp_list: 'false',
        },
      });
    }

    const appSettingsDto = pick(
      {
        ...wallet.config,
        ...wallet.theme,
      },
      appSettingsKeys,
    ) as unknown as WithNull<Backend.AppSettings> & WithNull<Backend.WalletTheme>;
    const appSettings = transformDtoToAppSettings(appSettingsDto);

    // FIXME: This is a temp workaround for old data without default field
    if (appSettings.default === undefined) {
      appSettings.default = {
        feature: false,
        dapp_list: 'false',
      };
    }

    form.reset({
      ...formDefaultValues,
      ...appSettings,
    });
  }, [wallets, projectId, form]);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (!defaultSettings.feature) return;

    form.reset({
      ...form.getValues(),
      ...DEFAULT_FEATURES,
    });
  }, [defaultSettings.feature, form]);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (!featureSectionDirty) return;

    form.setValue('default.feature', false);
  }, [featureSectionDirty, form]);
  useEffect(() => {
    if (!step) return;

    setCurrentStepValue(step);
  }, [step]);
  useEffect(() => {
    if (!anchor || currentStepValue !== step) return;

    const element = document.getElementById(anchor);
    if (!element) return;

    element.scrollIntoView({
      behavior: 'smooth',
    });
  }, [anchor, currentStepValue, step]);

  if (!currentStep) {
    const stepNotFoundError = createError('StepNotFoundError', 'StepNotFoundError');
    throw appSettingsError(stepNotFoundError(`Can not find current step: ${currentStepValue}`));
  }

  if (!wallets || walletsIsLoading) return <div>Loading...</div>;
  if (!wallets || walletsError) return <div>Error...</div>;

  return (
    <div className="mx-auto w-[768px] max-w-[768px] space-y-10">
      <Steps steps={steps} value={currentStepValue} onValueChange={setCurrentStepValue}>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-10">
            <Steps.Display />
            <Card className="border-primary space-y-6 border p-10">
              <div className="flex flex-col items-start gap-2">
                <h1 className="text-h1 text-primary capitalize">{currentStep.label}</h1>
                {currentStep.desc && <p className="body-2 text-secondary">{currentStep.desc}</p>}
              </div>
              <Separator />

              <Steps.Content value="feature">
                <fieldset className="space-y-6">
                  <FormField
                    name="default.feature"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem className="bg-brand-primary-lighter text-primary border-brand-primary flex items-center gap-1 rounded-xl border p-3">
                        <FormControl>
                          <Checkbox
                            name="defaultFeatureSettings"
                            checked={!!field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel>
                          <FormattedMessage id="kgstudio.wallet.use-recommended-option" />
                        </FormLabel>
                      </FormItem>
                    )}
                  />
                  <FormRadioGroup
                    title={t('kgstudio.wallet.config.title.displayed-chains')}
                    desc={t('kgstudio.wallet.config.desc.displayed-chains-desc')}
                    name="chain_scope"
                    control={form.control}
                    required
                    items={[
                      {
                        label: t('kgstudio.wallet.config.label.all-chains'),
                        desc: t('kgstudio.wallet.config.desc.all-chains-desc'),
                        value: 'all',
                      },
                      {
                        label: t('kgstudio.wallet.config.label.all-evm-chains'),
                        desc: t('kgstudio.wallet.config.desc.all-evm-chains-desc'),
                        value: 'evm',
                      },
                      {
                        label: t('kgstudio.wallet.config.label.custom'),
                        desc: t('kgstudio.wallet.config.desc.custom-desc'),
                        value: 'custom',
                      },
                    ]}
                  />
                  <div
                    className={cn('border-primary border-l px-6 py-3', {
                      hidden: scope !== 'custom',
                    })}
                  >
                    <FormMultiSelect
                      name="chain_ids"
                      control={form.control}
                      items={[
                        {
                          id: 'ethereum',
                          label: 'Ethereum',
                        },
                        {
                          id: 'polygon',
                          label: 'Polygon',
                        },
                        {
                          id: 'bsc',
                          label: 'BNB Chain',
                        },
                        {
                          id: 'arb',
                          label: 'Arbitrum',
                        },
                        {
                          id: 'kcc',
                          label: 'KCC',
                        },
                        {
                          id: 'ronin',
                          label: 'Ronin',
                        },
                        {
                          id: 'btc',
                          label: 'Bitcoin',
                        },
                        {
                          id: 'solana',
                          label: 'Solana',
                        },
                        {
                          id: 'tron',
                          label: 'Tron',
                        },
                      ]}
                    />
                  </div>

                  <FormMultiSelect
                    title={t('kgstudio.wallet.config.title.displayed-asset-type')}
                    desc={t('kgstudio.wallet.config.desc.displayed-asset-type-desc')}
                    name="supported_asset_types"
                    control={form.control}
                    required
                    items={[
                      {
                        id: 'token',
                        label: t('kgstudio.wallet.config.label.currency'),
                        desc: t('kgstudio.wallet.config.desc.currency-desc'),
                        disabled: true,
                      },
                      {
                        id: 'nft',
                        label: t('kgstudio.wallet.config.label.nft'),
                        desc: t('kgstudio.wallet.config.desc.nft-desc'),
                      },
                      {
                        id: 'defi',
                        label: t('kgstudio.wallet.config.label.defi'),
                        desc: t('kgstudio.wallet.config.desc.defi-desc'),
                      },
                    ]}
                  />
                  <Separator />

                  <FormMultiSelect
                    title={t('kgstudio.wallet.config.title.enable-features')}
                    name="modules"
                    control={form.control}
                    required
                    items={[
                      {
                        id: 'show_swap',
                        label: t('kgstudio.wallet.config.label.swap'),
                        desc: t('kgstudio.wallet.config.desc.swap-desc'),
                      },
                      {
                        id: 'show_explore_tab',
                        label: t('kgstudio.wallet.config.label.explore-dapp-browser'),
                        desc: t('kgstudio.wallet.config.desc.explore-dapp-browser-desc'),
                      },
                      {
                        id: 'show_nft_sell_btn',
                        label: t('kgstudio.wallet.config.label.nft-sell'),
                        desc: t('kgstudio.wallet.config.desc.nft-sell-desc'),
                      },
                      {
                        id: 'show_poap',
                        label: t('kgstudio.wallet.config.label.show-poap'),
                        desc: t('kgstudio.wallet.config.desc.show-poap-desc'),
                      },
                      {
                        id: 'show_rewards',
                        label: t('kgstudio.wallet.config.label.nft-rewards'),
                        desc: t('kgstudio.wallet.config.desc.nft-rewards-desc'),
                      },
                      {
                        id: 'show_kyc',
                        label: t('kgstudio.wallet.config.label.kyc-user-verification'),
                        desc: t('kgstudio.wallet.config.desc.kyc-user-verification-desc'),
                      },
                    ]}
                  />

                  <Separator />
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FormLabel>{t('kgstudio.wallet.config.label.custom-token')}</FormLabel>
                        <div className="bg-brand-primary-lighter text-highlight flex items-center gap-1 rounded-lg p-1">
                          <Crown className="fill-brand-primary h-4 w-4" />
                          <p className="text-body-2 font-bold">{t('kgstudio.wallet.config.label.pro')}</p>
                        </div>
                      </div>

                      <FormDescription>{t('kgstudio.wallet.config.desc.custom-token-description')}</FormDescription>
                    </div>

                    <div className="space-y-2">
                      <Controller
                        name="custom_tokens"
                        control={form.control}
                        render={({ field }) => {
                          return (
                            <>
                              {field.value?.map((token, index) => <TokenListTile key={index} token={token} />)}
                              <div className="border-primary flex items-center gap-3 rounded-lg border p-3">
                                <div className="bg-surface-secondary flex h-10 w-10 items-center justify-center rounded-full">
                                  <Plus className="text-primary h-4 w-4" />
                                </div>
                                <p className="text-body-2 font-bold">{t('kgstudio.wallet.config.label.add')}</p>
                              </div>
                            </>
                          );
                        }}
                      />
                    </div>
                  </div>

                  <Separator />

                  <FormMultiSelect
                    title={t('kgstudio.wallet.config.title.languages')}
                    desc={t('kgstudio.wallet.config.desc.languages')}
                    name="supported_locales"
                    control={form.control}
                    required
                    items={[
                      {
                        id: 'en_US',
                        label: t('kgstudio.common.language.english'),
                        desc: t('kgstudio.wallet.config.desc.english'),
                        disabled: true,
                      },
                      {
                        id: 'zh_TW',
                        label: t('kgstudio.common.language.traditional-chinese'),
                        desc: 'Chinese (Traditional)',
                      },
                      {
                        id: 'zh_CN',
                        label: t('kgstudio.common.language.simplified-chinese'),
                        desc: 'Chinese (Simplified)',
                      },
                      {
                        id: 'ja_JP',
                        label: t('kgstudio.common.language.japanese'),
                        desc: 'Japanese',
                      },
                    ]}
                  />

                  <Separator />

                  <FormMultiSelect
                    title={t('kgstudio.wallet.config.title.login-methods')}
                    desc={t('kgstudio.wallet.config.desc.login-methods')}
                    name="login_methods"
                    control={form.control}
                    required
                    items={[
                      {
                        id: 'phone',
                        label: t('kgstudio.wallet.config.label.phone'),
                        disabled: true,
                      },
                      {
                        id: 'email',
                        label: t('kgstudio.wallet.config.label.email'),
                        disabled: true,
                      },
                    ]}
                  />
                </fieldset>
              </Steps.Content>

              <Steps.Content value="theme">
                <fieldset className="space-y-10">
                  <section className="space-y-10">
                    <SectionTitle
                      title={t('kgstudio.wallet.config.title.theme')}
                      desc={t('kgstudio.wallet.config.desc.theme')}
                    />
                    <div className="grid grid-cols-2 gap-10">
                      <FormColorPicker
                        title={t('kgstudio.wallet.config.title.primary-color')}
                        required
                        name="primary_color"
                        control={form.control}
                      />
                      <FormColorPicker
                        title={t('kgstudio.wallet.config.title.secondary-color')}
                        required
                        name="secondary_color"
                        control={form.control}
                      />
                    </div>
                  </section>
                  <WalletPreviewer primaryColor={primaryColor} secondaryColor={secondaryColor}>
                    <div className="space-y-3">
                      <div className="bg-warning-light text-warning inline-block rounded-lg px-2 py-1">
                        {t('kgstudio.wallet.config.label.preview')}
                      </div>
                      <div className="flex justify-between">
                        <WalletPreviewer.Home />
                        <WalletPreviewer.Intro />
                        <WalletPreviewer.NftList />
                      </div>
                    </div>
                  </WalletPreviewer>
                </fieldset>
              </Steps.Content>

              <Steps.Content value="contact">
                <fieldset className="space-y-6">
                  <section className="space-y-6">
                    <SectionTitle
                      title={t('kgstudio.wallet.config.title.support-info')}
                      desc={t('kgstudio.wallet.config.desc.support-info')}
                    />
                    <div className="grid grid-cols-2 gap-x-10 gap-y-6">
                      <FormInput
                        title={t('kgstudio.wallet.config.title.support-email')}
                        required
                        name="support_email"
                        control={form.control}
                        placeholder="Email"
                      />
                      <FormInput
                        title={t('kgstudio.wallet.config.title.help-center-url')}
                        required
                        name="help_center_url"
                        control={form.control}
                        placeholder="https://"
                      />
                      <FormInput
                        title={t('kgstudio.wallet.config.title.terms-condition-url')}
                        required
                        name="privacy_and_legal_url"
                        control={form.control}
                        placeholder="https://"
                      />
                      <FormInput
                        title={t('kgstudio.wallet.config.title.privacy-policy-url')}
                        required
                        name="privacy_policy_url"
                        control={form.control}
                        placeholder="https://"
                      />
                    </div>
                  </section>
                  <Separator />
                  <section className="space-y-10">
                    <SectionTitle
                      title={t('kgstudio.common.community-links')}
                      desc={t('kgstudio.wallet.config.desc.displayed-asset-type-desc')}
                    />
                    <div className="grid grid-cols-2 gap-x-10 gap-y-6">
                      <FormInput
                        title={t('kgstudio.common.twitter')}
                        required
                        name="twitter_url"
                        control={form.control}
                        placeholder="https://"
                      />
                      <FormInput
                        title={t('kgstudio.common.discord')}
                        required
                        name="discord_url"
                        control={form.control}
                        placeholder="https://"
                      />
                      <FormInput
                        title={t('kgstudio.common.telegram')}
                        required
                        name="telegram_url"
                        control={form.control}
                        placeholder="https://"
                      />
                    </div>
                  </section>
                </fieldset>
              </Steps.Content>

              <Steps.Content value="explorer">
                <fieldset className="space-y-6">
                  <section id="banner">
                    <SectionTitle
                      title={t('kgstudio.wallet.config.promote-banner.title')}
                      desc={t('kgstudio.wallet.config.promote-banner.desc')}
                    />

                    <Tabs defaultValue="en_US" className="w-full">
                      <Tabs.List>
                        <Tabs.Trigger value="en_US">{t('kgstudio.common.language.english')}</Tabs.Trigger>
                        <Tabs.Trigger value="zh_TW">{t('kgstudio.common.language.traditional-chinese')}</Tabs.Trigger>
                        <Tabs.Trigger value="zh-CN">{t('kgstudio.common.language.simplified-chinese')}</Tabs.Trigger>
                        <Tabs.Trigger value="ja_JP">{t('kgstudio.common.language.japanese')}</Tabs.Trigger>
                        <Tabs.Trigger value="vi_VN">{t('kgstudio.common.language.vietnamese')}</Tabs.Trigger>
                      </Tabs.List>

                      <Tabs.Content value="en_US">
                        <div className="flex flex-col gap-2">
                          <Controller
                            name="explorer_banner_item"
                            control={form.control}
                            render={({ field }) => (
                              <>
                                {field.value.map((banner, index) => (
                                  <BannerListTile key={index} banner={banner} />
                                ))}
                                <div className="border-primary flex items-center gap-3 rounded-lg border p-3">
                                  <div className="bg-surface-secondary flex h-[80px] w-[240px] items-center justify-center rounded-lg">
                                    <Plus className="text-primary h-4 w-4" />
                                  </div>
                                  <p className="text-body-2 font-bold">{t('kgstudio.wallet.config.label.add')}</p>
                                </div>
                              </>
                            )}
                          />
                        </div>
                      </Tabs.Content>
                    </Tabs>
                  </section>
                  <Separator />
                  <section id="dapp">
                    <FormRadioGroup
                      title={t('kgstudio.wallet.config.title.dapp-list')}
                      desc={t('kgstudio.wallet.config.desc.dapp-list')}
                      name="default.dapp_list"
                      control={form.control}
                      items={[
                        {
                          label: t('kgstudio.wallet.config.label.default-list'),
                          value: 'true',
                        },
                        {
                          label: t('kgstudio.wallet.config.label.custom-list'),
                          value: 'false',
                        },
                      ]}
                    />

                    {defaultSettings.dapp_list === 'false' && (
                      <Tabs defaultValue="hot" className="mt-6 w-full">
                        <Tabs.List>
                          <Tabs.Trigger value="hot">{t('kgstudio.wallet.config.tabs.hot')}</Tabs.Trigger>
                          <Tabs.Trigger value="nft">{t('kgstudio.wallet.config.tabs.nft')}</Tabs.Trigger>
                          <Tabs.Trigger value="dex">{t('kgstudio.wallet.config.tabs.dex')}</Tabs.Trigger>
                        </Tabs.List>

                        <Tabs.Content value="hot">
                          <div className="flex flex-col gap-3">
                            <Controller
                              name="explorer_screen_recommend_item"
                              control={form.control}
                              render={({ field }) => (
                                <>
                                  {field.value.map((dapp, index) => (
                                    <DappListTile key={index} dapp={dapp} />
                                  ))}
                                </>
                              )}
                            />
                          </div>
                        </Tabs.Content>
                      </Tabs>
                    )}
                  </section>
                </fieldset>
              </Steps.Content>

              <Steps.Content value="check">
                <div className="space-y-6">
                  <div className="text-h3 font-bold text-[#2B3674]">
                    <p>{t('kgstudio.wallet.config.completion.title')}</p>
                    <ol className="list-inside list-decimal">
                      <li>{t('kgstudio.wallet.config.completion.step1')}</li>
                      <li>{t('kgstudio.wallet.config.completion.step2')}</li>
                      <li>{t('kgstudio.wallet.config.completion.step3')}</li>
                    </ol>
                  </div>

                  <div className="border-primary flex flex-col items-center justify-center gap-6 rounded-xl border p-6">
                    <div className="text-h3 flex flex-col items-center text-center text-[#2B3674]">
                      <p>{t('kgstudio.wallet.config.scanToDemo.title1')}</p>
                      <p>{t('kgstudio.wallet.config.scanToDemo.title2')}</p>
                    </div>

                    {currentStep.value === 'check' && (
                      <div>
                        <QRCodeCanvas
                          size={400}
                          level="L"
                          value={getWalletPreviewUri(transformAppSettingsToRemoteConfig(form.getValues()))}
                        />
                      </div>
                    )}

                    <p className="text-body-2 text-secondary">{t('kgstudio.wallet.config.scanInstruction')}</p>
                  </div>
                </div>
              </Steps.Content>
            </Card>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {hasPrevStep && (
                  <Button variant="secondary" onClick={handlePrevStep} type="button">
                    {t('kgstudio.common.prev-step')}
                  </Button>
                )}
                <Button variant="secondary" type="button" onClick={handleSaveDraft}>
                  {t('kgstudio.common.save-draft')}
                </Button>
              </div>
              {hasNextStep ? (
                <Button
                  type="button"
                  {...{
                    onClick:
                      currentStep.stepNumber === 4
                        ? async (e: React.MouseEvent<HTMLButtonElement>) => {
                            e.preventDefault();
                            const validationResult = await form.trigger();
                            if (!validationResult)
                              return showToast(t('kgstudio.wallet.config.data-verification'), 'error');

                            handleNextStep();
                          }
                        : handleNextStep,
                  }}
                >
                  {t('kgstudio.common.next-step')}
                </Button>
              ) : (
                <Button>{t('kgstudio.common.send')}</Button>
              )}
            </div>

            <Modal open={modalOpen} onOpenChange={setModalOpen}>
              {updateWalletSettingsLoading && (
                <Modal.Content>
                  <Modal.Header type="center">
                    <Modal.Title>{t('kgstudio.wallet.config.submitting-app-settings')}</Modal.Title>
                  </Modal.Header>
                  <div className="flex flex-col items-center gap-10 text-center">
                    <Spinner className="animate-spin" />
                    <p className="text-body-2 text-center">{t('kgstudio.wallet.config.processing-settings')}</p>
                  </div>
                </Modal.Content>
              )}
              {updateWalletSettingsError && (
                <Modal.Content>
                  <Modal.Header type="center">
                    <Modal.Title>{t('kgstudio.wallet.config.submit-failed')}</Modal.Title>
                  </Modal.Header>
                  <p className="text-body-2 text-center">{t('kgstudio.wallet.config.retry-or-contact')}</p>

                  <Modal.Footer>
                    <Modal.Close asChild>
                      <Button
                        className="w-full"
                        onClick={() => {
                          setModalOpen(false);
                          updateWalletSettingsReset();
                        }}
                      >
                        {t('kgstudio.common.close')}
                      </Button>
                    </Modal.Close>
                  </Modal.Footer>
                </Modal.Content>
              )}
              {updateWalletSettingsSuccess && (
                <Modal.Content>
                  <Modal.Header type="center">
                    <Modal.Title>{t('kgstudio.wallet.config.submit-success')}</Modal.Title>
                  </Modal.Header>
                  <Modal.Footer className="grid w-full grid-cols-2 gap-6">
                    <Button
                      variant="secondary"
                      type="button"
                      className="w-full"
                      onClick={() => {
                        router.push(`/wallet/projects/${projectId}`);
                      }}
                    >
                      {t('kgstudio.wallet.config.configure-later')}
                    </Button>
                    <Button
                      className="w-full"
                      onClick={() => {
                        router.push(`/wallet/projects/${projectId}/publish-settings/edit`);
                      }}
                    >
                      {t('kgstudio.wallet.config.configure-publish-data')}
                    </Button>
                  </Modal.Footer>
                </Modal.Content>
              )}
            </Modal>
          </form>
        </Form>
      </Steps>
    </div>
  );
};

export default AppSettings;
