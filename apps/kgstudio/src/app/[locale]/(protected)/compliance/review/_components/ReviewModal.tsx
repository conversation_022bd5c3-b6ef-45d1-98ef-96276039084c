import { ArrowR<PERSON>, Check, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { match } from 'ts-pattern';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import { LoadingModal } from '@/app/_common/components';
import { KycStatusBadge } from '@/app/_common/components/badge';
import { FormTextarea } from '@/app/_common/components/form';
import { cn } from '@/app/_common/lib/utils';
import { apiComplianceHooks, apiUser360Hooks } from '@/app/_common/services';
import { ReviewCase } from '@/app/_common/services/compliance/model';
import { useOrganizationStore } from '@/app/_common/store';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Form, FormControl, FormField, FormItem, Modal } from '@kryptogo/2b';
import { useQueryClient } from '@tanstack/react-query';

import { PersonalInfo } from './PersonalInfo';
import { ReviewResult } from './ReviewResult';
import { RiskScreening } from './RiskScreening';

export interface ReviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  data: ReviewCase;
}
const ReviewFormSchema = (t: any) =>
  z
    .object({
      kyc_status: z.union([z.literal(''), z.literal('rejected'), z.literal('verified')]),
      internal_notes: z
        .string()
        .trim()
        .max(50, {
          message: t('kgstudio.review.internal-notes-length-limit'),
        })
        .optional(),
    })
    .refine(
      (data) => {
        if (data.kyc_status === 'rejected') {
          return data.internal_notes && data.internal_notes.trim().length > 0;
        }
        return true;
      },
      {
        message: t('kgstudio.review.internal-notes-required'),
        path: ['internal_notes'],
      },
    );

type ReviewForm = z.infer<ReturnType<typeof ReviewFormSchema>>;

const ReviewModal = ({ open, onOpenChange, data }: ReviewModalProps) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  const orgId = useOrganizationStore((state) => state.orgId);
  const [modalMode, setModalMode] = useState<'detail' | 'review'>(data.kyc_status === 'pending' ? 'review' : 'detail');
  const [reviewPanelOpen, setReviewPanelOpen] = useState(false);

  const form = useForm<ReviewForm>({
    defaultValues: {
      kyc_status: '',
      internal_notes: '',
    },
    values: {
      kyc_status: data.kyc_status === 'rejected' || data.kyc_status === 'verified' ? data.kyc_status : '',
      internal_notes: data.internal_notes || '',
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    resolver: zodResolver(ReviewFormSchema(t)),
  });

  const {
    mutate: reviewCase,
    isLoading: reviewCaseLoading,
    error: reviewCaseError,
  } = apiComplianceHooks.useReviewComplianceCase(
    {
      params: { org_id: Number(orgId), uid: data.uid },
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries({
          queryKey: apiComplianceHooks.getKeyByAlias('getComplianceCases'),
        });
        queryClient.refetchQueries({
          queryKey: apiUser360Hooks.getKeyByAlias('getComplianceStat'),
        });
        onOpenChange(false);
      },
    },
  );

  const modalTitle = match(modalMode)
    .with('review', () => 'Review')
    .with('detail', () => 'Review Details')
    .exhaustive();
  const isAccepting = form.watch('kyc_status') === 'verified';
  const isKycStatusChanged = !(
    data.kyc_status === form.watch('kyc_status') ||
    (data.kyc_status === 'verified' && isAccepting)
  );

  const onSubmit = () => {
    reviewCase({
      kyc_status: form.getValues('kyc_status') as 'verified' | 'rejected',
      internal_notes: form.getValues('internal_notes') || '',
    });
  };

  useEffect(() => {
    if (!reviewCaseError) return;

    console.error(reviewCaseError);
    showToast(t('kgstudio.common.error'), 'error');
  }, [reviewCaseError, t]);

  useEffect(() => {
    // every time the modal is opened, reset states
    setReviewPanelOpen(false);
    setModalMode(data.kyc_status === 'pending' ? 'review' : 'detail');
  }, [data.kyc_status, open]);

  return (
    <>
      <Modal open={open} onOpenChange={onOpenChange}>
        <Form {...form}>
          <Modal.Content className="scrollbar-hide max-h-[80%] w-[375px] overflow-scroll md:!w-[768px] [&_.h-full]:space-y-4">
            <Modal.Header>
              <Modal.Title className="flex flex-col items-start gap-1">{modalTitle}</Modal.Title>
            </Modal.Header>

            <div
              className="radius-medium list-spacing-medium border-primary card-padding-large flex flex-col border"
              data-cy="review-modal-personal-info"
            >
              <PersonalInfo data={data} />
              {modalMode === 'detail' && (
                <ReviewResult
                  data={data}
                  onEdit={() => {
                    setModalMode('review');
                    if (data.kyc_status === 'pending' || data.kyc_status === 'processing') return;

                    form.setValue('kyc_status', data.kyc_status);
                    setReviewPanelOpen(true);
                  }}
                />
              )}
            </div>

            <RiskScreening data={data} />

            {modalMode === 'review' && (
              <Modal.Footer className="space-y-6" data-cy="review-modal-panel">
                <FormField
                  control={form.control}
                  name="kyc_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="grid grid-cols-2 gap-3">
                          <Button
                            value="rejected"
                            type="button"
                            variant={form.getValues('kyc_status') === 'verified' ? 'grey' : 'red'}
                            icon={<X />}
                            onClick={() => {
                              field.onChange('rejected');
                              setReviewPanelOpen(true);
                            }}
                            data-cy="review-modal-panel-reject-button"
                          >
                            {t('kgstudio.common.reject')}
                          </Button>
                          <Button
                            type="button"
                            variant={form.getValues('kyc_status') === 'rejected' ? 'grey' : 'green'}
                            icon={<Check />}
                            onClick={() => {
                              field.onChange('verified');
                              form.trigger('internal_notes');
                              setReviewPanelOpen(true);
                            }}
                            data-cy="review-modal-panel-accept-button"
                          >
                            {t('kgstudio.common.accept')}
                          </Button>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
                {reviewPanelOpen && (
                  <div
                    className={cn('radius-medium card-padding-large list-spacing-small relative flex flex-col border', {
                      'border-error': !isAccepting,
                      'border-success': isAccepting,
                    })}
                    data-cy="review-modal-form"
                  >
                    {isKycStatusChanged && (
                      <div className="space-y-2" data-cy="review-modal-form-status-changed">
                        <p className="text-body-bold text-primary">Status will change</p>
                        <div className="flex items-center gap-1">
                          <KycStatusBadge status={data.kyc_status} />
                          <ArrowRight size={16} />
                          {isAccepting ? (
                            <KycStatusBadge status={'verified'} />
                          ) : (
                            <KycStatusBadge status={'rejected'} />
                          )}
                        </div>
                      </div>
                    )}

                    <FormTextarea
                      title="Internal note"
                      name="internal_notes"
                      control={form.control}
                      required={!isAccepting}
                      data-cy="review-modal-form-internal-notes"
                    />
                    <div className="list-spacing-medium grid grid-cols-1 gap-3 md:grid-cols-2">
                      <Button
                        type="button"
                        variant="grey"
                        onClick={() => {
                          form.resetField('internal_notes');
                          if (data.kyc_status === 'pending') {
                            form.setValue('kyc_status', '');
                            setReviewPanelOpen(false);
                            return;
                          }
                          setModalMode('detail');
                        }}
                        data-cy="review-modal-panel-cancel-button"
                      >
                        {t('kgstudio.common.cancel')}
                      </Button>
                      <Button onClick={form.handleSubmit(onSubmit)} data-cy="review-modal-panel-confirm-button">
                        {t('common.confirm')}
                      </Button>
                    </div>

                    <div
                      className={cn(
                        'absolute top-[-1px] h-3 w-3 -translate-y-1/2 rotate-45 transform border-l border-t bg-white',
                        {
                          'border-error left-1/4 -translate-x-1/2': !isAccepting,
                          'border-success right-1/4 translate-x-1/2': isAccepting,
                        },
                      )}
                    />
                  </div>
                )}
              </Modal.Footer>
            )}
          </Modal.Content>
        </Form>
      </Modal>
      <LoadingModal open={reviewCaseLoading} />
    </>
  );
};

export { ReviewModal };
