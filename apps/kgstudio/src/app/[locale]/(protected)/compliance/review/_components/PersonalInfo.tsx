import { FormattedMessage } from '@/app/_common/components';
import { KycStatusBadge } from '@/app/_common/components/badge';
import { ReviewCase } from '@/app/_common/services/compliance/model';

const PersonalInfo = ({ data }: { data: ReviewCase }) => {
  return (
    <>
      <div className="space-y-1">
        <div className="text-h2">{data.name ?? 'N/A'}</div>
        {!!data.kyc_status && <KycStatusBadge status={data.kyc_status} data-cy="review-modal-kyc-status-badge" />}
      </div>
      <div className="list-spacing-medium grid grid-cols-2 md:!grid-cols-3">
        <div className="space-y-[4px]">
          <div className="text-body-2 text-secondary">
            <FormattedMessage id="kgstudio.user-dna.national-id" />
          </div>
          <div className="text-body-2-bold text-primary">{data.national_id ?? 'N/A'}</div>
        </div>
        <div className="space-y-[4px]">
          <div className="text-body-2 text-secondary">
            <FormattedMessage id="kgstudio.user-dna.dob" />
          </div>
          <div className="text-body-2-bold text-primary">{data.birthday?.replaceAll('-', '/') ?? 'N/A'}</div>
        </div>
        <div className="space-y-[4px]">
          <div className="text-body-2 text-secondary">
            <FormattedMessage id="kgstudio.audience.country" />
          </div>
          <div className="text-body-2-bold text-primary capitalize">{data.country ?? 'N/A'}</div>
        </div>
        <div className="space-y-[4px]">
          <div className="text-body-2 text-secondary">
            <FormattedMessage id="common.email" />
          </div>
          <div className="text-body-2-bold text-primary break-words">{data.email ?? 'N/A'}</div>
        </div>
        <div className="space-y-[4px]">
          <div className="text-body-2 text-secondary">
            <FormattedMessage id="kgstudio.user-dna.phone" />
          </div>
          <div className="text-body-2-bold text-primary">{data.phone ?? 'N/A'}</div>
        </div>
        <div className="space-y-[4px]">
          <div className="text-body-2 text-secondary">
            <FormattedMessage id="kgstudio.common.line-id" />
          </div>
          <div className="text-body-2-bold text-primary">{data.line_id ?? 'N/A'}</div>
        </div>
      </div>
    </>
  );
};

export { PersonalInfo };
