import { FormattedMessage } from '@/app/_common/components';
import { cn, formatDate } from '@/app/_common/lib/utils';
import { ReviewCase } from '@/app/_common/services/compliance/model';
import { Button } from '@kryptogo/2b';

const ReviewResult = ({ data, onEdit }: { data: ReviewCase; onEdit: () => void }) => {
  const isVerified = data.kyc_status === 'verified';

  return (
    <div
      className={cn('border-error radius-medium card-padding-large list-spacing-small flex flex-col border', {
        'border-success': isVerified,
        'border-error': !isVerified,
      })}
      data-cy="review-modal-result"
    >
      <div
        className={cn('text-h3 flex justify-between', {
          'text-success': isVerified,
          'text-error': !isVerified,
        })}
      >
        <FormattedMessage id="kgstudio.review.review-result" />
        {': '}
        {isVerified ? (
          <FormattedMessage id="kgstudio.kyc-status.verified" />
        ) : (
          <FormattedMessage id="kgstudio.kyc-status.rejected" />
        )}
        <Button
          type="button"
          className="text-secondary bg-transparent active:bg-transparent md:!p-2"
          onClick={onEdit}
          data-cy="review-modal-result-edit-btn"
        >
          <FormattedMessage id="kgstudio.common.edit" />
        </Button>
      </div>
      <div className="text-secondary list-spacing-small grid grid-cols-[auto_1fr]">
        <span className="text-body-2 text-secondary">
          <FormattedMessage id="kgstudio.review.internal-note" />
        </span>
        <span className="text-body-2-bold text-primary">{data.internal_notes || 'N/A'}</span>
        <span className="text-body-2 text-secondary">
          <FormattedMessage id="kgstudio.review.reviewer" />
        </span>
        <span className="text-body-2-bold text-primary">{data.reviewer_name}</span>
        <span className="text-body-2 text-secondary">
          <FormattedMessage id="kgstudio.review.review-time" />
        </span>
        <span className="text-body-2-bold text-primary">{!!data.reviewed_at && formatDate(data.reviewed_at)}</span>
      </div>
    </div>
  );
};

export { ReviewResult };
