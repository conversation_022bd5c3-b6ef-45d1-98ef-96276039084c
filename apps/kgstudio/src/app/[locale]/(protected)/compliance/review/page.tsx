'use client';

import { Search, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { showToast } from '@/2b/toast';
import {
  ReviewModal,
  TableColumnDesktop,
  TableColumnMobile,
} from '@/app/[locale]/(protected)/compliance/review/_components';
import { FilterItem, FormFilterGroup } from '@/app/_common/components/form';
import { useDebounce, useDeepCompareEffect, useDeviceSize, usePageHeader, useUpdateEffect } from '@/app/_common/hooks';
import { isApiError } from '@/app/_common/lib/api';
import { zodFilterSchema } from '@/app/_common/lib/zod';
import { apiComplianceHooks } from '@/app/_common/services';
import {
  ComplianceReview,
  ReviewCase,
  ReviewIDVSchema,
  ReviewRiskSchema,
  ReviewStatusSchema,
} from '@/app/_common/services/compliance/model';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, DataTable, Form, Input, useDataTable } from '@kryptogo/2b';
import { TableOptions, getCoreRowModel, useReactTable } from '@tanstack/react-table';

const ReviewFilterSchema = z
  .object({
    kyc_status: z.union([ReviewStatusSchema, z.literal('')]).optional(),
    submitted_at_from: z.coerce
      .date()
      .optional()
      .transform((date) => (!!date ? Math.floor(date.getTime() / 1000) : undefined)),
    submitted_at_to: z.coerce
      .date()
      .optional()
      .transform((date) => (!!date ? Math.floor(date.getTime() / 1000) : undefined)),
    risk_label: z.union([ReviewRiskSchema, z.literal('')]).optional(),
    sanctioned: z.union([z.boolean(), z.literal('')]).optional(),
    idv_status: z.union([ReviewIDVSchema, z.literal('')]).optional(),
  })
  .strip();
type ReviewFilter = z.infer<typeof ReviewFilterSchema>;

export default function Review() {
  const t = useTranslations();
  usePageHeader({ title: t('kgstudio.common.review') });

  const params = useSearchParams();
  const parsedParams = zodFilterSchema(ReviewFilterSchema).parse(Object.fromEntries(params.entries()));

  const orgId = useOrganizationStore((state) => state.orgId);
  const { deviceSize } = useDeviceSize();

  const [tableOption, setTableOption] = useState<TableOptions<ComplianceReview['data'][number]>>({
    data: [],
    columns: [],
    manualSorting: true,
    manualPagination: true,
    initialState: {
      sorting: [
        {
          id: 'submitted_at',
          desc: true,
        },
      ],
    },
    meta: {
      hideHeader: deviceSize === 'sm' ? true : false,
      rowHover: deviceSize === 'sm' ? false : true,
    },
    getCoreRowModel: getCoreRowModel(),
  });
  const table = useReactTable(tableOption);
  const { page_number, page_size, page_sort } = useDataTable(table);

  const [modalOpen, setModalOpen] = useState(false);
  const [reviewData, setReviewData] = useState<ReviewCase>();
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 500);

  const form = useForm<ReviewFilter>({
    defaultValues: {
      kyc_status: undefined,
      submitted_at_from: undefined,
      submitted_at_to: undefined,
      risk_label: undefined,
      sanctioned: undefined,
      idv_status: undefined,
    },
    values: parsedParams,
    mode: 'onChange',
    resolver: zodResolver(ReviewFilterSchema),
  });

  const getReviewFilterQuery = (values: ReviewFilter) => {
    const parsedQuery = ReviewFilterSchema.safeParse(values);
    if (!parsedQuery.success) return {};

    const queryObj = { ...parsedQuery.data };
    Object.keys(queryObj).forEach((key) => {
      if (queryObj[key as keyof ReviewFilter] === '') {
        queryObj[key as keyof ReviewFilter] = undefined;
      }
    });
    return queryObj;
  };

  const {
    data: complianceData,
    isLoading: complianceLoading,
    error: complianceError,
  } = apiComplianceHooks.useGetComplianceCases(
    {
      params: { org_id: Number(orgId) },
      queries: {
        page_size,
        page_number,
        q: debouncedQuery.trim() === '' ? undefined : debouncedQuery,
        page_sort: page_sort === '' ? undefined : page_sort,
        ...getReviewFilterQuery(form.watch()),
      },
      queryKey: ['getComplianceCases', orgId],
    },
    {
      enabled: !!orgId,
      staleTime: 60_000,
    },
  );

  const { data: complianceFilterData } = apiComplianceHooks.useGetComplianceCasesFilterOptions(
    {
      params: { org_id: Number(orgId) },
    },
    {
      enabled: !!orgId,
      refetchInterval: 18_000,
    },
  );

  const getFilterItems = useCallback(
    (totalCount?: number) => [
      {
        name: 'kyc_status',
        subject: t('kgstudio.review.kyc-status'),
        type: 'select',
        'data-cy': 'kyc-status',
        totalRecords: totalCount,
        options: [
          { label: `${t('kgstudio.review.processing')}`, value: 'processing' },
          { label: `${t('kgstudio.kyc-status.pending')}`, value: 'pending' },
          { label: `${t('kgstudio.kyc-status.verified')}`, value: 'verified' },
          { label: `${t('kgstudio.transaction.status-reject')}`, value: 'rejected' },
        ],
      },
      {
        names: ['submitted_at_from', 'submitted_at_to'],
        subject: t('kgstudio.review.latest-submit'),
        type: 'date-range',
        'data-cy': 'date-range-submit-time',
        fromDate: new Date((complianceFilterData?.data?.submitted_at_from ?? 0) * 1000),
        toDate: new Date((complianceFilterData?.data?.submitted_at_to ?? 0) * 1000),
      },
      {
        name: 'risk_label',
        subject: t('kgstudio.review.risk'),
        type: 'select',
        options: [
          { label: t('kgstudio.review.high-risk'), value: 'high' },
          { label: t('kgstudio.review.mid-risk'), value: 'mid' },
          { label: t('kgstudio.review.low-risk'), value: 'low' },
        ],
      },
      {
        name: 'sanctioned',
        subject: t('kgstudio.review.sanctioned'),
        type: 'select',
        options: [
          { label: t('kgstudio.review.sanctioned-true'), value: true },
          { label: t('kgstudio.review.sanctioned-false'), value: false },
        ],
      },
      {
        name: 'idv_status',
        subject: t('kgstudio.review.idv'),
        type: 'select',
        options: [
          { label: t('kgstudio.review.idv-pass'), value: 'pass' },
          { label: t('kgstudio.review.idv-fail'), value: 'failed' },
        ],
      },
    ],
    [complianceFilterData?.data, t],
  );
  const filterItems = useMemo(
    () => getFilterItems(complianceData?.paging.total_count) as FilterItem<ReviewFilter>[],
    [complianceData?.paging.total_count, getFilterItems],
  );

  const MemoDesktopColumn = useMemo(() => TableColumnDesktop(t, setModalOpen, setReviewData), [t]);
  const MemoMobileColumn = useMemo(() => TableColumnMobile(t, setModalOpen, setReviewData), [t]);

  useEffect(() => {
    setTableOption((prev) => ({
      ...prev,
      data: complianceData?.data || [],
      columns: deviceSize === 'sm' ? MemoMobileColumn : MemoDesktopColumn,
      meta: {
        hideHeader: deviceSize === 'sm' ? true : false,
        rowHover: deviceSize === 'sm' ? false : true,
      },
    }));
  }, [MemoDesktopColumn, MemoMobileColumn, deviceSize, complianceData]);

  useUpdateEffect(() => {
    if (complianceError) {
      console.error(complianceError);

      if (isApiError(complianceError)) {
        showToast(t('kgstudio.common.error'), 'error');
      }
    }
  }, [complianceError, t]);

  useDeepCompareEffect(() => {
    table.resetPageIndex();
  }, [form.watch(), debouncedQuery]);

  return (
    <>
      <Form {...form}>
        <div className="my-6 flex flex-wrap items-center gap-3">
          <FormFilterGroup control={form.control} items={filterItems} data-cy="cases-filter-group" />
          <Button
            variant="text"
            className="text-brand-primary"
            icon={<X size={16} />}
            onClick={() =>
              Object.keys(form.getValues()).forEach((key) => form.setValue(key as keyof ReviewFilter, undefined))
            }
          >
            {t('common.clear-all')}
          </Button>
        </div>
      </Form>
      <Card className="!p-0">
        <div className="p-4 md:p-6">
          <Input
            className="w-full md:!w-[403px]"
            placeholder={t('kgstudio.review.filter-placeholder')}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.currentTarget.value)}
            value={query}
            suffix={<Search />}
          />
        </div>
        <DataTable
          className="border-y border-[var(--border-primary)] md:border-none"
          table={table}
          isLoading={complianceLoading}
          dataLength={complianceData?.paging.total_count || 0}
          onRowClick={
            deviceSize !== 'sm'
              ? (rowData) => {
                  setModalOpen(true);
                  setReviewData(rowData);
                }
              : undefined
          }
        />
      </Card>
      {reviewData && <ReviewModal open={modalOpen} onOpenChange={setModalOpen} data={reviewData} />}
    </>
  );
}
