import { format, fromUnixTime } from 'date-fns';
import { Edit3, FileText, Mail, Smartphone } from 'lucide-react';
import { match } from 'ts-pattern';

import { CaseIDBadge, KycAlertBadge, KycStatusBadge } from '@/app/_common/components/badge';
import { cn } from '@/app/_common/lib/utils';
import { ComplianceReview, ReviewCase } from '@/app/_common/services/compliance/model';
import Line from '@/assets/sidebar/line';
import { Accordion, Button } from '@kryptogo/2b';
import { ColumnDef } from '@tanstack/react-table';

const TableColumnMobile = (
  t: any,
  setModalOpen: (value: boolean) => void,
  setReviewData: (value: ReviewCase) => void,
): ColumnDef<ComplianceReview['data'][number]>[] => [
  {
    accessorKey: 'name',
    header: t('kgstudio.review.name'),
    meta: {
      withoutPadding: true,
    },
    cell: ({ row }) => {
      const {
        name,
        phone,
        email,
        line_id,
        kyc_status,
        national_id,
        risk_label,
        sanction_matched,
        idv_status,
        form_id,
        idv_id,
        cdd_id,
        submitted_at,
      } = row.original;

      const borderColor = match(kyc_status)
        .with('processing', () => 'border-processing')
        .with('pending', () => 'border-warning')
        .with('verified', () => 'border-success')
        .with('rejected', () => 'border-error')
        .exhaustive();
      const [riskColor, riskLabel] = match(risk_label)
        .with('high', () => ['text-error', t('kgstudio.review.high-risk')])
        .with('mid', () => ['text-warning', t('kgstudio.review.mid-risk')])
        .with('low', () => ['text-success', t('kgstudio.review.low-risk')])
        .otherwise(() => ['', '']);

      return (
        <Accordion type="multiple">
          <Accordion.Item value={`case-${row.index}`} className="rounded-none border-none">
            <Accordion.Trigger showChevronIcon={false} className="w-full p-0">
              <div className={cn('flex h-full w-full items-center border-l-4', borderColor)}>
                <div className="flex w-full items-start justify-between p-4">
                  <div className="space-y-1 font-normal">
                    <p className="text-primary text-body-2">{name}</p>
                    <p className="text-secondary text-small">{national_id}</p>
                    <p className="text-secondary text-small">
                      {`${format(fromUnixTime(submitted_at), 'yyyy/MM/dd HH:mm')} ${t('kgstudio.review.created')}`}
                    </p>
                  </div>
                  <div className="font-normal">
                    <KycStatusBadge status={kyc_status} />
                  </div>
                </div>
              </div>
            </Accordion.Trigger>
            <Accordion.Content className="p-0">
              <div className={cn('h-full w-full border-l-4', borderColor)}>
                <div className="space-y-3 px-4 pb-4">
                  {kyc_status !== 'processing' && (
                    <>
                      <div className="flex gap-3">
                        {riskLabel && <p className={cn('text-body2', riskColor)}>{riskLabel}</p>}
                        {sanction_matched && <KycAlertBadge type="sanctioned" />}
                        {idv_status === 'failed' && <KycAlertBadge type="checkID" />}
                      </div>
                      <div className="flex gap-3">
                        {form_id !== null && <CaseIDBadge type="FORM" id={form_id} />}
                        {cdd_id !== null && <CaseIDBadge type="CDD" id={cdd_id} />}
                        {idv_id !== null && <CaseIDBadge type="IDV" id={idv_id} />}
                      </div>
                    </>
                  )}
                  <div className="flex flex-wrap gap-3">
                    {phone && (
                      <div className="text-secondary flex items-center gap-2">
                        <Smartphone size={12} />
                        <p className="text-small">{phone}</p>
                      </div>
                    )}
                    {email && (
                      <div className="text-secondary flex items-center gap-2">
                        <Mail size={12} />
                        <p className="text-small">{email}</p>
                      </div>
                    )}
                    {line_id && (
                      <div className="text-secondary flex items-center gap-2">
                        <Line />
                        <p className="text-small">{line_id}</p>
                      </div>
                    )}
                  </div>
                  <div className="w-full">
                    {kyc_status === 'pending' && (
                      <Button
                        className="text-button-md mt-2 w-full rounded-lg"
                        onClick={() => {
                          setModalOpen(true);
                          setReviewData(row.original);
                        }}
                      >
                        <span>
                          <Edit3 size={12} />
                        </span>
                        {t('kgstudio.common.review')}
                      </Button>
                    )}
                    {(kyc_status === 'verified' || kyc_status === 'rejected') && (
                      <Button
                        variant="grey"
                        className="text-button-md mt-2 w-full rounded-lg"
                        onClick={() => {
                          setModalOpen(true);
                          setReviewData(row.original);
                        }}
                      >
                        <span>
                          <FileText size={12} />
                        </span>
                        {t('kgstudio.review.details')}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </Accordion.Content>
          </Accordion.Item>
        </Accordion>
      );
    },
  },
];

export { TableColumnMobile };
