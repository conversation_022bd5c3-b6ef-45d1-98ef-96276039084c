import { format, fromUnixTime } from 'date-fns';
import { FileText, Mail, Smartphone } from 'lucide-react';
import { match } from 'ts-pattern';

import { CaseIDBadge, KycAlertBadge, KycStatusBadge, RiskScoreBadge } from '@/app/_common/components/badge';
import { cn } from '@/app/_common/lib/utils';
import { ComplianceReview, ReviewCase } from '@/app/_common/services/compliance/model';
import Line from '@/assets/sidebar/line';
import { Button, Tooltip, TooltipProvider } from '@kryptogo/2b';
import { ColumnDef } from '@tanstack/react-table';

const TableColumnDesktop = (
  t: any,
  setModalOpen: (value: boolean) => void,
  setReviewData: (value: ReviewCase) => void,
): ColumnDef<ComplianceReview['data'][number]>[] => [
  {
    accessorKey: 'name',
    header: t('kgstudio.review.name'),
    meta: {
      withoutPadding: true,
    },
    cell: ({ row }) => {
      const { name, national_id, kyc_status } = row.original;

      const borderColor = match(kyc_status)
        .with('processing', () => 'border-processing')
        .with('pending', () => 'border-warning')
        .with('verified', () => 'border-success')
        .with('rejected', () => 'border-error')
        .exhaustive();

      return (
        <>
          <div className={cn('absolute left-0 top-0  h-full border-l-4', borderColor)}></div>
          <div className="flex w-full items-start justify-between p-4">
            <div className="space-y-1 font-normal">
              <p className="text-primary text-body-2">{name}</p>
              <p className="text-secondary text-small">{national_id}</p>
            </div>
          </div>
        </>
      );
    },
  },
  {
    accessorKey: 'phone',
    header: t('kgstudio.review.personal-information'),
    size: 180,
    cell: ({ row }) => {
      const { phone, email, line_id } = row.original;

      return (
        <div className="flex flex-col gap-1">
          {phone && (
            <div className="text-secondary flex items-center gap-2">
              <Smartphone size={12} className="shrink-0" />
              <p className="text-small">{phone}</p>
            </div>
          )}
          {email && (
            <div className="text-secondary flex items-center gap-2">
              <Mail size={12} className="shrink-0" />
              <p className="text-small truncate">{email}</p>
            </div>
          )}
          {line_id && (
            <div className="text-secondary flex items-center gap-2">
              <Line className="shrink-0" />
              <p className="text-small">{line_id}</p>
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'submitted_at',
    header: t('kgstudio.review.latest-submit'),
    size: 120,
    meta: {
      sortable: true,
    },
    cell: ({ row }) => {
      const { submitted_at } = row.original;

      return <p className="text-primary text-body-2">{format(fromUnixTime(submitted_at), 'yyyy/MM/dd, HH:mm')}</p>;
    },
  },
  {
    accessorKey: 'updated_at',
    header: t('kgstudio.review.updated'),
    size: 120,
    meta: {
      sortable: true,
    },
    cell: ({ row }) => {
      const { updated_at } = row.original;

      return <p className="text-primary text-body-2">{format(fromUnixTime(updated_at), 'yyyy/MM/dd, HH:mm')}</p>;
    },
  },
  {
    accessorKey: 'risk_label',
    header: t('kgstudio.review.summary'),
    cell: ({ row }) => {
      const { risk_label, risk_score, sanction_matched, idv_status, form_id, cdd_id, idv_id } = row.original;

      const [riskColor, riskLabel] = match(risk_label)
        .with('high', () => ['text-error', t('kgstudio.review.high-risk')])
        .with('mid', () => ['text-warning', t('kgstudio.review.mid-risk')])
        .with('low', () => ['text-success', t('kgstudio.review.low-risk')])
        .otherwise(() => ['', '']);

      return (
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <Tooltip.Trigger>
              <div className="flex flex-col items-start gap-1">
                {riskLabel && <p className={cn('text-body2', riskColor)}>{riskLabel}</p>}
                {sanction_matched && <KycAlertBadge type="sanctioned" />}
                {idv_status === 'failed' && <KycAlertBadge type="checkID" />}
              </div>
            </Tooltip.Trigger>
            <Tooltip.Content
              data-test-id="tooltip-summary"
              side="bottom"
              className="bg-surface-primary border-none py-3 shadow-[0px_4px_16px_rgba(27,37,89,0.12)]"
            >
              <div className="space-y-3">
                {form_id !== null && (
                  <div className="flex gap-2">
                    <p className="text-small text-primary min-w-[80px] text-black">
                      {t('kgstudio.review.submission')}:
                    </p>
                    <CaseIDBadge type="FORM" id={form_id} />
                  </div>
                )}
                {cdd_id !== null && (
                  <div className="flex gap-2">
                    <p className="text-small text-primary min-w-[80px] text-black">{t('kgstudio.review.aml-risk')}:</p>
                    <CaseIDBadge type="CDD" id={cdd_id} />
                    {risk_score !== null && risk_label && <RiskScoreBadge riskScore={risk_score} type={risk_label} />}
                    {sanction_matched && <KycAlertBadge type="sanctioned" />}
                  </div>
                )}
                {idv_id !== null && (
                  <div className="flex gap-2">
                    <p className="text-small text-primary min-w-[80px] text-black">
                      {t('kgstudio.review.id-verification')}:
                    </p>
                    <CaseIDBadge type="IDV" id={idv_id} />
                    {idv_status === 'pass' && <KycAlertBadge type="pass" />}
                    {idv_status === 'failed' && <KycAlertBadge type="checkID" />}
                  </div>
                )}
              </div>
              <Tooltip.Arrow className="fill-[var(--surface-primary)]" />
            </Tooltip.Content>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    accessorKey: 'kyc_status',
    header: t('kgstudio.review.kyc-status'),
    size: 100,
    meta: {
      sortable: true,
    },
    cell: ({ row }) => {
      const { kyc_status } = row.original;

      return <KycStatusBadge status={kyc_status} />;
    },
  },
  {
    id: 'action',
    header: () => <div className="flex justify-end">{t('common.action')}</div>,
    cell: ({ row }) => {
      const { kyc_status } = row.original;

      return (
        <div className="flex justify-end gap-2">
          {kyc_status === 'pending' && (
            <Button
              size="md"
              className="leading-none"
              onClick={() => {
                setModalOpen(true);
                setReviewData(row.original);
              }}
            >
              {t('kgstudio.review.btn')}
            </Button>
          )}
          {(kyc_status === 'verified' || kyc_status === 'rejected') && (
            <Button
              variant="grey"
              className="h-9 w-9"
              onClick={() => {
                setModalOpen(true);
                setReviewData(row.original);
              }}
              icon={<FileText />}
            ></Button>
          )}
        </div>
      );
    },
  },
];

export { TableColumnDesktop };
