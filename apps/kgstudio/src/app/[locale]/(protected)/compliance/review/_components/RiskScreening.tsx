import { match } from 'ts-pattern';

import { FormattedMessage } from '@/app/_common/components';
import { CaseIDBadge, KycAlertBadge } from '@/app/_common/components/badge';
import { cn, formatDate } from '@/app/_common/lib/utils';
import { ReviewCase } from '@/app/_common/services/compliance/model';

const RiskScreening = ({ data }: { data: ReviewCase }) => {
  return (
    <div
      className="radius-medium list-spacing-medium border-primary card-padding-large grid grid-cols-2 border"
      data-cy="review-modal-risk-screening"
    >
      <div className="space-y-[4px]">
        <div className="text-body-2 text-secondary">
          <FormattedMessage id="kgstudio.review.latest-submit" />
        </div>
        <div className="text-body-2-bold text-primary">{formatDate(data.submitted_at)}</div>
      </div>
      <div className="space-y-[4px]">
        <div className="text-body-2 text-secondary">
          <FormattedMessage id="kgstudio.review.case-details" />
        </div>
        <div className="text-body-2-bold text-primary flex flex-wrap gap-2">
          {data.form_id !== null && <CaseIDBadge type="FORM" id={data.form_id} />}
          {data.cdd_id !== null && <CaseIDBadge type="CDD" id={data.cdd_id} />}
          {data.idv_id !== null && <CaseIDBadge type="IDV" id={data.idv_id} />}
        </div>
      </div>
      <div className="space-y-[4px]">
        <div className="text-body-2 text-secondary">
          <FormattedMessage id="kgstudio.review.id-verification" />
        </div>
        {!!data.idv_status && <KycAlertBadge type={data.idv_status === 'pass' ? 'pass' : 'checkID'} />}
      </div>
      <div className="space-y-[4px]">
        <div className="text-body-2 text-secondary">
          <FormattedMessage id="kgstudio.review.name-screening" />
        </div>
        <div className="item-gap-small flex flex-wrap">
          {!!data.risk_label && (
            <span
              className={cn('text-body-2-bold text-primary break-words', {
                'text-error': data.risk_label === 'high',
                'text-highlight': data.risk_label === 'mid',
                'text-success': data.risk_label === 'low',
              })}
            >
              {match(data.risk_label)
                .with('high', () => <FormattedMessage id="kgstudio.review.high-risk" />)
                .with('mid', () => <FormattedMessage id="kgstudio.review.mid-risk" />)
                .with('low', () => <FormattedMessage id="kgstudio.review.low-risk" />)
                .exhaustive()}
              {` (${data.risk_score})`}
            </span>
          )}
          {!!data.sanction_matched && <KycAlertBadge type="sanctioned" />}
        </div>
      </div>
    </div>
  );
};

export { RiskScreening };
