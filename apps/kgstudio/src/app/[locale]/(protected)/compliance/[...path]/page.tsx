'use client';

import <PERSON><PERSON> from 'js-cookie';
import { notFound } from 'next/navigation';
import { useEffect } from 'react';
import { match } from 'ts-pattern';

import { useIsClient } from '@/app/_common/hooks';
import { apiOrganizationHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store/useOrgStore';
import { env } from '@/env.mjs';
import { usePathname } from '@/i18n/navigation';

const STUDIO_COMPLIANCE_TOKEN = 'studio_compliance_token';

const Compliance = () => {
  const pathname = usePathname();
  const orgId = useOrganizationStore((state) => state.orgId);
  const complianceOrigin = env.NEXT_PUBLIC_COMPLIANCE_URL.split('/compliance')[0];

  const { isClient } = useIsClient();

  const { mutate: getComplianceToken } = apiOrganizationHooks.useGetComplianceToken(
    { params: { org_id: orgId ?? -1 } },
    {
      onSuccess: ({ data }) => {
        Cookie.set(STUDIO_COMPLIANCE_TOKEN, data.studio_compliance_token, {
          domain: '.kryptogo.com',
          path: '/',
          secure: true,
        });
      },
      onError: (error) => {
        console.error(error);
      },
    },
  );

  const matchedRoute = pathname.split('/compliance/')[1];
  const iframeUrl = match(matchedRoute)
    //
    .with('create-a-task', () => `${env.NEXT_PUBLIC_COMPLIANCE_URL}/search/task`)
    .with('case-management', () => `${env.NEXT_PUBLIC_COMPLIANCE_URL}/customer-case`)
    .with('all-tasks/cdd-tasks', () => `${env.NEXT_PUBLIC_COMPLIANCE_URL}/audit`)
    .with('all-tasks/idv-tasks', () => `${env.NEXT_PUBLIC_COMPLIANCE_URL}/idv`)
    .with('all-tasks/kyt-tasks', () => `${env.NEXT_PUBLIC_COMPLIANCE_URL}/addresses`)
    .with('all-tasks/kyc-form', () => `${env.NEXT_PUBLIC_COMPLIANCE_URL}/submission`)
    .otherwise(() => null);

  useEffect(() => {
    if (orgId && isClient) getComplianceToken(undefined);
  }, [orgId, isClient, getComplianceToken]);

  useEffect(() => {
    const handleIframeMessage = (event: MessageEvent) => {
      if (event.origin !== complianceOrigin) return;

      const { requestToken } = event.data;
      if (requestToken) getComplianceToken(undefined);
    };

    window.addEventListener('message', handleIframeMessage);

    return () => window.removeEventListener('message', handleIframeMessage);
  }, []);

  if (!iframeUrl) {
    return notFound();
  }

  return (
    <iframe
      src={iframeUrl}
      title="KryptoGO Compliance"
      allowFullScreen={true}
      className="h-[calc(100vh-88px)] w-full"
    ></iframe>
  );
};

export default Compliance;
