'use client';

import { jwtDecode } from 'jwt-decode';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import { showToast } from '@/2b/toast';
import { useAcceptInvitation } from '@/app/[locale]/check/_services/command/acceptInvitation';
import { StatusFeedback } from '@/app/_common/components/index';
import { StudioCookieKey } from '@/app/_common/constant';
import { OAuthTokenHandler } from '@/app/_common/lib/OAuthTokenHandler';
import { isApiError } from '@/app/_common/lib/api';
import { apiOrganizationHooks } from '@/app/_common/services';
import { useOrganizationStore } from '@/app/_common/store';
import { useAuthStore } from '@/app/_common/store/useAuthStore';
import { useRouter } from '@/i18n/navigation';

enum FeedbackStatus {
  LOADING = 'loading',
  CHANGE_ACCOUNT = 'change_account',
  INVALID = 'invalid',
  INVITATION_ACCEPTED = 'invitation_accepted',
}
type FeedbackActions = {
  message: {
    title: string;
    description: string;
  };
  link?: {
    href: string;
    text: string;
    onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
  };
};

export default function Index() {
  const { login } = useAuthStore();
  const t = useTranslations();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useMemo(
    () => ({
      kg_token: searchParams?.get('kg_token'),
      org_id: searchParams?.get('organization_id'),
    }),
    [searchParams],
  );
  const [status, setStatus] = useState<FeedbackStatus>(FeedbackStatus.LOADING);
  const [emailMismatch, setEmailMismatch] = useState<boolean>(false);
  const [currentOrgId, setOrgId] = useOrganizationStore((state) => [state.orgId, state.setOrgId]);

  const { data: acceptInvitationResponse, mutate: acceptInvitation } = useAcceptInvitation({
    onSuccess: (data) => {
      if (OAuthTokenHandler.get()) {
        const inviteTokenDecode = jwtDecode(data.studio_token);
        const currentTokenDecode = jwtDecode(OAuthTokenHandler.get() as string);

        if (inviteTokenDecode.sub !== currentTokenDecode.sub) {
          setEmailMismatch(true);
        } else {
          setOrgId(Number(params.org_id));
          router.push('/home/<USER>');
        }
      } else {
        login(data.studio_token);
        setOrgId(Number(params.org_id));
        router.push(`/auth/welcome`);
      }
    },
    onError: (error) => {
      console.error(error);
      if (isApiError(error.cause) && error.cause.status == 409 && error.cause.code == 7015) {
        setStatus(FeedbackStatus.INVITATION_ACCEPTED);
        return;
      }
      setStatus(FeedbackStatus.INVALID);
    },
  });

  const {
    data: currentUser,
    isSuccess: isCurrUserSuccess,
    isError: isCurrUserError,
  } = apiOrganizationHooks.useGetCurrentUserInfo(
    {
      params: { org_id: currentOrgId ?? -1 },
    },
    {
      queryHash: 'currentUser',
      enabled: !!currentOrgId && emailMismatch,
      onError: (error) => {
        console.error(error);
      },
    },
  );

  const {
    data: newUser,
    isSuccess: isNewUserSuccess,
    isError: isNewUserError,
  } = apiOrganizationHooks.useGetCurrentUserInfo(
    {
      params: { org_id: Number(params.org_id) ?? -1 },
      headers: { [StudioCookieKey.STUDIO_OAUTH_TOKEN]: acceptInvitationResponse?.studio_token as string },
    },
    {
      queryHash: 'newUser',
      enabled: !!params.org_id && emailMismatch,
      onError: (error) => {
        console.error(error);
      },
    },
  );

  const inviteEmail = newUser?.data.email;
  const currentEmail = currentUser?.data.email;

  const FEEDBACK_ACTIONS = {
    [FeedbackStatus.LOADING]: {
      message: {
        title: t('kgstudio.check.loading_title'),
        description: t('kgstudio.check.loading_desc'),
      },
    },
    [FeedbackStatus.CHANGE_ACCOUNT]: {
      message: {
        title: t('kgstudio.check.change_acc_title'),
        description: `${t('kgstudio.check.change_acc_apologize')}\n${t('kgstudio.check.change_acc_desc', {
          currentEmail: currentEmail || '',
          inviteEmail: inviteEmail || '',
        })}`,
      },
      link: {
        href: '/auth/welcome',
        text: t('kgstudio.common.logout'),
        onClick: (e) => {
          e.preventDefault();

          if (!acceptInvitationResponse?.studio_token) return;

          showToast(t('kgstudio.check.change_acc_toast'), 'success');

          login(acceptInvitationResponse.studio_token);
          setOrgId(Number(params.org_id));

          router.replace('/auth/welcome');
        },
      },
    },
    [FeedbackStatus.INVALID]: {
      message: {
        title: t('kgstudio.check.invalid_title'),
        description: t('kgstudio.check.invalid_desc'),
      },
      link: {
        href: '/home/<USER>',
        text: t('kgstudio.check.invalid_link'),
      },
    },
    [FeedbackStatus.INVITATION_ACCEPTED]: {
      message: {
        title: t('kgstudio.check.invitation-accepted-title'),
        description: t('kgstudio.check.invitation-accepted-desc'),
      },
      link: {
        href: '/auth/login',
        text: t('kgstudio.check.invitation-accepted-cta'),
      },
    },
  } satisfies Record<FeedbackStatus, FeedbackActions>;

  useEffect(() => {
    if (!params.kg_token || !params.org_id) {
      setStatus(FeedbackStatus.INVALID);
      return;
    }

    acceptInvitation({
      org_id: params.org_id,
      kg_token: params.kg_token,
    });
  }, [params, acceptInvitation]);

  useEffect(() => {
    if (isCurrUserSuccess && isNewUserSuccess) {
      setStatus(FeedbackStatus.CHANGE_ACCOUNT);
    }

    if (isCurrUserError || isNewUserError) {
      setStatus(FeedbackStatus.INVALID);
      return;
    }
  }, [isCurrUserError, isNewUserError, isCurrUserSuccess, isNewUserSuccess]);

  return (
    <div className="relative">
      <main className="flex h-screen items-center justify-center">
        <StatusFeedback>
          <StatusFeedback.Logo />
          <StatusFeedback.Content
            message={{
              title: FEEDBACK_ACTIONS[status].message.title,
              description: FEEDBACK_ACTIONS[status].message.description,
            }}
          />
          {status === FeedbackStatus.LOADING ? (
            <StatusFeedback.Spinner />
          ) : (
            <StatusFeedback.LinkButton
              href={FEEDBACK_ACTIONS[status].link.href}
              onClick={(e) => {
                const linkObj = FEEDBACK_ACTIONS[status].link;
                if ('onClick' in linkObj) {
                  linkObj.onClick(e);
                }
              }}
            >
              {FEEDBACK_ACTIONS[status].link?.text}
            </StatusFeedback.LinkButton>
          )}
        </StatusFeedback>
      </main>
      <footer className="fixed bottom-6 left-1/2 -translate-x-1/2">{/*  */}</footer>
    </div>
  );
}
