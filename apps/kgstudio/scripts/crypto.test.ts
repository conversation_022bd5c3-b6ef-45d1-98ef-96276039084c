import { describe, expect, it } from 'vitest';

import { byteArrayToHexString, decryptRemoteEncryptedData, generateX25519KeyPair } from '../src/app/_common/lib/crypto';

describe('X25519 Key Exchange and Decryption', () => {
  // Test values from the backend
  const clientPrivateKey = 'a8c4559549e14ef46174c521602c20d16e42307dffa680cdd861e19fd7349d5e';
  const serverPublicKey = '6v6juJUQpFIZLPNEVPfdc9jX0k+upNL1X5jcafRdXDk=';
  const encryptedMnemonic =
    'W8I4ShCcMfoLZqj3jlEJQK3I4O5t03n7JuPGU/XKeX5kr+8+H8qbY/DXs8DzrjeEL0oqVLAV7cbicfmy//SvVrHOEXqrN4Imwqlmf1ZW5PM=';
  const expectedDecryptedPrivateKey = 'b7850072a718d0d10359d9c228aab1740db083d811b1f747b321757ed3ac2f42';

  it('should decrypt the encrypted mnemonic correctly with the provided keys', async () => {
    // Attempt to decrypt using the provided keys
    const result = await decryptRemoteEncryptedData(
      clientPrivateKey,
      serverPublicKey,
      encryptedMnemonic,
      'hex', // client private key is in hex format
    );

    console.log('Decryption result:', result);

    // The result should be the private key in hex format
    expect(result).toBe(expectedDecryptedPrivateKey);
  });

  it('should generate a key pair correctly', async () => {
    // Generate a new key pair
    const keyPair = await generateX25519KeyPair('hex');

    console.log('Generated private key:', keyPair.privateKey);
    console.log('Generated public key:', keyPair.publicKey);

    // Verify the keys are non-empty and in the correct format
    expect(keyPair.privateKey).toBeTruthy();
    expect(keyPair.privateKey.length).toBe(64); // 32 bytes in hex = 64 characters
    expect(keyPair.publicKey).toBeTruthy();
    expect(keyPair.publicKey.length).toBe(64); // 32 bytes in hex = 64 characters
  });

  it('should handle base64 server public key correctly', async () => {
    // The server public key is in base64 format, but our function expects hex
    // Let's add a test to verify we can handle this conversion correctly

    // First, convert the base64 server public key to hex format
    const serverPublicKeyBytes = Buffer.from(serverPublicKey, 'base64');
    const serverPublicKeyHex = byteArrayToHexString(new Uint8Array(serverPublicKeyBytes));

    console.log('Server public key (base64):', serverPublicKey);
    console.log('Server public key (hex):', serverPublicKeyHex);

    // Now try decryption with the hex format
    const result = await decryptRemoteEncryptedData(clientPrivateKey, serverPublicKeyHex, encryptedMnemonic, 'hex');

    console.log('Decryption result with hex server key:', result);
    expect(result).toBe(expectedDecryptedPrivateKey);
  });

  it('should decrypt with base64 encoded keys', async () => {
    // Test decryption with both keys in base64 format
    const clientPrivateKeyBase64 = Buffer.from(clientPrivateKey, 'hex').toString('base64');

    console.log('Client private key (base64):', clientPrivateKeyBase64);

    const result = await decryptRemoteEncryptedData(
      clientPrivateKeyBase64,
      serverPublicKey,
      encryptedMnemonic,
      'base64',
    );

    console.log('Decryption result with base64 keys:', result);
    expect(result).toBe(expectedDecryptedPrivateKey);
  });
});
