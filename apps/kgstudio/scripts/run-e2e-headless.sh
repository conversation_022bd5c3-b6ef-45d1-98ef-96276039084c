#!/bin/bash

URL="http://127.0.0.1:3001"
TEST_API=${TEST_API:-false}
# START_SERVER means whether to start frontend server before running the tests
START_SERVER=${START_SERVER:-true}

# Set apiBaseUrl if TEST_API_BASE_URL is provided
ENVS="TEST_API=$TEST_API"
if [[ -n "$TEST_API_BASE_URL" ]]; then
    ENVS="$ENVS,apiBaseUrl=$TEST_API_BASE_URL"
fi

# calculate test spec files by env
SPEC="cypress/integration/ui/**/*"

# This is a reference for future feature flag testing
# if [[ "$TEST_API" != "true" ]]; then
#     if [[ "$NEXT_PUBLIC_KYC_ASSET_PRO_FLAG" == "true" ]]; then
#         SPEC="cypress/integration/ui/send-token-new.spec.ts"
#         ENVS="$ENVS,kycAssetProFlag=true"
#     else
#         SPEC="cypress/integration/ui/**/*"
#         ENVS="$ENVS,kycAssetProFlag=false"
#     fi
# fi

# Conditionally run Cypress tests only if TEST_API is true
if [[ "$TEST_API" == "true" ]]; then
    if [[ "$START_SERVER" == "true" ]]; then
        start-server-and-test start $URL "cypress run -b chrome --e2e --env $ENVS"
    else
        pnpx cypress run -b chrome --e2e --env "$ENVS"
    fi
else
    if [[ "$START_SERVER" == "true" ]]; then
        start-server-and-test start $URL "cypress run -b chrome --e2e --env $ENVS --spec '$SPEC'" 
    else
        pnpx cypress run -b chrome --e2e --env "$ENVS" --spec "$SPEC"
    fi
fi
