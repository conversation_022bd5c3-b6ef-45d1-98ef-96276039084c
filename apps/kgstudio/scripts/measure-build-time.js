#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs');

console.log('🔍 Starting dev server build time measurement...\n');

// Clear .next directory for a fresh build
console.log('🧹 Cleaning .next directory...');
try {
  execSync('rm -rf .next', { stdio: 'inherit' });
} catch {
  // Directory might not exist
}

const startTime = Date.now();
let compileStartTime = null;
let firstCompileEndTime = null;
let isFirstCompileComplete = false;

console.log('🚀 Starting dev server...');

const devProcess = spawn('next', ['dev', '-p', '3000'], {
  stdio: ['ignore', 'pipe', 'pipe'],
  cwd: process.cwd(),
});

let outputBuffer = '';

function parseOutput(data) {
  const output = data.toString();
  outputBuffer += output;

  // Look for compile start indicator
  if (output.includes('Compiling') && !compileStartTime) {
    compileStartTime = Date.now();
    console.log(`⚡ First compilation started at: ${(compileStartTime - startTime) / 1000}s`);
  }

  // Look for compile completion indicators
  if (output.includes('✓ Compiled') && compileStartTime && !isFirstCompileComplete) {
    firstCompileEndTime = Date.now();
    isFirstCompileComplete = true;

    const totalTime = (firstCompileEndTime - startTime) / 1000;
    const compileTime = (firstCompileEndTime - compileStartTime) / 1000;

    console.log(`\n🎉 First compilation completed!`);
    console.log(`📊 Total time from start to first compile: ${totalTime}s`);
    console.log(`📊 Pure compilation time: ${compileTime}s`);
    console.log(`📊 Setup overhead: ${(compileStartTime - startTime) / 1000}s`);

    // Kill the dev server
    devProcess.kill();

    // Save results to file
    const results = {
      timestamp: new Date().toISOString(),
      totalTime,
      compileTime,
      setupTime: (compileStartTime - startTime) / 1000,
      nodeVersion: process.version,
      nextVersion: getNextVersion(),
    };

    fs.writeFileSync('build-timing-results.json', JSON.stringify(results, null, 2));
    console.log(`\n💾 Results saved to build-timing-results.json`);

    process.exit(0);
  }

  // Error handling
  if (output.includes('Error:') || output.includes('error ')) {
    console.error('❌ Build error detected:', output);
  }
}

devProcess.stdout.on('data', parseOutput);
devProcess.stderr.on('data', parseOutput);

devProcess.on('exit', () => {
  if (!isFirstCompileComplete) {
    console.log('\n❌ Dev server exited before first compilation completed');
    console.log('Last output:', outputBuffer.slice(-500));
  }
});

// Timeout after 5 minutes
setTimeout(
  () => {
    console.log('\n⏰ Timeout reached (5 minutes)');
    devProcess.kill();
    process.exit(1);
  },
  5 * 60 * 1000,
);

function getNextVersion() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return packageJson.dependencies.next || 'unknown';
  } catch {
    return 'unknown';
  }
}
