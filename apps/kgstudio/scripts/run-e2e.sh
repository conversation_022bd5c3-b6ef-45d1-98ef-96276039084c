#!/bin/bash

URL="http://127.0.0.1:3001"
TEST_API=${TEST_API:-false}
# START_SERVER means whether to start frontend server before running the tests
START_SERVER=${START_SERVER:-true}

# Set apiBaseUrl if TEST_API_BASE_URL is provided
ENVS="TEST_API=$TEST_API"
if [[ -n "$TEST_API_BASE_URL" ]]; then
    ENVS="$ENVS,apiBaseUrl=$TEST_API_BASE_URL"
fi

# This is a reference for future feature flag testing
# if [[ "$NEXT_PUBLIC_KYC_ASSET_PRO_FLAG" == "true" ]]; then
#     ENVS="$ENVS,kycAssetProFlag=true"
# fi

if [[ "$START_SERVER" == "true" ]]; then
    start-server-and-test dev:test $URL "cypress open -b chrome --e2e --env $ENVS"
else
    cypress open -b chrome --e2e --env "$ENVS"
fi

